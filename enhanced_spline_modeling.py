#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的样条线建模模块
支持切矢方向控制的样条线创建
"""

from typing import List, Dict, Tuple, Optional, Any
from PyQt5.QtWidgets import QTreeWidgetItem, QMessageBox
from PyQt5.QtCore import Qt
from datetime import datetime

# OpenCASCADE导入
from OCC.Core.gp import gp_Pnt, gp_Vec
from OCC.Core.GeomAPI import GeomAPI_Interpolate
from OCC.Core.TColgp import TColgp_HArray1OfPnt, TColgp_Array1OfVec
from OCC.Core.TColStd import TColStd_HArray1OfBoolean
from OCC.Core.AIS import AIS_Shape
from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge, BRepBuilderAPI_MakeVertex
from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE, Quantity_NOC_GREEN, Quantity_NOC_RED
from OCC.Core.Aspect import Aspect_TOM_PLUS
from OCC.Core.Prs3d import Prs3d_PointAspect

def create_spline_with_tangents_preview(points: List[List[float]], tangent_data: Dict[int, Dict] = None):
    """
    创建带切矢约束的预览样条线（仅返回边，不创建AIS对象）

    Args:
        points: 控制点坐标列表 [[x1,y1,z1], [x2,y2,z2], ...]
        tangent_data: 切矢约束数据 {point_index: {'direction': [x,y,z], 'length': float}}

    Returns:
        TopoDS_Edge: 样条线边对象，如果失败返回None
    """
    try:
        print(f"🎨 创建预览样条线，控制点数: {len(points)}")
        if tangent_data:
            print(f"🎨 应用切矢约束，约束点数: {len(tangent_data)}")

        # 使用与正式样条线相同的创建逻辑
        from OCC.Core.TColgp import TColgp_Array1OfPnt, TColgp_HArray1OfPnt, TColgp_Array1OfVec
        from OCC.Core.gp import gp_Pnt, gp_Vec
        from OCC.Core.GeomAPI import GeomAPI_Interpolate
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge

        # 创建控制点数组
        point_array = TColgp_Array1OfPnt(1, len(points))
        for i, point in enumerate(points):
            gp_point = gp_Pnt(point[0], point[1], point[2])
            point_array.SetValue(i + 1, gp_point)

        # 🔧 关键修复：创建Handle类型的点数组
        h_point_array = TColgp_HArray1OfPnt(point_array)

        # 创建插值器
        interpolator = GeomAPI_Interpolate(h_point_array, False, 1e-6)

        # 应用切矢约束 - 🔧 使用与最终样条线创建完全相同的逻辑
        if tangent_data:
            tangent_count = len(tangent_data)
            print(f"🎨 预览应用切矢约束，约束点数: {tangent_count}")

            # 检查是否只有端点切矢
            has_only_endpoints = all(idx == 0 or idx == len(points) - 1 for idx in tangent_data.keys())

            if has_only_endpoints and tangent_count <= 2:
                # 端点切矢约束模式（与最终创建逻辑一致）
                print("🎨 预览使用端点切矢约束模式")

                start_tangent = None
                end_tangent = None

                for point_index, tangent_info in tangent_data.items():
                    direction = tangent_info['direction']
                    length = tangent_info.get('length', 1.0)

                    # 🔧 修复：检查并处理零向量
                    import math
                    dir_length = math.sqrt(sum(d * d for d in direction))
                    if dir_length < 1e-6:
                        print(f"⚠️ 预览模式检测到零向量切矢方向，使用默认方向")
                        # 使用相邻点计算默认方向
                        if point_index == 0 and len(points) >= 2:
                            p1, p2 = points[0], points[1]
                            direction = [p2[i] - p1[i] for i in range(3)]
                        elif point_index == len(points) - 1 and len(points) >= 2:
                            p1, p2 = points[-2], points[-1]
                            direction = [p2[i] - p1[i] for i in range(3)]
                        else:
                            direction = [1.0, 0.0, 0.0]

                        # 归一化方向
                        dir_length = math.sqrt(sum(d * d for d in direction))
                        if dir_length > 1e-6:
                            direction = [d / dir_length for d in direction]
                        else:
                            direction = [1.0, 0.0, 0.0]

                    if point_index == 0:
                        start_tangent = gp_Vec(direction[0] * length, direction[1] * length, direction[2] * length)
                        print(f"🎨 预览起始切矢: ({direction[0]*length:.2f}, {direction[1]*length:.2f}, {direction[2]*length:.2f})")
                    elif point_index == len(points) - 1:
                        end_tangent = gp_Vec(direction[0] * length, direction[1] * length, direction[2] * length)
                        print(f"🎨 预览终点切矢: ({direction[0]*length:.2f}, {direction[1]*length:.2f}, {direction[2]*length:.2f})")

                # 加载端点切矢（与最终创建逻辑一致）
                if start_tangent and end_tangent:
                    interpolator.Load(start_tangent, end_tangent)
                elif start_tangent:
                    # 只有起始切矢，创建一个默认的终点切矢
                    default_end = gp_Vec(1.0, 0.0, 0.0)
                    interpolator.Load(start_tangent, default_end)
                elif end_tangent:
                    # 只有终点切矢，创建一个默认的起始切矢
                    default_start = gp_Vec(1.0, 0.0, 0.0)
                    interpolator.Load(default_start, end_tangent)

            else:
                # 全点切矢约束模式（与最终创建逻辑一致）
                print("🎨 预览使用全点切矢约束模式")

                # 创建切矢数组和标志数组
                from OCC.Core.TColgp import TColgp_Array1OfVec
                from OCC.Core.TColStd import TColStd_HArray1OfBoolean

                tangents_array = TColgp_Array1OfVec(1, len(points))
                tangent_flags = TColStd_HArray1OfBoolean(1, len(points))

                for i in range(len(points)):
                    if i in tangent_data:
                        # 有切矢约束的点
                        tangent_info = tangent_data[i]
                        direction = tangent_info['direction']
                        length = tangent_info.get('length', 1.0)

                        # 🔧 修复：检查并处理零向量
                        import math
                        dir_length = math.sqrt(sum(d * d for d in direction))
                        if dir_length < 1e-6:
                            print(f"⚠️ 预览模式点{i+1}检测到零向量切矢方向，使用默认方向")
                            # 计算基于相邻点的默认方向
                            if i == 0 and len(points) >= 2:
                                p1, p2 = points[0], points[1]
                                direction = [p2[j] - p1[j] for j in range(3)]
                            elif i == len(points) - 1 and len(points) >= 2:
                                p1, p2 = points[-2], points[-1]
                                direction = [p2[j] - p1[j] for j in range(3)]
                            elif 0 < i < len(points) - 1:
                                prev_point = points[i - 1]
                                next_point = points[i + 1]
                                direction = [(next_point[j] - prev_point[j]) / 2.0 for j in range(3)]
                            else:
                                direction = [1.0, 0.0, 0.0]

                            # 归一化方向
                            dir_length = math.sqrt(sum(d * d for d in direction))
                            if dir_length > 1e-6:
                                direction = [d / dir_length for d in direction]
                            else:
                                direction = [1.0, 0.0, 0.0]

                        tangent_vec = gp_Vec(direction[0] * length, direction[1] * length, direction[2] * length)
                        tangents_array.SetValue(i + 1, tangent_vec)
                        tangent_flags.SetValue(i + 1, True)

                        print(f"🎨 预览点{i+1}切矢: ({direction[0]*length:.2f}, {direction[1]*length:.2f}, {direction[2]*length:.2f})")
                    else:
                        # 没有切矢约束的点
                        default_tangent = gp_Vec(1.0, 0.0, 0.0)
                        tangents_array.SetValue(i + 1, default_tangent)
                        tangent_flags.SetValue(i + 1, False)

                # 加载全点切矢约束
                interpolator.Load(tangents_array, tangent_flags)

        # 执行插值
        interpolator.Perform()

        if not interpolator.IsDone():
            print("❌ 预览样条线插值失败")
            return None

        # 获取曲线
        curve = interpolator.Curve()

        # 创建边
        edge_builder = BRepBuilderAPI_MakeEdge(curve)
        if not edge_builder.IsDone():
            print("❌ 预览样条线边创建失败")
            return None

        print("✅ 预览样条线创建成功（包含切矢约束）")
        return edge_builder.Edge()

    except Exception as e:
        print(f"❌ 创建预览样条线失败: {e}")
        return None

def create_basic_spline_preview(points: List[List[float]]):
    """
    创建基础的样条线预览（无切矢约束）

    Args:
        points: 控制点坐标列表 [[x1,y1,z1], [x2,y2,z2], ...]

    Returns:
        TopoDS_Edge: 样条线边对象，如果失败返回None
    """
    try:
        print(f"🎨 创建基础预览样条线，控制点数: {len(points)}")

        # 导入必要的模块
        from OCC.Core.TColgp import TColgp_Array1OfPnt
        from OCC.Core.gp import gp_Pnt
        from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge

        # 验证控制点数量
        if len(points) < 2:
            print("❌ 控制点数量不足，至少需要2个点")
            return None

        # 创建点数组
        point_array = TColgp_Array1OfPnt(1, len(points))
        for i, point in enumerate(points):
            gp_point = gp_Pnt(float(point[0]), float(point[1]), float(point[2]))
            point_array.SetValue(i + 1, gp_point)

        # 创建B样条曲线
        spline_generator = GeomAPI_PointsToBSpline(point_array)
        if spline_generator.IsDone():
            curve = spline_generator.Curve()

            # 创建边
            edge_builder = BRepBuilderAPI_MakeEdge(curve)
            if edge_builder.IsDone():
                edge = edge_builder.Edge()
                print("✅ 基础预览样条线创建成功")
                return edge
            else:
                print("❌ 创建样条线边失败")
                return None
        else:
            print("❌ B样条曲线生成失败")
            return None

    except Exception as e:
        print(f"❌ 创建基础预览样条线失败: {e}")
        return None

def create_spline_with_tangents(modeling_instance, points: List[List[float]], tangent_data: Dict[int, Dict] = None, active_layer=None):
    """
    创建带切矢方向控制的样条线
    
    Args:
        points: 控制点坐标列表 [[x1,y1,z1], [x2,y2,z2], ...]
        tangent_data: 切矢数据字典 {点索引: {'direction': [x,y,z], 'length': float}}
        active_layer: 活跃图层
    
    Returns:
        bool: 创建是否成功
    """
    if not points or len(points) < 2:
        QMessageBox.warning(modeling_instance, "错误", "创建样条线至少需要两个控制点")
        return False
    
    try:
        print(f"开始创建带切矢控制的样条线，控制点数: {len(points)}")
        
        # 创建控制点数组
        points_array = TColgp_HArray1OfPnt(1, len(points))
        for i, point_coords in enumerate(points):
            pnt = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])
            points_array.SetValue(i + 1, pnt)
            print(f"  控制点{i+1}: ({point_coords[0]:.2f}, {point_coords[1]:.2f}, {point_coords[2]:.2f})")
        
        # 创建插值器
        interpolator = GeomAPI_Interpolate(points_array, False, 0.0001)
        
        # 处理切矢约束
        if tangent_data and len(tangent_data) > 0:
            print(f"应用切矢约束，约束点数: {len(tangent_data)}")
            
            # 检查是否只有端点有切矢约束
            has_start_tangent = 0 in tangent_data
            has_end_tangent = (len(points) - 1) in tangent_data
            has_middle_tangents = any(0 < idx < len(points) - 1 for idx in tangent_data.keys())
            
            if (has_start_tangent or has_end_tangent) and not has_middle_tangents:
                # 只有端点切矢约束的情况
                print("使用端点切矢约束模式")
                
                start_tangent = None
                end_tangent = None
                
                if has_start_tangent:
                    start_data = tangent_data[0]
                    direction = start_data['direction']
                    length = start_data.get('length', 1.0)

                    # 🔧 修复：检查并处理零向量
                    import math
                    dir_length = math.sqrt(sum(d * d for d in direction))
                    if dir_length < 1e-6:
                        print(f"⚠️ 检测到零向量切矢方向，使用默认方向")
                        # 使用相邻点计算默认方向
                        if len(points) >= 2:
                            p1, p2 = points[0], points[1]
                            direction = [p2[i] - p1[i] for i in range(3)]
                            dir_length = math.sqrt(sum(d * d for d in direction))
                            if dir_length > 1e-6:
                                direction = [d / dir_length for d in direction]
                            else:
                                direction = [1.0, 0.0, 0.0]  # 最后的默认方向
                        else:
                            direction = [1.0, 0.0, 0.0]

                    start_tangent = gp_Vec(direction[0] * length, direction[1] * length, direction[2] * length)
                    print(f"  起始切矢: ({direction[0]*length:.2f}, {direction[1]*length:.2f}, {direction[2]*length:.2f})")
                
                if has_end_tangent:
                    end_data = tangent_data[len(points) - 1]
                    direction = end_data['direction']
                    length = end_data.get('length', 1.0)

                    # 🔧 修复：检查并处理零向量
                    import math
                    dir_length = math.sqrt(sum(d * d for d in direction))
                    if dir_length < 1e-6:
                        print(f"⚠️ 检测到零向量切矢方向，使用默认方向")
                        # 使用相邻点计算默认方向
                        if len(points) >= 2:
                            p1, p2 = points[-2], points[-1]
                            direction = [p2[i] - p1[i] for i in range(3)]
                            dir_length = math.sqrt(sum(d * d for d in direction))
                            if dir_length > 1e-6:
                                direction = [d / dir_length for d in direction]
                            else:
                                direction = [1.0, 0.0, 0.0]  # 最后的默认方向
                        else:
                            direction = [1.0, 0.0, 0.0]

                    end_tangent = gp_Vec(direction[0] * length, direction[1] * length, direction[2] * length)
                    print(f"  终点切矢: ({direction[0]*length:.2f}, {direction[1]*length:.2f}, {direction[2]*length:.2f})")
                
                # 加载端点切矢
                if start_tangent and end_tangent:
                    interpolator.Load(start_tangent, end_tangent)
                elif start_tangent:
                    # 只有起始切矢，创建一个默认的终点切矢
                    default_end = gp_Vec(1.0, 0.0, 0.0)
                    interpolator.Load(start_tangent, default_end)
                elif end_tangent:
                    # 只有终点切矢，创建一个默认的起始切矢
                    default_start = gp_Vec(1.0, 0.0, 0.0)
                    interpolator.Load(default_start, end_tangent)
            
            else:
                # 有中间点切矢约束的情况，使用全点切矢模式
                print("使用全点切矢约束模式")
                
                # 创建切矢数组和标志数组
                tangents_array = TColgp_Array1OfVec(1, len(points))
                tangent_flags = TColStd_HArray1OfBoolean(1, len(points))
                
                for i in range(len(points)):
                    if i in tangent_data:
                        # 有切矢约束的点
                        tangent_info = tangent_data[i]
                        direction = tangent_info['direction']
                        length = tangent_info.get('length', 1.0)

                        # 🔧 修复：检查并处理零向量
                        import math
                        dir_length = math.sqrt(sum(d * d for d in direction))
                        if dir_length < 1e-6:
                            print(f"⚠️ 点{i+1}检测到零向量切矢方向，使用默认方向")
                            # 计算基于相邻点的默认方向
                            if i == 0 and len(points) >= 2:
                                # 第一个点：使用到下一个点的方向
                                p1, p2 = points[0], points[1]
                                direction = [p2[j] - p1[j] for j in range(3)]
                            elif i == len(points) - 1 and len(points) >= 2:
                                # 最后一个点：使用从前一个点的方向
                                p1, p2 = points[-2], points[-1]
                                direction = [p2[j] - p1[j] for j in range(3)]
                            elif 0 < i < len(points) - 1:
                                # 中间点：使用前后点的平均方向
                                prev_point = points[i - 1]
                                next_point = points[i + 1]
                                direction = [(next_point[j] - prev_point[j]) / 2.0 for j in range(3)]
                            else:
                                direction = [1.0, 0.0, 0.0]

                            # 归一化方向
                            dir_length = math.sqrt(sum(d * d for d in direction))
                            if dir_length > 1e-6:
                                direction = [d / dir_length for d in direction]
                            else:
                                direction = [1.0, 0.0, 0.0]

                        tangent_vec = gp_Vec(direction[0] * length, direction[1] * length, direction[2] * length)
                        tangents_array.SetValue(i + 1, tangent_vec)
                        tangent_flags.SetValue(i + 1, True)

                        print(f"  点{i+1}切矢: ({direction[0]*length:.2f}, {direction[1]*length:.2f}, {direction[2]*length:.2f})")
                    else:
                        # 没有切矢约束的点
                        default_tangent = gp_Vec(1.0, 0.0, 0.0)
                        tangents_array.SetValue(i + 1, default_tangent)
                        tangent_flags.SetValue(i + 1, False)
                
                # 加载全点切矢约束
                interpolator.Load(tangents_array, tangent_flags)
        
        # 执行插值
        interpolator.Perform()
        
        if not interpolator.IsDone():
            QMessageBox.warning(modeling_instance, "错误", "样条线插值计算失败")
            return False
        
        # 获取生成的曲线
        bspline_curve = interpolator.Curve()
        
        # 创建边
        edge = BRepBuilderAPI_MakeEdge(bspline_curve).Edge()
        
        # 创建AIS形状
        ais_spline = AIS_Shape(edge)
        
        # 设置样条线外观
        spline_color = Quantity_Color(Quantity_NOC_GREEN)
        ais_spline.SetColor(spline_color)
        ais_spline.SetWidth(2.5)
        
        # 显示样条线
        context = modeling_instance.display.GetContext()
        context.Display(ais_spline, True)

        # 查找或创建活跃图层
        if active_layer is None:
            active_layer = modeling_instance.find_active_layer()

        if active_layer is None:
            # 创建样条线顶层项
            active_layer = get_or_create_spline_layer(modeling_instance)

        # 创建样条线数据结构
        spline_data = create_spline_data_structure(
            modeling_instance, ais_spline, edge, points, tangent_data, active_layer
        )

        # 创建并显示控制点
        create_control_points_visualization(modeling_instance, spline_data, points, tangent_data)

        # 添加到样条线列表
        if not hasattr(modeling_instance, 'spline_list'):
            modeling_instance.spline_list = []
        modeling_instance.spline_list.append(spline_data)

        # 更新显示
        modeling_instance.display.Repaint()

        print("带切矢控制的样条线创建成功")

        # 显示切矢箭头
        if hasattr(modeling_instance, 'show_tangent_arrows_for_spline'):
            modeling_instance.show_tangent_arrows_for_spline(spline_data)
            print("✅ 显示新创建样条线的切矢箭头")

        modeling_instance.statusBar().showMessage(f"已成功创建包含{len(points)}个控制点的样条线（含切矢控制）", 3000)

        return True
        
    except Exception as e:
        print(f"创建带切矢控制的样条线失败: {e}")
        import traceback
        traceback.print_exc()
        QMessageBox.warning(modeling_instance, "错误", f"创建样条线失败: {str(e)}")
        return False

def get_or_create_spline_layer(modeling_instance):
    """获取或创建样条线图层"""
    # 查找现有的样条线图层
    for i in range(modeling_instance.tree.topLevelItemCount()):
        item = modeling_instance.tree.topLevelItem(i)
        if item.text(0) == "样条线":
            return item

    # 创建新的样条线图层
    spline_layer = QTreeWidgetItem(modeling_instance.tree, ['样条线'])
    spline_layer.setFlags(spline_layer.flags() | Qt.ItemIsUserCheckable)
    spline_layer.setCheckState(0, Qt.Checked)

    if not hasattr(modeling_instance, 'spline_top_item'):
        modeling_instance.spline_top_item = spline_layer

    return spline_layer

def create_spline_data_structure(modeling_instance, ais_spline, edge, points, tangent_data, active_layer):
    """创建样条线数据结构"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    spline_data = {
        "ais_spline": ais_spline,
        "edge": edge,
        "points": points,
        "tangent_data": tangent_data or {},  # 存储切矢数据
        "tree_item": None,
        "point_ais_list": [],
        "tangent_ais_list": [],  # 存储切矢可视化对象
        "creation_method": "interpolate_with_tangents"  # 标记创建方法
    }
    
    # 在树形控件中添加样条线信息
    tangent_info = f"（含{len(tangent_data)}个切矢约束）" if tangent_data else ""
    spline_item = QTreeWidgetItem(active_layer, [f"样条线 [{current_time}]{tangent_info}"])
    spline_item.setFlags(spline_item.flags() | Qt.ItemIsUserCheckable)
    spline_item.setCheckState(0, Qt.Checked)
    spline_data["tree_item"] = spline_item
    
    # 添加控制点信息
    for i, point in enumerate(points):
        has_tangent = i in (tangent_data or {})
        tangent_mark = " [T]" if has_tangent else ""
        coord_item = QTreeWidgetItem(spline_item, [f"控制点{i+1}: ({point[0]:.1f}, {point[1]:.1f}, {point[2]:.1f}){tangent_mark}"])
        coord_item.setFlags(coord_item.flags() | Qt.ItemIsUserCheckable)
        coord_item.setCheckState(0, Qt.Checked)
    
    # 展开树节点
    modeling_instance.tree.expandItem(active_layer)
    modeling_instance.tree.expandItem(spline_item)

    return spline_data

def create_control_points_visualization(modeling_instance, spline_data, points, tangent_data):
    """创建控制点和切矢的可视化"""
    context = modeling_instance.display.GetContext()

    for i, point_coords in enumerate(points):
        # 创建控制点可视化
        point = gp_Pnt(*point_coords)
        vertex = BRepBuilderAPI_MakeVertex(point).Vertex()
        ais_point = AIS_Shape(vertex)

        # 设置控制点外观
        if i in (tangent_data or {}):
            # 有切矢约束的点用红色显示
            point_color = Quantity_Color(Quantity_NOC_RED)
        else:
            # 普通控制点用白色显示
            point_color = Quantity_Color(Quantity_NOC_WHITE)

        ais_point.SetColor(point_color)
        ais_point.SetWidth(8)

        # 设置点样式
        drawer = ais_point.Attributes()
        point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, point_color, 6)
        drawer.SetPointAspect(point_aspect)
        ais_point.SetAttributes(drawer)

        # 显示控制点
        context.Display(ais_point, False)

        # 添加到样条线数据
        spline_data["point_ais_list"].append({
            "ais_point": ais_point,
            "vertex": vertex,
            "point": point,
            "coordinates": point_coords,
            "has_tangent": i in (tangent_data or {})
        })

        # 创建切矢可视化
        if i in (tangent_data or {}):
            tangent_info = tangent_data[i]
            create_tangent_visualization(modeling_instance, spline_data, i, point, tangent_info)

def create_tangent_visualization(modeling_instance, spline_data, point_index, point, tangent_info):
    """创建增强的切矢箭头可视化"""
    try:
        # 尝试使用增强箭头可视化
        try:
            from 增强切矢箭头可视化 import create_enhanced_tangent_arrow
            result = create_enhanced_tangent_arrow(modeling_instance, spline_data, point_index, point, tangent_info)
            if result:
                print(f"✅ 使用增强箭头创建点{point_index+1}的切矢可视化")
                return
        except ImportError:
            print("增强箭头模块未找到，使用传统方式")
        except Exception as e:
            print(f"增强箭头创建失败: {e}，使用传统方式")

        # 传统方式：创建带简单箭头的可视化
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge
        from OCC.Core.gp import gp_Pnt, gp_Vec
        from OCC.Core.TopoDS import TopoDS_Compound
        from OCC.Core.BRep import BRep_Builder
        import math

        direction = tangent_info['direction']
        length = tangent_info.get('length', 1.0)

        print(f"创建传统切矢箭头 - 点{point_index+1}")

        # 计算自适应缩放因子
        try:
            view = modeling_instance.display.GetView()
            scale = view.Scale()
            adaptive_factor = 120.0 / max(scale, 0.1)
            adaptive_factor = max(20.0, min(300.0, adaptive_factor))
        except:
            adaptive_factor = 150.0

        # 计算切矢终点
        actual_length = length * adaptive_factor
        end_point = gp_Pnt(
            point.X() + direction[0] * actual_length,
            point.Y() + direction[1] * actual_length,
            point.Z() + direction[2] * actual_length
        )

        # 创建复合体
        builder = BRep_Builder()
        compound = TopoDS_Compound()
        builder.MakeCompound(compound)

        # 主体线条
        main_edge = BRepBuilderAPI_MakeEdge(point, end_point).Edge()
        builder.Add(compound, main_edge)

        # 创建简单箭头头部
        arrow_length = actual_length * 0.15
        arrow_angle = math.radians(20)

        # 计算箭头方向
        main_vec = gp_Vec(point, end_point)
        if main_vec.Magnitude() > 1e-6:
            main_vec.Normalize()

            # 创建两个箭头翼
            for angle_sign in [-1, 1]:
                # 计算箭头翼的方向
                if abs(main_vec.Z()) < 0.9:
                    side_vec = main_vec.Crossed(gp_Vec(0, 0, 1))
                else:
                    side_vec = main_vec.Crossed(gp_Vec(1, 0, 0))

                side_vec.Normalize()
                if angle_sign > 0:
                    side_vec.Reverse()

                # 计算箭头翼的终点
                back_vec = main_vec.Reversed()
                back_vec.Scale(arrow_length * math.cos(arrow_angle))
                side_vec.Scale(arrow_length * math.sin(arrow_angle))

                wing_point = gp_Pnt(
                    end_point.X() + back_vec.X() + side_vec.X(),
                    end_point.Y() + back_vec.Y() + side_vec.Y(),
                    end_point.Z() + back_vec.Z() + side_vec.Z()
                )

                wing_edge = BRepBuilderAPI_MakeEdge(end_point, wing_point).Edge()
                builder.Add(compound, wing_edge)

        # 创建AIS对象
        ais_tangent = AIS_Shape(compound)

        # 设置外观
        tangent_color = Quantity_Color(0.0, 0.4, 1.0, 0)  # 深蓝色
        ais_tangent.SetColor(tangent_color)
        ais_tangent.SetWidth(5.0)

        # 创建但不立即显示（由智能管理系统控制显示）
        context = modeling_instance.display.GetContext()
        # context.Display(ais_tangent, False)  # 注释掉自动显示

        # 添加到样条线数据
        spline_data["tangent_ais_list"].append({
            "point_index": point_index,
            "ais_tangent": ais_tangent,
            "compound": compound,
            "main_edge": main_edge,
            "direction": direction.copy(),
            "length": length,
            "adaptive_scale": adaptive_factor,
            "actual_length": actual_length
        })

        print(f"✅ 创建点{point_index+1}的增强切矢箭头")

    except Exception as e:
        print(f"❌ 创建切矢可视化失败: {e}")
        import traceback
        traceback.print_exc()

def update_tangent_arrows_scale(modeling_instance, spline_data_list):
    """
    更新所有切矢箭头的自适应缩放

    Args:
        modeling_instance: 建模实例
        spline_data_list: 样条线数据列表
    """
    try:
        print("🔄 更新切矢箭头自适应缩放")

        # 获取当前视图缩放
        view = modeling_instance.display.GetView()
        current_scale = view.Scale()

        updated_count = 0

        # 遍历所有样条线
        for spline_data in spline_data_list:
            tangent_ais_list = spline_data.get("tangent_ais_list", [])

            for tangent_ais_data in tangent_ais_list:
                # 检查是否需要更新
                old_scale = tangent_ais_data.get("last_view_scale", 1.0)
                scale_change_ratio = abs(current_scale - old_scale) / max(old_scale, 0.1)

                # 如果缩放变化超过20%，则更新
                if scale_change_ratio > 0.2:
                    if update_single_tangent_arrow(modeling_instance, spline_data, tangent_ais_data, current_scale):
                        updated_count += 1
                        tangent_ais_data["last_view_scale"] = current_scale

        if updated_count > 0:
            print(f"✅ 更新了 {updated_count} 个切矢箭头的缩放")
            modeling_instance.display.Repaint()

    except Exception as e:
        print(f"❌ 更新切矢箭头缩放失败: {e}")

def update_single_tangent_arrow(modeling_instance, spline_data, tangent_ais_data, view_scale):
    """
    更新单个切矢箭头的缩放

    Args:
        modeling_instance: 建模实例
        spline_data: 样条线数据
        tangent_ais_data: 切矢AIS数据
        view_scale: 当前视图缩放

    Returns:
        bool: 是否成功更新
    """
    try:
        point_index = tangent_ais_data["point_index"]
        direction = tangent_ais_data["direction"]
        length = tangent_ais_data["length"]

        # 计算新的自适应缩放因子
        new_adaptive_factor = 120.0 / max(view_scale, 0.1)
        new_adaptive_factor = max(20.0, min(300.0, new_adaptive_factor))

        # 隐藏旧的箭头
        context = modeling_instance.display.GetContext()
        old_ais = tangent_ais_data["ais_tangent"]
        context.Erase(old_ais, False)

        # 获取控制点坐标（需要从样条线数据中获取）
        points = spline_data.get("points", [])
        if point_index >= len(points):
            return False

        point_coords = points[point_index]
        point = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])

        # 重新创建箭头
        tangent_info = {
            'direction': direction,
            'length': length
        }

        # 移除旧的数据
        tangent_ais_list = spline_data.get("tangent_ais_list", [])
        if tangent_ais_data in tangent_ais_list:
            tangent_ais_list.remove(tangent_ais_data)

        # 创建新的箭头
        create_tangent_visualization(modeling_instance, spline_data, point_index, point, tangent_info)

        return True

    except Exception as e:
        print(f"❌ 更新单个切矢箭头失败: {e}")
        return False

def setup_view_change_callback(modeling_instance):
    """
    设置视图变化回调，实现自适应缩放

    Args:
        modeling_instance: 建模实例
    """
    try:
        # 这里需要根据具体的GUI框架实现视图变化监听
        # 由于OpenCASCADE的限制，这里提供一个简化的实现思路

        print("💡 视图变化回调设置提示:")
        print("  - 可以在主窗口的缩放事件中调用 update_tangent_arrows_scale()")
        print("  - 建议在鼠标滚轮事件中触发更新")
        print("  - 可以设置定时器定期检查视图变化")

        # 示例：如果有定时器功能
        # timer = QTimer()
        # timer.timeout.connect(lambda: update_tangent_arrows_scale(modeling_instance, modeling_instance.spline_list))
        # timer.start(1000)  # 每秒检查一次

    except Exception as e:
        print(f"❌ 设置视图变化回调失败: {e}")

def get_tangent_arrow_info(spline_data):
    """
    获取切矢箭头信息

    Args:
        spline_data: 样条线数据

    Returns:
        list: 切矢箭头信息列表
    """
    try:
        tangent_ais_list = spline_data.get("tangent_ais_list", [])
        info_list = []

        for tangent_ais_data in tangent_ais_list:
            info = {
                'point_index': tangent_ais_data.get("point_index", -1),
                'direction': tangent_ais_data.get("direction", [0, 0, 0]),
                'length': tangent_ais_data.get("length", 1.0),
                'adaptive_scale': tangent_ais_data.get("adaptive_scale", 150.0),
                'actual_length': tangent_ais_data.get("actual_length", 150.0)
            }
            info_list.append(info)

        return info_list

    except Exception as e:
        print(f"❌ 获取切矢箭头信息失败: {e}")
        return []

def update_spline_with_tangents(modeling_instance, spline_data, new_tangent_data):
    """更新样条线的切矢约束"""
    try:
        # 重新创建样条线
        points = spline_data["points"]
        active_layer = spline_data["tree_item"].parent()
        
        # 隐藏旧的样条线和切矢
        context = modeling_instance.display.GetContext()
        context.Erase(spline_data["ais_spline"], False)

        # 清除旧的切矢可视化
        for tangent_vis in spline_data.get("tangent_ais_list", []):
            context.Erase(tangent_vis["ais_tangent"], False)

        # 创建新的样条线
        success = create_spline_with_tangents(modeling_instance, points, new_tangent_data, active_layer)

        if success:
            # 从列表中移除旧的样条线数据
            if hasattr(modeling_instance, 'spline_list') and spline_data in modeling_instance.spline_list:
                modeling_instance.spline_list.remove(spline_data)
            
            # 移除旧的树节点
            if spline_data["tree_item"]:
                parent = spline_data["tree_item"].parent()
                if parent:
                    parent.removeChild(spline_data["tree_item"])
        
        return success
        
    except Exception as e:
        print(f"更新样条线切矢失败: {e}")
        return False
