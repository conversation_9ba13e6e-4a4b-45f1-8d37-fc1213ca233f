import os
import sys
from OCC.Extend.DataExchange import read_step_file
from OCC.Extend.TopologyUtils import TopologyExplorer
from OCC.Display.WebGl import x3dom_renderer
stp_file = os.path.join('.', 'assets', 'models', 'as1_pe_203.stp')
if not os.path.isfile(stp_file):
	print("File 3864470050F1.stp not found. First unzip 3864470050F1.zip file from the assets folder")
	sys.exit(0)
# file exist, we can load the file
big_shp = read_step_file(stp_file)
all_subshapes = TopologyExplorer(big_shp).solids()
my_renderer = x3dom_renderer.X3DomRenderer()
for single_shape in all_subshapes:
	my_renderer.DisplayShape(single_shape)
# then call the renderer
my_renderer.render()
