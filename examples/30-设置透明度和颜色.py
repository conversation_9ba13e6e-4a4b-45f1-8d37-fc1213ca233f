from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox, BRepPrimAPI_MakeSphere
from OCC.Display.SimpleGui import init_display
from OCC.Core.gp import gp_Vec, gp_Pnt

from OCC.Extend.ShapeFactory import translate_shp
display, start_display, add_menu, add_function_to_menu = init_display()
# Create Box
box = BRepPrimAPI_MakeBox(200, 60, 60).Shape()
# Create Sphere
sphere = BRepPrimAPI_MakeSphere(gp_Pnt(100, 20, 20), 80).Shape()
# move the sphere
moved_sphere = translate_shp(sphere, gp_Vec(0., -200., 0.))
ais_box = display.DisplayShape(box)[0]
ais_box.SetTransparency(0.1)
ais_sphere = display.DisplayColoredShape(moved_sphere, color="BLUE")[0]
ais_sphere.SetTransparency(0.9)
display.FitAll()
start_display()
