'''
This examples creates and displays a simple box.
显示结果如下，按键盘上的W，S，H键可以在线框模型，面模型和消隐线模型之间切换。按住左键移动鼠标可以旋转物体，鼠标中键用于缩放，按住鼠标中键可以平移物体
'''

# The first line loads the init_display function, necessary to
# enable the builtin simple gui provided with pythonocc
from OCC.Display.SimpleGui import init_display

# Then we import the class that instanciates a box
# Here the BRepPrimAPI module means Boundary Representation Primitive API.
# It provides an API for creation of basic geometries like spheres,cones etc
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox

# Following line initializes the display
# By default, the init_display function looks for a Qt based Gui (PyQt, PySide)
display, start_display, add_menu, add_function_to_menu = init_display()

# The BRepPrimAPI_MakeBox class is initialized with the 3 parameters of the box: widht, height, depth
my_box = BRepPrimAPI_MakeBox(10., 20., 30.).Shape()

# Then the box shape is sent to the renderer
display.DisplayShape(my_box, update=True)

# At last, we enter the gui mainloop
start_display()