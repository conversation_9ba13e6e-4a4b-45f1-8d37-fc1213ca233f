ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2008-07-24T15:00:20',(
    '--- Datakit Converter ---'),('--- Datakit www.datakit.com---'),
  ' Release Version  Jun 30 2008','Open CASCADE 6.1',' ');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('as1','as1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23,#27),#31);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#13 = DIRECTION('',(0.E+000,0.E+000,1.));
#14 = DIRECTION('',(1.,0.E+000,0.E+000));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(-10.,75.,60.));
#17 = DIRECTION('',(1.,0.E+000,0.E+000));
#18 = DIRECTION('',(0.E+000,0.E+000,-1.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(5.,125.,20.));
#21 = DIRECTION('',(0.E+000,0.E+000,1.));
#22 = DIRECTION('',(1.,0.E+000,0.E+000));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#25 = DIRECTION('',(0.E+000,0.E+000,1.));
#26 = DIRECTION('',(1.,0.E+000,0.E+000));
#27 = AXIS2_PLACEMENT_3D('',#28,#29,#30);
#28 = CARTESIAN_POINT('',(175.,25.,20.));
#29 = DIRECTION('',(0.E+000,0.E+000,1.));
#30 = DIRECTION('',(-1.,0.E+000,0.E+000));
#31 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#35)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#32,#33,#34)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#32 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#33 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#34 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#35 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-006),#32,
  'distance_accuracy_value','confusion accuracy');
#36 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#37 = SHAPE_DEFINITION_REPRESENTATION(#38,#44);
#38 = PRODUCT_DEFINITION_SHAPE('','',#39);
#39 = PRODUCT_DEFINITION('design','',#40,#43);
#40 = PRODUCT_DEFINITION_FORMATION('','',#41);
#41 = PRODUCT('rod-assembly','rod-assembly','',(#42));
#42 = PRODUCT_CONTEXT('',#2,'mechanical');
#43 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#44 = SHAPE_REPRESENTATION('',(#11,#45,#49,#53),#57);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(-10.,-7.5,185.));
#47 = DIRECTION('',(0.E+000,0.E+000,1.));
#48 = DIRECTION('',(1.,0.E+000,0.E+000));
#49 = AXIS2_PLACEMENT_3D('',#50,#51,#52);
#50 = CARTESIAN_POINT('',(-10.,-7.5,12.));
#51 = DIRECTION('',(0.E+000,0.E+000,1.));
#52 = DIRECTION('',(1.,0.E+000,0.E+000));
#53 = AXIS2_PLACEMENT_3D('',#54,#55,#56);
#54 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#55 = DIRECTION('',(0.E+000,0.E+000,1.));
#56 = DIRECTION('',(1.,0.E+000,0.E+000));
#57 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#61)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#58,#59,#60)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#58 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#59 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#60 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#61 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-006),#58,
  'distance_accuracy_value','confusion accuracy');
#62 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#63),#735);
#63 = MANIFOLD_SOLID_BREP('',#64);
#64 = CLOSED_SHELL('',(#65,#423,#499,#548,#597,#624,#695,#724));
#65 = ADVANCED_FACE('',(#66,#185),#80,.T.);
#66 = FACE_BOUND('',#67,.T.);
#67 = EDGE_LOOP('',(#68,#103,#131,#159));
#68 = ORIENTED_EDGE('',*,*,#69,.F.);
#69 = EDGE_CURVE('',#70,#72,#74,.T.);
#70 = VERTEX_POINT('',#71);
#71 = CARTESIAN_POINT('',(20.,0.E+000,3.));
#72 = VERTEX_POINT('',#73);
#73 = CARTESIAN_POINT('',(0.E+000,0.E+000,3.));
#74 = SURFACE_CURVE('',#75,(#79,#91),.PCURVE_S1.);
#75 = LINE('',#76,#77);
#76 = CARTESIAN_POINT('',(10.,0.E+000,3.));
#77 = VECTOR('',#78,1.);
#78 = DIRECTION('',(-1.,0.E+000,0.E+000));
#79 = PCURVE('',#80,#85);
#80 = PLANE('',#81);
#81 = AXIS2_PLACEMENT_3D('',#82,#83,#84);
#82 = CARTESIAN_POINT('',(10.,7.5,3.));
#83 = DIRECTION('',(0.E+000,0.E+000,1.));
#84 = DIRECTION('',(1.,0.E+000,0.E+000));
#85 = DEFINITIONAL_REPRESENTATION('',(#86),#90);
#86 = LINE('',#87,#88);
#87 = CARTESIAN_POINT('',(0.E+000,-7.5));
#88 = VECTOR('',#89,1.);
#89 = DIRECTION('',(-1.,0.E+000));
#90 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#91 = PCURVE('',#92,#97);
#92 = PLANE('',#93);
#93 = AXIS2_PLACEMENT_3D('',#94,#95,#96);
#94 = CARTESIAN_POINT('',(10.,0.E+000,0.E+000));
#95 = DIRECTION('',(0.E+000,-1.,0.E+000));
#96 = DIRECTION('',(0.E+000,0.E+000,-1.));
#97 = DEFINITIONAL_REPRESENTATION('',(#98),#102);
#98 = LINE('',#99,#100);
#99 = CARTESIAN_POINT('',(-3.,0.E+000));
#100 = VECTOR('',#101,1.);
#101 = DIRECTION('',(0.E+000,-1.));
#102 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#103 = ORIENTED_EDGE('',*,*,#104,.F.);
#104 = EDGE_CURVE('',#105,#70,#107,.T.);
#105 = VERTEX_POINT('',#106);
#106 = CARTESIAN_POINT('',(20.,15.,3.));
#107 = SURFACE_CURVE('',#108,(#112,#119),.PCURVE_S1.);
#108 = LINE('',#109,#110);
#109 = CARTESIAN_POINT('',(20.,7.5,3.));
#110 = VECTOR('',#111,1.);
#111 = DIRECTION('',(0.E+000,-1.,0.E+000));
#112 = PCURVE('',#80,#113);
#113 = DEFINITIONAL_REPRESENTATION('',(#114),#118);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(10.,0.E+000));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(0.E+000,-1.));
#118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#119 = PCURVE('',#120,#125);
#120 = PLANE('',#121);
#121 = AXIS2_PLACEMENT_3D('',#122,#123,#124);
#122 = CARTESIAN_POINT('',(20.,7.5,0.E+000));
#123 = DIRECTION('',(1.,0.E+000,0.E+000));
#124 = DIRECTION('',(0.E+000,0.E+000,-1.));
#125 = DEFINITIONAL_REPRESENTATION('',(#126),#130);
#126 = LINE('',#127,#128);
#127 = CARTESIAN_POINT('',(-3.,0.E+000));
#128 = VECTOR('',#129,1.);
#129 = DIRECTION('',(0.E+000,-1.));
#130 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#131 = ORIENTED_EDGE('',*,*,#132,.F.);
#132 = EDGE_CURVE('',#133,#105,#135,.T.);
#133 = VERTEX_POINT('',#134);
#134 = CARTESIAN_POINT('',(0.E+000,15.,3.));
#135 = SURFACE_CURVE('',#136,(#140,#147),.PCURVE_S1.);
#136 = LINE('',#137,#138);
#137 = CARTESIAN_POINT('',(10.,15.,3.));
#138 = VECTOR('',#139,1.);
#139 = DIRECTION('',(1.,0.E+000,0.E+000));
#140 = PCURVE('',#80,#141);
#141 = DEFINITIONAL_REPRESENTATION('',(#142),#146);
#142 = LINE('',#143,#144);
#143 = CARTESIAN_POINT('',(0.E+000,7.5));
#144 = VECTOR('',#145,1.);
#145 = DIRECTION('',(1.,0.E+000));
#146 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#147 = PCURVE('',#148,#153);
#148 = PLANE('',#149);
#149 = AXIS2_PLACEMENT_3D('',#150,#151,#152);
#150 = CARTESIAN_POINT('',(10.,15.,0.E+000));
#151 = DIRECTION('',(0.E+000,1.,0.E+000));
#152 = DIRECTION('',(0.E+000,0.E+000,1.));
#153 = DEFINITIONAL_REPRESENTATION('',(#154),#158);
#154 = LINE('',#155,#156);
#155 = CARTESIAN_POINT('',(3.,0.E+000));
#156 = VECTOR('',#157,1.);
#157 = DIRECTION('',(0.E+000,1.));
#158 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#159 = ORIENTED_EDGE('',*,*,#160,.F.);
#160 = EDGE_CURVE('',#72,#133,#161,.T.);
#161 = SURFACE_CURVE('',#162,(#166,#173),.PCURVE_S1.);
#162 = LINE('',#163,#164);
#163 = CARTESIAN_POINT('',(0.E+000,7.5,3.));
#164 = VECTOR('',#165,1.);
#165 = DIRECTION('',(0.E+000,1.,0.E+000));
#166 = PCURVE('',#80,#167);
#167 = DEFINITIONAL_REPRESENTATION('',(#168),#172);
#168 = LINE('',#169,#170);
#169 = CARTESIAN_POINT('',(-10.,0.E+000));
#170 = VECTOR('',#171,1.);
#171 = DIRECTION('',(0.E+000,1.));
#172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#173 = PCURVE('',#174,#179);
#174 = PLANE('',#175);
#175 = AXIS2_PLACEMENT_3D('',#176,#177,#178);
#176 = CARTESIAN_POINT('',(0.E+000,7.5,0.E+000));
#177 = DIRECTION('',(-1.,0.E+000,0.E+000));
#178 = DIRECTION('',(0.E+000,0.E+000,1.));
#179 = DEFINITIONAL_REPRESENTATION('',(#180),#184);
#180 = LINE('',#181,#182);
#181 = CARTESIAN_POINT('',(3.,0.E+000));
#182 = VECTOR('',#183,1.);
#183 = DIRECTION('',(0.E+000,1.));
#184 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#185 = FACE_BOUND('',#186,.T.);
#186 = EDGE_LOOP('',(#187,#307));
#187 = ORIENTED_EDGE('',*,*,#188,.T.);
#188 = EDGE_CURVE('',#189,#191,#193,.T.);
#189 = VERTEX_POINT('',#190);
#190 = CARTESIAN_POINT('',(5.,7.5,3.));
#191 = VERTEX_POINT('',#192);
#192 = CARTESIAN_POINT('',(15.,7.5,3.));
#193 = SURFACE_CURVE('',#194,(#219,#247),.PCURVE_S1.);
#194 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#195,#196,#197,#198,#199,#200,
    #201,#202,#203,#204,#205,#206,#207,#208,#209,#210,#211,#212,#213,
    #214,#215,#216,#217,#218),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164414,7.85828164644,10.7238180516,13.583658994,
    16.4911855022,20.3877608702,22.3658107336),.UNSPECIFIED.);
#195 = CARTESIAN_POINT('',(5.,7.5,3.));
#196 = CARTESIAN_POINT('',(5.,7.96719825234,3.));
#197 = CARTESIAN_POINT('',(5.05456967986,8.46798546394,3.));
#198 = CARTESIAN_POINT('',(5.17958225879,8.9911230353,3.));
#199 = CARTESIAN_POINT('',(5.57268612552,9.98006143429,3.));
#200 = CARTESIAN_POINT('',(6.25801463611,10.8809047397,3.));
#201 = CARTESIAN_POINT('',(6.64523619345,11.2686263331,3.));
#202 = CARTESIAN_POINT('',(7.43250862613,11.8620880289,3.));
#203 = CARTESIAN_POINT('',(8.35481073757,12.2518403653,3.));
#204 = CARTESIAN_POINT('',(8.77677855674,12.3779193361,3.));
#205 = CARTESIAN_POINT('',(9.64371296306,12.5354809914,3.));
#206 = CARTESIAN_POINT('',(10.5264003018,12.501400762,3.));
#207 = CARTESIAN_POINT('',(10.9630506746,12.435748566,3.));
#208 = CARTESIAN_POINT('',(11.8186421203,12.2088457881,3.));
#209 = CARTESIAN_POINT('',(12.5957546194,11.8071306708,3.));
#210 = CARTESIAN_POINT('',(12.9603131848,11.5642190824,3.));
#211 = CARTESIAN_POINT('',(13.7355490363,10.916301294,3.));
#212 = CARTESIAN_POINT('',(14.3095225983,10.1246556547,3.));
#213 = CARTESIAN_POINT('',(14.5637500219,9.64244819984,3.));
#214 = CARTESIAN_POINT('',(14.8362924347,8.90481893489,3.));
#215 = CARTESIAN_POINT('',(14.96121877,8.18885510165,3.));
#216 = CARTESIAN_POINT('',(14.9876332288,7.95243137655,3.));
#217 = CARTESIAN_POINT('',(15.,7.72240966553,3.));
#218 = CARTESIAN_POINT('',(15.,7.5,3.));
#219 = PCURVE('',#80,#220);
#220 = DEFINITIONAL_REPRESENTATION('',(#221),#246);
#221 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#222,#223,#224,#225,#226,#227,
    #228,#229,#230,#231,#232,#233,#234,#235,#236,#237,#238,#239,#240,
    #241,#242,#243,#244,#245),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164414,7.85828164644,10.7238180516,13.583658994,
    16.4911855022,20.3877608702,22.3658107336),.UNSPECIFIED.);
#222 = CARTESIAN_POINT('',(-5.,0.E+000));
#223 = CARTESIAN_POINT('',(-5.,0.46719825234));
#224 = CARTESIAN_POINT('',(-4.94543032014,0.96798546394));
#225 = CARTESIAN_POINT('',(-4.82041774121,1.4911230353));
#226 = CARTESIAN_POINT('',(-4.42731387448,2.48006143429));
#227 = CARTESIAN_POINT('',(-3.74198536389,3.3809047397));
#228 = CARTESIAN_POINT('',(-3.35476380655,3.7686263331));
#229 = CARTESIAN_POINT('',(-2.56749137387,4.3620880289));
#230 = CARTESIAN_POINT('',(-1.64518926243,4.7518403653));
#231 = CARTESIAN_POINT('',(-1.22322144326,4.8779193361));
#232 = CARTESIAN_POINT('',(-0.35628703694,5.0354809914));
#233 = CARTESIAN_POINT('',(0.5264003018,5.001400762));
#234 = CARTESIAN_POINT('',(0.9630506746,4.935748566));
#235 = CARTESIAN_POINT('',(1.8186421203,4.7088457881));
#236 = CARTESIAN_POINT('',(2.5957546194,4.3071306708));
#237 = CARTESIAN_POINT('',(2.9603131848,4.0642190824));
#238 = CARTESIAN_POINT('',(3.7355490363,3.416301294));
#239 = CARTESIAN_POINT('',(4.3095225983,2.6246556547));
#240 = CARTESIAN_POINT('',(4.5637500219,2.14244819984));
#241 = CARTESIAN_POINT('',(4.8362924347,1.40481893489));
#242 = CARTESIAN_POINT('',(4.96121877,0.68885510165));
#243 = CARTESIAN_POINT('',(4.9876332288,0.45243137655));
#244 = CARTESIAN_POINT('',(5.,0.22240966553));
#245 = CARTESIAN_POINT('',(5.,0.E+000));
#246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#247 = PCURVE('',#248,#257);
#248 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#249,#250,#251,#252)
    ,(#253,#254,#255,#256
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,3.00099800399),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#249 = CARTESIAN_POINT('',(5.,7.5,3.));
#250 = CARTESIAN_POINT('',(5.,17.5,3.));
#251 = CARTESIAN_POINT('',(15.,17.5,3.));
#252 = CARTESIAN_POINT('',(15.,7.5,3.));
#253 = CARTESIAN_POINT('',(5.,7.5,0.E+000));
#254 = CARTESIAN_POINT('',(5.,17.5,0.E+000));
#255 = CARTESIAN_POINT('',(15.,17.5,0.E+000));
#256 = CARTESIAN_POINT('',(15.,7.5,0.E+000));
#257 = DEFINITIONAL_REPRESENTATION('',(#258),#306);
#258 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#259,#260,#261,#262,#263,#264,
    #265,#266,#267,#268,#269,#270,#271,#272,#273,#274,#275,#276,#277,
    #278,#279,#280,#281,#282,#283,#284,#285,#286,#287,#288,#289,#290,
    #291,#292,#293,#294,#295,#296,#297,#298,#299,#300,#301,#302,#303,
    #304,#305),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,
    0.508313880309,1.016627760618,1.524941640927,2.033255521236,
    2.541569401545,3.049883281855,3.558197162164,4.066511042473,
    4.574824922782,5.083138803091,5.5914526834,6.099766563709,
    6.608080444018,7.116394324327,7.624708204636,8.133022084945,
    8.641335965255,9.149649845564,9.657963725873,10.166277606182,
    10.674591486491,11.1829053668,11.691219247109,12.199533127418,
    12.707847007727,13.216160888036,13.724474768345,14.232788648655,
    14.741102528964,15.249416409273,15.757730289582,16.266044169891,
    16.7743580502,17.282671930509,17.790985810818,18.299299691127,
    18.807613571436,19.315927451745,19.824241332055,20.332555212364,
    20.840869092673,21.349182972982,21.857496853291,22.3658107336),
  .QUASI_UNIFORM_KNOTS.);
#259 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#260 = CARTESIAN_POINT('',(9.9800399E-004,0.285786133984));
#261 = CARTESIAN_POINT('',(9.9800399E-004,0.851023724305));
#262 = CARTESIAN_POINT('',(9.9800399E-004,1.679658949906));
#263 = CARTESIAN_POINT('',(9.980039899999E-004,2.488775842698));
#264 = CARTESIAN_POINT('',(9.980039900006E-004,3.278357390721));
#265 = CARTESIAN_POINT('',(9.980039900005E-004,4.048590090071));
#266 = CARTESIAN_POINT('',(9.9800399E-004,4.799873550567));
#267 = CARTESIAN_POINT('',(9.980039899996E-004,5.532780975437));
#268 = CARTESIAN_POINT('',(9.980039900015E-004,6.248020910349));
#269 = CARTESIAN_POINT('',(9.980039899997E-004,6.946360574083));
#270 = CARTESIAN_POINT('',(9.9800399E-004,7.628688634712));
#271 = CARTESIAN_POINT('',(9.980039900006E-004,8.296073973071));
#272 = CARTESIAN_POINT('',(9.980039900004E-004,8.949683944662));
#273 = CARTESIAN_POINT('',(9.980039900006E-004,9.590744782664));
#274 = CARTESIAN_POINT('',(9.980039899999E-004,10.220499188568));
#275 = CARTESIAN_POINT('',(9.980039900001E-004,10.840182523672));
#276 = CARTESIAN_POINT('',(9.9800399E-004,11.450961995018));
#277 = CARTESIAN_POINT('',(9.980039900003E-004,12.054057835882));
#278 = CARTESIAN_POINT('',(9.980039899991E-004,12.650784954516));
#279 = CARTESIAN_POINT('',(9.98003990001E-004,13.242437006153));
#280 = CARTESIAN_POINT('',(9.980039899998E-004,13.830311318193));
#281 = CARTESIAN_POINT('',(9.980039900001E-004,14.415700441563));
#282 = CARTESIAN_POINT('',(9.980039900002E-004,14.999897614205));
#283 = CARTESIAN_POINT('',(9.980039899993E-004,15.584089012766));
#284 = CARTESIAN_POINT('',(9.980039900001E-004,16.169496122547));
#285 = CARTESIAN_POINT('',(9.980039900007E-004,16.757374012694));
#286 = CARTESIAN_POINT('',(9.980039900001E-004,17.349001918787));
#287 = CARTESIAN_POINT('',(9.980039899992E-004,17.945677527815));
#288 = CARTESIAN_POINT('',(9.980039900009E-004,18.54871222184));
#289 = CARTESIAN_POINT('',(9.980039900002E-004,19.159406297875));
#290 = CARTESIAN_POINT('',(9.980039900015E-004,19.779034542658));
#291 = CARTESIAN_POINT('',(9.980039899996E-004,20.408844113292));
#292 = CARTESIAN_POINT('',(9.980039900003E-004,21.050050717178));
#293 = CARTESIAN_POINT('',(9.980039899995E-004,21.703821241748));
#294 = CARTESIAN_POINT('',(9.980039899995E-004,22.371286808828));
#295 = CARTESIAN_POINT('',(9.980039900003E-004,23.053580533636));
#296 = CARTESIAN_POINT('',(9.980039899995E-004,23.751780889668));
#297 = CARTESIAN_POINT('',(9.980039899993E-004,24.466876468307));
#298 = CARTESIAN_POINT('',(9.980039900012E-004,25.199732652869));
#299 = CARTESIAN_POINT('',(9.980039899991E-004,25.951064418362));
#300 = CARTESIAN_POINT('',(9.980039900002E-004,26.721413686029));
#301 = CARTESIAN_POINT('',(9.980039900004E-004,27.511129454125));
#302 = CARTESIAN_POINT('',(9.980039899986E-004,28.320321954363));
#303 = CARTESIAN_POINT('',(9.980039900003E-004,29.148977248214));
#304 = CARTESIAN_POINT('',(9.980039900005E-004,29.714213803107));
#305 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#306 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#307 = ORIENTED_EDGE('',*,*,#308,.T.);
#308 = EDGE_CURVE('',#191,#189,#309,.T.);
#309 = SURFACE_CURVE('',#310,(#335,#363),.PCURVE_S1.);
#310 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#311,#312,#313,#314,#315,#316,
    #317,#318,#319,#320,#321,#322,#323,#324,#325,#326,#327,#328,#329,
    #330,#331,#332,#333,#334),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164517,7.85828164968,10.7238180555,13.5836589972,
    16.4911855043,20.3877608712,22.3658107337),.UNSPECIFIED.);
#311 = CARTESIAN_POINT('',(15.,7.5,3.));
#312 = CARTESIAN_POINT('',(15.,7.03280174754,3.));
#313 = CARTESIAN_POINT('',(14.9454303202,6.53201453581,3.));
#314 = CARTESIAN_POINT('',(14.8204177413,6.00887696498,3.));
#315 = CARTESIAN_POINT('',(14.4273138745,5.01993856555,3.));
#316 = CARTESIAN_POINT('',(13.7419853635,4.11909525976,3.));
#317 = CARTESIAN_POINT('',(13.3547638071,3.73137366727,3.));
#318 = CARTESIAN_POINT('',(12.5674913741,3.13791197119,3.));
#319 = CARTESIAN_POINT('',(11.6451892622,2.74815963462,3.));
#320 = CARTESIAN_POINT('',(11.2232214435,2.62208066399,3.));
#321 = CARTESIAN_POINT('',(10.3562870372,2.46451900862,3.));
#322 = CARTESIAN_POINT('',(9.47359969847,2.49859923799,3.));
#323 = CARTESIAN_POINT('',(9.03694932519,2.56425143411,3.));
#324 = CARTESIAN_POINT('',(8.18135787977,2.79115421194,3.));
#325 = CARTESIAN_POINT('',(7.40424538089,3.19286932902,3.));
#326 = CARTESIAN_POINT('',(7.03968681504,3.43578091778,3.));
#327 = CARTESIAN_POINT('',(6.26445096378,4.08369870599,3.));
#328 = CARTESIAN_POINT('',(5.69047740185,4.87534434499,3.));
#329 = CARTESIAN_POINT('',(5.43624997802,5.35755180047,3.));
#330 = CARTESIAN_POINT('',(5.1637075653,6.09518106513,3.));
#331 = CARTESIAN_POINT('',(5.03878123004,6.81114489813,3.));
#332 = CARTESIAN_POINT('',(5.01236677119,7.04756862366,3.));
#333 = CARTESIAN_POINT('',(5.,7.27759033457,3.));
#334 = CARTESIAN_POINT('',(5.,7.5,3.));
#335 = PCURVE('',#80,#336);
#336 = DEFINITIONAL_REPRESENTATION('',(#337),#362);
#337 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#338,#339,#340,#341,#342,#343,
    #344,#345,#346,#347,#348,#349,#350,#351,#352,#353,#354,#355,#356,
    #357,#358,#359,#360,#361),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164517,7.85828164968,10.7238180555,13.5836589972,
    16.4911855043,20.3877608712,22.3658107337),.UNSPECIFIED.);
#338 = CARTESIAN_POINT('',(5.,0.E+000));
#339 = CARTESIAN_POINT('',(5.,-0.46719825246));
#340 = CARTESIAN_POINT('',(4.9454303202,-0.96798546419));
#341 = CARTESIAN_POINT('',(4.8204177413,-1.49112303502));
#342 = CARTESIAN_POINT('',(4.4273138745,-2.48006143445));
#343 = CARTESIAN_POINT('',(3.7419853635,-3.38090474024));
#344 = CARTESIAN_POINT('',(3.3547638071,-3.76862633273));
#345 = CARTESIAN_POINT('',(2.5674913741,-4.36208802881));
#346 = CARTESIAN_POINT('',(1.6451892622,-4.75184036538));
#347 = CARTESIAN_POINT('',(1.2232214435,-4.87791933601));
#348 = CARTESIAN_POINT('',(0.3562870372,-5.03548099138));
#349 = CARTESIAN_POINT('',(-0.52640030153,-5.00140076201));
#350 = CARTESIAN_POINT('',(-0.96305067481,-4.93574856589));
#351 = CARTESIAN_POINT('',(-1.81864212023,-4.70884578806));
#352 = CARTESIAN_POINT('',(-2.59575461911,-4.30713067098));
#353 = CARTESIAN_POINT('',(-2.96031318496,-4.06421908222));
#354 = CARTESIAN_POINT('',(-3.73554903622,-3.41630129401));
#355 = CARTESIAN_POINT('',(-4.30952259815,-2.62465565501));
#356 = CARTESIAN_POINT('',(-4.56375002198,-2.14244819953));
#357 = CARTESIAN_POINT('',(-4.8362924347,-1.40481893487));
#358 = CARTESIAN_POINT('',(-4.96121876996,-0.68885510187));
#359 = CARTESIAN_POINT('',(-4.98763322881,-0.45243137634));
#360 = CARTESIAN_POINT('',(-5.,-0.22240966543));
#361 = CARTESIAN_POINT('',(-5.,0.E+000));
#362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#363 = PCURVE('',#364,#373);
#364 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#365,#366,#367,#368)
    ,(#369,#370,#371,#372
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,3.00099800399),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#365 = CARTESIAN_POINT('',(15.,7.5,3.));
#366 = CARTESIAN_POINT('',(15.,-2.5,3.));
#367 = CARTESIAN_POINT('',(5.,-2.5,3.));
#368 = CARTESIAN_POINT('',(5.,7.5,3.));
#369 = CARTESIAN_POINT('',(15.,7.5,0.E+000));
#370 = CARTESIAN_POINT('',(15.,-2.5,0.E+000));
#371 = CARTESIAN_POINT('',(5.,-2.5,0.E+000));
#372 = CARTESIAN_POINT('',(5.,7.5,0.E+000));
#373 = DEFINITIONAL_REPRESENTATION('',(#374),#422);
#374 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#375,#376,#377,#378,#379,#380,
    #381,#382,#383,#384,#385,#386,#387,#388,#389,#390,#391,#392,#393,
    #394,#395,#396,#397,#398,#399,#400,#401,#402,#403,#404,#405,#406,
    #407,#408,#409,#410,#411,#412,#413,#414,#415,#416,#417,#418,#419,
    #420,#421),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,
    0.508313880311,1.016627760623,1.524941640934,2.033255521245,
    2.541569401557,3.049883281868,3.55819716218,4.066511042491,
    4.574824922802,5.083138803114,5.591452683425,6.099766563736,
    6.608080444048,7.116394324359,7.62470820467,8.133022084982,
    8.641335965293,9.149649845605,9.657963725916,10.166277606227,
    10.674591486539,11.18290536685,11.691219247161,12.199533127473,
    12.707847007784,13.216160888095,13.724474768407,14.232788648718,
    14.74110252903,15.249416409341,15.757730289652,16.266044169964,
    16.774358050275,17.282671930586,17.790985810898,18.299299691209,
    18.80761357152,19.315927451832,19.824241332143,20.332555212455,
    20.840869092766,21.349182973077,21.857496853389,22.3658107337),
  .QUASI_UNIFORM_KNOTS.);
#375 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#376 = CARTESIAN_POINT('',(9.980039900002E-004,0.285786133999));
#377 = CARTESIAN_POINT('',(9.980039900001E-004,0.851023724304));
#378 = CARTESIAN_POINT('',(9.980039899993E-004,1.679658949757));
#379 = CARTESIAN_POINT('',(9.980039900002E-004,2.48877584225));
#380 = CARTESIAN_POINT('',(9.980039899999E-004,3.278357389909));
#381 = CARTESIAN_POINT('',(9.9800399E-004,4.048590088927));
#382 = CARTESIAN_POINT('',(9.9800399E-004,4.799873549198));
#383 = CARTESIAN_POINT('',(9.9800399E-004,5.532780973984));
#384 = CARTESIAN_POINT('',(9.9800399E-004,6.248020908926));
#385 = CARTESIAN_POINT('',(9.9800399E-004,6.946360572727));
#386 = CARTESIAN_POINT('',(9.980039899998E-004,7.628688633133));
#387 = CARTESIAN_POINT('',(9.980039900008E-004,8.296073970944));
#388 = CARTESIAN_POINT('',(9.980039899996E-004,8.949683941827));
#389 = CARTESIAN_POINT('',(9.980039900005E-004,9.590744779194));
#390 = CARTESIAN_POINT('',(9.980039900008E-004,10.220499184724));
#391 = CARTESIAN_POINT('',(9.980039899988E-004,10.840182519777));
#392 = CARTESIAN_POINT('',(9.98003990001E-004,11.450961991235));
#393 = CARTESIAN_POINT('',(9.980039899995E-004,12.054057832055));
#394 = CARTESIAN_POINT('',(9.980039900008E-004,12.650784950465));
#395 = CARTESIAN_POINT('',(9.980039899998E-004,13.242437001825));
#396 = CARTESIAN_POINT('',(9.980039899997E-004,13.830311313687));
#397 = CARTESIAN_POINT('',(9.980039900009E-004,14.415700437053));
#398 = CARTESIAN_POINT('',(9.980039899989E-004,14.999897609704));
#399 = CARTESIAN_POINT('',(9.980039900004E-004,15.584089008431));
#400 = CARTESIAN_POINT('',(9.980039899991E-004,16.169496118509));
#401 = CARTESIAN_POINT('',(9.980039900002E-004,16.757374008936));
#402 = CARTESIAN_POINT('',(9.980039899996E-004,17.349001915149));
#403 = CARTESIAN_POINT('',(9.980039900008E-004,17.945677524114));
#404 = CARTESIAN_POINT('',(9.980039899991E-004,18.548712218151));
#405 = CARTESIAN_POINT('',(9.980039899994E-004,19.159406294427));
#406 = CARTESIAN_POINT('',(9.9800399E-004,19.779034539582));
#407 = CARTESIAN_POINT('',(9.980039899999E-004,20.40884411053));
#408 = CARTESIAN_POINT('',(9.980039899998E-004,21.050050714504));
#409 = CARTESIAN_POINT('',(9.980039900001E-004,21.703821239013));
#410 = CARTESIAN_POINT('',(9.98003989999E-004,22.371286806128));
#411 = CARTESIAN_POINT('',(9.980039900005E-004,23.053580531118));
#412 = CARTESIAN_POINT('',(9.980039899983E-004,23.751780887468));
#413 = CARTESIAN_POINT('',(9.980039900003E-004,24.46687646648));
#414 = CARTESIAN_POINT('',(9.980039899998E-004,25.199732651355));
#415 = CARTESIAN_POINT('',(9.980039899995E-004,25.951064417007));
#416 = CARTESIAN_POINT('',(9.980039899985E-004,26.721413684648));
#417 = CARTESIAN_POINT('',(9.980039900002E-004,27.511129452701));
#418 = CARTESIAN_POINT('',(9.980039899999E-004,28.320321953565));
#419 = CARTESIAN_POINT('',(9.980039899992E-004,29.148977248108));
#420 = CARTESIAN_POINT('',(9.980039899995E-004,29.714213803178));
#421 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#422 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#423 = ADVANCED_FACE('',(#424),#92,.T.);
#424 = FACE_BOUND('',#425,.T.);
#425 = EDGE_LOOP('',(#426,#449,#450,#473));
#426 = ORIENTED_EDGE('',*,*,#427,.T.);
#427 = EDGE_CURVE('',#428,#70,#430,.T.);
#428 = VERTEX_POINT('',#429);
#429 = CARTESIAN_POINT('',(20.,0.E+000,0.E+000));
#430 = SURFACE_CURVE('',#431,(#435,#442),.PCURVE_S1.);
#431 = LINE('',#432,#433);
#432 = CARTESIAN_POINT('',(20.,0.E+000,1.5));
#433 = VECTOR('',#434,1.);
#434 = DIRECTION('',(0.E+000,0.E+000,1.));
#435 = PCURVE('',#92,#436);
#436 = DEFINITIONAL_REPRESENTATION('',(#437),#441);
#437 = LINE('',#438,#439);
#438 = CARTESIAN_POINT('',(-1.5,10.));
#439 = VECTOR('',#440,1.);
#440 = DIRECTION('',(-1.,0.E+000));
#441 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#442 = PCURVE('',#120,#443);
#443 = DEFINITIONAL_REPRESENTATION('',(#444),#448);
#444 = LINE('',#445,#446);
#445 = CARTESIAN_POINT('',(-1.5,-7.5));
#446 = VECTOR('',#447,1.);
#447 = DIRECTION('',(-1.,0.E+000));
#448 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#449 = ORIENTED_EDGE('',*,*,#69,.T.);
#450 = ORIENTED_EDGE('',*,*,#451,.F.);
#451 = EDGE_CURVE('',#452,#72,#454,.T.);
#452 = VERTEX_POINT('',#453);
#453 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#454 = SURFACE_CURVE('',#455,(#459,#466),.PCURVE_S1.);
#455 = LINE('',#456,#457);
#456 = CARTESIAN_POINT('',(0.E+000,0.E+000,1.5));
#457 = VECTOR('',#458,1.);
#458 = DIRECTION('',(0.E+000,0.E+000,1.));
#459 = PCURVE('',#92,#460);
#460 = DEFINITIONAL_REPRESENTATION('',(#461),#465);
#461 = LINE('',#462,#463);
#462 = CARTESIAN_POINT('',(-1.5,-10.));
#463 = VECTOR('',#464,1.);
#464 = DIRECTION('',(-1.,0.E+000));
#465 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#466 = PCURVE('',#174,#467);
#467 = DEFINITIONAL_REPRESENTATION('',(#468),#472);
#468 = LINE('',#469,#470);
#469 = CARTESIAN_POINT('',(1.5,-7.5));
#470 = VECTOR('',#471,1.);
#471 = DIRECTION('',(1.,0.E+000));
#472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#473 = ORIENTED_EDGE('',*,*,#474,.T.);
#474 = EDGE_CURVE('',#452,#428,#475,.T.);
#475 = SURFACE_CURVE('',#476,(#480,#487),.PCURVE_S1.);
#476 = LINE('',#477,#478);
#477 = CARTESIAN_POINT('',(10.,0.E+000,0.E+000));
#478 = VECTOR('',#479,1.);
#479 = DIRECTION('',(1.,0.E+000,0.E+000));
#480 = PCURVE('',#92,#481);
#481 = DEFINITIONAL_REPRESENTATION('',(#482),#486);
#482 = LINE('',#483,#484);
#483 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#484 = VECTOR('',#485,1.);
#485 = DIRECTION('',(0.E+000,1.));
#486 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#487 = PCURVE('',#488,#493);
#488 = PLANE('',#489);
#489 = AXIS2_PLACEMENT_3D('',#490,#491,#492);
#490 = CARTESIAN_POINT('',(10.,7.5,0.E+000));
#491 = DIRECTION('',(0.E+000,0.E+000,-1.));
#492 = DIRECTION('',(-1.,0.E+000,0.E+000));
#493 = DEFINITIONAL_REPRESENTATION('',(#494),#498);
#494 = LINE('',#495,#496);
#495 = CARTESIAN_POINT('',(0.E+000,-7.5));
#496 = VECTOR('',#497,1.);
#497 = DIRECTION('',(-1.,0.E+000));
#498 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#499 = ADVANCED_FACE('',(#500),#120,.T.);
#500 = FACE_BOUND('',#501,.T.);
#501 = EDGE_LOOP('',(#502,#525,#546,#547));
#502 = ORIENTED_EDGE('',*,*,#503,.T.);
#503 = EDGE_CURVE('',#428,#504,#506,.T.);
#504 = VERTEX_POINT('',#505);
#505 = CARTESIAN_POINT('',(20.,15.,0.E+000));
#506 = SURFACE_CURVE('',#507,(#511,#518),.PCURVE_S1.);
#507 = LINE('',#508,#509);
#508 = CARTESIAN_POINT('',(20.,7.5,0.E+000));
#509 = VECTOR('',#510,1.);
#510 = DIRECTION('',(0.E+000,1.,0.E+000));
#511 = PCURVE('',#120,#512);
#512 = DEFINITIONAL_REPRESENTATION('',(#513),#517);
#513 = LINE('',#514,#515);
#514 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#515 = VECTOR('',#516,1.);
#516 = DIRECTION('',(0.E+000,1.));
#517 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#518 = PCURVE('',#488,#519);
#519 = DEFINITIONAL_REPRESENTATION('',(#520),#524);
#520 = LINE('',#521,#522);
#521 = CARTESIAN_POINT('',(-10.,0.E+000));
#522 = VECTOR('',#523,1.);
#523 = DIRECTION('',(0.E+000,1.));
#524 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#525 = ORIENTED_EDGE('',*,*,#526,.T.);
#526 = EDGE_CURVE('',#504,#105,#527,.T.);
#527 = SURFACE_CURVE('',#528,(#532,#539),.PCURVE_S1.);
#528 = LINE('',#529,#530);
#529 = CARTESIAN_POINT('',(20.,15.,1.5));
#530 = VECTOR('',#531,1.);
#531 = DIRECTION('',(0.E+000,0.E+000,1.));
#532 = PCURVE('',#120,#533);
#533 = DEFINITIONAL_REPRESENTATION('',(#534),#538);
#534 = LINE('',#535,#536);
#535 = CARTESIAN_POINT('',(-1.5,7.5));
#536 = VECTOR('',#537,1.);
#537 = DIRECTION('',(-1.,0.E+000));
#538 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#539 = PCURVE('',#148,#540);
#540 = DEFINITIONAL_REPRESENTATION('',(#541),#545);
#541 = LINE('',#542,#543);
#542 = CARTESIAN_POINT('',(1.5,10.));
#543 = VECTOR('',#544,1.);
#544 = DIRECTION('',(1.,0.E+000));
#545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#546 = ORIENTED_EDGE('',*,*,#104,.T.);
#547 = ORIENTED_EDGE('',*,*,#427,.F.);
#548 = ADVANCED_FACE('',(#549),#148,.T.);
#549 = FACE_BOUND('',#550,.T.);
#550 = EDGE_LOOP('',(#551,#574,#575,#576));
#551 = ORIENTED_EDGE('',*,*,#552,.T.);
#552 = EDGE_CURVE('',#553,#133,#555,.T.);
#553 = VERTEX_POINT('',#554);
#554 = CARTESIAN_POINT('',(0.E+000,15.,0.E+000));
#555 = SURFACE_CURVE('',#556,(#560,#567),.PCURVE_S1.);
#556 = LINE('',#557,#558);
#557 = CARTESIAN_POINT('',(0.E+000,15.,1.5));
#558 = VECTOR('',#559,1.);
#559 = DIRECTION('',(0.E+000,0.E+000,1.));
#560 = PCURVE('',#148,#561);
#561 = DEFINITIONAL_REPRESENTATION('',(#562),#566);
#562 = LINE('',#563,#564);
#563 = CARTESIAN_POINT('',(1.5,-10.));
#564 = VECTOR('',#565,1.);
#565 = DIRECTION('',(1.,0.E+000));
#566 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#567 = PCURVE('',#174,#568);
#568 = DEFINITIONAL_REPRESENTATION('',(#569),#573);
#569 = LINE('',#570,#571);
#570 = CARTESIAN_POINT('',(1.5,7.5));
#571 = VECTOR('',#572,1.);
#572 = DIRECTION('',(1.,0.E+000));
#573 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#574 = ORIENTED_EDGE('',*,*,#132,.T.);
#575 = ORIENTED_EDGE('',*,*,#526,.F.);
#576 = ORIENTED_EDGE('',*,*,#577,.T.);
#577 = EDGE_CURVE('',#504,#553,#578,.T.);
#578 = SURFACE_CURVE('',#579,(#583,#590),.PCURVE_S1.);
#579 = LINE('',#580,#581);
#580 = CARTESIAN_POINT('',(10.,15.,0.E+000));
#581 = VECTOR('',#582,1.);
#582 = DIRECTION('',(-1.,0.E+000,0.E+000));
#583 = PCURVE('',#148,#584);
#584 = DEFINITIONAL_REPRESENTATION('',(#585),#589);
#585 = LINE('',#586,#587);
#586 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#587 = VECTOR('',#588,1.);
#588 = DIRECTION('',(0.E+000,-1.));
#589 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#590 = PCURVE('',#488,#591);
#591 = DEFINITIONAL_REPRESENTATION('',(#592),#596);
#592 = LINE('',#593,#594);
#593 = CARTESIAN_POINT('',(0.E+000,7.5));
#594 = VECTOR('',#595,1.);
#595 = DIRECTION('',(1.,0.E+000));
#596 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#597 = ADVANCED_FACE('',(#598),#174,.T.);
#598 = FACE_BOUND('',#599,.T.);
#599 = EDGE_LOOP('',(#600,#601,#602,#603));
#600 = ORIENTED_EDGE('',*,*,#451,.T.);
#601 = ORIENTED_EDGE('',*,*,#160,.T.);
#602 = ORIENTED_EDGE('',*,*,#552,.F.);
#603 = ORIENTED_EDGE('',*,*,#604,.T.);
#604 = EDGE_CURVE('',#553,#452,#605,.T.);
#605 = SURFACE_CURVE('',#606,(#610,#617),.PCURVE_S1.);
#606 = LINE('',#607,#608);
#607 = CARTESIAN_POINT('',(0.E+000,7.5,0.E+000));
#608 = VECTOR('',#609,1.);
#609 = DIRECTION('',(0.E+000,-1.,0.E+000));
#610 = PCURVE('',#174,#611);
#611 = DEFINITIONAL_REPRESENTATION('',(#612),#616);
#612 = LINE('',#613,#614);
#613 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#614 = VECTOR('',#615,1.);
#615 = DIRECTION('',(0.E+000,-1.));
#616 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#617 = PCURVE('',#488,#618);
#618 = DEFINITIONAL_REPRESENTATION('',(#619),#623);
#619 = LINE('',#620,#621);
#620 = CARTESIAN_POINT('',(10.,0.E+000));
#621 = VECTOR('',#622,1.);
#622 = DIRECTION('',(0.E+000,-1.));
#623 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#624 = ADVANCED_FACE('',(#625),#248,.T.);
#625 = FACE_BOUND('',#626,.T.);
#626 = EDGE_LOOP('',(#627,#654,#674,#675));
#627 = ORIENTED_EDGE('',*,*,#628,.T.);
#628 = EDGE_CURVE('',#629,#631,#633,.T.);
#629 = VERTEX_POINT('',#630);
#630 = CARTESIAN_POINT('',(5.,7.5,2.22044604925E-016));
#631 = VERTEX_POINT('',#632);
#632 = CARTESIAN_POINT('',(15.,7.5,0.E+000));
#633 = SURFACE_CURVE('',#634,(#639,#646),.PCURVE_S1.);
#634 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#635,#636,#637,#638),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#635 = CARTESIAN_POINT('',(5.,7.5,0.E+000));
#636 = CARTESIAN_POINT('',(5.,17.5,0.E+000));
#637 = CARTESIAN_POINT('',(15.,17.5,0.E+000));
#638 = CARTESIAN_POINT('',(15.,7.5,0.E+000));
#639 = PCURVE('',#248,#640);
#640 = DEFINITIONAL_REPRESENTATION('',(#641),#645);
#641 = LINE('',#642,#643);
#642 = CARTESIAN_POINT('',(3.00099800399,0.E+000));
#643 = VECTOR('',#644,1.);
#644 = DIRECTION('',(0.E+000,1.));
#645 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#646 = PCURVE('',#488,#647);
#647 = DEFINITIONAL_REPRESENTATION('',(#648),#653);
#648 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#649,#650,#651,#652),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#649 = CARTESIAN_POINT('',(5.,0.E+000));
#650 = CARTESIAN_POINT('',(5.,10.));
#651 = CARTESIAN_POINT('',(-5.,10.));
#652 = CARTESIAN_POINT('',(-5.,0.E+000));
#653 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#654 = ORIENTED_EDGE('',*,*,#655,.F.);
#655 = EDGE_CURVE('',#191,#631,#656,.T.);
#656 = SURFACE_CURVE('',#657,(#660,#667),.PCURVE_S1.);
#657 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#658,#659),.UNSPECIFIED.,.F.,.F.,
  (2,2),(9.9800399E-004,3.00099800399),.PIECEWISE_BEZIER_KNOTS.);
#658 = CARTESIAN_POINT('',(15.,7.5,3.));
#659 = CARTESIAN_POINT('',(15.,7.5,0.E+000));
#660 = PCURVE('',#248,#661);
#661 = DEFINITIONAL_REPRESENTATION('',(#662),#666);
#662 = LINE('',#663,#664);
#663 = CARTESIAN_POINT('',(0.E+000,30.));
#664 = VECTOR('',#665,1.);
#665 = DIRECTION('',(1.,0.E+000));
#666 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#667 = PCURVE('',#364,#668);
#668 = DEFINITIONAL_REPRESENTATION('',(#669),#673);
#669 = LINE('',#670,#671);
#670 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#671 = VECTOR('',#672,1.);
#672 = DIRECTION('',(1.,0.E+000));
#673 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#674 = ORIENTED_EDGE('',*,*,#188,.F.);
#675 = ORIENTED_EDGE('',*,*,#676,.T.);
#676 = EDGE_CURVE('',#189,#629,#677,.T.);
#677 = SURFACE_CURVE('',#678,(#681,#688),.PCURVE_S1.);
#678 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#679,#680),.UNSPECIFIED.,.F.,.F.,
  (2,2),(9.9800399E-004,3.00099800399),.PIECEWISE_BEZIER_KNOTS.);
#679 = CARTESIAN_POINT('',(5.,7.5,3.));
#680 = CARTESIAN_POINT('',(5.,7.5,0.E+000));
#681 = PCURVE('',#248,#682);
#682 = DEFINITIONAL_REPRESENTATION('',(#683),#687);
#683 = LINE('',#684,#685);
#684 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#685 = VECTOR('',#686,1.);
#686 = DIRECTION('',(1.,0.E+000));
#687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#688 = PCURVE('',#364,#689);
#689 = DEFINITIONAL_REPRESENTATION('',(#690),#694);
#690 = LINE('',#691,#692);
#691 = CARTESIAN_POINT('',(0.E+000,30.));
#692 = VECTOR('',#693,1.);
#693 = DIRECTION('',(1.,0.E+000));
#694 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#695 = ADVANCED_FACE('',(#696),#364,.T.);
#696 = FACE_BOUND('',#697,.T.);
#697 = EDGE_LOOP('',(#698,#721,#722,#723));
#698 = ORIENTED_EDGE('',*,*,#699,.T.);
#699 = EDGE_CURVE('',#631,#629,#700,.T.);
#700 = SURFACE_CURVE('',#701,(#706,#713),.PCURVE_S1.);
#701 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#702,#703,#704,#705),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#702 = CARTESIAN_POINT('',(15.,7.5,0.E+000));
#703 = CARTESIAN_POINT('',(15.,-2.5,0.E+000));
#704 = CARTESIAN_POINT('',(5.,-2.5,0.E+000));
#705 = CARTESIAN_POINT('',(5.,7.5,0.E+000));
#706 = PCURVE('',#364,#707);
#707 = DEFINITIONAL_REPRESENTATION('',(#708),#712);
#708 = LINE('',#709,#710);
#709 = CARTESIAN_POINT('',(3.00099800399,0.E+000));
#710 = VECTOR('',#711,1.);
#711 = DIRECTION('',(0.E+000,1.));
#712 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#713 = PCURVE('',#488,#714);
#714 = DEFINITIONAL_REPRESENTATION('',(#715),#720);
#715 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#716,#717,#718,#719),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#716 = CARTESIAN_POINT('',(-5.,0.E+000));
#717 = CARTESIAN_POINT('',(-5.,-10.));
#718 = CARTESIAN_POINT('',(5.,-10.));
#719 = CARTESIAN_POINT('',(5.,0.E+000));
#720 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#721 = ORIENTED_EDGE('',*,*,#676,.F.);
#722 = ORIENTED_EDGE('',*,*,#308,.F.);
#723 = ORIENTED_EDGE('',*,*,#655,.T.);
#724 = ADVANCED_FACE('',(#725,#731),#488,.T.);
#725 = FACE_BOUND('',#726,.T.);
#726 = EDGE_LOOP('',(#727,#728,#729,#730));
#727 = ORIENTED_EDGE('',*,*,#503,.F.);
#728 = ORIENTED_EDGE('',*,*,#474,.F.);
#729 = ORIENTED_EDGE('',*,*,#604,.F.);
#730 = ORIENTED_EDGE('',*,*,#577,.F.);
#731 = FACE_BOUND('',#732,.T.);
#732 = EDGE_LOOP('',(#733,#734));
#733 = ORIENTED_EDGE('',*,*,#699,.F.);
#734 = ORIENTED_EDGE('',*,*,#628,.F.);
#735 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#739)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#736,#737,#738)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#736 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#737 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#738 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#739 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-006),#736,
  'distance_accuracy_value','confusion accuracy');
#740 = SHAPE_DEFINITION_REPRESENTATION(#741,#62);
#741 = PRODUCT_DEFINITION_SHAPE('','',#742);
#742 = PRODUCT_DEFINITION('design','',#743,#746);
#743 = PRODUCT_DEFINITION_FORMATION('','',#744);
#744 = PRODUCT('nut','nut','',(#745));
#745 = PRODUCT_CONTEXT('',#2,'mechanical');
#746 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#747 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#748,#750);
#748 = ( REPRESENTATION_RELATIONSHIP('','',#62,#44) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#749) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#749 = ITEM_DEFINED_TRANSFORMATION('','',#11,#45);
#750 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#751
  );
#751 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','nut_1','',#39,#742,$);
#752 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#744));
#753 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#754,#756);
#754 = ( REPRESENTATION_RELATIONSHIP('','',#62,#44) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#755) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#755 = ITEM_DEFINED_TRANSFORMATION('','',#11,#49);
#756 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#757
  );
#757 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','nut_2','',#39,#742,$);
#758 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#759),#1115);
#759 = MANIFOLD_SOLID_BREP('',#760);
#760 = CLOSED_SHELL('',(#761,#1005,#1081,#1110));
#761 = ADVANCED_FACE('',(#762),#797,.T.);
#762 = FACE_BOUND('',#763,.T.);
#763 = EDGE_LOOP('',(#764,#889));
#764 = ORIENTED_EDGE('',*,*,#765,.F.);
#765 = EDGE_CURVE('',#766,#768,#770,.T.);
#766 = VERTEX_POINT('',#767);
#767 = CARTESIAN_POINT('',(5.,2.22044604925E-016,200.));
#768 = VERTEX_POINT('',#769);
#769 = CARTESIAN_POINT('',(-5.,-2.22044604925E-016,200.));
#770 = SURFACE_CURVE('',#771,(#796,#829),.PCURVE_S1.);
#771 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#772,#773,#774,#775,#776,#777,
    #778,#779,#780,#781,#782,#783,#784,#785,#786,#787,#788,#789,#790,
    #791,#792,#793,#794,#795),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164387,7.85828164661,10.7238180516,13.5836589935,
    16.4911855015,20.38776087,22.3658107353),.UNSPECIFIED.);
#772 = CARTESIAN_POINT('',(5.,-2.22044604925E-016,200.));
#773 = CARTESIAN_POINT('',(5.,-0.467198252312,200.));
#774 = CARTESIAN_POINT('',(4.94543032016,-0.967985463874,200.));
#775 = CARTESIAN_POINT('',(4.82041774119,-1.49112303535,200.));
#776 = CARTESIAN_POINT('',(4.42731387443,-2.48006143438,200.));
#777 = CARTESIAN_POINT('',(3.74198536382,-3.38090473983,200.));
#778 = CARTESIAN_POINT('',(3.35476380665,-3.76862633308,200.));
#779 = CARTESIAN_POINT('',(2.56749137395,-4.36208802884,200.));
#780 = CARTESIAN_POINT('',(1.64518926245,-4.75184036526,200.));
#781 = CARTESIAN_POINT('',(1.22322144323,-4.87791933608,200.));
#782 = CARTESIAN_POINT('',(0.356287037014,-5.03548099138,200.));
#783 = CARTESIAN_POINT('',(-0.52640030158,-5.00140076198,200.));
#784 = CARTESIAN_POINT('',(-0.963050674765,-4.93574856594,200.));
#785 = CARTESIAN_POINT('',(-1.81864212033,-4.70884578804,200.));
#786 = CARTESIAN_POINT('',(-2.59575461931,-4.30713067084,200.));
#787 = CARTESIAN_POINT('',(-2.9603131848,-4.06421908239,200.));
#788 = CARTESIAN_POINT('',(-3.73554903634,-3.41630129394,200.));
#789 = CARTESIAN_POINT('',(-4.3095225984,-2.62465565461,200.));
#790 = CARTESIAN_POINT('',(-4.56375002186,-2.14244819995,200.));
#791 = CARTESIAN_POINT('',(-4.8362924348,-1.40481893471,200.));
#792 = CARTESIAN_POINT('',(-4.96121877006,-0.68885510118,200.));
#793 = CARTESIAN_POINT('',(-4.98763322877,-0.452431376999,200.));
#794 = CARTESIAN_POINT('',(-5.,-0.222409665749,200.));
#795 = CARTESIAN_POINT('',(-5.,4.4408920985E-016,200.));
#796 = PCURVE('',#797,#802);
#797 = PLANE('',#798);
#798 = AXIS2_PLACEMENT_3D('',#799,#800,#801);
#799 = CARTESIAN_POINT('',(0.E+000,0.E+000,200.));
#800 = DIRECTION('',(0.E+000,0.E+000,1.));
#801 = DIRECTION('',(1.,0.E+000,0.E+000));
#802 = DEFINITIONAL_REPRESENTATION('',(#803),#828);
#803 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#804,#805,#806,#807,#808,#809,
    #810,#811,#812,#813,#814,#815,#816,#817,#818,#819,#820,#821,#822,
    #823,#824,#825,#826,#827),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164387,7.85828164661,10.7238180516,13.5836589935,
    16.4911855015,20.38776087,22.3658107353),.UNSPECIFIED.);
#804 = CARTESIAN_POINT('',(5.,-2.22044604925E-016));
#805 = CARTESIAN_POINT('',(5.,-0.467198252312));
#806 = CARTESIAN_POINT('',(4.94543032016,-0.967985463874));
#807 = CARTESIAN_POINT('',(4.82041774119,-1.49112303535));
#808 = CARTESIAN_POINT('',(4.42731387443,-2.48006143438));
#809 = CARTESIAN_POINT('',(3.74198536382,-3.38090473983));
#810 = CARTESIAN_POINT('',(3.35476380665,-3.76862633308));
#811 = CARTESIAN_POINT('',(2.56749137395,-4.36208802884));
#812 = CARTESIAN_POINT('',(1.64518926245,-4.75184036526));
#813 = CARTESIAN_POINT('',(1.22322144323,-4.87791933608));
#814 = CARTESIAN_POINT('',(0.356287037014,-5.03548099138));
#815 = CARTESIAN_POINT('',(-0.52640030158,-5.00140076198));
#816 = CARTESIAN_POINT('',(-0.963050674765,-4.93574856594));
#817 = CARTESIAN_POINT('',(-1.81864212033,-4.70884578804));
#818 = CARTESIAN_POINT('',(-2.59575461931,-4.30713067084));
#819 = CARTESIAN_POINT('',(-2.9603131848,-4.06421908239));
#820 = CARTESIAN_POINT('',(-3.73554903634,-3.41630129394));
#821 = CARTESIAN_POINT('',(-4.3095225984,-2.62465565461));
#822 = CARTESIAN_POINT('',(-4.56375002186,-2.14244819995));
#823 = CARTESIAN_POINT('',(-4.8362924348,-1.40481893471));
#824 = CARTESIAN_POINT('',(-4.96121877006,-0.68885510118));
#825 = CARTESIAN_POINT('',(-4.98763322877,-0.452431376999));
#826 = CARTESIAN_POINT('',(-5.,-0.222409665749));
#827 = CARTESIAN_POINT('',(-5.,4.4408920985E-016));
#828 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#829 = PCURVE('',#830,#839);
#830 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#831,#832,#833,#834)
    ,(#835,#836,#837,#838
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,200.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#831 = CARTESIAN_POINT('',(-5.,0.E+000,200.));
#832 = CARTESIAN_POINT('',(-5.,-10.,200.));
#833 = CARTESIAN_POINT('',(5.,-10.,200.));
#834 = CARTESIAN_POINT('',(5.,0.E+000,200.));
#835 = CARTESIAN_POINT('',(-5.,0.E+000,0.E+000));
#836 = CARTESIAN_POINT('',(-5.,-10.,0.E+000));
#837 = CARTESIAN_POINT('',(5.,-10.,0.E+000));
#838 = CARTESIAN_POINT('',(5.,0.E+000,0.E+000));
#839 = DEFINITIONAL_REPRESENTATION('',(#840),#888);
#840 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#841,#842,#843,#844,#845,#846,
    #847,#848,#849,#850,#851,#852,#853,#854,#855,#856,#857,#858,#859,
    #860,#861,#862,#863,#864,#865,#866,#867,#868,#869,#870,#871,#872,
    #873,#874,#875,#876,#877,#878,#879,#880,#881,#882,#883,#884,#885,
    #886,#887),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,
    0.508313880348,1.016627760695,1.524941641043,2.033255521391,
    2.541569401739,3.049883282086,3.558197162434,4.066511042782,
    4.57482492313,5.083138803477,5.591452683825,6.099766564173,
    6.60808044452,7.116394324868,7.624708205216,8.133022085564,
    8.641335965911,9.149649846259,9.657963726607,10.166277606955,
    10.674591487302,11.18290536765,11.691219247998,12.199533128345,
    12.707847008693,13.216160889041,13.724474769389,14.232788649736,
    14.741102530084,15.249416410432,15.75773029078,16.266044171127,
    16.774358051475,17.282671931823,17.79098581217,18.299299692518,
    18.807613572866,19.315927453214,19.824241333561,20.332555213909,
    20.840869094257,21.349182974605,21.857496854952,22.3658107353),
  .QUASI_UNIFORM_KNOTS.);
#841 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#842 = CARTESIAN_POINT('',(9.980039899826E-004,29.714213865995));
#843 = CARTESIAN_POINT('',(9.980039899667E-004,29.148976275626));
#844 = CARTESIAN_POINT('',(9.98003989972E-004,28.320341049933));
#845 = CARTESIAN_POINT('',(9.980039899747E-004,27.511224157016));
#846 = CARTESIAN_POINT('',(9.980039899587E-004,26.721642608853));
#847 = CARTESIAN_POINT('',(9.980039900196E-004,25.951409909365));
#848 = CARTESIAN_POINT('',(9.980039899628E-004,25.200126448755));
#849 = CARTESIAN_POINT('',(9.980039899586E-004,24.467219023802));
#850 = CARTESIAN_POINT('',(9.98003990032E-004,23.751979088838));
#851 = CARTESIAN_POINT('',(9.980039899132E-004,23.053639425058));
#852 = CARTESIAN_POINT('',(9.98003989974E-004,22.371311364439));
#853 = CARTESIAN_POINT('',(9.9800399002E-004,21.703926026155));
#854 = CARTESIAN_POINT('',(9.980039899456E-004,21.050316054675));
#855 = CARTESIAN_POINT('',(9.980039900268E-004,20.409255216776));
#856 = CARTESIAN_POINT('',(9.980039899471E-004,19.779500810931));
#857 = CARTESIAN_POINT('',(9.98003990014E-004,19.159817475822));
#858 = CARTESIAN_POINT('',(9.98003989997E-004,18.549038004437));
#859 = CARTESIAN_POINT('',(9.980039899983E-004,17.945942163512));
#860 = CARTESIAN_POINT('',(9.980039900102E-004,17.349215044793));
#861 = CARTESIAN_POINT('',(9.980039899614E-004,16.757562993069));
#862 = CARTESIAN_POINT('',(9.980039899745E-004,16.169688680961));
#863 = CARTESIAN_POINT('',(9.980039899711E-004,15.584299557553));
#864 = CARTESIAN_POINT('',(9.980039899716E-004,15.000102384886));
#865 = CARTESIAN_POINT('',(9.980039899734E-004,14.415910986161));
#866 = CARTESIAN_POINT('',(9.980039899657E-004,13.830503876104));
#867 = CARTESIAN_POINT('',(9.980039899949E-004,13.242625985685));
#868 = CARTESIAN_POINT('',(9.980039900566E-004,12.650998079437));
#869 = CARTESIAN_POINT('',(9.980039899516E-004,12.054322470375));
#870 = CARTESIAN_POINT('',(9.980039899689E-004,11.451287776291));
#871 = CARTESIAN_POINT('',(9.980039900049E-004,10.840593700147));
#872 = CARTESIAN_POINT('',(9.980039900144E-004,10.220965455217));
#873 = CARTESIAN_POINT('',(9.980039899406E-004,9.59115588443));
#874 = CARTESIAN_POINT('',(9.98003990056E-004,8.94994928042));
#875 = CARTESIAN_POINT('',(9.980039900095E-004,8.296178755736));
#876 = CARTESIAN_POINT('',(9.980039899101E-004,7.628713188564));
#877 = CARTESIAN_POINT('',(9.980039900137E-004,6.946419463728));
#878 = CARTESIAN_POINT('',(9.980039900403E-004,6.248219107721));
#879 = CARTESIAN_POINT('',(9.980039900015E-004,5.533123529128));
#880 = CARTESIAN_POINT('',(9.980039899607E-004,4.800267344587));
#881 = CARTESIAN_POINT('',(9.980039899929E-004,4.048935579056));
#882 = CARTESIAN_POINT('',(9.980039899063E-004,3.278586311278));
#883 = CARTESIAN_POINT('',(9.980039900514E-004,2.488870543065));
#884 = CARTESIAN_POINT('',(9.980039899004E-004,1.679678044096));
#885 = CARTESIAN_POINT('',(9.980039900201E-004,0.851022751652));
#886 = CARTESIAN_POINT('',(9.980039900301E-004,0.285786197076));
#887 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#888 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#889 = ORIENTED_EDGE('',*,*,#890,.F.);
#890 = EDGE_CURVE('',#768,#766,#891,.T.);
#891 = SURFACE_CURVE('',#892,(#917,#945),.PCURVE_S1.);
#892 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#893,#894,#895,#896,#897,#898,
    #899,#900,#901,#902,#903,#904,#905,#906,#907,#908,#909,#910,#911,
    #912,#913,#914,#915,#916),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164387,7.85828164661,10.7238180516,13.5836589935,
    16.4911855015,20.38776087,22.3658107353),.UNSPECIFIED.);
#893 = CARTESIAN_POINT('',(-5.,2.22044604925E-016,200.));
#894 = CARTESIAN_POINT('',(-5.,0.467198252312,200.));
#895 = CARTESIAN_POINT('',(-4.94543032016,0.967985463874,200.));
#896 = CARTESIAN_POINT('',(-4.82041774119,1.49112303535,200.));
#897 = CARTESIAN_POINT('',(-4.42731387443,2.48006143438,200.));
#898 = CARTESIAN_POINT('',(-3.74198536382,3.38090473983,200.));
#899 = CARTESIAN_POINT('',(-3.35476380665,3.76862633308,200.));
#900 = CARTESIAN_POINT('',(-2.56749137395,4.36208802884,200.));
#901 = CARTESIAN_POINT('',(-1.64518926245,4.75184036526,200.));
#902 = CARTESIAN_POINT('',(-1.22322144323,4.87791933608,200.));
#903 = CARTESIAN_POINT('',(-0.356287037014,5.03548099138,200.));
#904 = CARTESIAN_POINT('',(0.52640030158,5.00140076198,200.));
#905 = CARTESIAN_POINT('',(0.963050674765,4.93574856594,200.));
#906 = CARTESIAN_POINT('',(1.81864212033,4.70884578804,200.));
#907 = CARTESIAN_POINT('',(2.59575461931,4.30713067084,200.));
#908 = CARTESIAN_POINT('',(2.9603131848,4.06421908239,200.));
#909 = CARTESIAN_POINT('',(3.73554903634,3.41630129394,200.));
#910 = CARTESIAN_POINT('',(4.3095225984,2.62465565461,200.));
#911 = CARTESIAN_POINT('',(4.56375002186,2.14244819995,200.));
#912 = CARTESIAN_POINT('',(4.8362924348,1.40481893471,200.));
#913 = CARTESIAN_POINT('',(4.96121877006,0.68885510118,200.));
#914 = CARTESIAN_POINT('',(4.98763322877,0.452431376999,200.));
#915 = CARTESIAN_POINT('',(5.,0.222409665749,200.));
#916 = CARTESIAN_POINT('',(5.,-4.4408920985E-016,200.));
#917 = PCURVE('',#797,#918);
#918 = DEFINITIONAL_REPRESENTATION('',(#919),#944);
#919 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#920,#921,#922,#923,#924,#925,
    #926,#927,#928,#929,#930,#931,#932,#933,#934,#935,#936,#937,#938,
    #939,#940,#941,#942,#943),.UNSPECIFIED.,.F.,.F.,(6,3,3,3,3,3,3,6),(
    0.E+000,4.15513164387,7.85828164661,10.7238180516,13.5836589935,
    16.4911855015,20.38776087,22.3658107353),.UNSPECIFIED.);
#920 = CARTESIAN_POINT('',(-5.,2.22044604925E-016));
#921 = CARTESIAN_POINT('',(-5.,0.467198252312));
#922 = CARTESIAN_POINT('',(-4.94543032016,0.967985463874));
#923 = CARTESIAN_POINT('',(-4.82041774119,1.49112303535));
#924 = CARTESIAN_POINT('',(-4.42731387443,2.48006143438));
#925 = CARTESIAN_POINT('',(-3.74198536382,3.38090473983));
#926 = CARTESIAN_POINT('',(-3.35476380665,3.76862633308));
#927 = CARTESIAN_POINT('',(-2.56749137395,4.36208802884));
#928 = CARTESIAN_POINT('',(-1.64518926245,4.75184036526));
#929 = CARTESIAN_POINT('',(-1.22322144323,4.87791933608));
#930 = CARTESIAN_POINT('',(-0.356287037014,5.03548099138));
#931 = CARTESIAN_POINT('',(0.52640030158,5.00140076198));
#932 = CARTESIAN_POINT('',(0.963050674765,4.93574856594));
#933 = CARTESIAN_POINT('',(1.81864212033,4.70884578804));
#934 = CARTESIAN_POINT('',(2.59575461931,4.30713067084));
#935 = CARTESIAN_POINT('',(2.9603131848,4.06421908239));
#936 = CARTESIAN_POINT('',(3.73554903634,3.41630129394));
#937 = CARTESIAN_POINT('',(4.3095225984,2.62465565461));
#938 = CARTESIAN_POINT('',(4.56375002186,2.14244819995));
#939 = CARTESIAN_POINT('',(4.8362924348,1.40481893471));
#940 = CARTESIAN_POINT('',(4.96121877006,0.68885510118));
#941 = CARTESIAN_POINT('',(4.98763322877,0.452431376999));
#942 = CARTESIAN_POINT('',(5.,0.222409665749));
#943 = CARTESIAN_POINT('',(5.,-4.4408920985E-016));
#944 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#945 = PCURVE('',#946,#955);
#946 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#947,#948,#949,#950)
    ,(#951,#952,#953,#954
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,200.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#947 = CARTESIAN_POINT('',(5.,0.E+000,200.));
#948 = CARTESIAN_POINT('',(5.,10.,200.));
#949 = CARTESIAN_POINT('',(-5.,10.,200.));
#950 = CARTESIAN_POINT('',(-5.,0.E+000,200.));
#951 = CARTESIAN_POINT('',(5.,0.E+000,0.E+000));
#952 = CARTESIAN_POINT('',(5.,10.,0.E+000));
#953 = CARTESIAN_POINT('',(-5.,10.,0.E+000));
#954 = CARTESIAN_POINT('',(-5.,0.E+000,0.E+000));
#955 = DEFINITIONAL_REPRESENTATION('',(#956),#1004);
#956 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#957,#958,#959,#960,#961,#962,
    #963,#964,#965,#966,#967,#968,#969,#970,#971,#972,#973,#974,#975,
    #976,#977,#978,#979,#980,#981,#982,#983,#984,#985,#986,#987,#988,
    #989,#990,#991,#992,#993,#994,#995,#996,#997,#998,#999,#1000,#1001,
    #1002,#1003),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
    1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,
    0.508313880348,1.016627760695,1.524941641043,2.033255521391,
    2.541569401739,3.049883282086,3.558197162434,4.066511042782,
    4.57482492313,5.083138803477,5.591452683825,6.099766564173,
    6.60808044452,7.116394324868,7.624708205216,8.133022085564,
    8.641335965911,9.149649846259,9.657963726607,10.166277606955,
    10.674591487302,11.18290536765,11.691219247998,12.199533128345,
    12.707847008693,13.216160889041,13.724474769389,14.232788649736,
    14.741102530084,15.249416410432,15.75773029078,16.266044171127,
    16.774358051475,17.282671931823,17.79098581217,18.299299692518,
    18.807613572866,19.315927453214,19.824241333561,20.332555213909,
    20.840869094257,21.349182974605,21.857496854952,22.3658107353),
  .QUASI_UNIFORM_KNOTS.);
#957 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#958 = CARTESIAN_POINT('',(9.980039899826E-004,29.714213865995));
#959 = CARTESIAN_POINT('',(9.980039899667E-004,29.148976275626));
#960 = CARTESIAN_POINT('',(9.98003989972E-004,28.320341049933));
#961 = CARTESIAN_POINT('',(9.980039899747E-004,27.511224157016));
#962 = CARTESIAN_POINT('',(9.980039899587E-004,26.721642608853));
#963 = CARTESIAN_POINT('',(9.980039900196E-004,25.951409909365));
#964 = CARTESIAN_POINT('',(9.980039899628E-004,25.200126448755));
#965 = CARTESIAN_POINT('',(9.980039899586E-004,24.467219023802));
#966 = CARTESIAN_POINT('',(9.98003990032E-004,23.751979088838));
#967 = CARTESIAN_POINT('',(9.980039899132E-004,23.053639425058));
#968 = CARTESIAN_POINT('',(9.98003989974E-004,22.371311364439));
#969 = CARTESIAN_POINT('',(9.9800399002E-004,21.703926026155));
#970 = CARTESIAN_POINT('',(9.980039899456E-004,21.050316054675));
#971 = CARTESIAN_POINT('',(9.980039900268E-004,20.409255216776));
#972 = CARTESIAN_POINT('',(9.980039899471E-004,19.779500810931));
#973 = CARTESIAN_POINT('',(9.98003990014E-004,19.159817475822));
#974 = CARTESIAN_POINT('',(9.98003989997E-004,18.549038004437));
#975 = CARTESIAN_POINT('',(9.980039899983E-004,17.945942163512));
#976 = CARTESIAN_POINT('',(9.980039900102E-004,17.349215044793));
#977 = CARTESIAN_POINT('',(9.980039899614E-004,16.757562993069));
#978 = CARTESIAN_POINT('',(9.980039899745E-004,16.169688680961));
#979 = CARTESIAN_POINT('',(9.980039899711E-004,15.584299557553));
#980 = CARTESIAN_POINT('',(9.980039899716E-004,15.000102384886));
#981 = CARTESIAN_POINT('',(9.980039899734E-004,14.415910986161));
#982 = CARTESIAN_POINT('',(9.980039899657E-004,13.830503876104));
#983 = CARTESIAN_POINT('',(9.980039899949E-004,13.242625985685));
#984 = CARTESIAN_POINT('',(9.980039900566E-004,12.650998079437));
#985 = CARTESIAN_POINT('',(9.980039899516E-004,12.054322470375));
#986 = CARTESIAN_POINT('',(9.980039899689E-004,11.451287776291));
#987 = CARTESIAN_POINT('',(9.980039900049E-004,10.840593700147));
#988 = CARTESIAN_POINT('',(9.980039900144E-004,10.220965455217));
#989 = CARTESIAN_POINT('',(9.980039899406E-004,9.59115588443));
#990 = CARTESIAN_POINT('',(9.98003990056E-004,8.94994928042));
#991 = CARTESIAN_POINT('',(9.980039900095E-004,8.296178755736));
#992 = CARTESIAN_POINT('',(9.980039899101E-004,7.628713188564));
#993 = CARTESIAN_POINT('',(9.980039900137E-004,6.946419463728));
#994 = CARTESIAN_POINT('',(9.980039900403E-004,6.248219107721));
#995 = CARTESIAN_POINT('',(9.980039900015E-004,5.533123529128));
#996 = CARTESIAN_POINT('',(9.980039899607E-004,4.800267344587));
#997 = CARTESIAN_POINT('',(9.980039899929E-004,4.048935579056));
#998 = CARTESIAN_POINT('',(9.980039899063E-004,3.278586311278));
#999 = CARTESIAN_POINT('',(9.980039900514E-004,2.488870543065));
#1000 = CARTESIAN_POINT('',(9.980039899004E-004,1.679678044096));
#1001 = CARTESIAN_POINT('',(9.980039900201E-004,0.851022751652));
#1002 = CARTESIAN_POINT('',(9.980039900301E-004,0.285786197076));
#1003 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#1004 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1005 = ADVANCED_FACE('',(#1006),#830,.T.);
#1006 = FACE_BOUND('',#1007,.T.);
#1007 = EDGE_LOOP('',(#1008,#1009,#1031,#1061));
#1008 = ORIENTED_EDGE('',*,*,#765,.T.);
#1009 = ORIENTED_EDGE('',*,*,#1010,.T.);
#1010 = EDGE_CURVE('',#768,#1011,#1013,.T.);
#1011 = VERTEX_POINT('',#1012);
#1012 = CARTESIAN_POINT('',(-5.,0.E+000,0.E+000));
#1013 = SURFACE_CURVE('',#1014,(#1017,#1024),.PCURVE_S1.);
#1014 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1015,#1016),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,200.000998004),.PIECEWISE_BEZIER_KNOTS.);
#1015 = CARTESIAN_POINT('',(-5.,-5.55111512307E-016,200.));
#1016 = CARTESIAN_POINT('',(-5.,-5.55111512307E-016,0.E+000));
#1017 = PCURVE('',#830,#1018);
#1018 = DEFINITIONAL_REPRESENTATION('',(#1019),#1023);
#1019 = LINE('',#1020,#1021);
#1020 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1021 = VECTOR('',#1022,1.);
#1022 = DIRECTION('',(1.,0.E+000));
#1023 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1024 = PCURVE('',#946,#1025);
#1025 = DEFINITIONAL_REPRESENTATION('',(#1026),#1030);
#1026 = LINE('',#1027,#1028);
#1027 = CARTESIAN_POINT('',(0.E+000,30.));
#1028 = VECTOR('',#1029,1.);
#1029 = DIRECTION('',(1.,0.E+000));
#1030 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1031 = ORIENTED_EDGE('',*,*,#1032,.T.);
#1032 = EDGE_CURVE('',#1011,#1033,#1035,.T.);
#1033 = VERTEX_POINT('',#1034);
#1034 = CARTESIAN_POINT('',(5.,0.E+000,0.E+000));
#1035 = SURFACE_CURVE('',#1036,(#1041,#1048),.PCURVE_S1.);
#1036 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1037,#1038,#1039,#1040),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1037 = CARTESIAN_POINT('',(-5.,0.E+000,0.E+000));
#1038 = CARTESIAN_POINT('',(-5.,-10.,0.E+000));
#1039 = CARTESIAN_POINT('',(5.,-10.,0.E+000));
#1040 = CARTESIAN_POINT('',(5.,0.E+000,0.E+000));
#1041 = PCURVE('',#830,#1042);
#1042 = DEFINITIONAL_REPRESENTATION('',(#1043),#1047);
#1043 = LINE('',#1044,#1045);
#1044 = CARTESIAN_POINT('',(200.000998004,0.E+000));
#1045 = VECTOR('',#1046,1.);
#1046 = DIRECTION('',(0.E+000,1.));
#1047 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1048 = PCURVE('',#1049,#1054);
#1049 = PLANE('',#1050);
#1050 = AXIS2_PLACEMENT_3D('',#1051,#1052,#1053);
#1051 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#1052 = DIRECTION('',(0.E+000,0.E+000,-1.));
#1053 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1054 = DEFINITIONAL_REPRESENTATION('',(#1055),#1060);
#1055 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1056,#1057,#1058,#1059),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1056 = CARTESIAN_POINT('',(5.,0.E+000));
#1057 = CARTESIAN_POINT('',(5.,-10.));
#1058 = CARTESIAN_POINT('',(-5.,-10.));
#1059 = CARTESIAN_POINT('',(-5.,0.E+000));
#1060 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1061 = ORIENTED_EDGE('',*,*,#1062,.F.);
#1062 = EDGE_CURVE('',#766,#1033,#1063,.T.);
#1063 = SURFACE_CURVE('',#1064,(#1067,#1074),.PCURVE_S1.);
#1064 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1065,#1066),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,200.000998004),.PIECEWISE_BEZIER_KNOTS.);
#1065 = CARTESIAN_POINT('',(5.,-5.55111512307E-016,200.));
#1066 = CARTESIAN_POINT('',(5.,-5.55111512307E-016,0.E+000));
#1067 = PCURVE('',#830,#1068);
#1068 = DEFINITIONAL_REPRESENTATION('',(#1069),#1073);
#1069 = LINE('',#1070,#1071);
#1070 = CARTESIAN_POINT('',(0.E+000,30.));
#1071 = VECTOR('',#1072,1.);
#1072 = DIRECTION('',(1.,0.E+000));
#1073 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1074 = PCURVE('',#946,#1075);
#1075 = DEFINITIONAL_REPRESENTATION('',(#1076),#1080);
#1076 = LINE('',#1077,#1078);
#1077 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1078 = VECTOR('',#1079,1.);
#1079 = DIRECTION('',(1.,0.E+000));
#1080 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1081 = ADVANCED_FACE('',(#1082),#946,.T.);
#1082 = FACE_BOUND('',#1083,.T.);
#1083 = EDGE_LOOP('',(#1084,#1085,#1086,#1109));
#1084 = ORIENTED_EDGE('',*,*,#890,.T.);
#1085 = ORIENTED_EDGE('',*,*,#1062,.T.);
#1086 = ORIENTED_EDGE('',*,*,#1087,.T.);
#1087 = EDGE_CURVE('',#1033,#1011,#1088,.T.);
#1088 = SURFACE_CURVE('',#1089,(#1094,#1101),.PCURVE_S1.);
#1089 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1090,#1091,#1092,#1093),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1090 = CARTESIAN_POINT('',(5.,0.E+000,0.E+000));
#1091 = CARTESIAN_POINT('',(5.,10.,0.E+000));
#1092 = CARTESIAN_POINT('',(-5.,10.,0.E+000));
#1093 = CARTESIAN_POINT('',(-5.,0.E+000,0.E+000));
#1094 = PCURVE('',#946,#1095);
#1095 = DEFINITIONAL_REPRESENTATION('',(#1096),#1100);
#1096 = LINE('',#1097,#1098);
#1097 = CARTESIAN_POINT('',(200.000998004,0.E+000));
#1098 = VECTOR('',#1099,1.);
#1099 = DIRECTION('',(0.E+000,1.));
#1100 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1101 = PCURVE('',#1049,#1102);
#1102 = DEFINITIONAL_REPRESENTATION('',(#1103),#1108);
#1103 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1104,#1105,#1106,#1107),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1104 = CARTESIAN_POINT('',(-5.,0.E+000));
#1105 = CARTESIAN_POINT('',(-5.,10.));
#1106 = CARTESIAN_POINT('',(5.,10.));
#1107 = CARTESIAN_POINT('',(5.,0.E+000));
#1108 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1109 = ORIENTED_EDGE('',*,*,#1010,.F.);
#1110 = ADVANCED_FACE('',(#1111),#1049,.T.);
#1111 = FACE_BOUND('',#1112,.T.);
#1112 = EDGE_LOOP('',(#1113,#1114));
#1113 = ORIENTED_EDGE('',*,*,#1032,.F.);
#1114 = ORIENTED_EDGE('',*,*,#1087,.F.);
#1115 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1119)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1116,#1117,#1118)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1116 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1117 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1118 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1119 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-005),#1116,
  'distance_accuracy_value','confusion accuracy');
#1120 = SHAPE_DEFINITION_REPRESENTATION(#1121,#758);
#1121 = PRODUCT_DEFINITION_SHAPE('','',#1122);
#1122 = PRODUCT_DEFINITION('design','',#1123,#1126);
#1123 = PRODUCT_DEFINITION_FORMATION('','',#1124);
#1124 = PRODUCT('rod','rod','',(#1125));
#1125 = PRODUCT_CONTEXT('',#2,'mechanical');
#1126 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1127 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1128,#1130);
#1128 = ( REPRESENTATION_RELATIONSHIP('','',#758,#44) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1129) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1129 = ITEM_DEFINED_TRANSFORMATION('','',#11,#53);
#1130 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1131);
#1131 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','rod_1','',#39,#1122,$);
#1132 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1124));
#1133 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1134,#1136);
#1134 = ( REPRESENTATION_RELATIONSHIP('','',#44,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1135) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1135 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#1136 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1137);
#1137 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('4','rod-assembly_1','',#5,#39,$
  );
#1138 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#41));
#1139 = SHAPE_DEFINITION_REPRESENTATION(#1140,#1146);
#1140 = PRODUCT_DEFINITION_SHAPE('','',#1141);
#1141 = PRODUCT_DEFINITION('design','',#1142,#1145);
#1142 = PRODUCT_DEFINITION_FORMATION('','',#1143);
#1143 = PRODUCT('l-bracket-assembly','l-bracket-assembly','',(#1144));
#1144 = PRODUCT_CONTEXT('',#2,'mechanical');
#1145 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1146 = SHAPE_REPRESENTATION('',(#11,#1147,#1151,#1155,#1159),#1163);
#1147 = AXIS2_PLACEMENT_3D('',#1148,#1149,#1150);
#1148 = CARTESIAN_POINT('',(27.5,-40.,0.E+000));
#1149 = DIRECTION('',(0.E+000,0.E+000,1.));
#1150 = DIRECTION('',(1.,0.E+000,0.E+000));
#1151 = AXIS2_PLACEMENT_3D('',#1152,#1153,#1154);
#1152 = CARTESIAN_POINT('',(50.,-52.99038106,0.E+000));
#1153 = DIRECTION('',(0.E+000,0.E+000,1.));
#1154 = DIRECTION('',(1.,0.E+000,0.E+000));
#1155 = AXIS2_PLACEMENT_3D('',#1156,#1157,#1158);
#1156 = CARTESIAN_POINT('',(50.,-27.00961894,0.E+000));
#1157 = DIRECTION('',(0.E+000,0.E+000,1.));
#1158 = DIRECTION('',(1.,0.E+000,0.E+000));
#1159 = AXIS2_PLACEMENT_3D('',#1160,#1161,#1162);
#1160 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#1161 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1162 = DIRECTION('',(1.,0.E+000,0.E+000));
#1163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1167)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1164,#1165,#1166)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1164 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1165 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1166 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1167 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-006),#1164,
  'distance_accuracy_value','confusion accuracy');
#1168 = SHAPE_DEFINITION_REPRESENTATION(#1169,#1175);
#1169 = PRODUCT_DEFINITION_SHAPE('','',#1170);
#1170 = PRODUCT_DEFINITION('design','',#1171,#1174);
#1171 = PRODUCT_DEFINITION_FORMATION('','',#1172);
#1172 = PRODUCT('nut-bolt-assembly','nut-bolt-assembly','',(#1173));
#1173 = PRODUCT_CONTEXT('',#2,'mechanical');
#1174 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1175 = SHAPE_REPRESENTATION('',(#11,#1176,#1180),#1184);
#1176 = AXIS2_PLACEMENT_3D('',#1177,#1178,#1179);
#1177 = CARTESIAN_POINT('',(-7.5,-10.,13.));
#1178 = DIRECTION('',(0.E+000,0.E+000,-1.));
#1179 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1180 = AXIS2_PLACEMENT_3D('',#1181,#1182,#1183);
#1181 = CARTESIAN_POINT('',(2.5,-17.5,-20.));
#1182 = DIRECTION('',(0.E+000,0.E+000,-1.));
#1183 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1184 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1188)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1185,#1186,#1187)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1185 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1186 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1187 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1188 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-006),#1185,
  'distance_accuracy_value','confusion accuracy');
#1189 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1190),#1894);
#1190 = MANIFOLD_SOLID_BREP('',#1191);
#1191 = CLOSED_SHELL('',(#1192,#1674,#1750,#1779,#1855,#1884,#1889));
#1192 = ADVANCED_FACE('',(#1193,#1436),#1228,.T.);
#1193 = FACE_BOUND('',#1194,.T.);
#1194 = EDGE_LOOP('',(#1195,#1320));
#1195 = ORIENTED_EDGE('',*,*,#1196,.F.);
#1196 = EDGE_CURVE('',#1197,#1199,#1201,.T.);
#1197 = VERTEX_POINT('',#1198);
#1198 = CARTESIAN_POINT('',(7.5,0.E+000,3.));
#1199 = VERTEX_POINT('',#1200);
#1200 = CARTESIAN_POINT('',(-7.5,0.E+000,3.));
#1201 = SURFACE_CURVE('',#1202,(#1227,#1260),.PCURVE_S1.);
#1202 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1203,#1204,#1205,#1206,#1207,
    #1208,#1209,#1210,#1211,#1212,#1213,#1214,#1215,#1216,#1217,#1218,
    #1219,#1220,#1221,#1222,#1223,#1224,#1225,#1226),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,5.20225778542,9.84158873828,
    14.2673349509,18.6433186512,23.0548848731,27.6530164185,
    33.5425690087),.UNSPECIFIED.);
#1203 = CARTESIAN_POINT('',(7.5,6.66133814775E-016,3.));
#1204 = CARTESIAN_POINT('',(7.5,-0.585054612929,3.));
#1205 = CARTESIAN_POINT('',(7.44295106424,-1.20521801478,3.));
#1206 = CARTESIAN_POINT('',(7.31515940691,-1.85033890984,3.));
#1207 = CARTESIAN_POINT('',(6.9174836202,-3.08527233291,3.));
#1208 = CARTESIAN_POINT('',(6.21610886075,-4.27235963842,3.));
#1209 = CARTESIAN_POINT('',(5.81621499215,-4.80660561995,3.));
#1210 = CARTESIAN_POINT('',(4.90603051399,-5.77088806315,3.));
#1211 = CARTESIAN_POINT('',(3.775988505,-6.53134212728,3.));
#1212 = CARTESIAN_POINT('',(3.1790299248,-6.8428729705,3.));
#1213 = CARTESIAN_POINT('',(1.92404155108,-7.32665470362,3.));
#1214 = CARTESIAN_POINT('',(0.582116172098,-7.52278240149,3.));
#1215 = CARTESIAN_POINT('',(-9.46313364034E-002,-7.54474978799,3.));
#1216 = CARTESIAN_POINT('',(-1.44588275644,-7.43589277948,3.));
#1217 = CARTESIAN_POINT('',(-2.73149765405,-7.03353365966,3.));
#1218 = CARTESIAN_POINT('',(-3.34804882139,-6.76091512264,3.));
#1219 = CARTESIAN_POINT('',(-4.52434338626,-6.07498368569,3.));
#1220 = CARTESIAN_POINT('',(-5.49752166125,-5.16815745669,3.));
#1221 = CARTESIAN_POINT('',(-5.93188641726,-4.6595782538,3.));
#1222 = CARTESIAN_POINT('',(-6.76982690894,-3.42768019481,3.));
#1223 = CARTESIAN_POINT('',(-7.26056394836,-2.1079334227,3.));
#1224 = CARTESIAN_POINT('',(-7.42688130669,-1.36969623529,3.));
#1225 = CARTESIAN_POINT('',(-7.5,-0.662348936385,3.));
#1226 = CARTESIAN_POINT('',(-7.5,-6.66133814775E-016,3.));
#1227 = PCURVE('',#1228,#1233);
#1228 = PLANE('',#1229);
#1229 = AXIS2_PLACEMENT_3D('',#1230,#1231,#1232);
#1230 = CARTESIAN_POINT('',(0.E+000,0.E+000,3.));
#1231 = DIRECTION('',(0.E+000,0.E+000,1.));
#1232 = DIRECTION('',(1.,0.E+000,0.E+000));
#1233 = DEFINITIONAL_REPRESENTATION('',(#1234),#1259);
#1234 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1235,#1236,#1237,#1238,#1239,
    #1240,#1241,#1242,#1243,#1244,#1245,#1246,#1247,#1248,#1249,#1250,
    #1251,#1252,#1253,#1254,#1255,#1256,#1257,#1258),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,5.20225778542,9.84158873828,
    14.2673349509,18.6433186512,23.0548848731,27.6530164185,
    33.5425690087),.UNSPECIFIED.);
#1235 = CARTESIAN_POINT('',(7.5,6.66133814775E-016));
#1236 = CARTESIAN_POINT('',(7.5,-0.585054612929));
#1237 = CARTESIAN_POINT('',(7.44295106424,-1.20521801478));
#1238 = CARTESIAN_POINT('',(7.31515940691,-1.85033890984));
#1239 = CARTESIAN_POINT('',(6.9174836202,-3.08527233291));
#1240 = CARTESIAN_POINT('',(6.21610886075,-4.27235963842));
#1241 = CARTESIAN_POINT('',(5.81621499215,-4.80660561995));
#1242 = CARTESIAN_POINT('',(4.90603051399,-5.77088806315));
#1243 = CARTESIAN_POINT('',(3.775988505,-6.53134212728));
#1244 = CARTESIAN_POINT('',(3.1790299248,-6.8428729705));
#1245 = CARTESIAN_POINT('',(1.92404155108,-7.32665470362));
#1246 = CARTESIAN_POINT('',(0.582116172098,-7.52278240149));
#1247 = CARTESIAN_POINT('',(-9.46313364034E-002,-7.54474978799));
#1248 = CARTESIAN_POINT('',(-1.44588275644,-7.43589277948));
#1249 = CARTESIAN_POINT('',(-2.73149765405,-7.03353365966));
#1250 = CARTESIAN_POINT('',(-3.34804882139,-6.76091512264));
#1251 = CARTESIAN_POINT('',(-4.52434338626,-6.07498368569));
#1252 = CARTESIAN_POINT('',(-5.49752166125,-5.16815745669));
#1253 = CARTESIAN_POINT('',(-5.93188641726,-4.6595782538));
#1254 = CARTESIAN_POINT('',(-6.76982690894,-3.42768019481));
#1255 = CARTESIAN_POINT('',(-7.26056394836,-2.1079334227));
#1256 = CARTESIAN_POINT('',(-7.42688130669,-1.36969623529));
#1257 = CARTESIAN_POINT('',(-7.5,-0.662348936385));
#1258 = CARTESIAN_POINT('',(-7.5,-6.66133814775E-016));
#1259 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1260 = PCURVE('',#1261,#1270);
#1261 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#1262,#1263,#1264,#1265)
    ,(#1266,#1267,#1268,#1269
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,3.00099800399),(0.E+000,45.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#1262 = CARTESIAN_POINT('',(-7.5,0.E+000,3.));
#1263 = CARTESIAN_POINT('',(-7.5,-15.,3.));
#1264 = CARTESIAN_POINT('',(7.5,-15.,3.));
#1265 = CARTESIAN_POINT('',(7.5,0.E+000,3.));
#1266 = CARTESIAN_POINT('',(-7.5,0.E+000,0.E+000));
#1267 = CARTESIAN_POINT('',(-7.5,-15.,0.E+000));
#1268 = CARTESIAN_POINT('',(7.5,-15.,0.E+000));
#1269 = CARTESIAN_POINT('',(7.5,0.E+000,0.E+000));
#1270 = DEFINITIONAL_REPRESENTATION('',(#1271),#1319);
#1271 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1272,#1273,#1274,#1275,#1276,
    #1277,#1278,#1279,#1280,#1281,#1282,#1283,#1284,#1285,#1286,#1287,
    #1288,#1289,#1290,#1291,#1292,#1293,#1294,#1295,#1296,#1297,#1298,
    #1299,#1300,#1301,#1302,#1303,#1304,#1305,#1306,#1307,#1308,#1309,
    #1310,#1311,#1312,#1313,#1314,#1315,#1316,#1317,#1318),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.762331113834,
    1.524662227668,2.286993341502,3.049324455336,3.81165556917,
    4.573986683005,5.336317796839,6.098648910673,6.860980024507,
    7.623311138341,8.385642252175,9.147973366009,9.910304479843,
    10.672635593677,11.434966707511,12.197297821345,12.95962893518,
    13.721960049014,14.484291162848,15.246622276682,16.008953390516,
    16.77128450435,17.533615618184,18.295946732018,19.058277845852,
    19.820608959686,20.58294007352,21.345271187355,22.107602301189,
    22.869933415023,23.632264528857,24.394595642691,25.156926756525,
    25.919257870359,26.681588984193,27.443920098027,28.206251211861,
    28.968582325695,29.73091343953,30.493244553364,31.255575667198,
    32.017906781032,32.780237894866,33.5425690087),
  .QUASI_UNIFORM_KNOTS.);
#1272 = CARTESIAN_POINT('',(9.9800399E-004,45.));
#1273 = CARTESIAN_POINT('',(9.980039900001E-004,44.571302812759));
#1274 = CARTESIAN_POINT('',(9.980039900001E-004,43.723451988301));
#1275 = CARTESIAN_POINT('',(9.980039899997E-004,42.480603180286));
#1276 = CARTESIAN_POINT('',(9.980039899987E-004,41.267127064423));
#1277 = CARTESIAN_POINT('',(9.980039900005E-004,40.082949207123));
#1278 = CARTESIAN_POINT('',(9.980039899997E-004,38.92770430726));
#1279 = CARTESIAN_POINT('',(9.980039899987E-004,37.800756852125));
#1280 = CARTESIAN_POINT('',(9.980039900008E-004,36.701299976325));
#1281 = CARTESIAN_POINT('',(9.980039899991E-004,35.628440627625));
#1282 = CARTESIAN_POINT('',(9.980039900013E-004,34.580978071595));
#1283 = CARTESIAN_POINT('',(9.980039899994E-004,33.557472237094));
#1284 = CARTESIAN_POINT('',(9.980039899998E-004,32.556310364454));
#1285 = CARTESIAN_POINT('',(9.980039900001E-004,31.575759692059));
#1286 = CARTESIAN_POINT('',(9.980039900011E-004,30.614017309608));
#1287 = CARTESIAN_POINT('',(9.980039899995E-004,29.6692735353));
#1288 = CARTESIAN_POINT('',(9.980039899997E-004,28.739730155524));
#1289 = CARTESIAN_POINT('',(9.980039900007E-004,27.82355261073));
#1290 = CARTESIAN_POINT('',(9.980039899995E-004,26.918879220695));
#1291 = CARTESIAN_POINT('',(9.980039900007E-004,26.023811406403));
#1292 = CARTESIAN_POINT('',(9.980039899997E-004,25.136388793607));
#1293 = CARTESIAN_POINT('',(9.980039900002E-004,24.254616243117));
#1294 = CARTESIAN_POINT('',(9.980039899993E-004,23.376593359876));
#1295 = CARTESIAN_POINT('',(9.980039899997E-004,22.500427783925));
#1296 = CARTESIAN_POINT('',(9.980039899991E-004,21.624247365846));
#1297 = CARTESIAN_POINT('',(9.980039900012E-004,20.74618278857));
#1298 = CARTESIAN_POINT('',(9.980039899988E-004,19.864397566237));
#1299 = CARTESIAN_POINT('',(9.980039900012E-004,18.976941798027));
#1300 = CARTESIAN_POINT('',(9.980039899995E-004,18.081820706376));
#1301 = CARTESIAN_POINT('',(9.980039900011E-004,17.17711381209));
#1302 = CARTESIAN_POINT('',(9.980039899992E-004,16.260927030417));
#1303 = CARTESIAN_POINT('',(9.980039900002E-004,15.331390617179));
#1304 = CARTESIAN_POINT('',(9.980039900008E-004,14.386646151192));
#1305 = CARTESIAN_POINT('',(9.9800399E-004,13.424926609852));
#1306 = CARTESIAN_POINT('',(9.980039900002E-004,12.444427651184));
#1307 = CARTESIAN_POINT('',(9.980039900002E-004,11.443331536935));
#1308 = CARTESIAN_POINT('',(9.980039900002E-004,10.419877046088));
#1309 = CARTESIAN_POINT('',(9.980039900002E-004,9.372427008604));
#1310 = CARTESIAN_POINT('',(9.980039900004E-004,8.299579036962));
#1311 = CARTESIAN_POINT('',(9.980039899994E-004,7.200183660574));
#1312 = CARTESIAN_POINT('',(9.980039900006E-004,6.07319337542));
#1313 = CARTESIAN_POINT('',(9.980039899995E-004,4.917761146069));
#1314 = CARTESIAN_POINT('',(9.980039900001E-004,3.733303759495));
#1315 = CARTESIAN_POINT('',(9.980039899989E-004,2.519557037946));
#1316 = CARTESIAN_POINT('',(9.980039900006E-004,1.276559770167));
#1317 = CARTESIAN_POINT('',(9.980039900006E-004,0.428685598944));
#1318 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#1319 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1320 = ORIENTED_EDGE('',*,*,#1321,.F.);
#1321 = EDGE_CURVE('',#1199,#1197,#1322,.T.);
#1322 = SURFACE_CURVE('',#1323,(#1348,#1376),.PCURVE_S1.);
#1323 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1324,#1325,#1326,#1327,#1328,
    #1329,#1330,#1331,#1332,#1333,#1334,#1335,#1336,#1337,#1338,#1339,
    #1340,#1341,#1342,#1343,#1344,#1345,#1346,#1347),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,5.20225778542,9.84158873828,
    14.2673349509,18.6433186512,23.0548848731,27.6530164185,
    33.5425690087),.UNSPECIFIED.);
#1324 = CARTESIAN_POINT('',(-7.5,-6.66133814775E-016,3.));
#1325 = CARTESIAN_POINT('',(-7.5,0.585054612929,3.));
#1326 = CARTESIAN_POINT('',(-7.44295106424,1.20521801478,3.));
#1327 = CARTESIAN_POINT('',(-7.31515940691,1.85033890984,3.));
#1328 = CARTESIAN_POINT('',(-6.9174836202,3.08527233291,3.));
#1329 = CARTESIAN_POINT('',(-6.21610886075,4.27235963842,3.));
#1330 = CARTESIAN_POINT('',(-5.81621499215,4.80660561995,3.));
#1331 = CARTESIAN_POINT('',(-4.90603051399,5.77088806315,3.));
#1332 = CARTESIAN_POINT('',(-3.775988505,6.53134212728,3.));
#1333 = CARTESIAN_POINT('',(-3.1790299248,6.8428729705,3.));
#1334 = CARTESIAN_POINT('',(-1.92404155108,7.32665470362,3.));
#1335 = CARTESIAN_POINT('',(-0.582116172098,7.52278240149,3.));
#1336 = CARTESIAN_POINT('',(9.46313364034E-002,7.54474978799,3.));
#1337 = CARTESIAN_POINT('',(1.44588275644,7.43589277948,3.));
#1338 = CARTESIAN_POINT('',(2.73149765405,7.03353365966,3.));
#1339 = CARTESIAN_POINT('',(3.34804882139,6.76091512264,3.));
#1340 = CARTESIAN_POINT('',(4.52434338626,6.07498368569,3.));
#1341 = CARTESIAN_POINT('',(5.49752166125,5.16815745669,3.));
#1342 = CARTESIAN_POINT('',(5.93188641726,4.6595782538,3.));
#1343 = CARTESIAN_POINT('',(6.76982690894,3.42768019481,3.));
#1344 = CARTESIAN_POINT('',(7.26056394836,2.1079334227,3.));
#1345 = CARTESIAN_POINT('',(7.42688130669,1.36969623529,3.));
#1346 = CARTESIAN_POINT('',(7.5,0.662348936385,3.));
#1347 = CARTESIAN_POINT('',(7.5,6.66133814775E-016,3.));
#1348 = PCURVE('',#1228,#1349);
#1349 = DEFINITIONAL_REPRESENTATION('',(#1350),#1375);
#1350 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1351,#1352,#1353,#1354,#1355,
    #1356,#1357,#1358,#1359,#1360,#1361,#1362,#1363,#1364,#1365,#1366,
    #1367,#1368,#1369,#1370,#1371,#1372,#1373,#1374),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,5.20225778542,9.84158873828,
    14.2673349509,18.6433186512,23.0548848731,27.6530164185,
    33.5425690087),.UNSPECIFIED.);
#1351 = CARTESIAN_POINT('',(-7.5,-6.66133814775E-016));
#1352 = CARTESIAN_POINT('',(-7.5,0.585054612929));
#1353 = CARTESIAN_POINT('',(-7.44295106424,1.20521801478));
#1354 = CARTESIAN_POINT('',(-7.31515940691,1.85033890984));
#1355 = CARTESIAN_POINT('',(-6.9174836202,3.08527233291));
#1356 = CARTESIAN_POINT('',(-6.21610886075,4.27235963842));
#1357 = CARTESIAN_POINT('',(-5.81621499215,4.80660561995));
#1358 = CARTESIAN_POINT('',(-4.90603051399,5.77088806315));
#1359 = CARTESIAN_POINT('',(-3.775988505,6.53134212728));
#1360 = CARTESIAN_POINT('',(-3.1790299248,6.8428729705));
#1361 = CARTESIAN_POINT('',(-1.92404155108,7.32665470362));
#1362 = CARTESIAN_POINT('',(-0.582116172098,7.52278240149));
#1363 = CARTESIAN_POINT('',(9.46313364034E-002,7.54474978799));
#1364 = CARTESIAN_POINT('',(1.44588275644,7.43589277948));
#1365 = CARTESIAN_POINT('',(2.73149765405,7.03353365966));
#1366 = CARTESIAN_POINT('',(3.34804882139,6.76091512264));
#1367 = CARTESIAN_POINT('',(4.52434338626,6.07498368569));
#1368 = CARTESIAN_POINT('',(5.49752166125,5.16815745669));
#1369 = CARTESIAN_POINT('',(5.93188641726,4.6595782538));
#1370 = CARTESIAN_POINT('',(6.76982690894,3.42768019481));
#1371 = CARTESIAN_POINT('',(7.26056394836,2.1079334227));
#1372 = CARTESIAN_POINT('',(7.42688130669,1.36969623529));
#1373 = CARTESIAN_POINT('',(7.5,0.662348936385));
#1374 = CARTESIAN_POINT('',(7.5,6.66133814775E-016));
#1375 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1376 = PCURVE('',#1377,#1386);
#1377 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#1378,#1379,#1380,#1381)
    ,(#1382,#1383,#1384,#1385
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,3.00099800399),(0.E+000,45.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#1378 = CARTESIAN_POINT('',(7.5,0.E+000,3.));
#1379 = CARTESIAN_POINT('',(7.5,15.,3.));
#1380 = CARTESIAN_POINT('',(-7.5,15.,3.));
#1381 = CARTESIAN_POINT('',(-7.5,0.E+000,3.));
#1382 = CARTESIAN_POINT('',(7.5,0.E+000,0.E+000));
#1383 = CARTESIAN_POINT('',(7.5,15.,0.E+000));
#1384 = CARTESIAN_POINT('',(-7.5,15.,0.E+000));
#1385 = CARTESIAN_POINT('',(-7.5,0.E+000,0.E+000));
#1386 = DEFINITIONAL_REPRESENTATION('',(#1387),#1435);
#1387 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1388,#1389,#1390,#1391,#1392,
    #1393,#1394,#1395,#1396,#1397,#1398,#1399,#1400,#1401,#1402,#1403,
    #1404,#1405,#1406,#1407,#1408,#1409,#1410,#1411,#1412,#1413,#1414,
    #1415,#1416,#1417,#1418,#1419,#1420,#1421,#1422,#1423,#1424,#1425,
    #1426,#1427,#1428,#1429,#1430,#1431,#1432,#1433,#1434),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.762331113834,
    1.524662227668,2.286993341502,3.049324455336,3.81165556917,
    4.573986683005,5.336317796839,6.098648910673,6.860980024507,
    7.623311138341,8.385642252175,9.147973366009,9.910304479843,
    10.672635593677,11.434966707511,12.197297821345,12.95962893518,
    13.721960049014,14.484291162848,15.246622276682,16.008953390516,
    16.77128450435,17.533615618184,18.295946732018,19.058277845852,
    19.820608959686,20.58294007352,21.345271187355,22.107602301189,
    22.869933415023,23.632264528857,24.394595642691,25.156926756525,
    25.919257870359,26.681588984193,27.443920098027,28.206251211861,
    28.968582325695,29.73091343953,30.493244553364,31.255575667198,
    32.017906781032,32.780237894866,33.5425690087),
  .QUASI_UNIFORM_KNOTS.);
#1388 = CARTESIAN_POINT('',(9.9800399E-004,45.));
#1389 = CARTESIAN_POINT('',(9.980039900001E-004,44.571302812759));
#1390 = CARTESIAN_POINT('',(9.980039900001E-004,43.723451988301));
#1391 = CARTESIAN_POINT('',(9.980039899997E-004,42.480603180286));
#1392 = CARTESIAN_POINT('',(9.980039899987E-004,41.267127064423));
#1393 = CARTESIAN_POINT('',(9.980039900005E-004,40.082949207123));
#1394 = CARTESIAN_POINT('',(9.980039899997E-004,38.92770430726));
#1395 = CARTESIAN_POINT('',(9.980039899987E-004,37.800756852125));
#1396 = CARTESIAN_POINT('',(9.980039900008E-004,36.701299976325));
#1397 = CARTESIAN_POINT('',(9.980039899991E-004,35.628440627625));
#1398 = CARTESIAN_POINT('',(9.980039900013E-004,34.580978071595));
#1399 = CARTESIAN_POINT('',(9.980039899994E-004,33.557472237094));
#1400 = CARTESIAN_POINT('',(9.980039899998E-004,32.556310364454));
#1401 = CARTESIAN_POINT('',(9.980039900001E-004,31.575759692059));
#1402 = CARTESIAN_POINT('',(9.980039900011E-004,30.614017309608));
#1403 = CARTESIAN_POINT('',(9.980039899995E-004,29.6692735353));
#1404 = CARTESIAN_POINT('',(9.980039899997E-004,28.739730155524));
#1405 = CARTESIAN_POINT('',(9.980039900007E-004,27.82355261073));
#1406 = CARTESIAN_POINT('',(9.980039899995E-004,26.918879220695));
#1407 = CARTESIAN_POINT('',(9.980039900007E-004,26.023811406403));
#1408 = CARTESIAN_POINT('',(9.980039899997E-004,25.136388793607));
#1409 = CARTESIAN_POINT('',(9.980039900002E-004,24.254616243117));
#1410 = CARTESIAN_POINT('',(9.980039899993E-004,23.376593359876));
#1411 = CARTESIAN_POINT('',(9.980039899997E-004,22.500427783925));
#1412 = CARTESIAN_POINT('',(9.980039899991E-004,21.624247365846));
#1413 = CARTESIAN_POINT('',(9.980039900012E-004,20.74618278857));
#1414 = CARTESIAN_POINT('',(9.980039899988E-004,19.864397566237));
#1415 = CARTESIAN_POINT('',(9.980039900012E-004,18.976941798027));
#1416 = CARTESIAN_POINT('',(9.980039899995E-004,18.081820706376));
#1417 = CARTESIAN_POINT('',(9.980039900011E-004,17.17711381209));
#1418 = CARTESIAN_POINT('',(9.980039899992E-004,16.260927030417));
#1419 = CARTESIAN_POINT('',(9.980039900002E-004,15.331390617179));
#1420 = CARTESIAN_POINT('',(9.980039900008E-004,14.386646151192));
#1421 = CARTESIAN_POINT('',(9.9800399E-004,13.424926609852));
#1422 = CARTESIAN_POINT('',(9.980039900002E-004,12.444427651184));
#1423 = CARTESIAN_POINT('',(9.980039900002E-004,11.443331536935));
#1424 = CARTESIAN_POINT('',(9.980039900002E-004,10.419877046088));
#1425 = CARTESIAN_POINT('',(9.980039900002E-004,9.372427008604));
#1426 = CARTESIAN_POINT('',(9.980039900004E-004,8.299579036962));
#1427 = CARTESIAN_POINT('',(9.980039899994E-004,7.200183660574));
#1428 = CARTESIAN_POINT('',(9.980039900006E-004,6.07319337542));
#1429 = CARTESIAN_POINT('',(9.980039899995E-004,4.917761146069));
#1430 = CARTESIAN_POINT('',(9.980039900001E-004,3.733303759495));
#1431 = CARTESIAN_POINT('',(9.980039899989E-004,2.519557037946));
#1432 = CARTESIAN_POINT('',(9.980039900006E-004,1.276559770167));
#1433 = CARTESIAN_POINT('',(9.980039900006E-004,0.428685598944));
#1434 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#1435 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1436 = FACE_BOUND('',#1437,.T.);
#1437 = EDGE_LOOP('',(#1438,#1558));
#1438 = ORIENTED_EDGE('',*,*,#1439,.F.);
#1439 = EDGE_CURVE('',#1440,#1442,#1444,.T.);
#1440 = VERTEX_POINT('',#1441);
#1441 = CARTESIAN_POINT('',(-5.,2.22044604925E-016,3.));
#1442 = VERTEX_POINT('',#1443);
#1443 = CARTESIAN_POINT('',(5.,-2.22044604925E-016,3.));
#1444 = SURFACE_CURVE('',#1445,(#1470,#1498),.PCURVE_S1.);
#1445 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1446,#1447,#1448,#1449,#1450,
    #1451,#1452,#1453,#1454,#1455,#1456,#1457,#1458,#1459,#1460,#1461,
    #1462,#1463,#1464,#1465,#1466,#1467,#1468,#1469),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164387,7.85828164661,
    10.7238180516,13.5836589935,16.4911855015,20.38776087,22.3658107353)
  ,.UNSPECIFIED.);
#1446 = CARTESIAN_POINT('',(-5.,-2.22044604925E-016,3.));
#1447 = CARTESIAN_POINT('',(-5.,-0.467198252312,3.));
#1448 = CARTESIAN_POINT('',(-4.94543032016,-0.967985463874,3.));
#1449 = CARTESIAN_POINT('',(-4.82041774119,-1.49112303535,3.));
#1450 = CARTESIAN_POINT('',(-4.42731387443,-2.48006143438,3.));
#1451 = CARTESIAN_POINT('',(-3.74198536382,-3.38090473983,3.));
#1452 = CARTESIAN_POINT('',(-3.35476380665,-3.76862633308,3.));
#1453 = CARTESIAN_POINT('',(-2.56749137395,-4.36208802884,3.));
#1454 = CARTESIAN_POINT('',(-1.64518926245,-4.75184036526,3.));
#1455 = CARTESIAN_POINT('',(-1.22322144323,-4.87791933608,3.));
#1456 = CARTESIAN_POINT('',(-0.356287037014,-5.03548099138,3.));
#1457 = CARTESIAN_POINT('',(0.52640030158,-5.00140076198,3.));
#1458 = CARTESIAN_POINT('',(0.963050674765,-4.93574856594,3.));
#1459 = CARTESIAN_POINT('',(1.81864212033,-4.70884578804,3.));
#1460 = CARTESIAN_POINT('',(2.59575461931,-4.30713067084,3.));
#1461 = CARTESIAN_POINT('',(2.9603131848,-4.06421908239,3.));
#1462 = CARTESIAN_POINT('',(3.73554903634,-3.41630129394,3.));
#1463 = CARTESIAN_POINT('',(4.3095225984,-2.62465565461,3.));
#1464 = CARTESIAN_POINT('',(4.56375002186,-2.14244819995,3.));
#1465 = CARTESIAN_POINT('',(4.8362924348,-1.40481893471,3.));
#1466 = CARTESIAN_POINT('',(4.96121877006,-0.68885510118,3.));
#1467 = CARTESIAN_POINT('',(4.98763322877,-0.452431376999,3.));
#1468 = CARTESIAN_POINT('',(5.,-0.222409665749,3.));
#1469 = CARTESIAN_POINT('',(5.,4.4408920985E-016,3.));
#1470 = PCURVE('',#1228,#1471);
#1471 = DEFINITIONAL_REPRESENTATION('',(#1472),#1497);
#1472 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1473,#1474,#1475,#1476,#1477,
    #1478,#1479,#1480,#1481,#1482,#1483,#1484,#1485,#1486,#1487,#1488,
    #1489,#1490,#1491,#1492,#1493,#1494,#1495,#1496),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164387,7.85828164661,
    10.7238180516,13.5836589935,16.4911855015,20.38776087,22.3658107353)
  ,.UNSPECIFIED.);
#1473 = CARTESIAN_POINT('',(-5.,-2.22044604925E-016));
#1474 = CARTESIAN_POINT('',(-5.,-0.467198252312));
#1475 = CARTESIAN_POINT('',(-4.94543032016,-0.967985463874));
#1476 = CARTESIAN_POINT('',(-4.82041774119,-1.49112303535));
#1477 = CARTESIAN_POINT('',(-4.42731387443,-2.48006143438));
#1478 = CARTESIAN_POINT('',(-3.74198536382,-3.38090473983));
#1479 = CARTESIAN_POINT('',(-3.35476380665,-3.76862633308));
#1480 = CARTESIAN_POINT('',(-2.56749137395,-4.36208802884));
#1481 = CARTESIAN_POINT('',(-1.64518926245,-4.75184036526));
#1482 = CARTESIAN_POINT('',(-1.22322144323,-4.87791933608));
#1483 = CARTESIAN_POINT('',(-0.356287037014,-5.03548099138));
#1484 = CARTESIAN_POINT('',(0.52640030158,-5.00140076198));
#1485 = CARTESIAN_POINT('',(0.963050674765,-4.93574856594));
#1486 = CARTESIAN_POINT('',(1.81864212033,-4.70884578804));
#1487 = CARTESIAN_POINT('',(2.59575461931,-4.30713067084));
#1488 = CARTESIAN_POINT('',(2.9603131848,-4.06421908239));
#1489 = CARTESIAN_POINT('',(3.73554903634,-3.41630129394));
#1490 = CARTESIAN_POINT('',(4.3095225984,-2.62465565461));
#1491 = CARTESIAN_POINT('',(4.56375002186,-2.14244819995));
#1492 = CARTESIAN_POINT('',(4.8362924348,-1.40481893471));
#1493 = CARTESIAN_POINT('',(4.96121877006,-0.68885510118));
#1494 = CARTESIAN_POINT('',(4.98763322877,-0.452431376999));
#1495 = CARTESIAN_POINT('',(5.,-0.222409665749));
#1496 = CARTESIAN_POINT('',(5.,4.4408920985E-016));
#1497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1498 = PCURVE('',#1499,#1508);
#1499 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#1500,#1501,#1502,#1503)
    ,(#1504,#1505,#1506,#1507
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,34.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#1500 = CARTESIAN_POINT('',(-5.,0.E+000,37.));
#1501 = CARTESIAN_POINT('',(-5.,-10.,37.));
#1502 = CARTESIAN_POINT('',(5.,-10.,37.));
#1503 = CARTESIAN_POINT('',(5.,0.E+000,37.));
#1504 = CARTESIAN_POINT('',(-5.,0.E+000,3.));
#1505 = CARTESIAN_POINT('',(-5.,-10.,3.));
#1506 = CARTESIAN_POINT('',(5.,-10.,3.));
#1507 = CARTESIAN_POINT('',(5.,0.E+000,3.));
#1508 = DEFINITIONAL_REPRESENTATION('',(#1509),#1557);
#1509 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1510,#1511,#1512,#1513,#1514,
    #1515,#1516,#1517,#1518,#1519,#1520,#1521,#1522,#1523,#1524,#1525,
    #1526,#1527,#1528,#1529,#1530,#1531,#1532,#1533,#1534,#1535,#1536,
    #1537,#1538,#1539,#1540,#1541,#1542,#1543,#1544,#1545,#1546,#1547,
    #1548,#1549,#1550,#1551,#1552,#1553,#1554,#1555,#1556),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880348,
    1.016627760695,1.524941641043,2.033255521391,2.541569401739,
    3.049883282086,3.558197162434,4.066511042782,4.57482492313,
    5.083138803477,5.591452683825,6.099766564173,6.60808044452,
    7.116394324868,7.624708205216,8.133022085564,8.641335965911,
    9.149649846259,9.657963726607,10.166277606955,10.674591487302,
    11.18290536765,11.691219247998,12.199533128345,12.707847008693,
    13.216160889041,13.724474769389,14.232788649736,14.741102530084,
    15.249416410432,15.75773029078,16.266044171127,16.774358051475,
    17.282671931823,17.79098581217,18.299299692518,18.807613572866,
    19.315927453214,19.824241333561,20.332555213909,20.840869094257,
    21.349182974605,21.857496854952,22.3658107353),
  .QUASI_UNIFORM_KNOTS.);
#1510 = CARTESIAN_POINT('',(34.000998004,0.E+000));
#1511 = CARTESIAN_POINT('',(34.000998004,0.285786134005));
#1512 = CARTESIAN_POINT('',(34.000998004,0.851023724374));
#1513 = CARTESIAN_POINT('',(34.000998004,1.679658950067));
#1514 = CARTESIAN_POINT('',(34.000998004,2.488775842984));
#1515 = CARTESIAN_POINT('',(34.000998004,3.278357391147));
#1516 = CARTESIAN_POINT('',(34.000998004,4.048590090635));
#1517 = CARTESIAN_POINT('',(34.000998004,4.799873551245));
#1518 = CARTESIAN_POINT('',(34.000998004,5.532780976198));
#1519 = CARTESIAN_POINT('',(34.000998004,6.248020911162));
#1520 = CARTESIAN_POINT('',(34.000998004,6.946360574942));
#1521 = CARTESIAN_POINT('',(34.000998004,7.628688635561));
#1522 = CARTESIAN_POINT('',(34.000998004,8.296073973845));
#1523 = CARTESIAN_POINT('',(34.000998004,8.949683945325));
#1524 = CARTESIAN_POINT('',(34.000998004,9.590744783224));
#1525 = CARTESIAN_POINT('',(34.000998004,10.220499189069));
#1526 = CARTESIAN_POINT('',(34.000998004,10.840182524178));
#1527 = CARTESIAN_POINT('',(34.000998004,11.450961995563));
#1528 = CARTESIAN_POINT('',(34.000998004,12.054057836488));
#1529 = CARTESIAN_POINT('',(34.000998004,12.650784955207));
#1530 = CARTESIAN_POINT('',(34.000998004,13.242437006931));
#1531 = CARTESIAN_POINT('',(34.000998004,13.830311319039));
#1532 = CARTESIAN_POINT('',(34.000998004,14.415700442447));
#1533 = CARTESIAN_POINT('',(34.000998004,14.999897615114));
#1534 = CARTESIAN_POINT('',(34.000998004,15.584089013839));
#1535 = CARTESIAN_POINT('',(34.000998004,16.169496123896));
#1536 = CARTESIAN_POINT('',(34.000998004,16.757374014315));
#1537 = CARTESIAN_POINT('',(34.000998004,17.349001920563));
#1538 = CARTESIAN_POINT('',(34.000998004,17.945677529625));
#1539 = CARTESIAN_POINT('',(34.000998004,18.548712223709));
#1540 = CARTESIAN_POINT('',(34.000998004,19.159406299853));
#1541 = CARTESIAN_POINT('',(34.000998004,19.779034544783));
#1542 = CARTESIAN_POINT('',(34.000998004,20.40884411557));
#1543 = CARTESIAN_POINT('',(34.000998004,21.05005071958));
#1544 = CARTESIAN_POINT('',(34.000998004,21.703821244264));
#1545 = CARTESIAN_POINT('',(34.000998004,22.371286811436));
#1546 = CARTESIAN_POINT('',(34.000998004,23.053580536272));
#1547 = CARTESIAN_POINT('',(34.000998004,23.751780892279));
#1548 = CARTESIAN_POINT('',(34.000998004,24.466876470872));
#1549 = CARTESIAN_POINT('',(34.000998004,25.199732655413));
#1550 = CARTESIAN_POINT('',(34.000998004,25.951064420944));
#1551 = CARTESIAN_POINT('',(34.000998004,26.721413688722));
#1552 = CARTESIAN_POINT('',(34.000998004,27.511129456935));
#1553 = CARTESIAN_POINT('',(34.000998004,28.320321955904));
#1554 = CARTESIAN_POINT('',(34.000998004,29.148977248348));
#1555 = CARTESIAN_POINT('',(34.000998004,29.714213802924));
#1556 = CARTESIAN_POINT('',(34.000998004,30.));
#1557 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1558 = ORIENTED_EDGE('',*,*,#1559,.F.);
#1559 = EDGE_CURVE('',#1442,#1440,#1560,.T.);
#1560 = SURFACE_CURVE('',#1561,(#1586,#1614),.PCURVE_S1.);
#1561 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1562,#1563,#1564,#1565,#1566,
    #1567,#1568,#1569,#1570,#1571,#1572,#1573,#1574,#1575,#1576,#1577,
    #1578,#1579,#1580,#1581,#1582,#1583,#1584,#1585),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164387,7.85828164661,
    10.7238180516,13.5836589935,16.4911855015,20.38776087,22.3658107353)
  ,.UNSPECIFIED.);
#1562 = CARTESIAN_POINT('',(5.,2.22044604925E-016,3.));
#1563 = CARTESIAN_POINT('',(5.,0.467198252312,3.));
#1564 = CARTESIAN_POINT('',(4.94543032016,0.967985463874,3.));
#1565 = CARTESIAN_POINT('',(4.82041774119,1.49112303535,3.));
#1566 = CARTESIAN_POINT('',(4.42731387443,2.48006143438,3.));
#1567 = CARTESIAN_POINT('',(3.74198536382,3.38090473983,3.));
#1568 = CARTESIAN_POINT('',(3.35476380665,3.76862633308,3.));
#1569 = CARTESIAN_POINT('',(2.56749137395,4.36208802884,3.));
#1570 = CARTESIAN_POINT('',(1.64518926245,4.75184036526,3.));
#1571 = CARTESIAN_POINT('',(1.22322144323,4.87791933608,3.));
#1572 = CARTESIAN_POINT('',(0.356287037014,5.03548099138,3.));
#1573 = CARTESIAN_POINT('',(-0.52640030158,5.00140076198,3.));
#1574 = CARTESIAN_POINT('',(-0.963050674765,4.93574856594,3.));
#1575 = CARTESIAN_POINT('',(-1.81864212033,4.70884578804,3.));
#1576 = CARTESIAN_POINT('',(-2.59575461931,4.30713067084,3.));
#1577 = CARTESIAN_POINT('',(-2.9603131848,4.06421908239,3.));
#1578 = CARTESIAN_POINT('',(-3.73554903634,3.41630129394,3.));
#1579 = CARTESIAN_POINT('',(-4.3095225984,2.62465565461,3.));
#1580 = CARTESIAN_POINT('',(-4.56375002186,2.14244819995,3.));
#1581 = CARTESIAN_POINT('',(-4.8362924348,1.40481893471,3.));
#1582 = CARTESIAN_POINT('',(-4.96121877006,0.68885510118,3.));
#1583 = CARTESIAN_POINT('',(-4.98763322877,0.452431376999,3.));
#1584 = CARTESIAN_POINT('',(-5.,0.222409665749,3.));
#1585 = CARTESIAN_POINT('',(-5.,-4.4408920985E-016,3.));
#1586 = PCURVE('',#1228,#1587);
#1587 = DEFINITIONAL_REPRESENTATION('',(#1588),#1613);
#1588 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#1589,#1590,#1591,#1592,#1593,
    #1594,#1595,#1596,#1597,#1598,#1599,#1600,#1601,#1602,#1603,#1604,
    #1605,#1606,#1607,#1608,#1609,#1610,#1611,#1612),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164387,7.85828164661,
    10.7238180516,13.5836589935,16.4911855015,20.38776087,22.3658107353)
  ,.UNSPECIFIED.);
#1589 = CARTESIAN_POINT('',(5.,2.22044604925E-016));
#1590 = CARTESIAN_POINT('',(5.,0.467198252312));
#1591 = CARTESIAN_POINT('',(4.94543032016,0.967985463874));
#1592 = CARTESIAN_POINT('',(4.82041774119,1.49112303535));
#1593 = CARTESIAN_POINT('',(4.42731387443,2.48006143438));
#1594 = CARTESIAN_POINT('',(3.74198536382,3.38090473983));
#1595 = CARTESIAN_POINT('',(3.35476380665,3.76862633308));
#1596 = CARTESIAN_POINT('',(2.56749137395,4.36208802884));
#1597 = CARTESIAN_POINT('',(1.64518926245,4.75184036526));
#1598 = CARTESIAN_POINT('',(1.22322144323,4.87791933608));
#1599 = CARTESIAN_POINT('',(0.356287037014,5.03548099138));
#1600 = CARTESIAN_POINT('',(-0.52640030158,5.00140076198));
#1601 = CARTESIAN_POINT('',(-0.963050674765,4.93574856594));
#1602 = CARTESIAN_POINT('',(-1.81864212033,4.70884578804));
#1603 = CARTESIAN_POINT('',(-2.59575461931,4.30713067084));
#1604 = CARTESIAN_POINT('',(-2.9603131848,4.06421908239));
#1605 = CARTESIAN_POINT('',(-3.73554903634,3.41630129394));
#1606 = CARTESIAN_POINT('',(-4.3095225984,2.62465565461));
#1607 = CARTESIAN_POINT('',(-4.56375002186,2.14244819995));
#1608 = CARTESIAN_POINT('',(-4.8362924348,1.40481893471));
#1609 = CARTESIAN_POINT('',(-4.96121877006,0.68885510118));
#1610 = CARTESIAN_POINT('',(-4.98763322877,0.452431376999));
#1611 = CARTESIAN_POINT('',(-5.,0.222409665749));
#1612 = CARTESIAN_POINT('',(-5.,-4.4408920985E-016));
#1613 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1614 = PCURVE('',#1615,#1624);
#1615 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#1616,#1617,#1618,#1619)
    ,(#1620,#1621,#1622,#1623
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,34.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#1616 = CARTESIAN_POINT('',(5.,0.E+000,37.));
#1617 = CARTESIAN_POINT('',(5.,10.,37.));
#1618 = CARTESIAN_POINT('',(-5.,10.,37.));
#1619 = CARTESIAN_POINT('',(-5.,0.E+000,37.));
#1620 = CARTESIAN_POINT('',(5.,0.E+000,3.));
#1621 = CARTESIAN_POINT('',(5.,10.,3.));
#1622 = CARTESIAN_POINT('',(-5.,10.,3.));
#1623 = CARTESIAN_POINT('',(-5.,0.E+000,3.));
#1624 = DEFINITIONAL_REPRESENTATION('',(#1625),#1673);
#1625 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#1626,#1627,#1628,#1629,#1630,
    #1631,#1632,#1633,#1634,#1635,#1636,#1637,#1638,#1639,#1640,#1641,
    #1642,#1643,#1644,#1645,#1646,#1647,#1648,#1649,#1650,#1651,#1652,
    #1653,#1654,#1655,#1656,#1657,#1658,#1659,#1660,#1661,#1662,#1663,
    #1664,#1665,#1666,#1667,#1668,#1669,#1670,#1671,#1672),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880348,
    1.016627760695,1.524941641043,2.033255521391,2.541569401739,
    3.049883282086,3.558197162434,4.066511042782,4.57482492313,
    5.083138803477,5.591452683825,6.099766564173,6.60808044452,
    7.116394324868,7.624708205216,8.133022085564,8.641335965911,
    9.149649846259,9.657963726607,10.166277606955,10.674591487302,
    11.18290536765,11.691219247998,12.199533128345,12.707847008693,
    13.216160889041,13.724474769389,14.232788649736,14.741102530084,
    15.249416410432,15.75773029078,16.266044171127,16.774358051475,
    17.282671931823,17.79098581217,18.299299692518,18.807613572866,
    19.315927453214,19.824241333561,20.332555213909,20.840869094257,
    21.349182974605,21.857496854952,22.3658107353),
  .QUASI_UNIFORM_KNOTS.);
#1626 = CARTESIAN_POINT('',(34.000998004,0.E+000));
#1627 = CARTESIAN_POINT('',(34.000998004,0.285786134005));
#1628 = CARTESIAN_POINT('',(34.000998004,0.851023724374));
#1629 = CARTESIAN_POINT('',(34.000998004,1.679658950067));
#1630 = CARTESIAN_POINT('',(34.000998004,2.488775842984));
#1631 = CARTESIAN_POINT('',(34.000998004,3.278357391147));
#1632 = CARTESIAN_POINT('',(34.000998004,4.048590090635));
#1633 = CARTESIAN_POINT('',(34.000998004,4.799873551245));
#1634 = CARTESIAN_POINT('',(34.000998004,5.532780976198));
#1635 = CARTESIAN_POINT('',(34.000998004,6.248020911162));
#1636 = CARTESIAN_POINT('',(34.000998004,6.946360574942));
#1637 = CARTESIAN_POINT('',(34.000998004,7.628688635561));
#1638 = CARTESIAN_POINT('',(34.000998004,8.296073973845));
#1639 = CARTESIAN_POINT('',(34.000998004,8.949683945325));
#1640 = CARTESIAN_POINT('',(34.000998004,9.590744783224));
#1641 = CARTESIAN_POINT('',(34.000998004,10.220499189069));
#1642 = CARTESIAN_POINT('',(34.000998004,10.840182524178));
#1643 = CARTESIAN_POINT('',(34.000998004,11.450961995563));
#1644 = CARTESIAN_POINT('',(34.000998004,12.054057836488));
#1645 = CARTESIAN_POINT('',(34.000998004,12.650784955207));
#1646 = CARTESIAN_POINT('',(34.000998004,13.242437006931));
#1647 = CARTESIAN_POINT('',(34.000998004,13.830311319039));
#1648 = CARTESIAN_POINT('',(34.000998004,14.415700442447));
#1649 = CARTESIAN_POINT('',(34.000998004,14.999897615114));
#1650 = CARTESIAN_POINT('',(34.000998004,15.584089013839));
#1651 = CARTESIAN_POINT('',(34.000998004,16.169496123896));
#1652 = CARTESIAN_POINT('',(34.000998004,16.757374014315));
#1653 = CARTESIAN_POINT('',(34.000998004,17.349001920563));
#1654 = CARTESIAN_POINT('',(34.000998004,17.945677529625));
#1655 = CARTESIAN_POINT('',(34.000998004,18.548712223709));
#1656 = CARTESIAN_POINT('',(34.000998004,19.159406299853));
#1657 = CARTESIAN_POINT('',(34.000998004,19.779034544783));
#1658 = CARTESIAN_POINT('',(34.000998004,20.40884411557));
#1659 = CARTESIAN_POINT('',(34.000998004,21.05005071958));
#1660 = CARTESIAN_POINT('',(34.000998004,21.703821244264));
#1661 = CARTESIAN_POINT('',(34.000998004,22.371286811436));
#1662 = CARTESIAN_POINT('',(34.000998004,23.053580536272));
#1663 = CARTESIAN_POINT('',(34.000998004,23.751780892279));
#1664 = CARTESIAN_POINT('',(34.000998004,24.466876470872));
#1665 = CARTESIAN_POINT('',(34.000998004,25.199732655413));
#1666 = CARTESIAN_POINT('',(34.000998004,25.951064420944));
#1667 = CARTESIAN_POINT('',(34.000998004,26.721413688722));
#1668 = CARTESIAN_POINT('',(34.000998004,27.511129456935));
#1669 = CARTESIAN_POINT('',(34.000998004,28.320321955904));
#1670 = CARTESIAN_POINT('',(34.000998004,29.148977248348));
#1671 = CARTESIAN_POINT('',(34.000998004,29.714213802924));
#1672 = CARTESIAN_POINT('',(34.000998004,30.));
#1673 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1674 = ADVANCED_FACE('',(#1675),#1261,.T.);
#1675 = FACE_BOUND('',#1676,.T.);
#1676 = EDGE_LOOP('',(#1677,#1678,#1700,#1730));
#1677 = ORIENTED_EDGE('',*,*,#1196,.T.);
#1678 = ORIENTED_EDGE('',*,*,#1679,.T.);
#1679 = EDGE_CURVE('',#1199,#1680,#1682,.T.);
#1680 = VERTEX_POINT('',#1681);
#1681 = CARTESIAN_POINT('',(-7.5,0.E+000,-2.22044604925E-016));
#1682 = SURFACE_CURVE('',#1683,(#1686,#1693),.PCURVE_S1.);
#1683 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1684,#1685),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,3.00099800399),.PIECEWISE_BEZIER_KNOTS.);
#1684 = CARTESIAN_POINT('',(-7.5,8.32667268461E-016,3.));
#1685 = CARTESIAN_POINT('',(-7.5,8.32667268461E-016,0.E+000));
#1686 = PCURVE('',#1261,#1687);
#1687 = DEFINITIONAL_REPRESENTATION('',(#1688),#1692);
#1688 = LINE('',#1689,#1690);
#1689 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1690 = VECTOR('',#1691,1.);
#1691 = DIRECTION('',(1.,0.E+000));
#1692 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1693 = PCURVE('',#1377,#1694);
#1694 = DEFINITIONAL_REPRESENTATION('',(#1695),#1699);
#1695 = LINE('',#1696,#1697);
#1696 = CARTESIAN_POINT('',(0.E+000,45.));
#1697 = VECTOR('',#1698,1.);
#1698 = DIRECTION('',(1.,0.E+000));
#1699 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1700 = ORIENTED_EDGE('',*,*,#1701,.T.);
#1701 = EDGE_CURVE('',#1680,#1702,#1704,.T.);
#1702 = VERTEX_POINT('',#1703);
#1703 = CARTESIAN_POINT('',(7.5,0.E+000,2.22044604925E-016));
#1704 = SURFACE_CURVE('',#1705,(#1710,#1717),.PCURVE_S1.);
#1705 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1706,#1707,#1708,#1709),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,45.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1706 = CARTESIAN_POINT('',(-7.5,0.E+000,0.E+000));
#1707 = CARTESIAN_POINT('',(-7.5,-15.,0.E+000));
#1708 = CARTESIAN_POINT('',(7.5,-15.,0.E+000));
#1709 = CARTESIAN_POINT('',(7.5,0.E+000,0.E+000));
#1710 = PCURVE('',#1261,#1711);
#1711 = DEFINITIONAL_REPRESENTATION('',(#1712),#1716);
#1712 = LINE('',#1713,#1714);
#1713 = CARTESIAN_POINT('',(3.00099800399,0.E+000));
#1714 = VECTOR('',#1715,1.);
#1715 = DIRECTION('',(0.E+000,1.));
#1716 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1717 = PCURVE('',#1718,#1723);
#1718 = PLANE('',#1719);
#1719 = AXIS2_PLACEMENT_3D('',#1720,#1721,#1722);
#1720 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#1721 = DIRECTION('',(0.E+000,0.E+000,-1.));
#1722 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1723 = DEFINITIONAL_REPRESENTATION('',(#1724),#1729);
#1724 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1725,#1726,#1727,#1728),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,45.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1725 = CARTESIAN_POINT('',(7.5,0.E+000));
#1726 = CARTESIAN_POINT('',(7.5,-15.));
#1727 = CARTESIAN_POINT('',(-7.5,-15.));
#1728 = CARTESIAN_POINT('',(-7.5,0.E+000));
#1729 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1730 = ORIENTED_EDGE('',*,*,#1731,.F.);
#1731 = EDGE_CURVE('',#1197,#1702,#1732,.T.);
#1732 = SURFACE_CURVE('',#1733,(#1736,#1743),.PCURVE_S1.);
#1733 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1734,#1735),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,3.00099800399),.PIECEWISE_BEZIER_KNOTS.);
#1734 = CARTESIAN_POINT('',(7.5,8.32667268461E-016,3.));
#1735 = CARTESIAN_POINT('',(7.5,8.32667268461E-016,0.E+000));
#1736 = PCURVE('',#1261,#1737);
#1737 = DEFINITIONAL_REPRESENTATION('',(#1738),#1742);
#1738 = LINE('',#1739,#1740);
#1739 = CARTESIAN_POINT('',(0.E+000,45.));
#1740 = VECTOR('',#1741,1.);
#1741 = DIRECTION('',(1.,0.E+000));
#1742 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1743 = PCURVE('',#1377,#1744);
#1744 = DEFINITIONAL_REPRESENTATION('',(#1745),#1749);
#1745 = LINE('',#1746,#1747);
#1746 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1747 = VECTOR('',#1748,1.);
#1748 = DIRECTION('',(1.,0.E+000));
#1749 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1750 = ADVANCED_FACE('',(#1751),#1377,.T.);
#1751 = FACE_BOUND('',#1752,.T.);
#1752 = EDGE_LOOP('',(#1753,#1754,#1755,#1778));
#1753 = ORIENTED_EDGE('',*,*,#1321,.T.);
#1754 = ORIENTED_EDGE('',*,*,#1731,.T.);
#1755 = ORIENTED_EDGE('',*,*,#1756,.T.);
#1756 = EDGE_CURVE('',#1702,#1680,#1757,.T.);
#1757 = SURFACE_CURVE('',#1758,(#1763,#1770),.PCURVE_S1.);
#1758 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1759,#1760,#1761,#1762),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,45.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1759 = CARTESIAN_POINT('',(7.5,0.E+000,0.E+000));
#1760 = CARTESIAN_POINT('',(7.5,15.,0.E+000));
#1761 = CARTESIAN_POINT('',(-7.5,15.,0.E+000));
#1762 = CARTESIAN_POINT('',(-7.5,0.E+000,0.E+000));
#1763 = PCURVE('',#1377,#1764);
#1764 = DEFINITIONAL_REPRESENTATION('',(#1765),#1769);
#1765 = LINE('',#1766,#1767);
#1766 = CARTESIAN_POINT('',(3.00099800399,0.E+000));
#1767 = VECTOR('',#1768,1.);
#1768 = DIRECTION('',(0.E+000,1.));
#1769 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1770 = PCURVE('',#1718,#1771);
#1771 = DEFINITIONAL_REPRESENTATION('',(#1772),#1777);
#1772 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1773,#1774,#1775,#1776),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,45.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1773 = CARTESIAN_POINT('',(-7.5,0.E+000));
#1774 = CARTESIAN_POINT('',(-7.5,15.));
#1775 = CARTESIAN_POINT('',(7.5,15.));
#1776 = CARTESIAN_POINT('',(7.5,0.E+000));
#1777 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1778 = ORIENTED_EDGE('',*,*,#1679,.F.);
#1779 = ADVANCED_FACE('',(#1780),#1499,.T.);
#1780 = FACE_BOUND('',#1781,.T.);
#1781 = EDGE_LOOP('',(#1782,#1783,#1805,#1835));
#1782 = ORIENTED_EDGE('',*,*,#1439,.T.);
#1783 = ORIENTED_EDGE('',*,*,#1784,.F.);
#1784 = EDGE_CURVE('',#1785,#1442,#1787,.T.);
#1785 = VERTEX_POINT('',#1786);
#1786 = CARTESIAN_POINT('',(5.,4.4408920985E-016,37.));
#1787 = SURFACE_CURVE('',#1788,(#1791,#1798),.PCURVE_S1.);
#1788 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1789,#1790),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,34.000998004),.PIECEWISE_BEZIER_KNOTS.);
#1789 = CARTESIAN_POINT('',(5.,-5.55111512307E-016,37.));
#1790 = CARTESIAN_POINT('',(5.,-5.55111512307E-016,3.));
#1791 = PCURVE('',#1499,#1792);
#1792 = DEFINITIONAL_REPRESENTATION('',(#1793),#1797);
#1793 = LINE('',#1794,#1795);
#1794 = CARTESIAN_POINT('',(0.E+000,30.));
#1795 = VECTOR('',#1796,1.);
#1796 = DIRECTION('',(1.,0.E+000));
#1797 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1798 = PCURVE('',#1615,#1799);
#1799 = DEFINITIONAL_REPRESENTATION('',(#1800),#1804);
#1800 = LINE('',#1801,#1802);
#1801 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1802 = VECTOR('',#1803,1.);
#1803 = DIRECTION('',(1.,0.E+000));
#1804 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1805 = ORIENTED_EDGE('',*,*,#1806,.F.);
#1806 = EDGE_CURVE('',#1807,#1785,#1809,.T.);
#1807 = VERTEX_POINT('',#1808);
#1808 = CARTESIAN_POINT('',(-5.,4.4408920985E-016,37.));
#1809 = SURFACE_CURVE('',#1810,(#1815,#1822),.PCURVE_S1.);
#1810 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1811,#1812,#1813,#1814),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1811 = CARTESIAN_POINT('',(-5.,0.E+000,37.));
#1812 = CARTESIAN_POINT('',(-5.,-10.,37.));
#1813 = CARTESIAN_POINT('',(5.,-10.,37.));
#1814 = CARTESIAN_POINT('',(5.,0.E+000,37.));
#1815 = PCURVE('',#1499,#1816);
#1816 = DEFINITIONAL_REPRESENTATION('',(#1817),#1821);
#1817 = LINE('',#1818,#1819);
#1818 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#1819 = VECTOR('',#1820,1.);
#1820 = DIRECTION('',(0.E+000,1.));
#1821 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1822 = PCURVE('',#1823,#1828);
#1823 = PLANE('',#1824);
#1824 = AXIS2_PLACEMENT_3D('',#1825,#1826,#1827);
#1825 = CARTESIAN_POINT('',(0.E+000,0.E+000,37.));
#1826 = DIRECTION('',(0.E+000,0.E+000,1.));
#1827 = DIRECTION('',(1.,0.E+000,0.E+000));
#1828 = DEFINITIONAL_REPRESENTATION('',(#1829),#1834);
#1829 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1830,#1831,#1832,#1833),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1830 = CARTESIAN_POINT('',(-5.,0.E+000));
#1831 = CARTESIAN_POINT('',(-5.,-10.));
#1832 = CARTESIAN_POINT('',(5.,-10.));
#1833 = CARTESIAN_POINT('',(5.,0.E+000));
#1834 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1835 = ORIENTED_EDGE('',*,*,#1836,.T.);
#1836 = EDGE_CURVE('',#1807,#1440,#1837,.T.);
#1837 = SURFACE_CURVE('',#1838,(#1841,#1848),.PCURVE_S1.);
#1838 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#1839,#1840),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,34.000998004),.PIECEWISE_BEZIER_KNOTS.);
#1839 = CARTESIAN_POINT('',(-5.,-5.55111512307E-016,37.));
#1840 = CARTESIAN_POINT('',(-5.,-5.55111512307E-016,3.));
#1841 = PCURVE('',#1499,#1842);
#1842 = DEFINITIONAL_REPRESENTATION('',(#1843),#1847);
#1843 = LINE('',#1844,#1845);
#1844 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#1845 = VECTOR('',#1846,1.);
#1846 = DIRECTION('',(1.,0.E+000));
#1847 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1848 = PCURVE('',#1615,#1849);
#1849 = DEFINITIONAL_REPRESENTATION('',(#1850),#1854);
#1850 = LINE('',#1851,#1852);
#1851 = CARTESIAN_POINT('',(0.E+000,30.));
#1852 = VECTOR('',#1853,1.);
#1853 = DIRECTION('',(1.,0.E+000));
#1854 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1855 = ADVANCED_FACE('',(#1856),#1615,.T.);
#1856 = FACE_BOUND('',#1857,.T.);
#1857 = EDGE_LOOP('',(#1858,#1859,#1860,#1883));
#1858 = ORIENTED_EDGE('',*,*,#1559,.T.);
#1859 = ORIENTED_EDGE('',*,*,#1836,.F.);
#1860 = ORIENTED_EDGE('',*,*,#1861,.F.);
#1861 = EDGE_CURVE('',#1785,#1807,#1862,.T.);
#1862 = SURFACE_CURVE('',#1863,(#1868,#1875),.PCURVE_S1.);
#1863 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1864,#1865,#1866,#1867),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1864 = CARTESIAN_POINT('',(5.,0.E+000,37.));
#1865 = CARTESIAN_POINT('',(5.,10.,37.));
#1866 = CARTESIAN_POINT('',(-5.,10.,37.));
#1867 = CARTESIAN_POINT('',(-5.,0.E+000,37.));
#1868 = PCURVE('',#1615,#1869);
#1869 = DEFINITIONAL_REPRESENTATION('',(#1870),#1874);
#1870 = LINE('',#1871,#1872);
#1871 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#1872 = VECTOR('',#1873,1.);
#1873 = DIRECTION('',(0.E+000,1.));
#1874 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1875 = PCURVE('',#1823,#1876);
#1876 = DEFINITIONAL_REPRESENTATION('',(#1877),#1882);
#1877 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#1878,#1879,#1880,#1881),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#1878 = CARTESIAN_POINT('',(5.,0.E+000));
#1879 = CARTESIAN_POINT('',(5.,10.));
#1880 = CARTESIAN_POINT('',(-5.,10.));
#1881 = CARTESIAN_POINT('',(-5.,0.E+000));
#1882 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1883 = ORIENTED_EDGE('',*,*,#1784,.T.);
#1884 = ADVANCED_FACE('',(#1885),#1718,.T.);
#1885 = FACE_BOUND('',#1886,.T.);
#1886 = EDGE_LOOP('',(#1887,#1888));
#1887 = ORIENTED_EDGE('',*,*,#1701,.F.);
#1888 = ORIENTED_EDGE('',*,*,#1756,.F.);
#1889 = ADVANCED_FACE('',(#1890),#1823,.T.);
#1890 = FACE_BOUND('',#1891,.T.);
#1891 = EDGE_LOOP('',(#1892,#1893));
#1892 = ORIENTED_EDGE('',*,*,#1806,.T.);
#1893 = ORIENTED_EDGE('',*,*,#1861,.T.);
#1894 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1898)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1895,#1896,#1897)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1895 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1896 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1897 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1898 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-005),#1895,
  'distance_accuracy_value','confusion accuracy');
#1899 = SHAPE_DEFINITION_REPRESENTATION(#1900,#1189);
#1900 = PRODUCT_DEFINITION_SHAPE('','',#1901);
#1901 = PRODUCT_DEFINITION('design','',#1902,#1905);
#1902 = PRODUCT_DEFINITION_FORMATION('','',#1903);
#1903 = PRODUCT('bolt','bolt','',(#1904));
#1904 = PRODUCT_CONTEXT('',#2,'mechanical');
#1905 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1906 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1907,#1909);
#1907 = ( REPRESENTATION_RELATIONSHIP('','',#1189,#1175) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1908) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1908 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1176);
#1909 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1910);
#1910 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('5','bolt_1','',#1170,#1901,$);
#1911 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1903));
#1912 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1913,#1915);
#1913 = ( REPRESENTATION_RELATIONSHIP('','',#62,#1175) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1914) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1914 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1180);
#1915 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1916);
#1916 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('6','nut_3','',#1170,#742,$);
#1917 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1918,#1920);
#1918 = ( REPRESENTATION_RELATIONSHIP('','',#1175,#1146) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1919) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1919 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1147);
#1920 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1921);
#1921 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('7','nut-bolt-assembly_1','',
  #1141,#1170,$);
#1922 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1172));
#1923 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1924,#1926);
#1924 = ( REPRESENTATION_RELATIONSHIP('','',#1175,#1146) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1925) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1925 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1151);
#1926 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1927);
#1927 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('8','nut-bolt-assembly_2','',
  #1141,#1170,$);
#1928 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1929,#1931);
#1929 = ( REPRESENTATION_RELATIONSHIP('','',#1175,#1146) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1930) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1930 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1155);
#1931 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1932);
#1932 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9','nut-bolt-assembly_3','',
  #1141,#1170,$);
#1933 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1934),#3788);
#1934 = MANIFOLD_SOLID_BREP('',#1935);
#1935 = CLOSED_SHELL('',(#1936,#2294,#3084,#3189,#3238,#3311,#3382,#3411
    ,#3438,#3509,#3538,#3609,#3638,#3709,#3738,#3777));
#1936 = ADVANCED_FACE('',(#1937,#2056),#1951,.T.);
#1937 = FACE_BOUND('',#1938,.T.);
#1938 = EDGE_LOOP('',(#1939,#1974,#2002,#2030));
#1939 = ORIENTED_EDGE('',*,*,#1940,.F.);
#1940 = EDGE_CURVE('',#1941,#1943,#1945,.T.);
#1941 = VERTEX_POINT('',#1942);
#1942 = CARTESIAN_POINT('',(0.E+000,0.E+000,100.));
#1943 = VERTEX_POINT('',#1944);
#1944 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#1945 = SURFACE_CURVE('',#1946,(#1950,#1962),.PCURVE_S1.);
#1946 = LINE('',#1947,#1948);
#1947 = CARTESIAN_POINT('',(0.E+000,0.E+000,50.));
#1948 = VECTOR('',#1949,1.);
#1949 = DIRECTION('',(0.E+000,0.E+000,-1.));
#1950 = PCURVE('',#1951,#1956);
#1951 = PLANE('',#1952);
#1952 = AXIS2_PLACEMENT_3D('',#1953,#1954,#1955);
#1953 = CARTESIAN_POINT('',(0.E+000,60.,100.));
#1954 = DIRECTION('',(-1.,0.E+000,0.E+000));
#1955 = DIRECTION('',(0.E+000,0.E+000,1.));
#1956 = DEFINITIONAL_REPRESENTATION('',(#1957),#1961);
#1957 = LINE('',#1958,#1959);
#1958 = CARTESIAN_POINT('',(-50.,-60.));
#1959 = VECTOR('',#1960,1.);
#1960 = DIRECTION('',(-1.,0.E+000));
#1961 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1962 = PCURVE('',#1963,#1968);
#1963 = PLANE('',#1964);
#1964 = AXIS2_PLACEMENT_3D('',#1965,#1966,#1967);
#1965 = CARTESIAN_POINT('',(0.E+000,0.E+000,100.));
#1966 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1967 = DIRECTION('',(0.E+000,0.E+000,-1.));
#1968 = DEFINITIONAL_REPRESENTATION('',(#1969),#1973);
#1969 = LINE('',#1970,#1971);
#1970 = CARTESIAN_POINT('',(50.,0.E+000));
#1971 = VECTOR('',#1972,1.);
#1972 = DIRECTION('',(1.,0.E+000));
#1973 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1974 = ORIENTED_EDGE('',*,*,#1975,.F.);
#1975 = EDGE_CURVE('',#1976,#1941,#1978,.T.);
#1976 = VERTEX_POINT('',#1977);
#1977 = CARTESIAN_POINT('',(0.E+000,60.,100.));
#1978 = SURFACE_CURVE('',#1979,(#1983,#1990),.PCURVE_S1.);
#1979 = LINE('',#1980,#1981);
#1980 = CARTESIAN_POINT('',(0.E+000,30.,100.));
#1981 = VECTOR('',#1982,1.);
#1982 = DIRECTION('',(0.E+000,-1.,0.E+000));
#1983 = PCURVE('',#1951,#1984);
#1984 = DEFINITIONAL_REPRESENTATION('',(#1985),#1989);
#1985 = LINE('',#1986,#1987);
#1986 = CARTESIAN_POINT('',(0.E+000,-30.));
#1987 = VECTOR('',#1988,1.);
#1988 = DIRECTION('',(0.E+000,-1.));
#1989 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1990 = PCURVE('',#1991,#1996);
#1991 = PLANE('',#1992);
#1992 = AXIS2_PLACEMENT_3D('',#1993,#1994,#1995);
#1993 = CARTESIAN_POINT('',(0.E+000,0.E+000,100.));
#1994 = DIRECTION('',(0.E+000,0.E+000,1.));
#1995 = DIRECTION('',(1.,0.E+000,0.E+000));
#1996 = DEFINITIONAL_REPRESENTATION('',(#1997),#2001);
#1997 = LINE('',#1998,#1999);
#1998 = CARTESIAN_POINT('',(0.E+000,30.));
#1999 = VECTOR('',#2000,1.);
#2000 = DIRECTION('',(0.E+000,-1.));
#2001 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2002 = ORIENTED_EDGE('',*,*,#2003,.T.);
#2003 = EDGE_CURVE('',#1976,#2004,#2006,.T.);
#2004 = VERTEX_POINT('',#2005);
#2005 = CARTESIAN_POINT('',(0.E+000,60.,0.E+000));
#2006 = SURFACE_CURVE('',#2007,(#2011,#2018),.PCURVE_S1.);
#2007 = LINE('',#2008,#2009);
#2008 = CARTESIAN_POINT('',(0.E+000,60.,50.));
#2009 = VECTOR('',#2010,1.);
#2010 = DIRECTION('',(0.E+000,0.E+000,-1.));
#2011 = PCURVE('',#1951,#2012);
#2012 = DEFINITIONAL_REPRESENTATION('',(#2013),#2017);
#2013 = LINE('',#2014,#2015);
#2014 = CARTESIAN_POINT('',(-50.,0.E+000));
#2015 = VECTOR('',#2016,1.);
#2016 = DIRECTION('',(-1.,0.E+000));
#2017 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2018 = PCURVE('',#2019,#2024);
#2019 = PLANE('',#2020);
#2020 = AXIS2_PLACEMENT_3D('',#2021,#2022,#2023);
#2021 = CARTESIAN_POINT('',(10.,60.,100.));
#2022 = DIRECTION('',(0.E+000,1.,0.E+000));
#2023 = DIRECTION('',(0.E+000,0.E+000,1.));
#2024 = DEFINITIONAL_REPRESENTATION('',(#2025),#2029);
#2025 = LINE('',#2026,#2027);
#2026 = CARTESIAN_POINT('',(-50.,-10.));
#2027 = VECTOR('',#2028,1.);
#2028 = DIRECTION('',(-1.,0.E+000));
#2029 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2030 = ORIENTED_EDGE('',*,*,#2031,.T.);
#2031 = EDGE_CURVE('',#2004,#1943,#2032,.T.);
#2032 = SURFACE_CURVE('',#2033,(#2037,#2044),.PCURVE_S1.);
#2033 = LINE('',#2034,#2035);
#2034 = CARTESIAN_POINT('',(0.E+000,30.,0.E+000));
#2035 = VECTOR('',#2036,1.);
#2036 = DIRECTION('',(0.E+000,-1.,0.E+000));
#2037 = PCURVE('',#1951,#2038);
#2038 = DEFINITIONAL_REPRESENTATION('',(#2039),#2043);
#2039 = LINE('',#2040,#2041);
#2040 = CARTESIAN_POINT('',(-100.,-30.));
#2041 = VECTOR('',#2042,1.);
#2042 = DIRECTION('',(0.E+000,-1.));
#2043 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2044 = PCURVE('',#2045,#2050);
#2045 = PLANE('',#2046);
#2046 = AXIS2_PLACEMENT_3D('',#2047,#2048,#2049);
#2047 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#2048 = DIRECTION('',(0.E+000,0.E+000,-1.));
#2049 = DIRECTION('',(-1.,0.E+000,0.E+000));
#2050 = DEFINITIONAL_REPRESENTATION('',(#2051),#2055);
#2051 = LINE('',#2052,#2053);
#2052 = CARTESIAN_POINT('',(0.E+000,30.));
#2053 = VECTOR('',#2054,1.);
#2054 = DIRECTION('',(0.E+000,-1.));
#2055 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2056 = FACE_BOUND('',#2057,.T.);
#2057 = EDGE_LOOP('',(#2058,#2178));
#2058 = ORIENTED_EDGE('',*,*,#2059,.T.);
#2059 = EDGE_CURVE('',#2060,#2062,#2064,.T.);
#2060 = VERTEX_POINT('',#2061);
#2061 = CARTESIAN_POINT('',(0.E+000,40.,45.));
#2062 = VERTEX_POINT('',#2063);
#2063 = CARTESIAN_POINT('',(0.E+000,40.,55.));
#2064 = SURFACE_CURVE('',#2065,(#2090,#2118),.PCURVE_S1.);
#2065 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2066,#2067,#2068,#2069,#2070,
    #2071,#2072,#2073,#2074,#2075,#2076,#2077,#2078,#2079,#2080,#2081,
    #2082,#2083,#2084,#2085,#2086,#2087,#2088,#2089),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164424,7.85828164686,
    10.7238180515,13.5836589937,16.4911855013,20.3877608685,
    22.3658107304),.UNSPECIFIED.);
#2066 = CARTESIAN_POINT('',(0.E+000,40.,45.));
#2067 = CARTESIAN_POINT('',(0.E+000,40.4671982524,45.));
#2068 = CARTESIAN_POINT('',(0.E+000,40.967985464,45.0545696798));
#2069 = CARTESIAN_POINT('',(0.E+000,41.4911230353,45.1795822588));
#2070 = CARTESIAN_POINT('',(0.E+000,42.4800614343,45.5726861255));
#2071 = CARTESIAN_POINT('',(0.E+000,43.3809047398,46.2580146362));
#2072 = CARTESIAN_POINT('',(0.E+000,43.7686263331,46.6452361934));
#2073 = CARTESIAN_POINT('',(0.E+000,44.3620880288,47.432508626));
#2074 = CARTESIAN_POINT('',(0.E+000,44.7518403652,48.3548107374));
#2075 = CARTESIAN_POINT('',(0.E+000,44.8779193361,48.7767785569));
#2076 = CARTESIAN_POINT('',(0.E+000,45.0354809914,49.6437129631));
#2077 = CARTESIAN_POINT('',(0.E+000,45.001400762,50.5264003017));
#2078 = CARTESIAN_POINT('',(0.E+000,44.935748566,50.9630506747));
#2079 = CARTESIAN_POINT('',(0.E+000,44.7088457881,51.8186421202));
#2080 = CARTESIAN_POINT('',(0.E+000,44.3071306709,52.5957546192));
#2081 = CARTESIAN_POINT('',(0.E+000,44.0642190823,52.9603131849));
#2082 = CARTESIAN_POINT('',(0.E+000,43.416301294,53.7355490362));
#2083 = CARTESIAN_POINT('',(0.E+000,42.624655655,54.3095225982));
#2084 = CARTESIAN_POINT('',(0.E+000,42.1424481996,54.563750022));
#2085 = CARTESIAN_POINT('',(0.E+000,41.404818935,54.8362924347));
#2086 = CARTESIAN_POINT('',(0.E+000,40.688855102,54.9612187699));
#2087 = CARTESIAN_POINT('',(0.E+000,40.4524313762,54.9876332288));
#2088 = CARTESIAN_POINT('',(0.E+000,40.2224096654,55.));
#2089 = CARTESIAN_POINT('',(0.E+000,40.,55.));
#2090 = PCURVE('',#1951,#2091);
#2091 = DEFINITIONAL_REPRESENTATION('',(#2092),#2117);
#2092 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2093,#2094,#2095,#2096,#2097,
    #2098,#2099,#2100,#2101,#2102,#2103,#2104,#2105,#2106,#2107,#2108,
    #2109,#2110,#2111,#2112,#2113,#2114,#2115,#2116),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164424,7.85828164686,
    10.7238180515,13.5836589937,16.4911855013,20.3877608685,
    22.3658107304),.UNSPECIFIED.);
#2093 = CARTESIAN_POINT('',(-55.,-20.));
#2094 = CARTESIAN_POINT('',(-55.,-19.5328017476));
#2095 = CARTESIAN_POINT('',(-54.9454303202,-19.032014536));
#2096 = CARTESIAN_POINT('',(-54.8204177412,-18.5088769647));
#2097 = CARTESIAN_POINT('',(-54.4273138745,-17.5199385657));
#2098 = CARTESIAN_POINT('',(-53.7419853638,-16.6190952602));
#2099 = CARTESIAN_POINT('',(-53.3547638066,-16.2313736669));
#2100 = CARTESIAN_POINT('',(-52.567491374,-15.6379119712));
#2101 = CARTESIAN_POINT('',(-51.6451892626,-15.2481596348));
#2102 = CARTESIAN_POINT('',(-51.2232214431,-15.1220806639));
#2103 = CARTESIAN_POINT('',(-50.3562870369,-14.9645190086));
#2104 = CARTESIAN_POINT('',(-49.4735996983,-14.998599238));
#2105 = CARTESIAN_POINT('',(-49.0369493253,-15.064251434));
#2106 = CARTESIAN_POINT('',(-48.1813578798,-15.2911542119));
#2107 = CARTESIAN_POINT('',(-47.4042453808,-15.6928693291));
#2108 = CARTESIAN_POINT('',(-47.0396868151,-15.9357809177));
#2109 = CARTESIAN_POINT('',(-46.2644509638,-16.583698706));
#2110 = CARTESIAN_POINT('',(-45.6904774018,-17.375344345));
#2111 = CARTESIAN_POINT('',(-45.436249978,-17.8575518004));
#2112 = CARTESIAN_POINT('',(-45.1637075653,-18.595181065));
#2113 = CARTESIAN_POINT('',(-45.0387812301,-19.311144898));
#2114 = CARTESIAN_POINT('',(-45.0123667712,-19.5475686238));
#2115 = CARTESIAN_POINT('',(-45.,-19.7775903346));
#2116 = CARTESIAN_POINT('',(-45.,-20.));
#2117 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2118 = PCURVE('',#2119,#2128);
#2119 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2120,#2121,#2122,#2123)
    ,(#2124,#2125,#2126,#2127
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2120 = CARTESIAN_POINT('',(10.,40.,55.));
#2121 = CARTESIAN_POINT('',(10.,50.,55.));
#2122 = CARTESIAN_POINT('',(10.,50.,45.));
#2123 = CARTESIAN_POINT('',(10.,40.,45.));
#2124 = CARTESIAN_POINT('',(0.E+000,40.,55.));
#2125 = CARTESIAN_POINT('',(0.E+000,50.,55.));
#2126 = CARTESIAN_POINT('',(0.E+000,50.,45.));
#2127 = CARTESIAN_POINT('',(0.E+000,40.,45.));
#2128 = DEFINITIONAL_REPRESENTATION('',(#2129),#2177);
#2129 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2130,#2131,#2132,#2133,#2134,
    #2135,#2136,#2137,#2138,#2139,#2140,#2141,#2142,#2143,#2144,#2145,
    #2146,#2147,#2148,#2149,#2150,#2151,#2152,#2153,#2154,#2155,#2156,
    #2157,#2158,#2159,#2160,#2161,#2162,#2163,#2164,#2165,#2166,#2167,
    #2168,#2169,#2170,#2171,#2172,#2173,#2174,#2175,#2176),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880236,
    1.016627760473,1.524941640709,2.033255520945,2.541569401182,
    3.049883281418,3.558197161655,4.066511041891,4.574824922127,
    5.083138802364,5.5914526826,6.099766562836,6.608080443073,
    7.116394323309,7.624708203545,8.133022083782,8.641335964018,
    9.149649844255,9.657963724491,10.166277604727,10.674591484964,
    11.1829053652,11.691219245436,12.199533125673,12.707847005909,
    13.216160886145,13.724474766382,14.232788646618,14.741102526855,
    15.249416407091,15.757730287327,16.266044167564,16.7743580478,
    17.282671928036,17.790985808273,18.299299688509,18.807613568745,
    19.315927448982,19.824241329218,20.332555209455,20.840869089691,
    21.349182969927,21.857496850164,22.3658107304),
  .QUASI_UNIFORM_KNOTS.);
#2130 = CARTESIAN_POINT('',(10.000998004,30.));
#2131 = CARTESIAN_POINT('',(10.000998004,29.714213866026));
#2132 = CARTESIAN_POINT('',(10.000998004,29.148976275749));
#2133 = CARTESIAN_POINT('',(10.000998004,28.320341050263));
#2134 = CARTESIAN_POINT('',(10.000998004,27.511224157616));
#2135 = CARTESIAN_POINT('',(10.000998004,26.721642609747));
#2136 = CARTESIAN_POINT('',(10.000998004,25.951409910544));
#2137 = CARTESIAN_POINT('',(10.000998004,25.200126450178));
#2138 = CARTESIAN_POINT('',(10.000998004,24.467219025419));
#2139 = CARTESIAN_POINT('',(10.000998004,23.751979090598));
#2140 = CARTESIAN_POINT('',(10.000998004,23.053639426926));
#2141 = CARTESIAN_POINT('',(10.000998004,22.371311366386));
#2142 = CARTESIAN_POINT('',(10.000998004,21.703926028164));
#2143 = CARTESIAN_POINT('',(10.000998004,21.050316056745));
#2144 = CARTESIAN_POINT('',(10.000998004,20.40925521892));
#2145 = CARTESIAN_POINT('',(10.000998004,19.779500813173));
#2146 = CARTESIAN_POINT('',(10.000998004,19.15981747818));
#2147 = CARTESIAN_POINT('',(10.000998004,18.549038006927));
#2148 = CARTESIAN_POINT('',(10.000998004,17.94594216606));
#2149 = CARTESIAN_POINT('',(10.000998004,17.349215047295));
#2150 = CARTESIAN_POINT('',(10.000998004,16.757562995502));
#2151 = CARTESIAN_POINT('',(10.000998004,16.169688683392));
#2152 = CARTESIAN_POINT('',(10.000998004,15.584299560095));
#2153 = CARTESIAN_POINT('',(10.000998004,15.000102387554));
#2154 = CARTESIAN_POINT('',(10.000998004,14.415910989025));
#2155 = CARTESIAN_POINT('',(10.000998004,13.830503879233));
#2156 = CARTESIAN_POINT('',(10.000998004,13.242625989092));
#2157 = CARTESIAN_POINT('',(10.000998004,12.650998083074));
#2158 = CARTESIAN_POINT('',(10.000998004,12.054322474192));
#2159 = CARTESIAN_POINT('',(10.000998004,11.451287780254));
#2160 = CARTESIAN_POINT('',(10.000998004,10.840593704162));
#2161 = CARTESIAN_POINT('',(10.000998004,10.220965459246));
#2162 = CARTESIAN_POINT('',(10.000998004,9.591155888523));
#2163 = CARTESIAN_POINT('',(10.000998004,8.949949284694));
#2164 = CARTESIAN_POINT('',(10.000998004,8.296178760285));
#2165 = CARTESIAN_POINT('',(10.000998004,7.628713193302));
#2166 = CARTESIAN_POINT('',(10.000998004,6.94641946847));
#2167 = CARTESIAN_POINT('',(10.000998004,6.248219112305));
#2168 = CARTESIAN_POINT('',(10.000998004,5.533123533488));
#2169 = CARTESIAN_POINT('',(10.000998004,4.800267348802));
#2170 = CARTESIAN_POINT('',(10.000998004,4.048935583317));
#2171 = CARTESIAN_POINT('',(10.000998004,3.278586315814));
#2172 = CARTESIAN_POINT('',(10.000998004,2.488870547876));
#2173 = CARTESIAN_POINT('',(10.000998004,1.679678046715));
#2174 = CARTESIAN_POINT('',(10.000998004,0.851022751886));
#2175 = CARTESIAN_POINT('',(10.000998004,0.285786196767));
#2176 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2177 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2178 = ORIENTED_EDGE('',*,*,#2179,.T.);
#2179 = EDGE_CURVE('',#2062,#2060,#2180,.T.);
#2180 = SURFACE_CURVE('',#2181,(#2206,#2234),.PCURVE_S1.);
#2181 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2182,#2183,#2184,#2185,#2186,
    #2187,#2188,#2189,#2190,#2191,#2192,#2193,#2194,#2195,#2196,#2197,
    #2198,#2199,#2200,#2201,#2202,#2203,#2204,#2205),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164422,7.85828164677,
    10.7238180514,13.5836589927,16.4911854995,20.3877608665,
    22.3658107284),.UNSPECIFIED.);
#2182 = CARTESIAN_POINT('',(0.E+000,40.,55.));
#2183 = CARTESIAN_POINT('',(0.E+000,39.5328017476,55.));
#2184 = CARTESIAN_POINT('',(0.E+000,39.032014536,54.9454303202));
#2185 = CARTESIAN_POINT('',(0.E+000,38.5088769647,54.8204177412));
#2186 = CARTESIAN_POINT('',(0.E+000,37.5199385657,54.4273138745));
#2187 = CARTESIAN_POINT('',(0.E+000,36.6190952602,53.7419853638));
#2188 = CARTESIAN_POINT('',(0.E+000,36.2313736669,53.3547638066));
#2189 = CARTESIAN_POINT('',(0.E+000,35.6379119712,52.567491374));
#2190 = CARTESIAN_POINT('',(0.E+000,35.2481596348,51.6451892626));
#2191 = CARTESIAN_POINT('',(0.E+000,35.1220806639,51.2232214431));
#2192 = CARTESIAN_POINT('',(0.E+000,34.9645190086,50.356287037));
#2193 = CARTESIAN_POINT('',(0.E+000,34.998599238,49.4735996986));
#2194 = CARTESIAN_POINT('',(0.E+000,35.0642514341,49.036949325));
#2195 = CARTESIAN_POINT('',(0.E+000,35.291154212,48.1813578798));
#2196 = CARTESIAN_POINT('',(0.E+000,35.692869329,47.404245381));
#2197 = CARTESIAN_POINT('',(0.E+000,35.9357809179,47.0396868149));
#2198 = CARTESIAN_POINT('',(0.E+000,36.583698706,46.2644509637));
#2199 = CARTESIAN_POINT('',(0.E+000,37.375344345,45.6904774019));
#2200 = CARTESIAN_POINT('',(0.E+000,37.8575518004,45.436249978));
#2201 = CARTESIAN_POINT('',(0.E+000,38.595181065,45.1637075653));
#2202 = CARTESIAN_POINT('',(0.E+000,39.311144898,45.0387812301));
#2203 = CARTESIAN_POINT('',(0.E+000,39.5475686238,45.0123667712));
#2204 = CARTESIAN_POINT('',(0.E+000,39.7775903347,45.));
#2205 = CARTESIAN_POINT('',(0.E+000,40.,45.));
#2206 = PCURVE('',#1951,#2207);
#2207 = DEFINITIONAL_REPRESENTATION('',(#2208),#2233);
#2208 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2209,#2210,#2211,#2212,#2213,
    #2214,#2215,#2216,#2217,#2218,#2219,#2220,#2221,#2222,#2223,#2224,
    #2225,#2226,#2227,#2228,#2229,#2230,#2231,#2232),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164422,7.85828164677,
    10.7238180514,13.5836589927,16.4911854995,20.3877608665,
    22.3658107284),.UNSPECIFIED.);
#2209 = CARTESIAN_POINT('',(-45.,-20.));
#2210 = CARTESIAN_POINT('',(-45.,-20.4671982524));
#2211 = CARTESIAN_POINT('',(-45.0545696798,-20.967985464));
#2212 = CARTESIAN_POINT('',(-45.1795822588,-21.4911230353));
#2213 = CARTESIAN_POINT('',(-45.5726861255,-22.4800614343));
#2214 = CARTESIAN_POINT('',(-46.2580146362,-23.3809047398));
#2215 = CARTESIAN_POINT('',(-46.6452361934,-23.7686263331));
#2216 = CARTESIAN_POINT('',(-47.432508626,-24.3620880288));
#2217 = CARTESIAN_POINT('',(-48.3548107374,-24.7518403652));
#2218 = CARTESIAN_POINT('',(-48.7767785569,-24.8779193361));
#2219 = CARTESIAN_POINT('',(-49.643712963,-25.0354809914));
#2220 = CARTESIAN_POINT('',(-50.5264003014,-25.001400762));
#2221 = CARTESIAN_POINT('',(-50.963050675,-24.9357485659));
#2222 = CARTESIAN_POINT('',(-51.8186421202,-24.708845788));
#2223 = CARTESIAN_POINT('',(-52.595754619,-24.307130671));
#2224 = CARTESIAN_POINT('',(-52.9603131851,-24.0642190821));
#2225 = CARTESIAN_POINT('',(-53.7355490363,-23.416301294));
#2226 = CARTESIAN_POINT('',(-54.3095225981,-22.624655655));
#2227 = CARTESIAN_POINT('',(-54.563750022,-22.1424481996));
#2228 = CARTESIAN_POINT('',(-54.8362924347,-21.404818935));
#2229 = CARTESIAN_POINT('',(-54.9612187699,-20.688855102));
#2230 = CARTESIAN_POINT('',(-54.9876332288,-20.4524313762));
#2231 = CARTESIAN_POINT('',(-55.,-20.2224096653));
#2232 = CARTESIAN_POINT('',(-55.,-20.));
#2233 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2234 = PCURVE('',#2235,#2244);
#2235 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2236,#2237,#2238,#2239)
    ,(#2240,#2241,#2242,#2243
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2236 = CARTESIAN_POINT('',(10.,40.,45.));
#2237 = CARTESIAN_POINT('',(10.,30.,45.));
#2238 = CARTESIAN_POINT('',(10.,30.,55.));
#2239 = CARTESIAN_POINT('',(10.,40.,55.));
#2240 = CARTESIAN_POINT('',(0.E+000,40.,45.));
#2241 = CARTESIAN_POINT('',(0.E+000,30.,45.));
#2242 = CARTESIAN_POINT('',(0.E+000,30.,55.));
#2243 = CARTESIAN_POINT('',(0.E+000,40.,55.));
#2244 = DEFINITIONAL_REPRESENTATION('',(#2245),#2293);
#2245 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2246,#2247,#2248,#2249,#2250,
    #2251,#2252,#2253,#2254,#2255,#2256,#2257,#2258,#2259,#2260,#2261,
    #2262,#2263,#2264,#2265,#2266,#2267,#2268,#2269,#2270,#2271,#2272,
    #2273,#2274,#2275,#2276,#2277,#2278,#2279,#2280,#2281,#2282,#2283,
    #2284,#2285,#2286,#2287,#2288,#2289,#2290,#2291,#2292),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880191,
    1.016627760382,1.524941640573,2.033255520764,2.541569400955,
    3.049883281145,3.558197161336,4.066511041527,4.574824921718,
    5.083138801909,5.5914526821,6.099766562291,6.608080442482,
    7.116394322673,7.624708202864,8.133022083055,8.641335963245,
    9.149649843436,9.657963723627,10.166277603818,10.674591484009,
    11.1829053642,11.691219244391,12.199533124582,12.707847004773,
    13.216160884964,13.724474765155,14.232788645345,14.741102525536,
    15.249416405727,15.757730285918,16.266044166109,16.7743580463,
    17.282671926491,17.790985806682,18.299299686873,18.807613567064,
    19.315927447255,19.824241327445,20.332555207636,20.840869087827,
    21.349182968018,21.857496848209,22.3658107284),
  .QUASI_UNIFORM_KNOTS.);
#2246 = CARTESIAN_POINT('',(10.000998004,30.));
#2247 = CARTESIAN_POINT('',(10.000998004,29.71421386605));
#2248 = CARTESIAN_POINT('',(10.000998004,29.14897627582));
#2249 = CARTESIAN_POINT('',(10.000998004,28.320341050402));
#2250 = CARTESIAN_POINT('',(10.000998004,27.511224157819));
#2251 = CARTESIAN_POINT('',(10.000998004,26.72164261001));
#2252 = CARTESIAN_POINT('',(10.000998004,25.951409910862));
#2253 = CARTESIAN_POINT('',(10.000998004,25.200126450549));
#2254 = CARTESIAN_POINT('',(10.000998004,24.467219025838));
#2255 = CARTESIAN_POINT('',(10.000998004,23.751979091062));
#2256 = CARTESIAN_POINT('',(10.000998004,23.053639427433));
#2257 = CARTESIAN_POINT('',(10.000998004,22.371311366934));
#2258 = CARTESIAN_POINT('',(10.000998004,21.70392602875));
#2259 = CARTESIAN_POINT('',(10.000998004,21.050316057367));
#2260 = CARTESIAN_POINT('',(10.000998004,20.409255219579));
#2261 = CARTESIAN_POINT('',(10.000998004,19.779500813868));
#2262 = CARTESIAN_POINT('',(10.000998004,19.159817478911));
#2263 = CARTESIAN_POINT('',(10.000998004,18.549038007695));
#2264 = CARTESIAN_POINT('',(10.000998004,17.945942166867));
#2265 = CARTESIAN_POINT('',(10.000998004,17.349215048139));
#2266 = CARTESIAN_POINT('',(10.000998004,16.757562996382));
#2267 = CARTESIAN_POINT('',(10.000998004,16.169688684309));
#2268 = CARTESIAN_POINT('',(10.000998004,15.584299561055));
#2269 = CARTESIAN_POINT('',(10.000998004,15.000102388583));
#2270 = CARTESIAN_POINT('',(10.000998004,14.415910989914));
#2271 = CARTESIAN_POINT('',(10.000998004,13.830503879808));
#2272 = CARTESIAN_POINT('',(10.000998004,13.242625989363));
#2273 = CARTESIAN_POINT('',(10.000998004,12.650998083229));
#2274 = CARTESIAN_POINT('',(10.000998004,12.054322474433));
#2275 = CARTESIAN_POINT('',(10.000998004,11.4512877805));
#2276 = CARTESIAN_POINT('',(10.000998004,10.84059370422));
#2277 = CARTESIAN_POINT('',(10.000998004,10.220965459014));
#2278 = CARTESIAN_POINT('',(10.000998004,9.591155888064));
#2279 = CARTESIAN_POINT('',(10.000998004,8.949949284218));
#2280 = CARTESIAN_POINT('',(10.000998004,8.296178759904));
#2281 = CARTESIAN_POINT('',(10.000998004,7.62871319297));
#2282 = CARTESIAN_POINT('',(10.000998004,6.946419468164));
#2283 = CARTESIAN_POINT('',(10.000998004,6.248219112002));
#2284 = CARTESIAN_POINT('',(10.000998004,5.533123533185));
#2285 = CARTESIAN_POINT('',(10.000998004,4.800267348507));
#2286 = CARTESIAN_POINT('',(10.000998004,4.048935583046));
#2287 = CARTESIAN_POINT('',(10.000998004,3.278586315578));
#2288 = CARTESIAN_POINT('',(10.000998004,2.488870547681));
#2289 = CARTESIAN_POINT('',(10.000998004,1.67967804655));
#2290 = CARTESIAN_POINT('',(10.000998004,0.851022751666));
#2291 = CARTESIAN_POINT('',(10.000998004,0.28578619665));
#2292 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2293 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2294 = ADVANCED_FACE('',(#2295,#2370,#2608,#2846),#1963,.T.);
#2295 = FACE_BOUND('',#2296,.T.);
#2296 = EDGE_LOOP('',(#2297,#2327,#2348,#2349));
#2297 = ORIENTED_EDGE('',*,*,#2298,.F.);
#2298 = EDGE_CURVE('',#2299,#2301,#2303,.T.);
#2299 = VERTEX_POINT('',#2300);
#2300 = CARTESIAN_POINT('',(50.,0.E+000,100.));
#2301 = VERTEX_POINT('',#2302);
#2302 = CARTESIAN_POINT('',(50.,0.E+000,0.E+000));
#2303 = SURFACE_CURVE('',#2304,(#2308,#2315),.PCURVE_S1.);
#2304 = LINE('',#2305,#2306);
#2305 = CARTESIAN_POINT('',(50.,0.E+000,50.));
#2306 = VECTOR('',#2307,1.);
#2307 = DIRECTION('',(0.E+000,0.E+000,-1.));
#2308 = PCURVE('',#1963,#2309);
#2309 = DEFINITIONAL_REPRESENTATION('',(#2310),#2314);
#2310 = LINE('',#2311,#2312);
#2311 = CARTESIAN_POINT('',(50.,50.));
#2312 = VECTOR('',#2313,1.);
#2313 = DIRECTION('',(1.,0.E+000));
#2314 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2315 = PCURVE('',#2316,#2321);
#2316 = PLANE('',#2317);
#2317 = AXIS2_PLACEMENT_3D('',#2318,#2319,#2320);
#2318 = CARTESIAN_POINT('',(50.,0.E+000,100.));
#2319 = DIRECTION('',(1.,0.E+000,0.E+000));
#2320 = DIRECTION('',(0.E+000,0.E+000,-1.));
#2321 = DEFINITIONAL_REPRESENTATION('',(#2322),#2326);
#2322 = LINE('',#2323,#2324);
#2323 = CARTESIAN_POINT('',(50.,0.E+000));
#2324 = VECTOR('',#2325,1.);
#2325 = DIRECTION('',(1.,0.E+000));
#2326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2327 = ORIENTED_EDGE('',*,*,#2328,.F.);
#2328 = EDGE_CURVE('',#1941,#2299,#2329,.T.);
#2329 = SURFACE_CURVE('',#2330,(#2334,#2341),.PCURVE_S1.);
#2330 = LINE('',#2331,#2332);
#2331 = CARTESIAN_POINT('',(25.,0.E+000,100.));
#2332 = VECTOR('',#2333,1.);
#2333 = DIRECTION('',(1.,0.E+000,0.E+000));
#2334 = PCURVE('',#1963,#2335);
#2335 = DEFINITIONAL_REPRESENTATION('',(#2336),#2340);
#2336 = LINE('',#2337,#2338);
#2337 = CARTESIAN_POINT('',(0.E+000,25.));
#2338 = VECTOR('',#2339,1.);
#2339 = DIRECTION('',(0.E+000,1.));
#2340 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2341 = PCURVE('',#1991,#2342);
#2342 = DEFINITIONAL_REPRESENTATION('',(#2343),#2347);
#2343 = LINE('',#2344,#2345);
#2344 = CARTESIAN_POINT('',(25.,0.E+000));
#2345 = VECTOR('',#2346,1.);
#2346 = DIRECTION('',(1.,0.E+000));
#2347 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2348 = ORIENTED_EDGE('',*,*,#1940,.T.);
#2349 = ORIENTED_EDGE('',*,*,#2350,.T.);
#2350 = EDGE_CURVE('',#1943,#2301,#2351,.T.);
#2351 = SURFACE_CURVE('',#2352,(#2356,#2363),.PCURVE_S1.);
#2352 = LINE('',#2353,#2354);
#2353 = CARTESIAN_POINT('',(25.,0.E+000,0.E+000));
#2354 = VECTOR('',#2355,1.);
#2355 = DIRECTION('',(1.,0.E+000,0.E+000));
#2356 = PCURVE('',#1963,#2357);
#2357 = DEFINITIONAL_REPRESENTATION('',(#2358),#2362);
#2358 = LINE('',#2359,#2360);
#2359 = CARTESIAN_POINT('',(100.,25.));
#2360 = VECTOR('',#2361,1.);
#2361 = DIRECTION('',(0.E+000,1.));
#2362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2363 = PCURVE('',#2045,#2364);
#2364 = DEFINITIONAL_REPRESENTATION('',(#2365),#2369);
#2365 = LINE('',#2366,#2367);
#2366 = CARTESIAN_POINT('',(-25.,0.E+000));
#2367 = VECTOR('',#2368,1.);
#2368 = DIRECTION('',(-1.,0.E+000));
#2369 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2370 = FACE_BOUND('',#2371,.T.);
#2371 = EDGE_LOOP('',(#2372,#2492));
#2372 = ORIENTED_EDGE('',*,*,#2373,.T.);
#2373 = EDGE_CURVE('',#2374,#2376,#2378,.T.);
#2374 = VERTEX_POINT('',#2375);
#2375 = CARTESIAN_POINT('',(42.5,0.E+000,42.0096189398));
#2376 = VERTEX_POINT('',#2377);
#2377 = CARTESIAN_POINT('',(42.5,0.E+000,32.0096189398));
#2378 = SURFACE_CURVE('',#2379,(#2404,#2432),.PCURVE_S1.);
#2379 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2380,#2381,#2382,#2383,#2384,
    #2385,#2386,#2387,#2388,#2389,#2390,#2391,#2392,#2393,#2394,#2395,
    #2396,#2397,#2398,#2399,#2400,#2401,#2402,#2403),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165632,7.85828166598,
    10.7238180637,13.5836590149,16.4911855364,20.3877609237,
    22.3658108252),.UNSPECIFIED.);
#2380 = CARTESIAN_POINT('',(42.5,0.E+000,42.0096189398));
#2381 = CARTESIAN_POINT('',(42.9671982537,0.E+000,42.0096189398));
#2382 = CARTESIAN_POINT('',(43.4679854668,0.E+000,41.9550492597));
#2383 = CARTESIAN_POINT('',(43.9911230323,0.E+000,41.8300366822));
#2384 = CARTESIAN_POINT('',(44.9800614342,0.E+000,41.4369328146));
#2385 = CARTESIAN_POINT('',(45.8809047407,0.E+000,40.7516043032));
#2386 = CARTESIAN_POINT('',(46.2686263317,0.E+000,40.364382748));
#2387 = CARTESIAN_POINT('',(46.8620880278,0.E+000,39.5771103155));
#2388 = CARTESIAN_POINT('',(47.2518403645,0.E+000,38.6548082046));
#2389 = CARTESIAN_POINT('',(47.3779193365,0.E+000,38.2328403825));
#2390 = CARTESIAN_POINT('',(47.5354809915,0.E+000,37.3659059762));
#2391 = CARTESIAN_POINT('',(47.501400762,0.E+000,36.4832186373));
#2392 = CARTESIAN_POINT('',(47.4357485667,0.E+000,36.04656827));
#2393 = CARTESIAN_POINT('',(47.2088457881,0.E+000,35.1909768206));
#2394 = CARTESIAN_POINT('',(46.807130669,0.E+000,34.4138643184));
#2395 = CARTESIAN_POINT('',(46.564219085,0.E+000,34.0493057582));
#2396 = CARTESIAN_POINT('',(45.916301294,0.E+000,33.2740699026));
#2397 = CARTESIAN_POINT('',(45.1246556495,0.E+000,32.7000963378));
#2398 = CARTESIAN_POINT('',(44.6424482051,0.E+000,32.4458689217));
#2399 = CARTESIAN_POINT('',(43.9048189333,0.E+000,32.1733265057));
#2400 = CARTESIAN_POINT('',(43.1888550914,0.E+000,32.04840017));
#2401 = CARTESIAN_POINT('',(42.9524313854,0.E+000,32.0219857115));
#2402 = CARTESIAN_POINT('',(42.7224096698,0.E+000,32.0096189398));
#2403 = CARTESIAN_POINT('',(42.5,0.E+000,32.0096189398));
#2404 = PCURVE('',#1963,#2405);
#2405 = DEFINITIONAL_REPRESENTATION('',(#2406),#2431);
#2406 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2407,#2408,#2409,#2410,#2411,
    #2412,#2413,#2414,#2415,#2416,#2417,#2418,#2419,#2420,#2421,#2422,
    #2423,#2424,#2425,#2426,#2427,#2428,#2429,#2430),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165632,7.85828166598,
    10.7238180637,13.5836590149,16.4911855364,20.3877609237,
    22.3658108252),.UNSPECIFIED.);
#2407 = CARTESIAN_POINT('',(57.9903810602,42.5));
#2408 = CARTESIAN_POINT('',(57.9903810602,42.9671982537));
#2409 = CARTESIAN_POINT('',(58.0449507403,43.4679854668));
#2410 = CARTESIAN_POINT('',(58.1699633178,43.9911230323));
#2411 = CARTESIAN_POINT('',(58.5630671854,44.9800614342));
#2412 = CARTESIAN_POINT('',(59.2483956968,45.8809047407));
#2413 = CARTESIAN_POINT('',(59.635617252,46.2686263317));
#2414 = CARTESIAN_POINT('',(60.4228896845,46.8620880278));
#2415 = CARTESIAN_POINT('',(61.3451917954,47.2518403645));
#2416 = CARTESIAN_POINT('',(61.7671596175,47.3779193365));
#2417 = CARTESIAN_POINT('',(62.6340940238,47.5354809915));
#2418 = CARTESIAN_POINT('',(63.5167813627,47.501400762));
#2419 = CARTESIAN_POINT('',(63.95343173,47.4357485667));
#2420 = CARTESIAN_POINT('',(64.8090231794,47.2088457881));
#2421 = CARTESIAN_POINT('',(65.5861356816,46.807130669));
#2422 = CARTESIAN_POINT('',(65.9506942418,46.564219085));
#2423 = CARTESIAN_POINT('',(66.7259300974,45.916301294));
#2424 = CARTESIAN_POINT('',(67.2999036622,45.1246556495));
#2425 = CARTESIAN_POINT('',(67.5541310783,44.6424482051));
#2426 = CARTESIAN_POINT('',(67.8266734943,43.9048189333));
#2427 = CARTESIAN_POINT('',(67.95159983,43.1888550914));
#2428 = CARTESIAN_POINT('',(67.9780142885,42.9524313854));
#2429 = CARTESIAN_POINT('',(67.9903810602,42.7224096698));
#2430 = CARTESIAN_POINT('',(67.9903810602,42.5));
#2431 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2432 = PCURVE('',#2433,#2442);
#2433 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2434,#2435,#2436,#2437)
    ,(#2438,#2439,#2440,#2441
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2434 = CARTESIAN_POINT('',(42.5,10.,32.00961894));
#2435 = CARTESIAN_POINT('',(52.5,10.,32.00961894));
#2436 = CARTESIAN_POINT('',(52.5,10.,42.00961894));
#2437 = CARTESIAN_POINT('',(42.5,10.,42.00961894));
#2438 = CARTESIAN_POINT('',(42.5,0.E+000,32.00961894));
#2439 = CARTESIAN_POINT('',(52.5,0.E+000,32.00961894));
#2440 = CARTESIAN_POINT('',(52.5,0.E+000,42.00961894));
#2441 = CARTESIAN_POINT('',(42.5,0.E+000,42.00961894));
#2442 = DEFINITIONAL_REPRESENTATION('',(#2443),#2491);
#2443 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2444,#2445,#2446,#2447,#2448,
    #2449,#2450,#2451,#2452,#2453,#2454,#2455,#2456,#2457,#2458,#2459,
    #2460,#2461,#2462,#2463,#2464,#2465,#2466,#2467,#2468,#2469,#2470,
    #2471,#2472,#2473,#2474,#2475,#2476,#2477,#2478,#2479,#2480,#2481,
    #2482,#2483,#2484,#2485,#2486,#2487,#2488,#2489,#2490),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313882391,
    1.016627764782,1.524941647173,2.033255529564,2.541569411955,
    3.049883294345,3.558197176736,4.066511059127,4.574824941518,
    5.083138823909,5.5914527063,6.099766588691,6.608080471082,
    7.116394353473,7.624708235864,8.133022118255,8.641336000645,
    9.149649883036,9.657963765427,10.166277647818,10.674591530209,
    11.1829054126,11.691219294991,12.199533177382,12.707847059773,
    13.216160942164,13.724474824555,14.232788706945,14.741102589336,
    15.249416471727,15.757730354118,16.266044236509,16.7743581189,
    17.282672001291,17.790985883682,18.299299766073,18.807613648464,
    19.315927530855,19.824241413245,20.332555295636,20.840869178027,
    21.349183060418,21.857496942809,22.3658108252),
  .QUASI_UNIFORM_KNOTS.);
#2444 = CARTESIAN_POINT('',(10.000998004,30.));
#2445 = CARTESIAN_POINT('',(10.000998004,29.71421386473));
#2446 = CARTESIAN_POINT('',(10.000998004,29.148976272343));
#2447 = CARTESIAN_POINT('',(10.000998004,28.320341045137));
#2448 = CARTESIAN_POINT('',(10.000998004,27.511224152571));
#2449 = CARTESIAN_POINT('',(10.000998004,26.721642605677));
#2450 = CARTESIAN_POINT('',(10.000998004,25.951409907321));
#2451 = CARTESIAN_POINT('',(10.000998004,25.200126446802));
#2452 = CARTESIAN_POINT('',(10.000998004,24.467219020533));
#2453 = CARTESIAN_POINT('',(10.000998004,23.751979083143));
#2454 = CARTESIAN_POINT('',(10.000998004,23.053639417136));
#2455 = CARTESIAN_POINT('',(10.000998004,22.371311355221));
#2456 = CARTESIAN_POINT('',(10.000998004,21.703926016379));
#2457 = CARTESIAN_POINT('',(10.000998004,21.050316044609));
#2458 = CARTESIAN_POINT('',(10.000998004,20.409255206124));
#2459 = CARTESIAN_POINT('',(10.000998004,19.779500799029));
#2460 = CARTESIAN_POINT('',(10.000998004,19.159817461882));
#2461 = CARTESIAN_POINT('',(10.000998004,18.549037988407));
#2462 = CARTESIAN_POINT('',(10.000998004,17.945942144676));
#2463 = CARTESIAN_POINT('',(10.000998004,17.349215021909));
#2464 = CARTESIAN_POINT('',(10.000998004,16.757562965883));
#2465 = CARTESIAN_POINT('',(10.000998004,16.169688650255));
#2466 = CARTESIAN_POINT('',(10.000998004,15.584299524584));
#2467 = CARTESIAN_POINT('',(10.000998004,15.000102349713));
#2468 = CARTESIAN_POINT('',(10.000998004,14.41591095074));
#2469 = CARTESIAN_POINT('',(10.000998004,13.830503841967));
#2470 = CARTESIAN_POINT('',(10.000998004,13.24262595249));
#2471 = CARTESIAN_POINT('',(10.000998004,12.650998045143));
#2472 = CARTESIAN_POINT('',(10.000998004,12.054322432743));
#2473 = CARTESIAN_POINT('',(10.000998004,11.451287736281));
#2474 = CARTESIAN_POINT('',(10.000998004,10.840593660521));
#2475 = CARTESIAN_POINT('',(10.000998004,10.220965417485));
#2476 = CARTESIAN_POINT('',(10.000998004,9.591155847716));
#2477 = CARTESIAN_POINT('',(10.000998004,8.949949241796));
#2478 = CARTESIAN_POINT('',(10.000998004,8.296178712958));
#2479 = CARTESIAN_POINT('',(10.000998004,7.628713143093));
#2480 = CARTESIAN_POINT('',(10.000998004,6.946419418445));
#2481 = CARTESIAN_POINT('',(10.000998004,6.248219065189));
#2482 = CARTESIAN_POINT('',(10.000998004,5.533123490298));
#2483 = CARTESIAN_POINT('',(10.000998004,4.8002673082));
#2484 = CARTESIAN_POINT('',(10.000998004,4.048935541973));
#2485 = CARTESIAN_POINT('',(10.000998004,3.278586269626));
#2486 = CARTESIAN_POINT('',(10.000998004,2.488870495423));
#2487 = CARTESIAN_POINT('',(10.000998004,1.679678017969));
#2488 = CARTESIAN_POINT('',(10.000998004,0.851022750739));
#2489 = CARTESIAN_POINT('',(10.000998004,0.285786201188));
#2490 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2491 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2492 = ORIENTED_EDGE('',*,*,#2493,.T.);
#2493 = EDGE_CURVE('',#2376,#2374,#2494,.T.);
#2494 = SURFACE_CURVE('',#2495,(#2520,#2548),.PCURVE_S1.);
#2495 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2496,#2497,#2498,#2499,#2500,
    #2501,#2502,#2503,#2504,#2505,#2506,#2507,#2508,#2509,#2510,#2511,
    #2512,#2513,#2514,#2515,#2516,#2517,#2518,#2519),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513162148,7.85828163111,
    10.7238180489,13.583658992,16.4911855021,20.3877608676,22.3658107326
    ),.UNSPECIFIED.);
#2496 = CARTESIAN_POINT('',(42.5,0.E+000,32.0096189398));
#2497 = CARTESIAN_POINT('',(42.0328017497,0.E+000,32.0096189398));
#2498 = CARTESIAN_POINT('',(41.5320145405,0.E+000,32.0641886193));
#2499 = CARTESIAN_POINT('',(41.0088769576,0.E+000,32.1892012003));
#2500 = CARTESIAN_POINT('',(40.0199385585,0.E+000,32.5823050688));
#2501 = CARTESIAN_POINT('',(39.1190952597,0.E+000,33.2676335757));
#2502 = CARTESIAN_POINT('',(38.7313736684,0.E+000,33.6548551346));
#2503 = CARTESIAN_POINT('',(38.1379119704,0.E+000,34.4421275707));
#2504 = CARTESIAN_POINT('',(37.7481596331,0.E+000,35.3644296843));
#2505 = CARTESIAN_POINT('',(37.6220806643,0.E+000,35.7863974929));
#2506 = CARTESIAN_POINT('',(37.4645190086,0.E+000,36.6533319007));
#2507 = CARTESIAN_POINT('',(37.4985992382,0.E+000,37.5360192423));
#2508 = CARTESIAN_POINT('',(37.5642514339,0.E+000,37.972669614));
#2509 = CARTESIAN_POINT('',(37.7911542119,0.E+000,38.8282610603));
#2510 = CARTESIAN_POINT('',(38.1928693296,0.E+000,39.6053735599));
#2511 = CARTESIAN_POINT('',(38.4357809169,0.E+000,39.9699321238));
#2512 = CARTESIAN_POINT('',(39.0836987058,0.E+000,40.7451679759));
#2513 = CARTESIAN_POINT('',(39.8753443446,0.E+000,41.3191415378));
#2514 = CARTESIAN_POINT('',(40.3575518005,0.E+000,41.5733689617));
#2515 = CARTESIAN_POINT('',(41.0951810662,0.E+000,41.8459113747));
#2516 = CARTESIAN_POINT('',(41.8111448982,0.E+000,41.9708377099));
#2517 = CARTESIAN_POINT('',(42.0475686226,0.E+000,41.9972521686));
#2518 = CARTESIAN_POINT('',(42.2775903341,0.E+000,42.0096189398));
#2519 = CARTESIAN_POINT('',(42.5,0.E+000,42.0096189398));
#2520 = PCURVE('',#1963,#2521);
#2521 = DEFINITIONAL_REPRESENTATION('',(#2522),#2547);
#2522 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2523,#2524,#2525,#2526,#2527,
    #2528,#2529,#2530,#2531,#2532,#2533,#2534,#2535,#2536,#2537,#2538,
    #2539,#2540,#2541,#2542,#2543,#2544,#2545,#2546),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513162148,7.85828163111,
    10.7238180489,13.583658992,16.4911855021,20.3877608676,22.3658107326
    ),.UNSPECIFIED.);
#2523 = CARTESIAN_POINT('',(67.9903810602,42.5));
#2524 = CARTESIAN_POINT('',(67.9903810602,42.0328017497));
#2525 = CARTESIAN_POINT('',(67.9358113807,41.5320145405));
#2526 = CARTESIAN_POINT('',(67.8107987997,41.0088769576));
#2527 = CARTESIAN_POINT('',(67.4176949312,40.0199385585));
#2528 = CARTESIAN_POINT('',(66.7323664243,39.1190952597));
#2529 = CARTESIAN_POINT('',(66.3451448654,38.7313736684));
#2530 = CARTESIAN_POINT('',(65.5578724293,38.1379119704));
#2531 = CARTESIAN_POINT('',(64.6355703157,37.7481596331));
#2532 = CARTESIAN_POINT('',(64.2136025071,37.6220806643));
#2533 = CARTESIAN_POINT('',(63.3466680993,37.4645190086));
#2534 = CARTESIAN_POINT('',(62.4639807577,37.4985992382));
#2535 = CARTESIAN_POINT('',(62.027330386,37.5642514339));
#2536 = CARTESIAN_POINT('',(61.1717389397,37.7911542119));
#2537 = CARTESIAN_POINT('',(60.3946264401,38.1928693296));
#2538 = CARTESIAN_POINT('',(60.0300678762,38.4357809169));
#2539 = CARTESIAN_POINT('',(59.2548320241,39.0836987058));
#2540 = CARTESIAN_POINT('',(58.6808584622,39.8753443446));
#2541 = CARTESIAN_POINT('',(58.4266310383,40.3575518005));
#2542 = CARTESIAN_POINT('',(58.1540886253,41.0951810662));
#2543 = CARTESIAN_POINT('',(58.0291622901,41.8111448982));
#2544 = CARTESIAN_POINT('',(58.0027478314,42.0475686226));
#2545 = CARTESIAN_POINT('',(57.9903810602,42.2775903341));
#2546 = CARTESIAN_POINT('',(57.9903810602,42.5));
#2547 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2548 = PCURVE('',#2549,#2558);
#2549 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2550,#2551,#2552,#2553)
    ,(#2554,#2555,#2556,#2557
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2550 = CARTESIAN_POINT('',(42.5,10.,42.00961894));
#2551 = CARTESIAN_POINT('',(32.5,10.,42.00961894));
#2552 = CARTESIAN_POINT('',(32.5,10.,32.00961894));
#2553 = CARTESIAN_POINT('',(42.5,10.,32.00961894));
#2554 = CARTESIAN_POINT('',(42.5,0.E+000,42.00961894));
#2555 = CARTESIAN_POINT('',(32.5,0.E+000,42.00961894));
#2556 = CARTESIAN_POINT('',(32.5,0.E+000,32.00961894));
#2557 = CARTESIAN_POINT('',(42.5,0.E+000,32.00961894));
#2558 = DEFINITIONAL_REPRESENTATION('',(#2559),#2607);
#2559 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2560,#2561,#2562,#2563,#2564,
    #2565,#2566,#2567,#2568,#2569,#2570,#2571,#2572,#2573,#2574,#2575,
    #2576,#2577,#2578,#2579,#2580,#2581,#2582,#2583,#2584,#2585,#2586,
    #2587,#2588,#2589,#2590,#2591,#2592,#2593,#2594,#2595,#2596,#2597,
    #2598,#2599,#2600,#2601,#2602,#2603,#2604,#2605,#2606),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880286,
    1.016627760573,1.524941640859,2.033255521145,2.541569401432,
    3.049883281718,3.558197162005,4.066511042291,4.574824922577,
    5.083138802864,5.59145268315,6.099766563436,6.608080443723,
    7.116394324009,7.624708204295,8.133022084582,8.641335964868,
    9.149649845155,9.657963725441,10.166277605727,10.674591486014,
    11.1829053663,11.691219246586,12.199533126873,12.707847007159,
    13.216160887445,13.724474767732,14.232788648018,14.741102528305,
    15.249416408591,15.757730288877,16.266044169164,16.77435804945,
    17.282671929736,17.790985810023,18.299299690309,18.807613570595,
    19.315927450882,19.824241331168,20.332555211455,20.840869091741,
    21.349182972027,21.857496852314,22.3658107326),
  .QUASI_UNIFORM_KNOTS.);
#2560 = CARTESIAN_POINT('',(10.000998004,30.));
#2561 = CARTESIAN_POINT('',(10.000998004,29.714213865937));
#2562 = CARTESIAN_POINT('',(10.000998004,29.148976274665));
#2563 = CARTESIAN_POINT('',(10.000998004,28.320341045234));
#2564 = CARTESIAN_POINT('',(10.000998004,27.511224145495));
#2565 = CARTESIAN_POINT('',(10.000998004,26.721642589108));
#2566 = CARTESIAN_POINT('',(10.000998004,25.951409881938));
#2567 = CARTESIAN_POINT('',(10.000998004,25.200126415948));
#2568 = CARTESIAN_POINT('',(10.000998004,24.467218988867));
#2569 = CARTESIAN_POINT('',(10.000998004,23.751979054917));
#2570 = CARTESIAN_POINT('',(10.000998004,23.053639393732));
#2571 = CARTESIAN_POINT('',(10.000998004,22.371311336103));
#2572 = CARTESIAN_POINT('',(10.000998004,21.7039260005));
#2573 = CARTESIAN_POINT('',(10.000998004,21.050316030914));
#2574 = CARTESIAN_POINT('',(10.000998004,20.409255194012));
#2575 = CARTESIAN_POINT('',(10.000998004,19.779500788414));
#2576 = CARTESIAN_POINT('',(10.000998004,19.159817453332));
#2577 = CARTESIAN_POINT('',(10.000998004,18.549037981764));
#2578 = CARTESIAN_POINT('',(10.000998004,17.945942143431));
#2579 = CARTESIAN_POINT('',(10.000998004,17.349215031035));
#2580 = CARTESIAN_POINT('',(10.000998004,16.757562986474));
#2581 = CARTESIAN_POINT('',(10.000998004,16.16968867911));
#2582 = CARTESIAN_POINT('',(10.000998004,15.584299556328));
#2583 = CARTESIAN_POINT('',(10.000998004,15.000102383364));
#2584 = CARTESIAN_POINT('',(10.000998004,14.415910984911));
#2585 = CARTESIAN_POINT('',(10.000998004,13.830503875548));
#2586 = CARTESIAN_POINT('',(10.000998004,13.242625985881));
#2587 = CARTESIAN_POINT('',(10.000998004,12.650998079982));
#2588 = CARTESIAN_POINT('',(10.000998004,12.05432247075));
#2589 = CARTESIAN_POINT('',(10.000998004,11.451287776763));
#2590 = CARTESIAN_POINT('',(10.000998004,10.840593701457));
#2591 = CARTESIAN_POINT('',(10.000998004,10.220965457727));
#2592 = CARTESIAN_POINT('',(10.000998004,9.59115588787));
#2593 = CARTESIAN_POINT('',(10.000998004,8.949949283992));
#2594 = CARTESIAN_POINT('',(10.000998004,8.296178759194));
#2595 = CARTESIAN_POINT('',(10.000998004,7.628713192038));
#2596 = CARTESIAN_POINT('',(10.000998004,6.94641946689));
#2597 = CARTESIAN_POINT('',(10.000998004,6.248219110218));
#2598 = CARTESIAN_POINT('',(10.000998004,5.533123530703));
#2599 = CARTESIAN_POINT('',(10.000998004,4.800267345232));
#2600 = CARTESIAN_POINT('',(10.000998004,4.048935579088));
#2601 = CARTESIAN_POINT('',(10.000998004,3.278586311318));
#2602 = CARTESIAN_POINT('',(10.000998004,2.488870543964));
#2603 = CARTESIAN_POINT('',(10.000998004,1.679678045349));
#2604 = CARTESIAN_POINT('',(10.000998004,0.8510227524));
#2605 = CARTESIAN_POINT('',(10.000998004,0.285786197317));
#2606 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2607 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2608 = FACE_BOUND('',#2609,.T.);
#2609 = EDGE_LOOP('',(#2610,#2730));
#2610 = ORIENTED_EDGE('',*,*,#2611,.T.);
#2611 = EDGE_CURVE('',#2612,#2614,#2616,.T.);
#2612 = VERTEX_POINT('',#2613);
#2613 = CARTESIAN_POINT('',(42.5,0.E+000,67.9903810602));
#2614 = VERTEX_POINT('',#2615);
#2615 = CARTESIAN_POINT('',(42.5,0.E+000,57.9903810602));
#2616 = SURFACE_CURVE('',#2617,(#2642,#2670),.PCURVE_S1.);
#2617 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2618,#2619,#2620,#2621,#2622,
    #2623,#2624,#2625,#2626,#2627,#2628,#2629,#2630,#2631,#2632,#2633,
    #2634,#2635,#2636,#2637,#2638,#2639,#2640,#2641),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513162009,7.85828162953,
    10.7238180471,13.5836589903,16.4911855013,20.3877608671,
    22.3658107334),.UNSPECIFIED.);
#2618 = CARTESIAN_POINT('',(42.5,0.E+000,67.9903810602));
#2619 = CARTESIAN_POINT('',(42.9671982501,0.E+000,67.9903810602));
#2620 = CARTESIAN_POINT('',(43.467985459,0.E+000,67.9358113808));
#2621 = CARTESIAN_POINT('',(43.9911230428,0.E+000,67.8107987996));
#2622 = CARTESIAN_POINT('',(44.9800614416,0.E+000,67.417694931));
#2623 = CARTESIAN_POINT('',(45.8809047403,0.E+000,66.7323664244));
#2624 = CARTESIAN_POINT('',(46.2686263317,0.E+000,66.3451448654));
#2625 = CARTESIAN_POINT('',(46.8620880296,0.E+000,65.5578724293));
#2626 = CARTESIAN_POINT('',(47.2518403668,0.E+000,64.6355703158));
#2627 = CARTESIAN_POINT('',(47.3779193357,0.E+000,64.213602507));
#2628 = CARTESIAN_POINT('',(47.5354809914,0.E+000,63.3466680992));
#2629 = CARTESIAN_POINT('',(47.5014007618,0.E+000,62.4639807577));
#2630 = CARTESIAN_POINT('',(47.4357485661,0.E+000,62.027330386));
#2631 = CARTESIAN_POINT('',(47.2088457881,0.E+000,61.1717389395));
#2632 = CARTESIAN_POINT('',(46.8071306702,0.E+000,60.3946264398));
#2633 = CARTESIAN_POINT('',(46.5642190833,0.E+000,60.0300678764));
#2634 = CARTESIAN_POINT('',(45.9163012943,0.E+000,59.2548320242));
#2635 = CARTESIAN_POINT('',(45.1246556554,0.E+000,58.6808584622));
#2636 = CARTESIAN_POINT('',(44.6424481995,0.E+000,58.4266310383));
#2637 = CARTESIAN_POINT('',(43.9048189337,0.E+000,58.1540886252));
#2638 = CARTESIAN_POINT('',(43.1888551014,0.E+000,58.0291622901));
#2639 = CARTESIAN_POINT('',(42.9524313776,0.E+000,58.0027478314));
#2640 = CARTESIAN_POINT('',(42.7224096661,0.E+000,57.9903810602));
#2641 = CARTESIAN_POINT('',(42.5,0.E+000,57.9903810602));
#2642 = PCURVE('',#1963,#2643);
#2643 = DEFINITIONAL_REPRESENTATION('',(#2644),#2669);
#2644 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2645,#2646,#2647,#2648,#2649,
    #2650,#2651,#2652,#2653,#2654,#2655,#2656,#2657,#2658,#2659,#2660,
    #2661,#2662,#2663,#2664,#2665,#2666,#2667,#2668),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513162009,7.85828162953,
    10.7238180471,13.5836589903,16.4911855013,20.3877608671,
    22.3658107334),.UNSPECIFIED.);
#2645 = CARTESIAN_POINT('',(32.0096189398,42.5));
#2646 = CARTESIAN_POINT('',(32.0096189398,42.9671982501));
#2647 = CARTESIAN_POINT('',(32.0641886192,43.467985459));
#2648 = CARTESIAN_POINT('',(32.1892012004,43.9911230428));
#2649 = CARTESIAN_POINT('',(32.582305069,44.9800614416));
#2650 = CARTESIAN_POINT('',(33.2676335756,45.8809047403));
#2651 = CARTESIAN_POINT('',(33.6548551346,46.2686263317));
#2652 = CARTESIAN_POINT('',(34.4421275707,46.8620880296));
#2653 = CARTESIAN_POINT('',(35.3644296842,47.2518403668));
#2654 = CARTESIAN_POINT('',(35.786397493,47.3779193357));
#2655 = CARTESIAN_POINT('',(36.6533319008,47.5354809914));
#2656 = CARTESIAN_POINT('',(37.5360192423,47.5014007618));
#2657 = CARTESIAN_POINT('',(37.972669614,47.4357485661));
#2658 = CARTESIAN_POINT('',(38.8282610605,47.2088457881));
#2659 = CARTESIAN_POINT('',(39.6053735602,46.8071306702));
#2660 = CARTESIAN_POINT('',(39.9699321236,46.5642190833));
#2661 = CARTESIAN_POINT('',(40.7451679758,45.9163012943));
#2662 = CARTESIAN_POINT('',(41.3191415378,45.1246556554));
#2663 = CARTESIAN_POINT('',(41.5733689617,44.6424481995));
#2664 = CARTESIAN_POINT('',(41.8459113748,43.9048189337));
#2665 = CARTESIAN_POINT('',(41.9708377099,43.1888551014));
#2666 = CARTESIAN_POINT('',(41.9972521686,42.9524313776));
#2667 = CARTESIAN_POINT('',(42.0096189398,42.7224096661));
#2668 = CARTESIAN_POINT('',(42.0096189398,42.5));
#2669 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2670 = PCURVE('',#2671,#2680);
#2671 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2672,#2673,#2674,#2675)
    ,(#2676,#2677,#2678,#2679
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2672 = CARTESIAN_POINT('',(42.5,10.,57.99038106));
#2673 = CARTESIAN_POINT('',(52.5,10.,57.99038106));
#2674 = CARTESIAN_POINT('',(52.5,10.,67.99038106));
#2675 = CARTESIAN_POINT('',(42.5,10.,67.99038106));
#2676 = CARTESIAN_POINT('',(42.5,0.E+000,57.99038106));
#2677 = CARTESIAN_POINT('',(52.5,0.E+000,57.99038106));
#2678 = CARTESIAN_POINT('',(52.5,0.E+000,67.99038106));
#2679 = CARTESIAN_POINT('',(42.5,0.E+000,67.99038106));
#2680 = DEFINITIONAL_REPRESENTATION('',(#2681),#2729);
#2681 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2682,#2683,#2684,#2685,#2686,
    #2687,#2688,#2689,#2690,#2691,#2692,#2693,#2694,#2695,#2696,#2697,
    #2698,#2699,#2700,#2701,#2702,#2703,#2704,#2705,#2706,#2707,#2708,
    #2709,#2710,#2711,#2712,#2713,#2714,#2715,#2716,#2717,#2718,#2719,
    #2720,#2721,#2722,#2723,#2724,#2725,#2726,#2727,#2728),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880305,
    1.016627760609,1.524941640914,2.033255521218,2.541569401523,
    3.049883281827,3.558197162132,4.066511042436,4.574824922741,
    5.083138803045,5.59145268335,6.099766563655,6.608080443959,
    7.116394324264,7.624708204568,8.133022084873,8.641335965177,
    9.149649845482,9.657963725786,10.166277606091,10.674591486395,
    11.1829053667,11.691219247005,12.199533127309,12.707847007614,
    13.216160887918,13.724474768223,14.232788648527,14.741102528832,
    15.249416409136,15.757730289441,16.266044169745,16.77435805005,
    17.282671930355,17.790985810659,18.299299690964,18.807613571268,
    19.315927451573,19.824241331877,20.332555212182,20.840869092486,
    21.349182972791,21.857496853095,22.3658107334),
  .QUASI_UNIFORM_KNOTS.);
#2682 = CARTESIAN_POINT('',(10.000998004,30.));
#2683 = CARTESIAN_POINT('',(10.000998004,29.714213865971));
#2684 = CARTESIAN_POINT('',(10.000998004,29.148976274717));
#2685 = CARTESIAN_POINT('',(10.000998004,28.320341045137));
#2686 = CARTESIAN_POINT('',(10.000998004,27.511224144985));
#2687 = CARTESIAN_POINT('',(10.000998004,26.721642588047));
#2688 = CARTESIAN_POINT('',(10.000998004,25.951409880337));
#2689 = CARTESIAN_POINT('',(10.000998004,25.200126413953));
#2690 = CARTESIAN_POINT('',(10.000998004,24.467218986691));
#2691 = CARTESIAN_POINT('',(10.000998004,23.751979052749));
#2692 = CARTESIAN_POINT('',(10.000998004,23.053639391603));
#2693 = CARTESIAN_POINT('',(10.000998004,22.37131133398));
#2694 = CARTESIAN_POINT('',(10.000998004,21.703925998355));
#2695 = CARTESIAN_POINT('',(10.000998004,21.050316028729));
#2696 = CARTESIAN_POINT('',(10.000998004,20.409255191791));
#2697 = CARTESIAN_POINT('',(10.000998004,19.779500786179));
#2698 = CARTESIAN_POINT('',(10.000998004,19.159817451111));
#2699 = CARTESIAN_POINT('',(10.000998004,18.549037979584));
#2700 = CARTESIAN_POINT('',(10.000998004,17.945942141233));
#2701 = CARTESIAN_POINT('',(10.000998004,17.349215028728));
#2702 = CARTESIAN_POINT('',(10.000998004,16.757562984029));
#2703 = CARTESIAN_POINT('',(10.000998004,16.16968867657));
#2704 = CARTESIAN_POINT('',(10.000998004,15.584299553772));
#2705 = CARTESIAN_POINT('',(10.000998004,15.000102380823));
#2706 = CARTESIAN_POINT('',(10.000998004,14.415910982381));
#2707 = CARTESIAN_POINT('',(10.000998004,13.830503873011));
#2708 = CARTESIAN_POINT('',(10.000998004,13.242625983313));
#2709 = CARTESIAN_POINT('',(10.000998004,12.650998077366));
#2710 = CARTESIAN_POINT('',(10.000998004,12.054322468057));
#2711 = CARTESIAN_POINT('',(10.000998004,11.451287774064));
#2712 = CARTESIAN_POINT('',(10.000998004,10.840593698998));
#2713 = CARTESIAN_POINT('',(10.000998004,10.220965455649));
#2714 = CARTESIAN_POINT('',(10.000998004,9.591155886117));
#2715 = CARTESIAN_POINT('',(10.000998004,8.949949282326));
#2716 = CARTESIAN_POINT('',(10.000998004,8.296178757472));
#2717 = CARTESIAN_POINT('',(10.000998004,7.628713190284));
#2718 = CARTESIAN_POINT('',(10.000998004,6.946419465101));
#2719 = CARTESIAN_POINT('',(10.000998004,6.248219108403));
#2720 = CARTESIAN_POINT('',(10.000998004,5.533123528866));
#2721 = CARTESIAN_POINT('',(10.000998004,4.800267343376));
#2722 = CARTESIAN_POINT('',(10.000998004,4.048935577202));
#2723 = CARTESIAN_POINT('',(10.000998004,3.278586309373));
#2724 = CARTESIAN_POINT('',(10.000998004,2.488870541882));
#2725 = CARTESIAN_POINT('',(10.000998004,1.679678044077));
#2726 = CARTESIAN_POINT('',(10.000998004,0.851022752257));
#2727 = CARTESIAN_POINT('',(10.000998004,0.285786197451));
#2728 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2729 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2730 = ORIENTED_EDGE('',*,*,#2731,.T.);
#2731 = EDGE_CURVE('',#2614,#2612,#2732,.T.);
#2732 = SURFACE_CURVE('',#2733,(#2758,#2786),.PCURVE_S1.);
#2733 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2734,#2735,#2736,#2737,#2738,
    #2739,#2740,#2741,#2742,#2743,#2744,#2745,#2746,#2747,#2748,#2749,
    #2750,#2751,#2752,#2753,#2754,#2755,#2756,#2757),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165736,7.85828166679,
    10.7238180644,13.5836590156,16.491185538,20.3877609254,22.3658108266
    ),.UNSPECIFIED.);
#2734 = CARTESIAN_POINT('',(42.5,0.E+000,57.9903810602));
#2735 = CARTESIAN_POINT('',(42.0328017462,0.E+000,57.9903810602));
#2736 = CARTESIAN_POINT('',(41.5320145329,0.E+000,58.0449507403));
#2737 = CARTESIAN_POINT('',(41.0088769679,0.E+000,58.1699633177));
#2738 = CARTESIAN_POINT('',(40.019938566,0.E+000,58.5630671853));
#2739 = CARTESIAN_POINT('',(39.1190952593,0.E+000,59.2483956968));
#2740 = CARTESIAN_POINT('',(38.7313736682,0.E+000,59.635617252));
#2741 = CARTESIAN_POINT('',(38.1379119722,0.E+000,60.4228896845));
#2742 = CARTESIAN_POINT('',(37.7481596355,0.E+000,61.3451917954));
#2743 = CARTESIAN_POINT('',(37.6220806636,0.E+000,61.7671596175));
#2744 = CARTESIAN_POINT('',(37.4645190085,0.E+000,62.6340940238));
#2745 = CARTESIAN_POINT('',(37.498599238,0.E+000,63.5167813627));
#2746 = CARTESIAN_POINT('',(37.5642514333,0.E+000,63.95343173));
#2747 = CARTESIAN_POINT('',(37.7911542119,0.E+000,64.8090231795));
#2748 = CARTESIAN_POINT('',(38.1928693311,0.E+000,65.5861356819));
#2749 = CARTESIAN_POINT('',(38.4357809149,0.E+000,65.9506942416));
#2750 = CARTESIAN_POINT('',(39.0836987059,0.E+000,66.7259300973));
#2751 = CARTESIAN_POINT('',(39.8753443505,0.E+000,67.2999036622));
#2752 = CARTESIAN_POINT('',(40.3575517948,0.E+000,67.5541310783));
#2753 = CARTESIAN_POINT('',(41.0951810667,0.E+000,67.8266734943));
#2754 = CARTESIAN_POINT('',(41.8111449084,0.E+000,67.95159983));
#2755 = CARTESIAN_POINT('',(42.0475686146,0.E+000,67.9780142885));
#2756 = CARTESIAN_POINT('',(42.2775903302,0.E+000,67.9903810602));
#2757 = CARTESIAN_POINT('',(42.5,0.E+000,67.9903810602));
#2758 = PCURVE('',#1963,#2759);
#2759 = DEFINITIONAL_REPRESENTATION('',(#2760),#2785);
#2760 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2761,#2762,#2763,#2764,#2765,
    #2766,#2767,#2768,#2769,#2770,#2771,#2772,#2773,#2774,#2775,#2776,
    #2777,#2778,#2779,#2780,#2781,#2782,#2783,#2784),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165736,7.85828166679,
    10.7238180644,13.5836590156,16.491185538,20.3877609254,22.3658108266
    ),.UNSPECIFIED.);
#2761 = CARTESIAN_POINT('',(42.0096189398,42.5));
#2762 = CARTESIAN_POINT('',(42.0096189398,42.0328017462));
#2763 = CARTESIAN_POINT('',(41.9550492597,41.5320145329));
#2764 = CARTESIAN_POINT('',(41.8300366823,41.0088769679));
#2765 = CARTESIAN_POINT('',(41.4369328147,40.019938566));
#2766 = CARTESIAN_POINT('',(40.7516043032,39.1190952593));
#2767 = CARTESIAN_POINT('',(40.364382748,38.7313736682));
#2768 = CARTESIAN_POINT('',(39.5771103155,38.1379119722));
#2769 = CARTESIAN_POINT('',(38.6548082046,37.7481596355));
#2770 = CARTESIAN_POINT('',(38.2328403825,37.6220806636));
#2771 = CARTESIAN_POINT('',(37.3659059762,37.4645190085));
#2772 = CARTESIAN_POINT('',(36.4832186373,37.498599238));
#2773 = CARTESIAN_POINT('',(36.04656827,37.5642514333));
#2774 = CARTESIAN_POINT('',(35.1909768205,37.7911542119));
#2775 = CARTESIAN_POINT('',(34.4138643181,38.1928693311));
#2776 = CARTESIAN_POINT('',(34.0493057584,38.4357809149));
#2777 = CARTESIAN_POINT('',(33.2740699027,39.0836987059));
#2778 = CARTESIAN_POINT('',(32.7000963378,39.8753443505));
#2779 = CARTESIAN_POINT('',(32.4458689217,40.3575517948));
#2780 = CARTESIAN_POINT('',(32.1733265057,41.0951810667));
#2781 = CARTESIAN_POINT('',(32.04840017,41.8111449084));
#2782 = CARTESIAN_POINT('',(32.0219857115,42.0475686146));
#2783 = CARTESIAN_POINT('',(32.0096189398,42.2775903302));
#2784 = CARTESIAN_POINT('',(32.0096189398,42.5));
#2785 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2786 = PCURVE('',#2787,#2796);
#2787 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2788,#2789,#2790,#2791)
    ,(#2792,#2793,#2794,#2795
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2788 = CARTESIAN_POINT('',(42.5,10.,67.99038106));
#2789 = CARTESIAN_POINT('',(32.5,10.,67.99038106));
#2790 = CARTESIAN_POINT('',(32.5,10.,57.99038106));
#2791 = CARTESIAN_POINT('',(42.5,10.,57.99038106));
#2792 = CARTESIAN_POINT('',(42.5,0.E+000,67.99038106));
#2793 = CARTESIAN_POINT('',(32.5,0.E+000,67.99038106));
#2794 = CARTESIAN_POINT('',(32.5,0.E+000,57.99038106));
#2795 = CARTESIAN_POINT('',(42.5,0.E+000,57.99038106));
#2796 = DEFINITIONAL_REPRESENTATION('',(#2797),#2845);
#2797 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2798,#2799,#2800,#2801,#2802,
    #2803,#2804,#2805,#2806,#2807,#2808,#2809,#2810,#2811,#2812,#2813,
    #2814,#2815,#2816,#2817,#2818,#2819,#2820,#2821,#2822,#2823,#2824,
    #2825,#2826,#2827,#2828,#2829,#2830,#2831,#2832,#2833,#2834,#2835,
    #2836,#2837,#2838,#2839,#2840,#2841,#2842,#2843,#2844),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313882423,
    1.016627764845,1.524941647268,2.033255529691,2.541569412114,
    3.049883294536,3.558197176959,4.066511059382,4.574824941805,
    5.083138824227,5.59145270665,6.099766589073,6.608080471495,
    7.116394353918,7.624708236341,8.133022118764,8.641336001186,
    9.149649883609,9.657963766032,10.166277648455,10.674591530877,
    11.1829054133,11.691219295723,12.199533178145,12.707847060568,
    13.216160942991,13.724474825414,14.232788707836,14.741102590259,
    15.249416472682,15.757730355105,16.266044237527,16.77435811995,
    17.282672002373,17.790985884795,18.299299767218,18.807613649641,
    19.315927532064,19.824241414486,20.332555296909,20.840869179332,
    21.349183061755,21.857496944177,22.3658108266),
  .QUASI_UNIFORM_KNOTS.);
#2798 = CARTESIAN_POINT('',(10.000998004,30.));
#2799 = CARTESIAN_POINT('',(10.000998004,29.714213864711));
#2800 = CARTESIAN_POINT('',(10.000998004,29.148976272306));
#2801 = CARTESIAN_POINT('',(10.000998004,28.320341045158));
#2802 = CARTESIAN_POINT('',(10.000998004,27.511224152804));
#2803 = CARTESIAN_POINT('',(10.000998004,26.721642606208));
#2804 = CARTESIAN_POINT('',(10.000998004,25.951409908151));
#2805 = CARTESIAN_POINT('',(10.000998004,25.200126447846));
#2806 = CARTESIAN_POINT('',(10.000998004,24.46721902166));
#2807 = CARTESIAN_POINT('',(10.000998004,23.751979084226));
#2808 = CARTESIAN_POINT('',(10.000998004,23.053639418127));
#2809 = CARTESIAN_POINT('',(10.000998004,22.371311356097));
#2810 = CARTESIAN_POINT('',(10.000998004,21.70392601713));
#2811 = CARTESIAN_POINT('',(10.000998004,21.050316045245));
#2812 = CARTESIAN_POINT('',(10.000998004,20.409255206665));
#2813 = CARTESIAN_POINT('',(10.000998004,19.779500799499));
#2814 = CARTESIAN_POINT('',(10.000998004,19.159817462296));
#2815 = CARTESIAN_POINT('',(10.000998004,18.54903798876));
#2816 = CARTESIAN_POINT('',(10.000998004,17.945942144969));
#2817 = CARTESIAN_POINT('',(10.000998004,17.349215022149));
#2818 = CARTESIAN_POINT('',(10.000998004,16.757562966067));
#2819 = CARTESIAN_POINT('',(10.000998004,16.169688650378));
#2820 = CARTESIAN_POINT('',(10.000998004,15.584299524649));
#2821 = CARTESIAN_POINT('',(10.000998004,15.000102349728));
#2822 = CARTESIAN_POINT('',(10.000998004,14.415910950711));
#2823 = CARTESIAN_POINT('',(10.000998004,13.830503841901));
#2824 = CARTESIAN_POINT('',(10.000998004,13.242625952391));
#2825 = CARTESIAN_POINT('',(10.000998004,12.650998045019));
#2826 = CARTESIAN_POINT('',(10.000998004,12.054322432562));
#2827 = CARTESIAN_POINT('',(10.000998004,11.451287736086));
#2828 = CARTESIAN_POINT('',(10.000998004,10.840593660536));
#2829 = CARTESIAN_POINT('',(10.000998004,10.220965417836));
#2830 = CARTESIAN_POINT('',(10.000998004,9.59115584836));
#2831 = CARTESIAN_POINT('',(10.000998004,8.949949242541));
#2832 = CARTESIAN_POINT('',(10.000998004,8.296178713686));
#2833 = CARTESIAN_POINT('',(10.000998004,7.628713143824));
#2834 = CARTESIAN_POINT('',(10.000998004,6.946419419182));
#2835 = CARTESIAN_POINT('',(10.000998004,6.248219065942));
#2836 = CARTESIAN_POINT('',(10.000998004,5.53312349106));
#2837 = CARTESIAN_POINT('',(10.000998004,4.800267308953));
#2838 = CARTESIAN_POINT('',(10.000998004,4.048935542703));
#2839 = CARTESIAN_POINT('',(10.000998004,3.278586270346));
#2840 = CARTESIAN_POINT('',(10.000998004,2.488870496242));
#2841 = CARTESIAN_POINT('',(10.000998004,1.679678018568));
#2842 = CARTESIAN_POINT('',(10.000998004,0.851022750959));
#2843 = CARTESIAN_POINT('',(10.000998004,0.285786201224));
#2844 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2845 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2846 = FACE_BOUND('',#2847,.T.);
#2847 = EDGE_LOOP('',(#2848,#2968));
#2848 = ORIENTED_EDGE('',*,*,#2849,.T.);
#2849 = EDGE_CURVE('',#2850,#2852,#2854,.T.);
#2850 = VERTEX_POINT('',#2851);
#2851 = CARTESIAN_POINT('',(20.,0.E+000,55.));
#2852 = VERTEX_POINT('',#2853);
#2853 = CARTESIAN_POINT('',(20.,0.E+000,45.));
#2854 = SURFACE_CURVE('',#2855,(#2880,#2908),.PCURVE_S1.);
#2855 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2856,#2857,#2858,#2859,#2860,
    #2861,#2862,#2863,#2864,#2865,#2866,#2867,#2868,#2869,#2870,#2871,
    #2872,#2873,#2874,#2875,#2876,#2877,#2878,#2879),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164501,7.85828164811,
    10.7238180535,13.5836589949,16.4911855021,20.3877608686,
    22.3658107291),.UNSPECIFIED.);
#2856 = CARTESIAN_POINT('',(20.,0.E+000,55.));
#2857 = CARTESIAN_POINT('',(20.4671982525,0.E+000,55.));
#2858 = CARTESIAN_POINT('',(20.9679854642,0.E+000,54.9454303202));
#2859 = CARTESIAN_POINT('',(21.4911230351,0.E+000,54.8204177413));
#2860 = CARTESIAN_POINT('',(22.4800614343,0.E+000,54.4273138745));
#2861 = CARTESIAN_POINT('',(23.38090474,0.E+000,53.7419853637));
#2862 = CARTESIAN_POINT('',(23.768626333,0.E+000,53.3547638067));
#2863 = CARTESIAN_POINT('',(24.3620880288,0.E+000,52.5674913739));
#2864 = CARTESIAN_POINT('',(24.7518403653,0.E+000,51.6451892624));
#2865 = CARTESIAN_POINT('',(24.8779193361,0.E+000,51.2232214433));
#2866 = CARTESIAN_POINT('',(25.0354809914,0.E+000,50.3562870372));
#2867 = CARTESIAN_POINT('',(25.001400762,0.E+000,49.4735996986));
#2868 = CARTESIAN_POINT('',(24.9357485659,0.E+000,49.0369493251));
#2869 = CARTESIAN_POINT('',(24.708845788,0.E+000,48.1813578797));
#2870 = CARTESIAN_POINT('',(24.307130671,0.E+000,47.4042453809));
#2871 = CARTESIAN_POINT('',(24.0642190822,0.E+000,47.039686815));
#2872 = CARTESIAN_POINT('',(23.416301294,0.E+000,46.2644509638));
#2873 = CARTESIAN_POINT('',(22.6246556551,0.E+000,45.6904774019));
#2874 = CARTESIAN_POINT('',(22.1424481995,0.E+000,45.436249978));
#2875 = CARTESIAN_POINT('',(21.4048189351,0.E+000,45.1637075654));
#2876 = CARTESIAN_POINT('',(20.6888551023,0.E+000,45.0387812301));
#2877 = CARTESIAN_POINT('',(20.4524313759,0.E+000,45.0123667712));
#2878 = CARTESIAN_POINT('',(20.2224096652,0.E+000,45.));
#2879 = CARTESIAN_POINT('',(20.,0.E+000,45.));
#2880 = PCURVE('',#1963,#2881);
#2881 = DEFINITIONAL_REPRESENTATION('',(#2882),#2907);
#2882 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2883,#2884,#2885,#2886,#2887,
    #2888,#2889,#2890,#2891,#2892,#2893,#2894,#2895,#2896,#2897,#2898,
    #2899,#2900,#2901,#2902,#2903,#2904,#2905,#2906),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164501,7.85828164811,
    10.7238180535,13.5836589949,16.4911855021,20.3877608686,
    22.3658107291),.UNSPECIFIED.);
#2883 = CARTESIAN_POINT('',(45.,20.));
#2884 = CARTESIAN_POINT('',(45.,20.4671982525));
#2885 = CARTESIAN_POINT('',(45.0545696798,20.9679854642));
#2886 = CARTESIAN_POINT('',(45.1795822587,21.4911230351));
#2887 = CARTESIAN_POINT('',(45.5726861255,22.4800614343));
#2888 = CARTESIAN_POINT('',(46.2580146363,23.38090474));
#2889 = CARTESIAN_POINT('',(46.6452361933,23.768626333));
#2890 = CARTESIAN_POINT('',(47.4325086261,24.3620880288));
#2891 = CARTESIAN_POINT('',(48.3548107376,24.7518403653));
#2892 = CARTESIAN_POINT('',(48.7767785567,24.8779193361));
#2893 = CARTESIAN_POINT('',(49.6437129628,25.0354809914));
#2894 = CARTESIAN_POINT('',(50.5264003014,25.001400762));
#2895 = CARTESIAN_POINT('',(50.9630506749,24.9357485659));
#2896 = CARTESIAN_POINT('',(51.8186421203,24.708845788));
#2897 = CARTESIAN_POINT('',(52.5957546191,24.307130671));
#2898 = CARTESIAN_POINT('',(52.960313185,24.0642190822));
#2899 = CARTESIAN_POINT('',(53.7355490362,23.416301294));
#2900 = CARTESIAN_POINT('',(54.3095225981,22.6246556551));
#2901 = CARTESIAN_POINT('',(54.563750022,22.1424481995));
#2902 = CARTESIAN_POINT('',(54.8362924346,21.4048189351));
#2903 = CARTESIAN_POINT('',(54.9612187699,20.6888551023));
#2904 = CARTESIAN_POINT('',(54.9876332288,20.4524313759));
#2905 = CARTESIAN_POINT('',(55.,20.2224096652));
#2906 = CARTESIAN_POINT('',(55.,20.));
#2907 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2908 = PCURVE('',#2909,#2918);
#2909 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#2910,#2911,#2912,#2913)
    ,(#2914,#2915,#2916,#2917
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#2910 = CARTESIAN_POINT('',(20.,10.,45.));
#2911 = CARTESIAN_POINT('',(30.,10.,45.));
#2912 = CARTESIAN_POINT('',(30.,10.,55.));
#2913 = CARTESIAN_POINT('',(20.,10.,55.));
#2914 = CARTESIAN_POINT('',(20.,0.E+000,45.));
#2915 = CARTESIAN_POINT('',(30.,0.E+000,45.));
#2916 = CARTESIAN_POINT('',(30.,0.E+000,55.));
#2917 = CARTESIAN_POINT('',(20.,0.E+000,55.));
#2918 = DEFINITIONAL_REPRESENTATION('',(#2919),#2967);
#2919 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#2920,#2921,#2922,#2923,#2924,
    #2925,#2926,#2927,#2928,#2929,#2930,#2931,#2932,#2933,#2934,#2935,
    #2936,#2937,#2938,#2939,#2940,#2941,#2942,#2943,#2944,#2945,#2946,
    #2947,#2948,#2949,#2950,#2951,#2952,#2953,#2954,#2955,#2956,#2957,
    #2958,#2959,#2960,#2961,#2962,#2963,#2964,#2965,#2966),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880207,
    1.016627760414,1.52494164062,2.033255520827,2.541569401034,
    3.049883281241,3.558197161448,4.066511041655,4.574824921861,
    5.083138802068,5.591452682275,6.099766562482,6.608080442689,
    7.116394322895,7.624708203102,8.133022083309,8.641335963516,
    9.149649843723,9.65796372393,10.166277604136,10.674591484343,
    11.18290536455,11.691219244757,12.199533124964,12.70784700517,
    13.216160885377,13.724474765584,14.232788645791,14.741102525998,
    15.249416406205,15.757730286411,16.266044166618,16.774358046825,
    17.282671927032,17.790985807239,18.299299687445,18.807613567652,
    19.315927447859,19.824241328066,20.332555208273,20.84086908848,
    21.349182968686,21.857496848893,22.3658107291),
  .QUASI_UNIFORM_KNOTS.);
#2920 = CARTESIAN_POINT('',(10.000998004,30.));
#2921 = CARTESIAN_POINT('',(10.000998004,29.714213866027));
#2922 = CARTESIAN_POINT('',(10.000998004,29.148976275785));
#2923 = CARTESIAN_POINT('',(10.000998004,28.320341050449));
#2924 = CARTESIAN_POINT('',(10.000998004,27.511224158067));
#2925 = CARTESIAN_POINT('',(10.000998004,26.721642610512));
#2926 = CARTESIAN_POINT('',(10.000998004,25.951409911596));
#2927 = CARTESIAN_POINT('',(10.000998004,25.20012645143));
#2928 = CARTESIAN_POINT('',(10.000998004,24.467219026753));
#2929 = CARTESIAN_POINT('',(10.000998004,23.751979091918));
#2930 = CARTESIAN_POINT('',(10.000998004,23.053639428221));
#2931 = CARTESIAN_POINT('',(10.000998004,22.371311367746));
#2932 = CARTESIAN_POINT('',(10.000998004,21.70392602968));
#2933 = CARTESIAN_POINT('',(10.000998004,21.050316058458));
#2934 = CARTESIAN_POINT('',(10.000998004,20.409255220807));
#2935 = CARTESIAN_POINT('',(10.000998004,19.779500815162));
#2936 = CARTESIAN_POINT('',(10.000998004,19.159817480195));
#2937 = CARTESIAN_POINT('',(10.000998004,18.549038008934));
#2938 = CARTESIAN_POINT('',(10.000998004,17.94594216819));
#2939 = CARTESIAN_POINT('',(10.000998004,17.349215049709));
#2940 = CARTESIAN_POINT('',(10.000998004,16.757562998245));
#2941 = CARTESIAN_POINT('',(10.000998004,16.169688686379));
#2942 = CARTESIAN_POINT('',(10.000998004,15.584299563168));
#2943 = CARTESIAN_POINT('',(10.000998004,15.000102390674));
#2944 = CARTESIAN_POINT('',(10.000998004,14.415910992005));
#2945 = CARTESIAN_POINT('',(10.000998004,13.830503881918));
#2946 = CARTESIAN_POINT('',(10.000998004,13.242625991484));
#2947 = CARTESIAN_POINT('',(10.000998004,12.650998085332));
#2948 = CARTESIAN_POINT('',(10.000998004,12.054322476506));
#2949 = CARTESIAN_POINT('',(10.000998004,11.451287782624));
#2950 = CARTESIAN_POINT('',(10.000998004,10.840593706492));
#2951 = CARTESIAN_POINT('',(10.000998004,10.220965461471));
#2952 = CARTESIAN_POINT('',(10.000998004,9.59115589066));
#2953 = CARTESIAN_POINT('',(10.000998004,8.949949286842));
#2954 = CARTESIAN_POINT('',(10.000998004,8.296178762516));
#2955 = CARTESIAN_POINT('',(10.000998004,7.628713195579));
#2956 = CARTESIAN_POINT('',(10.000998004,6.946419470734));
#2957 = CARTESIAN_POINT('',(10.000998004,6.248219114497));
#2958 = CARTESIAN_POINT('',(10.000998004,5.533123535579));
#2959 = CARTESIAN_POINT('',(10.000998004,4.800267350802));
#2960 = CARTESIAN_POINT('',(10.000998004,4.04893558527));
#2961 = CARTESIAN_POINT('',(10.000998004,3.278586317777));
#2962 = CARTESIAN_POINT('',(10.000998004,2.488870549857));
#2963 = CARTESIAN_POINT('',(10.000998004,1.679678047808));
#2964 = CARTESIAN_POINT('',(10.000998004,0.851022751946));
#2965 = CARTESIAN_POINT('',(10.000998004,0.285786196615));
#2966 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#2967 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#2968 = ORIENTED_EDGE('',*,*,#2969,.T.);
#2969 = EDGE_CURVE('',#2852,#2850,#2970,.T.);
#2970 = SURFACE_CURVE('',#2971,(#2996,#3024),.PCURVE_S1.);
#2971 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2972,#2973,#2974,#2975,#2976,
    #2977,#2978,#2979,#2980,#2981,#2982,#2983,#2984,#2985,#2986,#2987,
    #2988,#2989,#2990,#2991,#2992,#2993,#2994,#2995),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164518,7.85828164919,
    10.7238180549,13.583658997,16.491185504,20.3877608712,22.3658107361)
  ,.UNSPECIFIED.);
#2972 = CARTESIAN_POINT('',(20.,0.E+000,45.));
#2973 = CARTESIAN_POINT('',(19.5328017475,0.E+000,45.));
#2974 = CARTESIAN_POINT('',(19.0320145358,0.E+000,45.0545696798));
#2975 = CARTESIAN_POINT('',(18.508876965,0.E+000,45.1795822587));
#2976 = CARTESIAN_POINT('',(17.5199385656,0.E+000,45.5726861255));
#2977 = CARTESIAN_POINT('',(16.6190952599,0.E+000,46.2580146364));
#2978 = CARTESIAN_POINT('',(16.2313736672,0.E+000,46.645236193));
#2979 = CARTESIAN_POINT('',(15.6379119712,0.E+000,47.432508626));
#2980 = CARTESIAN_POINT('',(15.2481596346,0.E+000,48.3548107377));
#2981 = CARTESIAN_POINT('',(15.122080664,0.E+000,48.7767785566));
#2982 = CARTESIAN_POINT('',(14.9645190086,0.E+000,49.6437129629));
#2983 = CARTESIAN_POINT('',(14.998599238,0.E+000,50.5264003017));
#2984 = CARTESIAN_POINT('',(15.0642514341,0.E+000,50.9630506747));
#2985 = CARTESIAN_POINT('',(15.2911542119,0.E+000,51.8186421202));
#2986 = CARTESIAN_POINT('',(15.692869329,0.E+000,52.5957546191));
#2987 = CARTESIAN_POINT('',(15.9357809178,0.E+000,52.960313185));
#2988 = CARTESIAN_POINT('',(16.583698706,0.E+000,53.7355490363));
#2989 = CARTESIAN_POINT('',(17.3753443451,0.E+000,54.3095225982));
#2990 = CARTESIAN_POINT('',(17.8575518004,0.E+000,54.5637500219));
#2991 = CARTESIAN_POINT('',(18.5951810654,0.E+000,54.8362924348));
#2992 = CARTESIAN_POINT('',(19.3111448987,0.E+000,54.96121877));
#2993 = CARTESIAN_POINT('',(19.5475686231,0.E+000,54.9876332288));
#2994 = CARTESIAN_POINT('',(19.7775903343,0.E+000,55.));
#2995 = CARTESIAN_POINT('',(20.,0.E+000,55.));
#2996 = PCURVE('',#1963,#2997);
#2997 = DEFINITIONAL_REPRESENTATION('',(#2998),#3023);
#2998 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#2999,#3000,#3001,#3002,#3003,
    #3004,#3005,#3006,#3007,#3008,#3009,#3010,#3011,#3012,#3013,#3014,
    #3015,#3016,#3017,#3018,#3019,#3020,#3021,#3022),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164518,7.85828164919,
    10.7238180549,13.583658997,16.491185504,20.3877608712,22.3658107361)
  ,.UNSPECIFIED.);
#2999 = CARTESIAN_POINT('',(55.,20.));
#3000 = CARTESIAN_POINT('',(55.,19.5328017475));
#3001 = CARTESIAN_POINT('',(54.9454303202,19.0320145358));
#3002 = CARTESIAN_POINT('',(54.8204177413,18.508876965));
#3003 = CARTESIAN_POINT('',(54.4273138745,17.5199385656));
#3004 = CARTESIAN_POINT('',(53.7419853636,16.6190952599));
#3005 = CARTESIAN_POINT('',(53.354763807,16.2313736672));
#3006 = CARTESIAN_POINT('',(52.567491374,15.6379119712));
#3007 = CARTESIAN_POINT('',(51.6451892623,15.2481596346));
#3008 = CARTESIAN_POINT('',(51.2232214434,15.122080664));
#3009 = CARTESIAN_POINT('',(50.3562870371,14.9645190086));
#3010 = CARTESIAN_POINT('',(49.4735996983,14.998599238));
#3011 = CARTESIAN_POINT('',(49.0369493253,15.0642514341));
#3012 = CARTESIAN_POINT('',(48.1813578798,15.2911542119));
#3013 = CARTESIAN_POINT('',(47.4042453809,15.692869329));
#3014 = CARTESIAN_POINT('',(47.039686815,15.9357809178));
#3015 = CARTESIAN_POINT('',(46.2644509637,16.583698706));
#3016 = CARTESIAN_POINT('',(45.6904774018,17.3753443451));
#3017 = CARTESIAN_POINT('',(45.4362499781,17.8575518004));
#3018 = CARTESIAN_POINT('',(45.1637075652,18.5951810654));
#3019 = CARTESIAN_POINT('',(45.03878123,19.3111448987));
#3020 = CARTESIAN_POINT('',(45.0123667712,19.5475686231));
#3021 = CARTESIAN_POINT('',(45.,19.7775903343));
#3022 = CARTESIAN_POINT('',(45.,20.));
#3023 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3024 = PCURVE('',#3025,#3034);
#3025 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#3026,#3027,#3028,#3029)
    ,(#3030,#3031,#3032,#3033
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,10.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#3026 = CARTESIAN_POINT('',(20.,10.,55.));
#3027 = CARTESIAN_POINT('',(10.,10.,55.));
#3028 = CARTESIAN_POINT('',(10.,10.,45.));
#3029 = CARTESIAN_POINT('',(20.,10.,45.));
#3030 = CARTESIAN_POINT('',(20.,0.E+000,55.));
#3031 = CARTESIAN_POINT('',(10.,0.E+000,55.));
#3032 = CARTESIAN_POINT('',(10.,0.E+000,45.));
#3033 = CARTESIAN_POINT('',(20.,0.E+000,45.));
#3034 = DEFINITIONAL_REPRESENTATION('',(#3035),#3083);
#3035 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#3036,#3037,#3038,#3039,#3040,
    #3041,#3042,#3043,#3044,#3045,#3046,#3047,#3048,#3049,#3050,#3051,
    #3052,#3053,#3054,#3055,#3056,#3057,#3058,#3059,#3060,#3061,#3062,
    #3063,#3064,#3065,#3066,#3067,#3068,#3069,#3070,#3071,#3072,#3073,
    #3074,#3075,#3076,#3077,#3078,#3079,#3080,#3081,#3082),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880366,
    1.016627760732,1.524941641098,2.033255521464,2.54156940183,
    3.049883282195,3.558197162561,4.066511042927,4.574824923293,
    5.083138803659,5.591452684025,6.099766564391,6.608080444757,
    7.116394325123,7.624708205489,8.133022085855,8.64133596622,
    9.149649846586,9.657963726952,10.166277607318,10.674591487684,
    11.18290536805,11.691219248416,12.199533128782,12.707847009148,
    13.216160889514,13.72447476988,14.232788650245,14.741102530611,
    15.249416410977,15.757730291343,16.266044171709,16.774358052075,
    17.282671932441,17.790985812807,18.299299693173,18.807613573539,
    19.315927453905,19.82424133427,20.332555214636,20.840869095002,
    21.349182975368,21.857496855734,22.3658107361),
  .QUASI_UNIFORM_KNOTS.);
#3036 = CARTESIAN_POINT('',(10.000998004,30.));
#3037 = CARTESIAN_POINT('',(10.000998004,29.714213865947));
#3038 = CARTESIAN_POINT('',(10.000998004,29.148976275557));
#3039 = CARTESIAN_POINT('',(10.000998004,28.320341050023));
#3040 = CARTESIAN_POINT('',(10.000998004,27.51122415747));
#3041 = CARTESIAN_POINT('',(10.000998004,26.721642609755));
#3042 = CARTESIAN_POINT('',(10.000998004,25.951409910678));
#3043 = CARTESIAN_POINT('',(10.000998004,25.200126450341));
#3044 = CARTESIAN_POINT('',(10.000998004,24.467219025485));
#3045 = CARTESIAN_POINT('',(10.000998004,23.751979090475));
#3046 = CARTESIAN_POINT('',(10.000998004,23.053639426626));
#3047 = CARTESIAN_POINT('',(10.000998004,22.371311366117));
#3048 = CARTESIAN_POINT('',(10.000998004,21.70392602813));
#3049 = CARTESIAN_POINT('',(10.000998004,21.05031605703));
#3050 = CARTESIAN_POINT('',(10.000998004,20.409255219457));
#3051 = CARTESIAN_POINT('',(10.000998004,19.779500813775));
#3052 = CARTESIAN_POINT('',(10.000998004,19.159817478642));
#3053 = CARTESIAN_POINT('',(10.000998004,18.549038007162));
#3054 = CARTESIAN_POINT('',(10.000998004,17.94594216629));
#3055 = CARTESIAN_POINT('',(10.000998004,17.349215047768));
#3056 = CARTESIAN_POINT('',(10.000998004,16.75756299627));
#3057 = CARTESIAN_POINT('',(10.000998004,16.169688684297));
#3058 = CARTESIAN_POINT('',(10.000998004,15.584299560881));
#3059 = CARTESIAN_POINT('',(10.000998004,15.000102388171));
#3060 = CARTESIAN_POINT('',(10.000998004,14.415910989471));
#3061 = CARTESIAN_POINT('',(10.000998004,13.83050387949));
#3062 = CARTESIAN_POINT('',(10.000998004,13.242625989149));
#3063 = CARTESIAN_POINT('',(10.000998004,12.650998082929));
#3064 = CARTESIAN_POINT('',(10.000998004,12.054322473875));
#3065 = CARTESIAN_POINT('',(10.000998004,11.451287779753));
#3066 = CARTESIAN_POINT('',(10.000998004,10.840593703357));
#3067 = CARTESIAN_POINT('',(10.000998004,10.220965458061));
#3068 = CARTESIAN_POINT('',(10.000998004,9.591155886969));
#3069 = CARTESIAN_POINT('',(10.000998004,8.949949282871));
#3070 = CARTESIAN_POINT('',(10.000998004,8.296178758235));
#3071 = CARTESIAN_POINT('',(10.000998004,7.628713191002));
#3072 = CARTESIAN_POINT('',(10.000998004,6.946419465936));
#3073 = CARTESIAN_POINT('',(10.000998004,6.248219109545));
#3074 = CARTESIAN_POINT('',(10.000998004,5.533123530503));
#3075 = CARTESIAN_POINT('',(10.000998004,4.800267345576));
#3076 = CARTESIAN_POINT('',(10.000998004,4.048935579824));
#3077 = CARTESIAN_POINT('',(10.000998004,3.278586312029));
#3078 = CARTESIAN_POINT('',(10.000998004,2.488870543834));
#3079 = CARTESIAN_POINT('',(10.000998004,1.67967804453));
#3080 = CARTESIAN_POINT('',(10.000998004,0.851022751719));
#3081 = CARTESIAN_POINT('',(10.000998004,0.285786197044));
#3082 = CARTESIAN_POINT('',(10.000998004,0.E+000));
#3083 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3084 = ADVANCED_FACE('',(#3085),#1991,.T.);
#3085 = FACE_BOUND('',#3086,.T.);
#3086 = EDGE_LOOP('',(#3087,#3088,#3089,#3112,#3140,#3168));
#3087 = ORIENTED_EDGE('',*,*,#1975,.T.);
#3088 = ORIENTED_EDGE('',*,*,#2328,.T.);
#3089 = ORIENTED_EDGE('',*,*,#3090,.T.);
#3090 = EDGE_CURVE('',#2299,#3091,#3093,.T.);
#3091 = VERTEX_POINT('',#3092);
#3092 = CARTESIAN_POINT('',(50.,10.,100.));
#3093 = SURFACE_CURVE('',#3094,(#3098,#3105),.PCURVE_S1.);
#3094 = LINE('',#3095,#3096);
#3095 = CARTESIAN_POINT('',(50.,5.,100.));
#3096 = VECTOR('',#3097,1.);
#3097 = DIRECTION('',(0.E+000,1.,0.E+000));
#3098 = PCURVE('',#1991,#3099);
#3099 = DEFINITIONAL_REPRESENTATION('',(#3100),#3104);
#3100 = LINE('',#3101,#3102);
#3101 = CARTESIAN_POINT('',(50.,5.));
#3102 = VECTOR('',#3103,1.);
#3103 = DIRECTION('',(0.E+000,1.));
#3104 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3105 = PCURVE('',#2316,#3106);
#3106 = DEFINITIONAL_REPRESENTATION('',(#3107),#3111);
#3107 = LINE('',#3108,#3109);
#3108 = CARTESIAN_POINT('',(0.E+000,5.));
#3109 = VECTOR('',#3110,1.);
#3110 = DIRECTION('',(0.E+000,1.));
#3111 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3112 = ORIENTED_EDGE('',*,*,#3113,.T.);
#3113 = EDGE_CURVE('',#3091,#3114,#3116,.T.);
#3114 = VERTEX_POINT('',#3115);
#3115 = CARTESIAN_POINT('',(10.,10.,100.));
#3116 = SURFACE_CURVE('',#3117,(#3121,#3128),.PCURVE_S1.);
#3117 = LINE('',#3118,#3119);
#3118 = CARTESIAN_POINT('',(30.,10.,100.));
#3119 = VECTOR('',#3120,1.);
#3120 = DIRECTION('',(-1.,0.E+000,0.E+000));
#3121 = PCURVE('',#1991,#3122);
#3122 = DEFINITIONAL_REPRESENTATION('',(#3123),#3127);
#3123 = LINE('',#3124,#3125);
#3124 = CARTESIAN_POINT('',(30.,10.));
#3125 = VECTOR('',#3126,1.);
#3126 = DIRECTION('',(-1.,0.E+000));
#3127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3128 = PCURVE('',#3129,#3134);
#3129 = PLANE('',#3130);
#3130 = AXIS2_PLACEMENT_3D('',#3131,#3132,#3133);
#3131 = CARTESIAN_POINT('',(50.,10.,100.));
#3132 = DIRECTION('',(0.E+000,1.,0.E+000));
#3133 = DIRECTION('',(0.E+000,0.E+000,1.));
#3134 = DEFINITIONAL_REPRESENTATION('',(#3135),#3139);
#3135 = LINE('',#3136,#3137);
#3136 = CARTESIAN_POINT('',(0.E+000,-20.));
#3137 = VECTOR('',#3138,1.);
#3138 = DIRECTION('',(0.E+000,-1.));
#3139 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3140 = ORIENTED_EDGE('',*,*,#3141,.T.);
#3141 = EDGE_CURVE('',#3114,#3142,#3144,.T.);
#3142 = VERTEX_POINT('',#3143);
#3143 = CARTESIAN_POINT('',(10.,60.,100.));
#3144 = SURFACE_CURVE('',#3145,(#3149,#3156),.PCURVE_S1.);
#3145 = LINE('',#3146,#3147);
#3146 = CARTESIAN_POINT('',(10.,35.,100.));
#3147 = VECTOR('',#3148,1.);
#3148 = DIRECTION('',(0.E+000,1.,0.E+000));
#3149 = PCURVE('',#1991,#3150);
#3150 = DEFINITIONAL_REPRESENTATION('',(#3151),#3155);
#3151 = LINE('',#3152,#3153);
#3152 = CARTESIAN_POINT('',(10.,35.));
#3153 = VECTOR('',#3154,1.);
#3154 = DIRECTION('',(0.E+000,1.));
#3155 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3156 = PCURVE('',#3157,#3162);
#3157 = PLANE('',#3158);
#3158 = AXIS2_PLACEMENT_3D('',#3159,#3160,#3161);
#3159 = CARTESIAN_POINT('',(10.,10.,100.));
#3160 = DIRECTION('',(1.,0.E+000,0.E+000));
#3161 = DIRECTION('',(0.E+000,0.E+000,-1.));
#3162 = DEFINITIONAL_REPRESENTATION('',(#3163),#3167);
#3163 = LINE('',#3164,#3165);
#3164 = CARTESIAN_POINT('',(0.E+000,25.));
#3165 = VECTOR('',#3166,1.);
#3166 = DIRECTION('',(0.E+000,1.));
#3167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3168 = ORIENTED_EDGE('',*,*,#3169,.T.);
#3169 = EDGE_CURVE('',#3142,#1976,#3170,.T.);
#3170 = SURFACE_CURVE('',#3171,(#3175,#3182),.PCURVE_S1.);
#3171 = LINE('',#3172,#3173);
#3172 = CARTESIAN_POINT('',(5.,60.,100.));
#3173 = VECTOR('',#3174,1.);
#3174 = DIRECTION('',(-1.,0.E+000,0.E+000));
#3175 = PCURVE('',#1991,#3176);
#3176 = DEFINITIONAL_REPRESENTATION('',(#3177),#3181);
#3177 = LINE('',#3178,#3179);
#3178 = CARTESIAN_POINT('',(5.,60.));
#3179 = VECTOR('',#3180,1.);
#3180 = DIRECTION('',(-1.,0.E+000));
#3181 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3182 = PCURVE('',#2019,#3183);
#3183 = DEFINITIONAL_REPRESENTATION('',(#3184),#3188);
#3184 = LINE('',#3185,#3186);
#3185 = CARTESIAN_POINT('',(0.E+000,-5.));
#3186 = VECTOR('',#3187,1.);
#3187 = DIRECTION('',(0.E+000,-1.));
#3188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3189 = ADVANCED_FACE('',(#3190),#2019,.T.);
#3190 = FACE_BOUND('',#3191,.T.);
#3191 = EDGE_LOOP('',(#3192,#3193,#3216,#3237));
#3192 = ORIENTED_EDGE('',*,*,#3169,.F.);
#3193 = ORIENTED_EDGE('',*,*,#3194,.T.);
#3194 = EDGE_CURVE('',#3142,#3195,#3197,.T.);
#3195 = VERTEX_POINT('',#3196);
#3196 = CARTESIAN_POINT('',(10.,60.,0.E+000));
#3197 = SURFACE_CURVE('',#3198,(#3202,#3209),.PCURVE_S1.);
#3198 = LINE('',#3199,#3200);
#3199 = CARTESIAN_POINT('',(10.,60.,50.));
#3200 = VECTOR('',#3201,1.);
#3201 = DIRECTION('',(0.E+000,0.E+000,-1.));
#3202 = PCURVE('',#2019,#3203);
#3203 = DEFINITIONAL_REPRESENTATION('',(#3204),#3208);
#3204 = LINE('',#3205,#3206);
#3205 = CARTESIAN_POINT('',(-50.,0.E+000));
#3206 = VECTOR('',#3207,1.);
#3207 = DIRECTION('',(-1.,0.E+000));
#3208 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3209 = PCURVE('',#3157,#3210);
#3210 = DEFINITIONAL_REPRESENTATION('',(#3211),#3215);
#3211 = LINE('',#3212,#3213);
#3212 = CARTESIAN_POINT('',(50.,50.));
#3213 = VECTOR('',#3214,1.);
#3214 = DIRECTION('',(1.,0.E+000));
#3215 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3216 = ORIENTED_EDGE('',*,*,#3217,.T.);
#3217 = EDGE_CURVE('',#3195,#2004,#3218,.T.);
#3218 = SURFACE_CURVE('',#3219,(#3223,#3230),.PCURVE_S1.);
#3219 = LINE('',#3220,#3221);
#3220 = CARTESIAN_POINT('',(5.,60.,0.E+000));
#3221 = VECTOR('',#3222,1.);
#3222 = DIRECTION('',(-1.,0.E+000,0.E+000));
#3223 = PCURVE('',#2019,#3224);
#3224 = DEFINITIONAL_REPRESENTATION('',(#3225),#3229);
#3225 = LINE('',#3226,#3227);
#3226 = CARTESIAN_POINT('',(-100.,-5.));
#3227 = VECTOR('',#3228,1.);
#3228 = DIRECTION('',(0.E+000,-1.));
#3229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3230 = PCURVE('',#2045,#3231);
#3231 = DEFINITIONAL_REPRESENTATION('',(#3232),#3236);
#3232 = LINE('',#3233,#3234);
#3233 = CARTESIAN_POINT('',(-5.,60.));
#3234 = VECTOR('',#3235,1.);
#3235 = DIRECTION('',(1.,0.E+000));
#3236 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3237 = ORIENTED_EDGE('',*,*,#2003,.F.);
#3238 = ADVANCED_FACE('',(#3239),#2045,.T.);
#3239 = FACE_BOUND('',#3240,.T.);
#3240 = EDGE_LOOP('',(#3241,#3242,#3243,#3266,#3289,#3310));
#3241 = ORIENTED_EDGE('',*,*,#2031,.F.);
#3242 = ORIENTED_EDGE('',*,*,#3217,.F.);
#3243 = ORIENTED_EDGE('',*,*,#3244,.F.);
#3244 = EDGE_CURVE('',#3245,#3195,#3247,.T.);
#3245 = VERTEX_POINT('',#3246);
#3246 = CARTESIAN_POINT('',(10.,10.,0.E+000));
#3247 = SURFACE_CURVE('',#3248,(#3252,#3259),.PCURVE_S1.);
#3248 = LINE('',#3249,#3250);
#3249 = CARTESIAN_POINT('',(10.,35.,0.E+000));
#3250 = VECTOR('',#3251,1.);
#3251 = DIRECTION('',(0.E+000,1.,0.E+000));
#3252 = PCURVE('',#2045,#3253);
#3253 = DEFINITIONAL_REPRESENTATION('',(#3254),#3258);
#3254 = LINE('',#3255,#3256);
#3255 = CARTESIAN_POINT('',(-10.,35.));
#3256 = VECTOR('',#3257,1.);
#3257 = DIRECTION('',(0.E+000,1.));
#3258 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3259 = PCURVE('',#3157,#3260);
#3260 = DEFINITIONAL_REPRESENTATION('',(#3261),#3265);
#3261 = LINE('',#3262,#3263);
#3262 = CARTESIAN_POINT('',(100.,25.));
#3263 = VECTOR('',#3264,1.);
#3264 = DIRECTION('',(0.E+000,1.));
#3265 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3266 = ORIENTED_EDGE('',*,*,#3267,.F.);
#3267 = EDGE_CURVE('',#3268,#3245,#3270,.T.);
#3268 = VERTEX_POINT('',#3269);
#3269 = CARTESIAN_POINT('',(50.,10.,0.E+000));
#3270 = SURFACE_CURVE('',#3271,(#3275,#3282),.PCURVE_S1.);
#3271 = LINE('',#3272,#3273);
#3272 = CARTESIAN_POINT('',(30.,10.,0.E+000));
#3273 = VECTOR('',#3274,1.);
#3274 = DIRECTION('',(-1.,0.E+000,0.E+000));
#3275 = PCURVE('',#2045,#3276);
#3276 = DEFINITIONAL_REPRESENTATION('',(#3277),#3281);
#3277 = LINE('',#3278,#3279);
#3278 = CARTESIAN_POINT('',(-30.,10.));
#3279 = VECTOR('',#3280,1.);
#3280 = DIRECTION('',(1.,0.E+000));
#3281 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3282 = PCURVE('',#3129,#3283);
#3283 = DEFINITIONAL_REPRESENTATION('',(#3284),#3288);
#3284 = LINE('',#3285,#3286);
#3285 = CARTESIAN_POINT('',(-100.,-20.));
#3286 = VECTOR('',#3287,1.);
#3287 = DIRECTION('',(0.E+000,-1.));
#3288 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3289 = ORIENTED_EDGE('',*,*,#3290,.F.);
#3290 = EDGE_CURVE('',#2301,#3268,#3291,.T.);
#3291 = SURFACE_CURVE('',#3292,(#3296,#3303),.PCURVE_S1.);
#3292 = LINE('',#3293,#3294);
#3293 = CARTESIAN_POINT('',(50.,5.,0.E+000));
#3294 = VECTOR('',#3295,1.);
#3295 = DIRECTION('',(0.E+000,1.,0.E+000));
#3296 = PCURVE('',#2045,#3297);
#3297 = DEFINITIONAL_REPRESENTATION('',(#3298),#3302);
#3298 = LINE('',#3299,#3300);
#3299 = CARTESIAN_POINT('',(-50.,5.));
#3300 = VECTOR('',#3301,1.);
#3301 = DIRECTION('',(0.E+000,1.));
#3302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3303 = PCURVE('',#2316,#3304);
#3304 = DEFINITIONAL_REPRESENTATION('',(#3305),#3309);
#3305 = LINE('',#3306,#3307);
#3306 = CARTESIAN_POINT('',(100.,5.));
#3307 = VECTOR('',#3308,1.);
#3308 = DIRECTION('',(0.E+000,1.));
#3309 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3310 = ORIENTED_EDGE('',*,*,#2350,.F.);
#3311 = ADVANCED_FACE('',(#3312),#2119,.T.);
#3312 = FACE_BOUND('',#3313,.T.);
#3313 = EDGE_LOOP('',(#3314,#3341,#3361,#3362));
#3314 = ORIENTED_EDGE('',*,*,#3315,.F.);
#3315 = EDGE_CURVE('',#3316,#3318,#3320,.T.);
#3316 = VERTEX_POINT('',#3317);
#3317 = CARTESIAN_POINT('',(10.,40.,55.));
#3318 = VERTEX_POINT('',#3319);
#3319 = CARTESIAN_POINT('',(10.,40.,45.));
#3320 = SURFACE_CURVE('',#3321,(#3326,#3333),.PCURVE_S1.);
#3321 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3322,#3323,#3324,#3325),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3322 = CARTESIAN_POINT('',(10.,40.,55.));
#3323 = CARTESIAN_POINT('',(10.,50.,55.));
#3324 = CARTESIAN_POINT('',(10.,50.,45.));
#3325 = CARTESIAN_POINT('',(10.,40.,45.));
#3326 = PCURVE('',#2119,#3327);
#3327 = DEFINITIONAL_REPRESENTATION('',(#3328),#3332);
#3328 = LINE('',#3329,#3330);
#3329 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3330 = VECTOR('',#3331,1.);
#3331 = DIRECTION('',(0.E+000,1.));
#3332 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3333 = PCURVE('',#3157,#3334);
#3334 = DEFINITIONAL_REPRESENTATION('',(#3335),#3340);
#3335 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3336,#3337,#3338,#3339),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3336 = CARTESIAN_POINT('',(45.,30.));
#3337 = CARTESIAN_POINT('',(45.,40.));
#3338 = CARTESIAN_POINT('',(55.,40.));
#3339 = CARTESIAN_POINT('',(55.,30.));
#3340 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3341 = ORIENTED_EDGE('',*,*,#3342,.T.);
#3342 = EDGE_CURVE('',#3316,#2062,#3343,.T.);
#3343 = SURFACE_CURVE('',#3344,(#3347,#3354),.PCURVE_S1.);
#3344 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3345,#3346),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3345 = CARTESIAN_POINT('',(10.,40.,55.));
#3346 = CARTESIAN_POINT('',(0.E+000,40.,55.));
#3347 = PCURVE('',#2119,#3348);
#3348 = DEFINITIONAL_REPRESENTATION('',(#3349),#3353);
#3349 = LINE('',#3350,#3351);
#3350 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3351 = VECTOR('',#3352,1.);
#3352 = DIRECTION('',(1.,0.E+000));
#3353 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3354 = PCURVE('',#2235,#3355);
#3355 = DEFINITIONAL_REPRESENTATION('',(#3356),#3360);
#3356 = LINE('',#3357,#3358);
#3357 = CARTESIAN_POINT('',(0.E+000,30.));
#3358 = VECTOR('',#3359,1.);
#3359 = DIRECTION('',(1.,0.E+000));
#3360 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3361 = ORIENTED_EDGE('',*,*,#2059,.F.);
#3362 = ORIENTED_EDGE('',*,*,#3363,.F.);
#3363 = EDGE_CURVE('',#3318,#2060,#3364,.T.);
#3364 = SURFACE_CURVE('',#3365,(#3368,#3375),.PCURVE_S1.);
#3365 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3366,#3367),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3366 = CARTESIAN_POINT('',(10.,40.,45.));
#3367 = CARTESIAN_POINT('',(0.E+000,40.,45.));
#3368 = PCURVE('',#2119,#3369);
#3369 = DEFINITIONAL_REPRESENTATION('',(#3370),#3374);
#3370 = LINE('',#3371,#3372);
#3371 = CARTESIAN_POINT('',(0.E+000,30.));
#3372 = VECTOR('',#3373,1.);
#3373 = DIRECTION('',(1.,0.E+000));
#3374 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3375 = PCURVE('',#2235,#3376);
#3376 = DEFINITIONAL_REPRESENTATION('',(#3377),#3381);
#3377 = LINE('',#3378,#3379);
#3378 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3379 = VECTOR('',#3380,1.);
#3380 = DIRECTION('',(1.,0.E+000));
#3381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3382 = ADVANCED_FACE('',(#3383),#2235,.T.);
#3383 = FACE_BOUND('',#3384,.T.);
#3384 = EDGE_LOOP('',(#3385,#3408,#3409,#3410));
#3385 = ORIENTED_EDGE('',*,*,#3386,.F.);
#3386 = EDGE_CURVE('',#3318,#3316,#3387,.T.);
#3387 = SURFACE_CURVE('',#3388,(#3393,#3400),.PCURVE_S1.);
#3388 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3389,#3390,#3391,#3392),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3389 = CARTESIAN_POINT('',(10.,40.,45.));
#3390 = CARTESIAN_POINT('',(10.,30.,45.));
#3391 = CARTESIAN_POINT('',(10.,30.,55.));
#3392 = CARTESIAN_POINT('',(10.,40.,55.));
#3393 = PCURVE('',#2235,#3394);
#3394 = DEFINITIONAL_REPRESENTATION('',(#3395),#3399);
#3395 = LINE('',#3396,#3397);
#3396 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3397 = VECTOR('',#3398,1.);
#3398 = DIRECTION('',(0.E+000,1.));
#3399 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3400 = PCURVE('',#3157,#3401);
#3401 = DEFINITIONAL_REPRESENTATION('',(#3402),#3407);
#3402 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3403,#3404,#3405,#3406),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3403 = CARTESIAN_POINT('',(55.,30.));
#3404 = CARTESIAN_POINT('',(55.,20.));
#3405 = CARTESIAN_POINT('',(45.,20.));
#3406 = CARTESIAN_POINT('',(45.,30.));
#3407 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3408 = ORIENTED_EDGE('',*,*,#3363,.T.);
#3409 = ORIENTED_EDGE('',*,*,#2179,.F.);
#3410 = ORIENTED_EDGE('',*,*,#3342,.F.);
#3411 = ADVANCED_FACE('',(#3412),#2316,.T.);
#3412 = FACE_BOUND('',#3413,.T.);
#3413 = EDGE_LOOP('',(#3414,#3435,#3436,#3437));
#3414 = ORIENTED_EDGE('',*,*,#3415,.F.);
#3415 = EDGE_CURVE('',#3091,#3268,#3416,.T.);
#3416 = SURFACE_CURVE('',#3417,(#3421,#3428),.PCURVE_S1.);
#3417 = LINE('',#3418,#3419);
#3418 = CARTESIAN_POINT('',(50.,10.,50.));
#3419 = VECTOR('',#3420,1.);
#3420 = DIRECTION('',(0.E+000,0.E+000,-1.));
#3421 = PCURVE('',#2316,#3422);
#3422 = DEFINITIONAL_REPRESENTATION('',(#3423),#3427);
#3423 = LINE('',#3424,#3425);
#3424 = CARTESIAN_POINT('',(50.,10.));
#3425 = VECTOR('',#3426,1.);
#3426 = DIRECTION('',(1.,0.E+000));
#3427 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3428 = PCURVE('',#3129,#3429);
#3429 = DEFINITIONAL_REPRESENTATION('',(#3430),#3434);
#3430 = LINE('',#3431,#3432);
#3431 = CARTESIAN_POINT('',(-50.,0.E+000));
#3432 = VECTOR('',#3433,1.);
#3433 = DIRECTION('',(-1.,0.E+000));
#3434 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3435 = ORIENTED_EDGE('',*,*,#3090,.F.);
#3436 = ORIENTED_EDGE('',*,*,#2298,.T.);
#3437 = ORIENTED_EDGE('',*,*,#3290,.T.);
#3438 = ADVANCED_FACE('',(#3439),#2433,.T.);
#3439 = FACE_BOUND('',#3440,.T.);
#3440 = EDGE_LOOP('',(#3441,#3468,#3488,#3489));
#3441 = ORIENTED_EDGE('',*,*,#3442,.F.);
#3442 = EDGE_CURVE('',#3443,#3445,#3447,.T.);
#3443 = VERTEX_POINT('',#3444);
#3444 = CARTESIAN_POINT('',(42.5,10.,32.00961894));
#3445 = VERTEX_POINT('',#3446);
#3446 = CARTESIAN_POINT('',(42.5,10.,42.00961894));
#3447 = SURFACE_CURVE('',#3448,(#3453,#3460),.PCURVE_S1.);
#3448 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3449,#3450,#3451,#3452),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3449 = CARTESIAN_POINT('',(42.5,10.,32.00961894));
#3450 = CARTESIAN_POINT('',(52.5,10.,32.00961894));
#3451 = CARTESIAN_POINT('',(52.5,10.,42.00961894));
#3452 = CARTESIAN_POINT('',(42.5,10.,42.00961894));
#3453 = PCURVE('',#2433,#3454);
#3454 = DEFINITIONAL_REPRESENTATION('',(#3455),#3459);
#3455 = LINE('',#3456,#3457);
#3456 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3457 = VECTOR('',#3458,1.);
#3458 = DIRECTION('',(0.E+000,1.));
#3459 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3460 = PCURVE('',#3129,#3461);
#3461 = DEFINITIONAL_REPRESENTATION('',(#3462),#3467);
#3462 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3463,#3464,#3465,#3466),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3463 = CARTESIAN_POINT('',(-67.99038106,-7.5));
#3464 = CARTESIAN_POINT('',(-67.99038106,2.5));
#3465 = CARTESIAN_POINT('',(-57.99038106,2.5));
#3466 = CARTESIAN_POINT('',(-57.99038106,-7.5));
#3467 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3468 = ORIENTED_EDGE('',*,*,#3469,.T.);
#3469 = EDGE_CURVE('',#3443,#2376,#3470,.T.);
#3470 = SURFACE_CURVE('',#3471,(#3474,#3481),.PCURVE_S1.);
#3471 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3472,#3473),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3472 = CARTESIAN_POINT('',(42.5,10.,32.00961894));
#3473 = CARTESIAN_POINT('',(42.5,0.E+000,32.00961894));
#3474 = PCURVE('',#2433,#3475);
#3475 = DEFINITIONAL_REPRESENTATION('',(#3476),#3480);
#3476 = LINE('',#3477,#3478);
#3477 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3478 = VECTOR('',#3479,1.);
#3479 = DIRECTION('',(1.,0.E+000));
#3480 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3481 = PCURVE('',#2549,#3482);
#3482 = DEFINITIONAL_REPRESENTATION('',(#3483),#3487);
#3483 = LINE('',#3484,#3485);
#3484 = CARTESIAN_POINT('',(0.E+000,30.));
#3485 = VECTOR('',#3486,1.);
#3486 = DIRECTION('',(1.,0.E+000));
#3487 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3488 = ORIENTED_EDGE('',*,*,#2373,.F.);
#3489 = ORIENTED_EDGE('',*,*,#3490,.F.);
#3490 = EDGE_CURVE('',#3445,#2374,#3491,.T.);
#3491 = SURFACE_CURVE('',#3492,(#3495,#3502),.PCURVE_S1.);
#3492 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3493,#3494),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3493 = CARTESIAN_POINT('',(42.5,10.,42.00961894));
#3494 = CARTESIAN_POINT('',(42.5,0.E+000,42.00961894));
#3495 = PCURVE('',#2433,#3496);
#3496 = DEFINITIONAL_REPRESENTATION('',(#3497),#3501);
#3497 = LINE('',#3498,#3499);
#3498 = CARTESIAN_POINT('',(0.E+000,30.));
#3499 = VECTOR('',#3500,1.);
#3500 = DIRECTION('',(1.,0.E+000));
#3501 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3502 = PCURVE('',#2549,#3503);
#3503 = DEFINITIONAL_REPRESENTATION('',(#3504),#3508);
#3504 = LINE('',#3505,#3506);
#3505 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3506 = VECTOR('',#3507,1.);
#3507 = DIRECTION('',(1.,0.E+000));
#3508 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3509 = ADVANCED_FACE('',(#3510),#2549,.T.);
#3510 = FACE_BOUND('',#3511,.T.);
#3511 = EDGE_LOOP('',(#3512,#3535,#3536,#3537));
#3512 = ORIENTED_EDGE('',*,*,#3513,.F.);
#3513 = EDGE_CURVE('',#3445,#3443,#3514,.T.);
#3514 = SURFACE_CURVE('',#3515,(#3520,#3527),.PCURVE_S1.);
#3515 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3516,#3517,#3518,#3519),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3516 = CARTESIAN_POINT('',(42.5,10.,42.00961894));
#3517 = CARTESIAN_POINT('',(32.5,10.,42.00961894));
#3518 = CARTESIAN_POINT('',(32.5,10.,32.00961894));
#3519 = CARTESIAN_POINT('',(42.5,10.,32.00961894));
#3520 = PCURVE('',#2549,#3521);
#3521 = DEFINITIONAL_REPRESENTATION('',(#3522),#3526);
#3522 = LINE('',#3523,#3524);
#3523 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3524 = VECTOR('',#3525,1.);
#3525 = DIRECTION('',(0.E+000,1.));
#3526 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3527 = PCURVE('',#3129,#3528);
#3528 = DEFINITIONAL_REPRESENTATION('',(#3529),#3534);
#3529 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3530,#3531,#3532,#3533),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3530 = CARTESIAN_POINT('',(-57.99038106,-7.5));
#3531 = CARTESIAN_POINT('',(-57.99038106,-17.5));
#3532 = CARTESIAN_POINT('',(-67.99038106,-17.5));
#3533 = CARTESIAN_POINT('',(-67.99038106,-7.5));
#3534 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3535 = ORIENTED_EDGE('',*,*,#3490,.T.);
#3536 = ORIENTED_EDGE('',*,*,#2493,.F.);
#3537 = ORIENTED_EDGE('',*,*,#3469,.F.);
#3538 = ADVANCED_FACE('',(#3539),#2671,.T.);
#3539 = FACE_BOUND('',#3540,.T.);
#3540 = EDGE_LOOP('',(#3541,#3568,#3588,#3589));
#3541 = ORIENTED_EDGE('',*,*,#3542,.F.);
#3542 = EDGE_CURVE('',#3543,#3545,#3547,.T.);
#3543 = VERTEX_POINT('',#3544);
#3544 = CARTESIAN_POINT('',(42.5,10.,57.99038106));
#3545 = VERTEX_POINT('',#3546);
#3546 = CARTESIAN_POINT('',(42.5,10.,67.99038106));
#3547 = SURFACE_CURVE('',#3548,(#3553,#3560),.PCURVE_S1.);
#3548 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3549,#3550,#3551,#3552),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3549 = CARTESIAN_POINT('',(42.5,10.,57.99038106));
#3550 = CARTESIAN_POINT('',(52.5,10.,57.99038106));
#3551 = CARTESIAN_POINT('',(52.5,10.,67.99038106));
#3552 = CARTESIAN_POINT('',(42.5,10.,67.99038106));
#3553 = PCURVE('',#2671,#3554);
#3554 = DEFINITIONAL_REPRESENTATION('',(#3555),#3559);
#3555 = LINE('',#3556,#3557);
#3556 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3557 = VECTOR('',#3558,1.);
#3558 = DIRECTION('',(0.E+000,1.));
#3559 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3560 = PCURVE('',#3129,#3561);
#3561 = DEFINITIONAL_REPRESENTATION('',(#3562),#3567);
#3562 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3563,#3564,#3565,#3566),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3563 = CARTESIAN_POINT('',(-42.00961894,-7.5));
#3564 = CARTESIAN_POINT('',(-42.00961894,2.5));
#3565 = CARTESIAN_POINT('',(-32.00961894,2.5));
#3566 = CARTESIAN_POINT('',(-32.00961894,-7.5));
#3567 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3568 = ORIENTED_EDGE('',*,*,#3569,.T.);
#3569 = EDGE_CURVE('',#3543,#2614,#3570,.T.);
#3570 = SURFACE_CURVE('',#3571,(#3574,#3581),.PCURVE_S1.);
#3571 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3572,#3573),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3572 = CARTESIAN_POINT('',(42.5,10.,57.99038106));
#3573 = CARTESIAN_POINT('',(42.5,0.E+000,57.99038106));
#3574 = PCURVE('',#2671,#3575);
#3575 = DEFINITIONAL_REPRESENTATION('',(#3576),#3580);
#3576 = LINE('',#3577,#3578);
#3577 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3578 = VECTOR('',#3579,1.);
#3579 = DIRECTION('',(1.,0.E+000));
#3580 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3581 = PCURVE('',#2787,#3582);
#3582 = DEFINITIONAL_REPRESENTATION('',(#3583),#3587);
#3583 = LINE('',#3584,#3585);
#3584 = CARTESIAN_POINT('',(0.E+000,30.));
#3585 = VECTOR('',#3586,1.);
#3586 = DIRECTION('',(1.,0.E+000));
#3587 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3588 = ORIENTED_EDGE('',*,*,#2611,.F.);
#3589 = ORIENTED_EDGE('',*,*,#3590,.F.);
#3590 = EDGE_CURVE('',#3545,#2612,#3591,.T.);
#3591 = SURFACE_CURVE('',#3592,(#3595,#3602),.PCURVE_S1.);
#3592 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3593,#3594),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3593 = CARTESIAN_POINT('',(42.5,10.,67.99038106));
#3594 = CARTESIAN_POINT('',(42.5,0.E+000,67.99038106));
#3595 = PCURVE('',#2671,#3596);
#3596 = DEFINITIONAL_REPRESENTATION('',(#3597),#3601);
#3597 = LINE('',#3598,#3599);
#3598 = CARTESIAN_POINT('',(0.E+000,30.));
#3599 = VECTOR('',#3600,1.);
#3600 = DIRECTION('',(1.,0.E+000));
#3601 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3602 = PCURVE('',#2787,#3603);
#3603 = DEFINITIONAL_REPRESENTATION('',(#3604),#3608);
#3604 = LINE('',#3605,#3606);
#3605 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3606 = VECTOR('',#3607,1.);
#3607 = DIRECTION('',(1.,0.E+000));
#3608 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3609 = ADVANCED_FACE('',(#3610),#2787,.T.);
#3610 = FACE_BOUND('',#3611,.T.);
#3611 = EDGE_LOOP('',(#3612,#3635,#3636,#3637));
#3612 = ORIENTED_EDGE('',*,*,#3613,.F.);
#3613 = EDGE_CURVE('',#3545,#3543,#3614,.T.);
#3614 = SURFACE_CURVE('',#3615,(#3620,#3627),.PCURVE_S1.);
#3615 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3616,#3617,#3618,#3619),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3616 = CARTESIAN_POINT('',(42.5,10.,67.99038106));
#3617 = CARTESIAN_POINT('',(32.5,10.,67.99038106));
#3618 = CARTESIAN_POINT('',(32.5,10.,57.99038106));
#3619 = CARTESIAN_POINT('',(42.5,10.,57.99038106));
#3620 = PCURVE('',#2787,#3621);
#3621 = DEFINITIONAL_REPRESENTATION('',(#3622),#3626);
#3622 = LINE('',#3623,#3624);
#3623 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3624 = VECTOR('',#3625,1.);
#3625 = DIRECTION('',(0.E+000,1.));
#3626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3627 = PCURVE('',#3129,#3628);
#3628 = DEFINITIONAL_REPRESENTATION('',(#3629),#3634);
#3629 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3630,#3631,#3632,#3633),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3630 = CARTESIAN_POINT('',(-32.00961894,-7.5));
#3631 = CARTESIAN_POINT('',(-32.00961894,-17.5));
#3632 = CARTESIAN_POINT('',(-42.00961894,-17.5));
#3633 = CARTESIAN_POINT('',(-42.00961894,-7.5));
#3634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3635 = ORIENTED_EDGE('',*,*,#3590,.T.);
#3636 = ORIENTED_EDGE('',*,*,#2731,.F.);
#3637 = ORIENTED_EDGE('',*,*,#3569,.F.);
#3638 = ADVANCED_FACE('',(#3639),#2909,.T.);
#3639 = FACE_BOUND('',#3640,.T.);
#3640 = EDGE_LOOP('',(#3641,#3668,#3688,#3689));
#3641 = ORIENTED_EDGE('',*,*,#3642,.F.);
#3642 = EDGE_CURVE('',#3643,#3645,#3647,.T.);
#3643 = VERTEX_POINT('',#3644);
#3644 = CARTESIAN_POINT('',(20.,10.,45.));
#3645 = VERTEX_POINT('',#3646);
#3646 = CARTESIAN_POINT('',(20.,10.,55.));
#3647 = SURFACE_CURVE('',#3648,(#3653,#3660),.PCURVE_S1.);
#3648 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3649,#3650,#3651,#3652),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3649 = CARTESIAN_POINT('',(20.,10.,45.));
#3650 = CARTESIAN_POINT('',(30.,10.,45.));
#3651 = CARTESIAN_POINT('',(30.,10.,55.));
#3652 = CARTESIAN_POINT('',(20.,10.,55.));
#3653 = PCURVE('',#2909,#3654);
#3654 = DEFINITIONAL_REPRESENTATION('',(#3655),#3659);
#3655 = LINE('',#3656,#3657);
#3656 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3657 = VECTOR('',#3658,1.);
#3658 = DIRECTION('',(0.E+000,1.));
#3659 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3660 = PCURVE('',#3129,#3661);
#3661 = DEFINITIONAL_REPRESENTATION('',(#3662),#3667);
#3662 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3663,#3664,#3665,#3666),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3663 = CARTESIAN_POINT('',(-55.,-30.));
#3664 = CARTESIAN_POINT('',(-55.,-20.));
#3665 = CARTESIAN_POINT('',(-45.,-20.));
#3666 = CARTESIAN_POINT('',(-45.,-30.));
#3667 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3668 = ORIENTED_EDGE('',*,*,#3669,.T.);
#3669 = EDGE_CURVE('',#3643,#2852,#3670,.T.);
#3670 = SURFACE_CURVE('',#3671,(#3674,#3681),.PCURVE_S1.);
#3671 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3672,#3673),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3672 = CARTESIAN_POINT('',(20.,10.,45.));
#3673 = CARTESIAN_POINT('',(20.,0.E+000,45.));
#3674 = PCURVE('',#2909,#3675);
#3675 = DEFINITIONAL_REPRESENTATION('',(#3676),#3680);
#3676 = LINE('',#3677,#3678);
#3677 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3678 = VECTOR('',#3679,1.);
#3679 = DIRECTION('',(1.,0.E+000));
#3680 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3681 = PCURVE('',#3025,#3682);
#3682 = DEFINITIONAL_REPRESENTATION('',(#3683),#3687);
#3683 = LINE('',#3684,#3685);
#3684 = CARTESIAN_POINT('',(0.E+000,30.));
#3685 = VECTOR('',#3686,1.);
#3686 = DIRECTION('',(1.,0.E+000));
#3687 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3688 = ORIENTED_EDGE('',*,*,#2849,.F.);
#3689 = ORIENTED_EDGE('',*,*,#3690,.F.);
#3690 = EDGE_CURVE('',#3645,#2850,#3691,.T.);
#3691 = SURFACE_CURVE('',#3692,(#3695,#3702),.PCURVE_S1.);
#3692 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#3693,#3694),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,10.000998004),.PIECEWISE_BEZIER_KNOTS.);
#3693 = CARTESIAN_POINT('',(20.,10.,55.));
#3694 = CARTESIAN_POINT('',(20.,0.E+000,55.));
#3695 = PCURVE('',#2909,#3696);
#3696 = DEFINITIONAL_REPRESENTATION('',(#3697),#3701);
#3697 = LINE('',#3698,#3699);
#3698 = CARTESIAN_POINT('',(0.E+000,30.));
#3699 = VECTOR('',#3700,1.);
#3700 = DIRECTION('',(1.,0.E+000));
#3701 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3702 = PCURVE('',#3025,#3703);
#3703 = DEFINITIONAL_REPRESENTATION('',(#3704),#3708);
#3704 = LINE('',#3705,#3706);
#3705 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#3706 = VECTOR('',#3707,1.);
#3707 = DIRECTION('',(1.,0.E+000));
#3708 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3709 = ADVANCED_FACE('',(#3710),#3025,.T.);
#3710 = FACE_BOUND('',#3711,.T.);
#3711 = EDGE_LOOP('',(#3712,#3735,#3736,#3737));
#3712 = ORIENTED_EDGE('',*,*,#3713,.F.);
#3713 = EDGE_CURVE('',#3645,#3643,#3714,.T.);
#3714 = SURFACE_CURVE('',#3715,(#3720,#3727),.PCURVE_S1.);
#3715 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3716,#3717,#3718,#3719),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3716 = CARTESIAN_POINT('',(20.,10.,55.));
#3717 = CARTESIAN_POINT('',(10.,10.,55.));
#3718 = CARTESIAN_POINT('',(10.,10.,45.));
#3719 = CARTESIAN_POINT('',(20.,10.,45.));
#3720 = PCURVE('',#3025,#3721);
#3721 = DEFINITIONAL_REPRESENTATION('',(#3722),#3726);
#3722 = LINE('',#3723,#3724);
#3723 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#3724 = VECTOR('',#3725,1.);
#3725 = DIRECTION('',(0.E+000,1.));
#3726 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3727 = PCURVE('',#3129,#3728);
#3728 = DEFINITIONAL_REPRESENTATION('',(#3729),#3734);
#3729 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#3730,#3731,#3732,#3733),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#3730 = CARTESIAN_POINT('',(-45.,-30.));
#3731 = CARTESIAN_POINT('',(-45.,-40.));
#3732 = CARTESIAN_POINT('',(-55.,-40.));
#3733 = CARTESIAN_POINT('',(-55.,-30.));
#3734 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3735 = ORIENTED_EDGE('',*,*,#3690,.T.);
#3736 = ORIENTED_EDGE('',*,*,#2969,.F.);
#3737 = ORIENTED_EDGE('',*,*,#3669,.F.);
#3738 = ADVANCED_FACE('',(#3739,#3765,#3769,#3773),#3129,.T.);
#3739 = FACE_BOUND('',#3740,.T.);
#3740 = EDGE_LOOP('',(#3741,#3762,#3763,#3764));
#3741 = ORIENTED_EDGE('',*,*,#3742,.F.);
#3742 = EDGE_CURVE('',#3114,#3245,#3743,.T.);
#3743 = SURFACE_CURVE('',#3744,(#3748,#3755),.PCURVE_S1.);
#3744 = LINE('',#3745,#3746);
#3745 = CARTESIAN_POINT('',(10.,10.,50.));
#3746 = VECTOR('',#3747,1.);
#3747 = DIRECTION('',(0.E+000,0.E+000,-1.));
#3748 = PCURVE('',#3129,#3749);
#3749 = DEFINITIONAL_REPRESENTATION('',(#3750),#3754);
#3750 = LINE('',#3751,#3752);
#3751 = CARTESIAN_POINT('',(-50.,-40.));
#3752 = VECTOR('',#3753,1.);
#3753 = DIRECTION('',(-1.,0.E+000));
#3754 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3755 = PCURVE('',#3157,#3756);
#3756 = DEFINITIONAL_REPRESENTATION('',(#3757),#3761);
#3757 = LINE('',#3758,#3759);
#3758 = CARTESIAN_POINT('',(50.,0.E+000));
#3759 = VECTOR('',#3760,1.);
#3760 = DIRECTION('',(1.,0.E+000));
#3761 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3762 = ORIENTED_EDGE('',*,*,#3113,.F.);
#3763 = ORIENTED_EDGE('',*,*,#3415,.T.);
#3764 = ORIENTED_EDGE('',*,*,#3267,.T.);
#3765 = FACE_BOUND('',#3766,.T.);
#3766 = EDGE_LOOP('',(#3767,#3768));
#3767 = ORIENTED_EDGE('',*,*,#3442,.T.);
#3768 = ORIENTED_EDGE('',*,*,#3513,.T.);
#3769 = FACE_BOUND('',#3770,.T.);
#3770 = EDGE_LOOP('',(#3771,#3772));
#3771 = ORIENTED_EDGE('',*,*,#3542,.T.);
#3772 = ORIENTED_EDGE('',*,*,#3613,.T.);
#3773 = FACE_BOUND('',#3774,.T.);
#3774 = EDGE_LOOP('',(#3775,#3776));
#3775 = ORIENTED_EDGE('',*,*,#3642,.T.);
#3776 = ORIENTED_EDGE('',*,*,#3713,.T.);
#3777 = ADVANCED_FACE('',(#3778,#3784),#3157,.T.);
#3778 = FACE_BOUND('',#3779,.T.);
#3779 = EDGE_LOOP('',(#3780,#3781,#3782,#3783));
#3780 = ORIENTED_EDGE('',*,*,#3194,.F.);
#3781 = ORIENTED_EDGE('',*,*,#3141,.F.);
#3782 = ORIENTED_EDGE('',*,*,#3742,.T.);
#3783 = ORIENTED_EDGE('',*,*,#3244,.T.);
#3784 = FACE_BOUND('',#3785,.T.);
#3785 = EDGE_LOOP('',(#3786,#3787));
#3786 = ORIENTED_EDGE('',*,*,#3315,.T.);
#3787 = ORIENTED_EDGE('',*,*,#3386,.T.);
#3788 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#3792)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#3789,#3790,#3791)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#3789 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#3790 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#3791 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#3792 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(5.E-006),#3789,
  'distance_accuracy_value','confusion accuracy');
#3793 = SHAPE_DEFINITION_REPRESENTATION(#3794,#1933);
#3794 = PRODUCT_DEFINITION_SHAPE('','',#3795);
#3795 = PRODUCT_DEFINITION('design','',#3796,#3799);
#3796 = PRODUCT_DEFINITION_FORMATION('','',#3797);
#3797 = PRODUCT('l-bracket','l-bracket','',(#3798));
#3798 = PRODUCT_CONTEXT('',#2,'mechanical');
#3799 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#3800 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3801,#3803);
#3801 = ( REPRESENTATION_RELATIONSHIP('','',#1933,#1146) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3802) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3802 = ITEM_DEFINED_TRANSFORMATION('','',#11,#1159);
#3803 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3804);
#3804 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('10','l-bracket_1','',#1141,#3795
  ,$);
#3805 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#3797));
#3806 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#3807,#3809);
#3807 = ( REPRESENTATION_RELATIONSHIP('','',#1146,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#3808) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#3808 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#3809 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #3810);
#3810 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('11','l-bracket-assembly_1','',#5
  ,#1141,$);
#3811 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1143));
#3812 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#3813),#6195);
#3813 = MANIFOLD_SOLID_BREP('',#3814);
#3814 = CLOSED_SHELL('',(#3815,#5363,#5439,#5488,#5537,#5564,#5635,#5664
    ,#5735,#5764,#5835,#5864,#5935,#5964,#6035,#6064,#6135,#6164));
#3815 = ADVANCED_FACE('',(#3816,#3935,#4173,#4411,#4649,#4887,#5125),
  #3830,.T.);
#3816 = FACE_BOUND('',#3817,.T.);
#3817 = EDGE_LOOP('',(#3818,#3853,#3881,#3909));
#3818 = ORIENTED_EDGE('',*,*,#3819,.F.);
#3819 = EDGE_CURVE('',#3820,#3822,#3824,.T.);
#3820 = VERTEX_POINT('',#3821);
#3821 = CARTESIAN_POINT('',(180.,0.E+000,20.));
#3822 = VERTEX_POINT('',#3823);
#3823 = CARTESIAN_POINT('',(0.E+000,0.E+000,20.));
#3824 = SURFACE_CURVE('',#3825,(#3829,#3841),.PCURVE_S1.);
#3825 = LINE('',#3826,#3827);
#3826 = CARTESIAN_POINT('',(90.,0.E+000,20.));
#3827 = VECTOR('',#3828,1.);
#3828 = DIRECTION('',(-1.,0.E+000,0.E+000));
#3829 = PCURVE('',#3830,#3835);
#3830 = PLANE('',#3831);
#3831 = AXIS2_PLACEMENT_3D('',#3832,#3833,#3834);
#3832 = CARTESIAN_POINT('',(90.,75.,20.));
#3833 = DIRECTION('',(0.E+000,0.E+000,1.));
#3834 = DIRECTION('',(1.,0.E+000,0.E+000));
#3835 = DEFINITIONAL_REPRESENTATION('',(#3836),#3840);
#3836 = LINE('',#3837,#3838);
#3837 = CARTESIAN_POINT('',(0.E+000,-75.));
#3838 = VECTOR('',#3839,1.);
#3839 = DIRECTION('',(-1.,0.E+000));
#3840 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3841 = PCURVE('',#3842,#3847);
#3842 = PLANE('',#3843);
#3843 = AXIS2_PLACEMENT_3D('',#3844,#3845,#3846);
#3844 = CARTESIAN_POINT('',(90.,0.E+000,0.E+000));
#3845 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3846 = DIRECTION('',(0.E+000,0.E+000,-1.));
#3847 = DEFINITIONAL_REPRESENTATION('',(#3848),#3852);
#3848 = LINE('',#3849,#3850);
#3849 = CARTESIAN_POINT('',(-20.,0.E+000));
#3850 = VECTOR('',#3851,1.);
#3851 = DIRECTION('',(0.E+000,-1.));
#3852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3853 = ORIENTED_EDGE('',*,*,#3854,.F.);
#3854 = EDGE_CURVE('',#3855,#3820,#3857,.T.);
#3855 = VERTEX_POINT('',#3856);
#3856 = CARTESIAN_POINT('',(180.,150.,20.));
#3857 = SURFACE_CURVE('',#3858,(#3862,#3869),.PCURVE_S1.);
#3858 = LINE('',#3859,#3860);
#3859 = CARTESIAN_POINT('',(180.,75.,20.));
#3860 = VECTOR('',#3861,1.);
#3861 = DIRECTION('',(0.E+000,-1.,0.E+000));
#3862 = PCURVE('',#3830,#3863);
#3863 = DEFINITIONAL_REPRESENTATION('',(#3864),#3868);
#3864 = LINE('',#3865,#3866);
#3865 = CARTESIAN_POINT('',(90.,0.E+000));
#3866 = VECTOR('',#3867,1.);
#3867 = DIRECTION('',(0.E+000,-1.));
#3868 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3869 = PCURVE('',#3870,#3875);
#3870 = PLANE('',#3871);
#3871 = AXIS2_PLACEMENT_3D('',#3872,#3873,#3874);
#3872 = CARTESIAN_POINT('',(180.,75.,0.E+000));
#3873 = DIRECTION('',(1.,0.E+000,0.E+000));
#3874 = DIRECTION('',(0.E+000,0.E+000,-1.));
#3875 = DEFINITIONAL_REPRESENTATION('',(#3876),#3880);
#3876 = LINE('',#3877,#3878);
#3877 = CARTESIAN_POINT('',(-20.,0.E+000));
#3878 = VECTOR('',#3879,1.);
#3879 = DIRECTION('',(0.E+000,-1.));
#3880 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3881 = ORIENTED_EDGE('',*,*,#3882,.F.);
#3882 = EDGE_CURVE('',#3883,#3855,#3885,.T.);
#3883 = VERTEX_POINT('',#3884);
#3884 = CARTESIAN_POINT('',(0.E+000,150.,20.));
#3885 = SURFACE_CURVE('',#3886,(#3890,#3897),.PCURVE_S1.);
#3886 = LINE('',#3887,#3888);
#3887 = CARTESIAN_POINT('',(90.,150.,20.));
#3888 = VECTOR('',#3889,1.);
#3889 = DIRECTION('',(1.,0.E+000,0.E+000));
#3890 = PCURVE('',#3830,#3891);
#3891 = DEFINITIONAL_REPRESENTATION('',(#3892),#3896);
#3892 = LINE('',#3893,#3894);
#3893 = CARTESIAN_POINT('',(0.E+000,75.));
#3894 = VECTOR('',#3895,1.);
#3895 = DIRECTION('',(1.,0.E+000));
#3896 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3897 = PCURVE('',#3898,#3903);
#3898 = PLANE('',#3899);
#3899 = AXIS2_PLACEMENT_3D('',#3900,#3901,#3902);
#3900 = CARTESIAN_POINT('',(90.,150.,0.E+000));
#3901 = DIRECTION('',(0.E+000,1.,0.E+000));
#3902 = DIRECTION('',(0.E+000,0.E+000,1.));
#3903 = DEFINITIONAL_REPRESENTATION('',(#3904),#3908);
#3904 = LINE('',#3905,#3906);
#3905 = CARTESIAN_POINT('',(20.,0.E+000));
#3906 = VECTOR('',#3907,1.);
#3907 = DIRECTION('',(0.E+000,1.));
#3908 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3909 = ORIENTED_EDGE('',*,*,#3910,.F.);
#3910 = EDGE_CURVE('',#3822,#3883,#3911,.T.);
#3911 = SURFACE_CURVE('',#3912,(#3916,#3923),.PCURVE_S1.);
#3912 = LINE('',#3913,#3914);
#3913 = CARTESIAN_POINT('',(0.E+000,75.,20.));
#3914 = VECTOR('',#3915,1.);
#3915 = DIRECTION('',(0.E+000,1.,0.E+000));
#3916 = PCURVE('',#3830,#3917);
#3917 = DEFINITIONAL_REPRESENTATION('',(#3918),#3922);
#3918 = LINE('',#3919,#3920);
#3919 = CARTESIAN_POINT('',(-90.,0.E+000));
#3920 = VECTOR('',#3921,1.);
#3921 = DIRECTION('',(0.E+000,1.));
#3922 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3923 = PCURVE('',#3924,#3929);
#3924 = PLANE('',#3925);
#3925 = AXIS2_PLACEMENT_3D('',#3926,#3927,#3928);
#3926 = CARTESIAN_POINT('',(0.E+000,75.,0.E+000));
#3927 = DIRECTION('',(-1.,0.E+000,0.E+000));
#3928 = DIRECTION('',(0.E+000,0.E+000,1.));
#3929 = DEFINITIONAL_REPRESENTATION('',(#3930),#3934);
#3930 = LINE('',#3931,#3932);
#3931 = CARTESIAN_POINT('',(20.,0.E+000));
#3932 = VECTOR('',#3933,1.);
#3933 = DIRECTION('',(0.E+000,1.));
#3934 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3935 = FACE_BOUND('',#3936,.T.);
#3936 = EDGE_LOOP('',(#3937,#4057));
#3937 = ORIENTED_EDGE('',*,*,#3938,.T.);
#3938 = EDGE_CURVE('',#3939,#3941,#3943,.T.);
#3939 = VERTEX_POINT('',#3940);
#3940 = CARTESIAN_POINT('',(42.5,87.9903810602,20.));
#3941 = VERTEX_POINT('',#3942);
#3942 = CARTESIAN_POINT('',(52.5,87.9903810602,20.));
#3943 = SURFACE_CURVE('',#3944,(#3969,#3997),.PCURVE_S1.);
#3944 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#3945,#3946,#3947,#3948,#3949,
    #3950,#3951,#3952,#3953,#3954,#3955,#3956,#3957,#3958,#3959,#3960,
    #3961,#3962,#3963,#3964,#3965,#3966,#3967,#3968),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165568,7.85828166216,
    10.723818054,13.5836589983,16.4911855042,20.3877608737,22.3658107415
    ),.UNSPECIFIED.);
#3945 = CARTESIAN_POINT('',(42.5,87.9903810602,20.));
#3946 = CARTESIAN_POINT('',(42.5,88.4575793138,20.));
#3947 = CARTESIAN_POINT('',(42.5545696802,88.9583665269,20.));
#3948 = CARTESIAN_POINT('',(42.6795822577,89.4815040925,20.));
#3949 = CARTESIAN_POINT('',(43.0726861246,90.4704424936,20.));
#3950 = CARTESIAN_POINT('',(43.7580146369,91.3712858011,20.));
#3951 = CARTESIAN_POINT('',(44.1452361926,91.7590073924,20.));
#3952 = CARTESIAN_POINT('',(44.9325086237,92.3524690876,20.));
#3953 = CARTESIAN_POINT('',(45.8548107341,92.742221424,20.));
#3954 = CARTESIAN_POINT('',(46.2767785587,92.8683003968,20.));
#3955 = CARTESIAN_POINT('',(47.1437129636,93.0258620516,20.));
#3956 = CARTESIAN_POINT('',(48.0264003005,92.9917818222,20.));
#3957 = CARTESIAN_POINT('',(48.4630506736,92.9261296265,20.));
#3958 = CARTESIAN_POINT('',(49.3186421197,92.6992268484,20.));
#3959 = CARTESIAN_POINT('',(50.0957546192,92.2975117311,20.));
#3960 = CARTESIAN_POINT('',(50.4603131853,92.0546001422,20.));
#3961 = CARTESIAN_POINT('',(51.2355490366,91.4066823538,20.));
#3962 = CARTESIAN_POINT('',(51.8095225986,90.6150367145,20.));
#3963 = CARTESIAN_POINT('',(52.0637500218,90.13282926,20.));
#3964 = CARTESIAN_POINT('',(52.336292435,89.3951999942,20.));
#3965 = CARTESIAN_POINT('',(52.4612187701,88.6792361613,20.));
#3966 = CARTESIAN_POINT('',(52.4876332288,88.4428124377,20.));
#3967 = CARTESIAN_POINT('',(52.5,88.2127907262,20.));
#3968 = CARTESIAN_POINT('',(52.5,87.9903810602,20.));
#3969 = PCURVE('',#3830,#3970);
#3970 = DEFINITIONAL_REPRESENTATION('',(#3971),#3996);
#3971 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#3972,#3973,#3974,#3975,#3976,
    #3977,#3978,#3979,#3980,#3981,#3982,#3983,#3984,#3985,#3986,#3987,
    #3988,#3989,#3990,#3991,#3992,#3993,#3994,#3995),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165568,7.85828166216,
    10.723818054,13.5836589983,16.4911855042,20.3877608737,22.3658107415
    ),.UNSPECIFIED.);
#3972 = CARTESIAN_POINT('',(-47.5,12.9903810602));
#3973 = CARTESIAN_POINT('',(-47.5,13.4575793138));
#3974 = CARTESIAN_POINT('',(-47.4454303198,13.9583665269));
#3975 = CARTESIAN_POINT('',(-47.3204177423,14.4815040925));
#3976 = CARTESIAN_POINT('',(-46.9273138754,15.4704424936));
#3977 = CARTESIAN_POINT('',(-46.2419853631,16.3712858011));
#3978 = CARTESIAN_POINT('',(-45.8547638074,16.7590073924));
#3979 = CARTESIAN_POINT('',(-45.0674913763,17.3524690876));
#3980 = CARTESIAN_POINT('',(-44.1451892659,17.742221424));
#3981 = CARTESIAN_POINT('',(-43.7232214413,17.8683003968));
#3982 = CARTESIAN_POINT('',(-42.8562870364,18.0258620516));
#3983 = CARTESIAN_POINT('',(-41.9735996995,17.9917818222));
#3984 = CARTESIAN_POINT('',(-41.5369493264,17.9261296265));
#3985 = CARTESIAN_POINT('',(-40.6813578803,17.6992268484));
#3986 = CARTESIAN_POINT('',(-39.9042453808,17.2975117311));
#3987 = CARTESIAN_POINT('',(-39.5396868147,17.0546001422));
#3988 = CARTESIAN_POINT('',(-38.7644509634,16.4066823538));
#3989 = CARTESIAN_POINT('',(-38.1904774014,15.6150367145));
#3990 = CARTESIAN_POINT('',(-37.9362499782,15.13282926));
#3991 = CARTESIAN_POINT('',(-37.663707565,14.3951999942));
#3992 = CARTESIAN_POINT('',(-37.5387812299,13.6792361613));
#3993 = CARTESIAN_POINT('',(-37.5123667712,13.4428124377));
#3994 = CARTESIAN_POINT('',(-37.5,13.2127907262));
#3995 = CARTESIAN_POINT('',(-37.5,12.9903810602));
#3996 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#3997 = PCURVE('',#3998,#4007);
#3998 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#3999,#4000,#4001,#4002)
    ,(#4003,#4004,#4005,#4006
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#3999 = CARTESIAN_POINT('',(42.5,87.99038106,20.));
#4000 = CARTESIAN_POINT('',(42.5,97.99038106,20.));
#4001 = CARTESIAN_POINT('',(52.5,97.99038106,20.));
#4002 = CARTESIAN_POINT('',(52.5,87.99038106,20.));
#4003 = CARTESIAN_POINT('',(42.5,87.99038106,0.E+000));
#4004 = CARTESIAN_POINT('',(42.5,97.99038106,0.E+000));
#4005 = CARTESIAN_POINT('',(52.5,97.99038106,0.E+000));
#4006 = CARTESIAN_POINT('',(52.5,87.99038106,0.E+000));
#4007 = DEFINITIONAL_REPRESENTATION('',(#4008),#4056);
#4008 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4009,#4010,#4011,#4012,#4013,
    #4014,#4015,#4016,#4017,#4018,#4019,#4020,#4021,#4022,#4023,#4024,
    #4025,#4026,#4027,#4028,#4029,#4030,#4031,#4032,#4033,#4034,#4035,
    #4036,#4037,#4038,#4039,#4040,#4041,#4042,#4043,#4044,#4045,#4046,
    #4047,#4048,#4049,#4050,#4051,#4052,#4053,#4054,#4055),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880489,
    1.016627760977,1.524941641466,2.033255521955,2.541569402443,
    3.049883282932,3.55819716342,4.066511043909,4.574824924398,
    5.083138804886,5.591452685375,6.099766565864,6.608080446352,
    7.116394326841,7.62470820733,8.133022087818,8.641335968307,
    9.149649848795,9.657963729284,10.166277609773,10.674591490261,
    11.18290537075,11.691219251239,12.199533131727,12.707847012216,
    13.216160892705,13.724474773193,14.232788653682,14.74110253417,
    15.249416414659,15.757730295148,16.266044175636,16.774358056125,
    17.282671936614,17.790985817102,18.299299697591,18.80761357808,
    19.315927458568,19.824241339057,20.332555219545,20.840869100034,
    21.349182980523,21.857496861011,22.3658107415),
  .QUASI_UNIFORM_KNOTS.);
#4009 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4010 = CARTESIAN_POINT('',(9.980039899968E-004,0.285786134526));
#4011 = CARTESIAN_POINT('',(9.980039899955E-004,0.851023725123));
#4012 = CARTESIAN_POINT('',(9.980039899993E-004,1.679658949222));
#4013 = CARTESIAN_POINT('',(9.980039900076E-004,2.488775839043));
#4014 = CARTESIAN_POINT('',(9.980039899919E-004,3.278357383281));
#4015 = CARTESIAN_POINT('',(9.980039900039E-004,4.048590079098));
#4016 = CARTESIAN_POINT('',(9.980039899934E-004,4.799873537182));
#4017 = CARTESIAN_POINT('',(9.980039900023E-004,5.532780961181));
#4018 = CARTESIAN_POINT('',(9.980039899986E-004,6.248020896562));
#4019 = CARTESIAN_POINT('',(9.980039900048E-004,6.946360561026));
#4020 = CARTESIAN_POINT('',(9.980039900052E-004,7.62868862173));
#4021 = CARTESIAN_POINT('',(9.980039899975E-004,8.296073959471));
#4022 = CARTESIAN_POINT('',(9.980039900069E-004,8.949683930066));
#4023 = CARTESIAN_POINT('',(9.980039899987E-004,9.590744767173));
#4024 = CARTESIAN_POINT('',(9.98003990001E-004,10.22049917264));
#4025 = CARTESIAN_POINT('',(9.980039900004E-004,10.840182508009));
#4026 = CARTESIAN_POINT('',(9.980039900006E-004,11.450961979695));
#4027 = CARTESIAN_POINT('',(9.980039900006E-004,12.054057822467));
#4028 = CARTESIAN_POINT('',(9.980039900008E-004,12.650784945233));
#4029 = CARTESIAN_POINT('',(9.980039900005E-004,13.242437001407));
#4030 = CARTESIAN_POINT('',(9.980039900018E-004,13.830311316457));
#4031 = CARTESIAN_POINT('',(9.980039899971E-004,14.41570044039));
#4032 = CARTESIAN_POINT('',(9.980039900148E-004,14.99989761317));
#4033 = CARTESIAN_POINT('',(9.980039899915E-004,15.584089011939));
#4034 = CARTESIAN_POINT('',(9.980039900035E-004,16.169496121936));
#4035 = CARTESIAN_POINT('',(9.980039900002E-004,16.757374012386));
#4036 = CARTESIAN_POINT('',(9.980039900016E-004,17.349001918912));
#4037 = CARTESIAN_POINT('',(9.980039899997E-004,17.945677528451));
#4038 = CARTESIAN_POINT('',(9.980039900061E-004,18.548712223074));
#4039 = CARTESIAN_POINT('',(9.98003990004E-004,19.159406300008));
#4040 = CARTESIAN_POINT('',(9.980039900063E-004,19.779034545809));
#4041 = CARTESIAN_POINT('',(9.980039899995E-004,20.408844117306));
#4042 = CARTESIAN_POINT('',(9.980039900034E-004,21.050050721665));
#4043 = CARTESIAN_POINT('',(9.980039899948E-004,21.703821246548));
#4044 = CARTESIAN_POINT('',(9.980039900043E-004,22.371286813948));
#4045 = CARTESIAN_POINT('',(9.980039899967E-004,23.053580538936));
#4046 = CARTESIAN_POINT('',(9.980039899966E-004,23.751780895042));
#4047 = CARTESIAN_POINT('',(9.98003990005E-004,24.466876473707));
#4048 = CARTESIAN_POINT('',(9.980039899931E-004,25.199732658311));
#4049 = CARTESIAN_POINT('',(9.980039900112E-004,25.951064423859));
#4050 = CARTESIAN_POINT('',(9.980039899935E-004,26.721413691496));
#4051 = CARTESIAN_POINT('',(9.980039900041E-004,27.511129459065));
#4052 = CARTESIAN_POINT('',(9.980039900012E-004,28.320321956614));
#4053 = CARTESIAN_POINT('',(9.980039900025E-004,29.148977247728));
#4054 = CARTESIAN_POINT('',(9.980039900012E-004,29.714213802412));
#4055 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4056 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4057 = ORIENTED_EDGE('',*,*,#4058,.T.);
#4058 = EDGE_CURVE('',#3941,#3939,#4059,.T.);
#4059 = SURFACE_CURVE('',#4060,(#4085,#4113),.PCURVE_S1.);
#4060 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4061,#4062,#4063,#4064,#4065,
    #4066,#4067,#4068,#4069,#4070,#4071,#4072,#4073,#4074,#4075,#4076,
    #4077,#4078,#4079,#4080,#4081,#4082,#4083,#4084),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163241,7.85828165153,
    10.7238180696,13.583659015,16.4911855247,20.3877608942,22.3658107307
    ),.UNSPECIFIED.);
#4061 = CARTESIAN_POINT('',(52.5,87.9903810602,20.));
#4062 = CARTESIAN_POINT('',(52.5,87.5231828091,20.));
#4063 = CARTESIAN_POINT('',(52.4454303204,87.0223955989,20.));
#4064 = CARTESIAN_POINT('',(52.3204177402,86.4992580219,20.));
#4065 = CARTESIAN_POINT('',(51.9273138725,85.5103196223,20.));
#4066 = CARTESIAN_POINT('',(51.2419853611,84.6094763168,20.));
#4067 = CARTESIAN_POINT('',(50.8547638088,84.2217547299,20.));
#4068 = CARTESIAN_POINT('',(50.0674913726,83.6282930311,20.));
#4069 = CARTESIAN_POINT('',(49.1451892572,83.2385406935,20.));
#4070 = CARTESIAN_POINT('',(48.723221447,83.1124617246,20.));
#4071 = CARTESIAN_POINT('',(47.8562870386,82.9549000687,20.));
#4072 = CARTESIAN_POINT('',(46.9735996974,82.9889802983,20.));
#4073 = CARTESIAN_POINT('',(46.5369493258,83.0546324941,20.));
#4074 = CARTESIAN_POINT('',(45.6813578799,83.2815352719,20.));
#4075 = CARTESIAN_POINT('',(44.9042453807,83.6832503895,20.));
#4076 = CARTESIAN_POINT('',(44.5396868156,83.9261619774,20.));
#4077 = CARTESIAN_POINT('',(43.7644509637,84.5740797661,20.));
#4078 = CARTESIAN_POINT('',(43.1904774015,85.3657254057,20.));
#4079 = CARTESIAN_POINT('',(42.9362499782,85.8479328615,20.));
#4080 = CARTESIAN_POINT('',(42.6637075666,86.5855621231,20.));
#4081 = CARTESIAN_POINT('',(42.5387812311,87.3015259544,20.));
#4082 = CARTESIAN_POINT('',(42.5123667709,87.5379496899,20.));
#4083 = CARTESIAN_POINT('',(42.5,87.7679713976,20.));
#4084 = CARTESIAN_POINT('',(42.5,87.9903810602,20.));
#4085 = PCURVE('',#3830,#4086);
#4086 = DEFINITIONAL_REPRESENTATION('',(#4087),#4112);
#4087 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4088,#4089,#4090,#4091,#4092,
    #4093,#4094,#4095,#4096,#4097,#4098,#4099,#4100,#4101,#4102,#4103,
    #4104,#4105,#4106,#4107,#4108,#4109,#4110,#4111),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163241,7.85828165153,
    10.7238180696,13.583659015,16.4911855247,20.3877608942,22.3658107307
    ),.UNSPECIFIED.);
#4088 = CARTESIAN_POINT('',(-37.5,12.9903810602));
#4089 = CARTESIAN_POINT('',(-37.5,12.5231828091));
#4090 = CARTESIAN_POINT('',(-37.5545696796,12.0223955989));
#4091 = CARTESIAN_POINT('',(-37.6795822598,11.4992580219));
#4092 = CARTESIAN_POINT('',(-38.0726861275,10.5103196223));
#4093 = CARTESIAN_POINT('',(-38.7580146389,9.6094763168));
#4094 = CARTESIAN_POINT('',(-39.1452361912,9.2217547299));
#4095 = CARTESIAN_POINT('',(-39.9325086274,8.6282930311));
#4096 = CARTESIAN_POINT('',(-40.8548107428,8.2385406935));
#4097 = CARTESIAN_POINT('',(-41.276778553,8.1124617246));
#4098 = CARTESIAN_POINT('',(-42.1437129614,7.9549000687));
#4099 = CARTESIAN_POINT('',(-43.0264003026,7.9889802983));
#4100 = CARTESIAN_POINT('',(-43.4630506742,8.0546324941));
#4101 = CARTESIAN_POINT('',(-44.3186421201,8.2815352719));
#4102 = CARTESIAN_POINT('',(-45.0957546193,8.6832503895));
#4103 = CARTESIAN_POINT('',(-45.4603131844,8.9261619774));
#4104 = CARTESIAN_POINT('',(-46.2355490363,9.5740797661));
#4105 = CARTESIAN_POINT('',(-46.8095225985,10.3657254057));
#4106 = CARTESIAN_POINT('',(-47.0637500218,10.8479328615));
#4107 = CARTESIAN_POINT('',(-47.3362924334,11.5855621231));
#4108 = CARTESIAN_POINT('',(-47.4612187689,12.3015259544));
#4109 = CARTESIAN_POINT('',(-47.4876332291,12.5379496899));
#4110 = CARTESIAN_POINT('',(-47.5,12.7679713976));
#4111 = CARTESIAN_POINT('',(-47.5,12.9903810602));
#4112 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4113 = PCURVE('',#4114,#4123);
#4114 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4115,#4116,#4117,#4118)
    ,(#4119,#4120,#4121,#4122
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4115 = CARTESIAN_POINT('',(52.5,87.99038106,20.));
#4116 = CARTESIAN_POINT('',(52.5,77.99038106,20.));
#4117 = CARTESIAN_POINT('',(42.5,77.99038106,20.));
#4118 = CARTESIAN_POINT('',(42.5,87.99038106,20.));
#4119 = CARTESIAN_POINT('',(52.5,87.99038106,0.E+000));
#4120 = CARTESIAN_POINT('',(52.5,77.99038106,0.E+000));
#4121 = CARTESIAN_POINT('',(42.5,77.99038106,0.E+000));
#4122 = CARTESIAN_POINT('',(42.5,87.99038106,0.E+000));
#4123 = DEFINITIONAL_REPRESENTATION('',(#4124),#4172);
#4124 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4125,#4126,#4127,#4128,#4129,
    #4130,#4131,#4132,#4133,#4134,#4135,#4136,#4137,#4138,#4139,#4140,
    #4141,#4142,#4143,#4144,#4145,#4146,#4147,#4148,#4149,#4150,#4151,
    #4152,#4153,#4154,#4155,#4156,#4157,#4158,#4159,#4160,#4161,#4162,
    #4163,#4164,#4165,#4166,#4167,#4168,#4169,#4170,#4171),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880243,
    1.016627760486,1.52494164073,2.033255520973,2.541569401216,
    3.049883281459,3.558197161702,4.066511041945,4.574824922189,
    5.083138802432,5.591452682675,6.099766562918,6.608080443161,
    7.116394323405,7.624708203648,8.133022083891,8.641335964134,
    9.149649844377,9.65796372462,10.166277604864,10.674591485107,
    11.18290536535,11.691219245593,12.199533125836,12.70784700608,
    13.216160886323,13.724474766566,14.232788646809,14.741102527052,
    15.249416407295,15.757730287539,16.266044167782,16.774358048025,
    17.282671928268,17.790985808511,18.299299688755,18.807613568998,
    19.315927449241,19.824241329484,20.332555209727,20.84086908997,
    21.349182970214,21.857496850457,22.3658107307),
  .QUASI_UNIFORM_KNOTS.);
#4125 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4126 = CARTESIAN_POINT('',(9.9800399E-004,0.285786133536));
#4127 = CARTESIAN_POINT('',(9.980039900001E-004,0.851023723772));
#4128 = CARTESIAN_POINT('',(9.980039899996E-004,1.679658951148));
#4129 = CARTESIAN_POINT('',(9.980039900017E-004,2.488775847134));
#4130 = CARTESIAN_POINT('',(9.980039899938E-004,3.278357399113));
#4131 = CARTESIAN_POINT('',(9.980039900018E-004,4.048590102139));
#4132 = CARTESIAN_POINT('',(9.980039899992E-004,4.799873565183));
#4133 = CARTESIAN_POINT('',(9.980039900017E-004,5.532780991083));
#4134 = CARTESIAN_POINT('',(9.980039899942E-004,6.248020925664));
#4135 = CARTESIAN_POINT('',(9.980039900006E-004,6.946360588908));
#4136 = CARTESIAN_POINT('',(9.980039900041E-004,7.628688647214));
#4137 = CARTESIAN_POINT('',(9.980039900052E-004,8.296073981228));
#4138 = CARTESIAN_POINT('',(9.980039899972E-004,8.949683947635));
#4139 = CARTESIAN_POINT('',(9.980039900068E-004,9.5907447811));
#4140 = CARTESIAN_POINT('',(9.98003989998E-004,10.220499184278));
#4141 = CARTESIAN_POINT('',(9.980039900025E-004,10.840182518657));
#4142 = CARTESIAN_POINT('',(9.980039899935E-004,11.450961990405));
#4143 = CARTESIAN_POINT('',(9.980039900036E-004,12.054057829209));
#4144 = CARTESIAN_POINT('',(9.980039899937E-004,12.650784942582));
#4145 = CARTESIAN_POINT('',(9.980039900023E-004,13.242436988192));
#4146 = CARTESIAN_POINT('',(9.980039899994E-004,13.830311296248));
#4147 = CARTESIAN_POINT('',(9.980039900026E-004,14.415700419084));
#4148 = CARTESIAN_POINT('',(9.980039899926E-004,14.999897591734));
#4149 = CARTESIAN_POINT('',(9.980039900082E-004,15.58408898968));
#4150 = CARTESIAN_POINT('',(9.980039899988E-004,16.169496098413));
#4151 = CARTESIAN_POINT('',(9.980039899995E-004,16.757373987383));
#4152 = CARTESIAN_POINT('',(9.980039900064E-004,17.349001892551));
#4153 = CARTESIAN_POINT('',(9.980039899995E-004,17.945677500953));
#4154 = CARTESIAN_POINT('',(9.980039899991E-004,18.548712194227));
#4155 = CARTESIAN_POINT('',(9.980039900079E-004,19.159406269329));
#4156 = CARTESIAN_POINT('',(9.980039899944E-004,19.779034513082));
#4157 = CARTESIAN_POINT('',(9.980039899971E-004,20.408844082753));
#4158 = CARTESIAN_POINT('',(9.980039900001E-004,21.050050685885));
#4159 = CARTESIAN_POINT('',(9.980039900071E-004,21.703821209766));
#4160 = CARTESIAN_POINT('',(9.980039899977E-004,22.371286776084));
#4161 = CARTESIAN_POINT('',(9.980039900073E-004,23.053580500174));
#4162 = CARTESIAN_POINT('',(9.980039899996E-004,23.751780855547));
#4163 = CARTESIAN_POINT('',(9.980039899996E-004,24.466876433587));
#4164 = CARTESIAN_POINT('',(9.980039900076E-004,25.199732617576));
#4165 = CARTESIAN_POINT('',(9.980039899972E-004,25.95106438254));
#4166 = CARTESIAN_POINT('',(9.980039900096E-004,26.721413649762));
#4167 = CARTESIAN_POINT('',(9.98003989992E-004,27.511129418022));
#4168 = CARTESIAN_POINT('',(9.980039900078E-004,28.320321934731));
#4169 = CARTESIAN_POINT('',(9.98003990005E-004,29.148977246309));
#4170 = CARTESIAN_POINT('',(9.980039900021E-004,29.714213805265));
#4171 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4172 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4173 = FACE_BOUND('',#4174,.T.);
#4174 = EDGE_LOOP('',(#4175,#4295));
#4175 = ORIENTED_EDGE('',*,*,#4176,.T.);
#4176 = EDGE_CURVE('',#4177,#4179,#4181,.T.);
#4177 = VERTEX_POINT('',#4178);
#4178 = CARTESIAN_POINT('',(42.5,62.0096189398,20.));
#4179 = VERTEX_POINT('',#4180);
#4180 = CARTESIAN_POINT('',(52.5,62.0096189398,20.));
#4181 = SURFACE_CURVE('',#4182,(#4207,#4235),.PCURVE_S1.);
#4182 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4183,#4184,#4185,#4186,#4187,
    #4188,#4189,#4190,#4191,#4192,#4193,#4194,#4195,#4196,#4197,#4198,
    #4199,#4200,#4201,#4202,#4203,#4204,#4205,#4206),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163339,7.85828165276,
    10.7238180712,13.5836590167,16.4911855274,20.3877608974,
    22.3658107333),.UNSPECIFIED.);
#4183 = CARTESIAN_POINT('',(42.5,62.0096189398,20.));
#4184 = CARTESIAN_POINT('',(42.5,62.476817191,20.));
#4185 = CARTESIAN_POINT('',(42.5545696796,62.9776044013,20.));
#4186 = CARTESIAN_POINT('',(42.6795822598,63.5007419778,20.));
#4187 = CARTESIAN_POINT('',(43.0726861274,64.4896803776,20.));
#4188 = CARTESIAN_POINT('',(43.758014639,65.3905236833,20.));
#4189 = CARTESIAN_POINT('',(44.1452361911,65.7782452701,20.));
#4190 = CARTESIAN_POINT('',(44.9325086274,66.3717069689,20.));
#4191 = CARTESIAN_POINT('',(45.8548107429,66.7614593066,20.));
#4192 = CARTESIAN_POINT('',(46.2767785529,66.8875382754,20.));
#4193 = CARTESIAN_POINT('',(47.1437129614,67.0450999313,20.));
#4194 = CARTESIAN_POINT('',(48.0264003027,67.0110197017,20.));
#4195 = CARTESIAN_POINT('',(48.4630506741,66.9453675059,20.));
#4196 = CARTESIAN_POINT('',(49.3186421203,66.718464728,20.));
#4197 = CARTESIAN_POINT('',(50.0957546196,66.3167496104,20.));
#4198 = CARTESIAN_POINT('',(50.4603131842,66.0738380227,20.));
#4199 = CARTESIAN_POINT('',(51.2355490363,65.4259202339,20.));
#4200 = CARTESIAN_POINT('',(51.8095225986,64.6342745942,20.));
#4201 = CARTESIAN_POINT('',(52.0637500217,64.1520671386,20.));
#4202 = CARTESIAN_POINT('',(52.3362924333,63.4144378771,20.));
#4203 = CARTESIAN_POINT('',(52.4612187689,62.6984740458,20.));
#4204 = CARTESIAN_POINT('',(52.4876332292,62.46205031,20.));
#4205 = CARTESIAN_POINT('',(52.5,62.2320286023,20.));
#4206 = CARTESIAN_POINT('',(52.5,62.0096189398,20.));
#4207 = PCURVE('',#3830,#4208);
#4208 = DEFINITIONAL_REPRESENTATION('',(#4209),#4234);
#4209 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4210,#4211,#4212,#4213,#4214,
    #4215,#4216,#4217,#4218,#4219,#4220,#4221,#4222,#4223,#4224,#4225,
    #4226,#4227,#4228,#4229,#4230,#4231,#4232,#4233),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163339,7.85828165276,
    10.7238180712,13.5836590167,16.4911855274,20.3877608974,
    22.3658107333),.UNSPECIFIED.);
#4210 = CARTESIAN_POINT('',(-47.5,-12.9903810602));
#4211 = CARTESIAN_POINT('',(-47.5,-12.523182809));
#4212 = CARTESIAN_POINT('',(-47.4454303204,-12.0223955987));
#4213 = CARTESIAN_POINT('',(-47.3204177402,-11.4992580222));
#4214 = CARTESIAN_POINT('',(-46.9273138726,-10.5103196224));
#4215 = CARTESIAN_POINT('',(-46.241985361,-9.6094763167));
#4216 = CARTESIAN_POINT('',(-45.8547638089,-9.2217547299));
#4217 = CARTESIAN_POINT('',(-45.0674913726,-8.6282930311));
#4218 = CARTESIAN_POINT('',(-44.1451892571,-8.2385406934));
#4219 = CARTESIAN_POINT('',(-43.7232214471,-8.1124617246));
#4220 = CARTESIAN_POINT('',(-42.8562870386,-7.9549000687));
#4221 = CARTESIAN_POINT('',(-41.9735996973,-7.9889802983));
#4222 = CARTESIAN_POINT('',(-41.5369493259,-8.0546324941));
#4223 = CARTESIAN_POINT('',(-40.6813578797,-8.281535272));
#4224 = CARTESIAN_POINT('',(-39.9042453804,-8.6832503896));
#4225 = CARTESIAN_POINT('',(-39.5396868158,-8.9261619773));
#4226 = CARTESIAN_POINT('',(-38.7644509637,-9.5740797661));
#4227 = CARTESIAN_POINT('',(-38.1904774014,-10.3657254058));
#4228 = CARTESIAN_POINT('',(-37.9362499783,-10.8479328614));
#4229 = CARTESIAN_POINT('',(-37.6637075667,-11.5855621229));
#4230 = CARTESIAN_POINT('',(-37.5387812311,-12.3015259542));
#4231 = CARTESIAN_POINT('',(-37.5123667708,-12.53794969));
#4232 = CARTESIAN_POINT('',(-37.5,-12.7679713977));
#4233 = CARTESIAN_POINT('',(-37.5,-12.9903810602));
#4234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4235 = PCURVE('',#4236,#4245);
#4236 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4237,#4238,#4239,#4240)
    ,(#4241,#4242,#4243,#4244
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4237 = CARTESIAN_POINT('',(42.5,62.00961894,20.));
#4238 = CARTESIAN_POINT('',(42.5,72.00961894,20.));
#4239 = CARTESIAN_POINT('',(52.5,72.00961894,20.));
#4240 = CARTESIAN_POINT('',(52.5,62.00961894,20.));
#4241 = CARTESIAN_POINT('',(42.5,62.00961894,0.E+000));
#4242 = CARTESIAN_POINT('',(42.5,72.00961894,0.E+000));
#4243 = CARTESIAN_POINT('',(52.5,72.00961894,0.E+000));
#4244 = CARTESIAN_POINT('',(52.5,62.00961894,0.E+000));
#4245 = DEFINITIONAL_REPRESENTATION('',(#4246),#4294);
#4246 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4247,#4248,#4249,#4250,#4251,
    #4252,#4253,#4254,#4255,#4256,#4257,#4258,#4259,#4260,#4261,#4262,
    #4263,#4264,#4265,#4266,#4267,#4268,#4269,#4270,#4271,#4272,#4273,
    #4274,#4275,#4276,#4277,#4278,#4279,#4280,#4281,#4282,#4283,#4284,
    #4285,#4286,#4287,#4288,#4289,#4290,#4291,#4292,#4293),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880302,
    1.016627760605,1.524941640907,2.033255521209,2.541569401511,
    3.049883281814,3.558197162116,4.066511042418,4.57482492272,
    5.083138803023,5.591452683325,6.099766563627,6.60808044393,
    7.116394324232,7.624708204534,8.133022084836,8.641335965139,
    9.149649845441,9.657963725743,10.166277606045,10.674591486348,
    11.18290536665,11.691219246952,12.199533127255,12.707847007557,
    13.216160887859,13.724474768161,14.232788648464,14.741102528766,
    15.249416409068,15.75773028937,16.266044169673,16.774358049975,
    17.282671930277,17.79098581058,18.299299690882,18.807613571184,
    19.315927451486,19.824241331789,20.332555212091,20.840869092393,
    21.349182972695,21.857496852998,22.3658107333),
  .QUASI_UNIFORM_KNOTS.);
#4247 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4248 = CARTESIAN_POINT('',(9.9800399E-004,0.285786133572));
#4249 = CARTESIAN_POINT('',(9.980039899999E-004,0.851023723838));
#4250 = CARTESIAN_POINT('',(9.980039900003E-004,1.679658951146));
#4251 = CARTESIAN_POINT('',(9.980039899988E-004,2.488775846929));
#4252 = CARTESIAN_POINT('',(9.980039900044E-004,3.278357398652));
#4253 = CARTESIAN_POINT('',(9.980039900049E-004,4.048590101454));
#4254 = CARTESIAN_POINT('',(9.980039899975E-004,4.799873564371));
#4255 = CARTESIAN_POINT('',(9.980039900053E-004,5.532780990271));
#4256 = CARTESIAN_POINT('',(9.980039900029E-004,6.248020924957));
#4257 = CARTESIAN_POINT('',(9.980039900049E-004,6.946360588335));
#4258 = CARTESIAN_POINT('',(9.980039899992E-004,7.628688646726));
#4259 = CARTESIAN_POINT('',(9.980039899988E-004,8.296073980765));
#4260 = CARTESIAN_POINT('',(9.980039900062E-004,8.949683947154));
#4261 = CARTESIAN_POINT('',(9.980039899985E-004,9.590744780597));
#4262 = CARTESIAN_POINT('',(9.980039900007E-004,10.220499183786));
#4263 = CARTESIAN_POINT('',(9.980039899996E-004,10.840182518226));
#4264 = CARTESIAN_POINT('',(9.980039900019E-004,11.450961990074));
#4265 = CARTESIAN_POINT('',(9.980039899941E-004,12.054057828913));
#4266 = CARTESIAN_POINT('',(9.980039900019E-004,12.650784942234));
#4267 = CARTESIAN_POINT('',(9.980039899998E-004,13.242436987774));
#4268 = CARTESIAN_POINT('',(9.980039900004E-004,13.830311295816));
#4269 = CARTESIAN_POINT('',(9.980039900003E-004,14.41570041873));
#4270 = CARTESIAN_POINT('',(9.980039900004E-004,14.999897591469));
#4271 = CARTESIAN_POINT('',(9.980039900004E-004,15.584088989436));
#4272 = CARTESIAN_POINT('',(9.980039900003E-004,16.169496098151));
#4273 = CARTESIAN_POINT('',(9.980039900009E-004,16.757373987128));
#4274 = CARTESIAN_POINT('',(9.980039899987E-004,17.34900189237));
#4275 = CARTESIAN_POINT('',(9.980039900071E-004,17.945677500902));
#4276 = CARTESIAN_POINT('',(9.980039899972E-004,18.548712194178));
#4277 = CARTESIAN_POINT('',(9.980039900071E-004,19.159406269051));
#4278 = CARTESIAN_POINT('',(9.980039899987E-004,19.779034512466));
#4279 = CARTESIAN_POINT('',(9.980039900014E-004,20.408844081875));
#4280 = CARTESIAN_POINT('',(9.980039899993E-004,21.050050684956));
#4281 = CARTESIAN_POINT('',(9.98003990005E-004,21.703821208895));
#4282 = CARTESIAN_POINT('',(9.980039900056E-004,22.371286775205));
#4283 = CARTESIAN_POINT('',(9.980039899977E-004,23.05358049922));
#4284 = CARTESIAN_POINT('',(9.980039900076E-004,23.75178085446));
#4285 = CARTESIAN_POINT('',(9.980039899975E-004,24.466876432345));
#4286 = CARTESIAN_POINT('',(9.980039900069E-004,25.199732616206));
#4287 = CARTESIAN_POINT('',(9.980039900007E-004,25.951064381106));
#4288 = CARTESIAN_POINT('',(9.980039899949E-004,26.721413648344));
#4289 = CARTESIAN_POINT('',(9.980039900031E-004,27.511129416666));
#4290 = CARTESIAN_POINT('',(9.980039899977E-004,28.320321933917));
#4291 = CARTESIAN_POINT('',(9.98003990011E-004,29.148977246151));
#4292 = CARTESIAN_POINT('',(9.980039900076E-004,29.714213805302));
#4293 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4294 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4295 = ORIENTED_EDGE('',*,*,#4296,.T.);
#4296 = EDGE_CURVE('',#4179,#4177,#4297,.T.);
#4297 = SURFACE_CURVE('',#4298,(#4323,#4351),.PCURVE_S1.);
#4298 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4299,#4300,#4301,#4302,#4303,
    #4304,#4305,#4306,#4307,#4308,#4309,#4310,#4311,#4312,#4313,#4314,
    #4315,#4316,#4317,#4318,#4319,#4320,#4321,#4322),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165514,7.85828166212,
    10.7238180543,13.5836589987,16.4911855045,20.3877608737,
    22.3658107409),.UNSPECIFIED.);
#4299 = CARTESIAN_POINT('',(52.5,62.0096189398,20.));
#4300 = CARTESIAN_POINT('',(52.5,61.5424206863,20.));
#4301 = CARTESIAN_POINT('',(52.4454303198,61.0416334732,20.));
#4302 = CARTESIAN_POINT('',(52.3204177422,60.5184959073,20.));
#4303 = CARTESIAN_POINT('',(51.9273138753,59.5295575063,20.));
#4304 = CARTESIAN_POINT('',(51.241985363,58.6287141988,20.));
#4305 = CARTESIAN_POINT('',(50.8547638076,58.2409926076,20.));
#4306 = CARTESIAN_POINT('',(50.0674913763,57.6475309124,20.));
#4307 = CARTESIAN_POINT('',(49.1451892658,57.257778576,20.));
#4308 = CARTESIAN_POINT('',(48.7232214414,57.1316996033,20.));
#4309 = CARTESIAN_POINT('',(47.8562870364,56.9741379484,20.));
#4310 = CARTESIAN_POINT('',(46.9735996995,57.0082181778,20.));
#4311 = CARTESIAN_POINT('',(46.5369493264,57.0738703735,20.));
#4312 = CARTESIAN_POINT('',(45.6813578803,57.3007731516,20.));
#4313 = CARTESIAN_POINT('',(44.9042453808,57.7024882688,20.));
#4314 = CARTESIAN_POINT('',(44.5396868147,57.9453998579,20.));
#4315 = CARTESIAN_POINT('',(43.7644509634,58.5933176462,20.));
#4316 = CARTESIAN_POINT('',(43.1904774014,59.3849632855,20.));
#4317 = CARTESIAN_POINT('',(42.9362499782,59.8671707401,20.));
#4318 = CARTESIAN_POINT('',(42.6637075651,60.6048000057,20.));
#4319 = CARTESIAN_POINT('',(42.5387812299,61.3207638385,20.));
#4320 = CARTESIAN_POINT('',(42.5123667712,61.5571875624,20.));
#4321 = CARTESIAN_POINT('',(42.5,61.7872092739,20.));
#4322 = CARTESIAN_POINT('',(42.5,62.0096189398,20.));
#4323 = PCURVE('',#3830,#4324);
#4324 = DEFINITIONAL_REPRESENTATION('',(#4325),#4350);
#4325 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4326,#4327,#4328,#4329,#4330,
    #4331,#4332,#4333,#4334,#4335,#4336,#4337,#4338,#4339,#4340,#4341,
    #4342,#4343,#4344,#4345,#4346,#4347,#4348,#4349),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513165514,7.85828166212,
    10.7238180543,13.5836589987,16.4911855045,20.3877608737,
    22.3658107409),.UNSPECIFIED.);
#4326 = CARTESIAN_POINT('',(-37.5,-12.9903810602));
#4327 = CARTESIAN_POINT('',(-37.5,-13.4575793137));
#4328 = CARTESIAN_POINT('',(-37.5545696802,-13.9583665268));
#4329 = CARTESIAN_POINT('',(-37.6795822578,-14.4815040927));
#4330 = CARTESIAN_POINT('',(-38.0726861247,-15.4704424937));
#4331 = CARTESIAN_POINT('',(-38.758014637,-16.3712858012));
#4332 = CARTESIAN_POINT('',(-39.1452361924,-16.7590073924));
#4333 = CARTESIAN_POINT('',(-39.9325086237,-17.3524690876));
#4334 = CARTESIAN_POINT('',(-40.8548107342,-17.742221424));
#4335 = CARTESIAN_POINT('',(-41.2767785586,-17.8683003967));
#4336 = CARTESIAN_POINT('',(-42.1437129636,-18.0258620516));
#4337 = CARTESIAN_POINT('',(-43.0264003005,-17.9917818222));
#4338 = CARTESIAN_POINT('',(-43.4630506736,-17.9261296265));
#4339 = CARTESIAN_POINT('',(-44.3186421197,-17.6992268484));
#4340 = CARTESIAN_POINT('',(-45.0957546192,-17.2975117312));
#4341 = CARTESIAN_POINT('',(-45.4603131853,-17.0546001421));
#4342 = CARTESIAN_POINT('',(-46.2355490366,-16.4066823538));
#4343 = CARTESIAN_POINT('',(-46.8095225986,-15.6150367145));
#4344 = CARTESIAN_POINT('',(-47.0637500218,-15.1328292599));
#4345 = CARTESIAN_POINT('',(-47.3362924349,-14.3951999943));
#4346 = CARTESIAN_POINT('',(-47.4612187701,-13.6792361615));
#4347 = CARTESIAN_POINT('',(-47.4876332288,-13.4428124376));
#4348 = CARTESIAN_POINT('',(-47.5,-13.2127907261));
#4349 = CARTESIAN_POINT('',(-47.5,-12.9903810602));
#4350 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4351 = PCURVE('',#4352,#4361);
#4352 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4353,#4354,#4355,#4356)
    ,(#4357,#4358,#4359,#4360
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4353 = CARTESIAN_POINT('',(52.5,62.00961894,20.));
#4354 = CARTESIAN_POINT('',(52.5,52.00961894,20.));
#4355 = CARTESIAN_POINT('',(42.5,52.00961894,20.));
#4356 = CARTESIAN_POINT('',(42.5,62.00961894,20.));
#4357 = CARTESIAN_POINT('',(52.5,62.00961894,0.E+000));
#4358 = CARTESIAN_POINT('',(52.5,52.00961894,0.E+000));
#4359 = CARTESIAN_POINT('',(42.5,52.00961894,0.E+000));
#4360 = CARTESIAN_POINT('',(42.5,62.00961894,0.E+000));
#4361 = DEFINITIONAL_REPRESENTATION('',(#4362),#4410);
#4362 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4363,#4364,#4365,#4366,#4367,
    #4368,#4369,#4370,#4371,#4372,#4373,#4374,#4375,#4376,#4377,#4378,
    #4379,#4380,#4381,#4382,#4383,#4384,#4385,#4386,#4387,#4388,#4389,
    #4390,#4391,#4392,#4393,#4394,#4395,#4396,#4397,#4398,#4399,#4400,
    #4401,#4402,#4403,#4404,#4405,#4406,#4407,#4408,#4409),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880475,
    1.01662776095,1.524941641425,2.0332555219,2.541569402375,
    3.04988328285,3.558197163325,4.0665110438,4.574824924275,
    5.08313880475,5.591452685225,6.0997665657,6.608080446175,
    7.11639432665,7.624708207125,8.1330220876,8.641335968075,
    9.14964984855,9.657963729025,10.1662776095,10.674591489975,
    11.18290537045,11.691219250925,12.1995331314,12.707847011875,
    13.21616089235,13.724474772825,14.2327886533,14.741102533775,
    15.24941641425,15.757730294725,16.2660441752,16.774358055675,
    17.28267193615,17.790985816625,18.2992996971,18.807613577575,
    19.31592745805,19.824241338525,20.332555219,20.840869099475,
    21.34918297995,21.857496860425,22.3658107409),
  .QUASI_UNIFORM_KNOTS.);
#4363 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4364 = CARTESIAN_POINT('',(9.980039899982E-004,0.28578613449));
#4365 = CARTESIAN_POINT('',(9.980039899992E-004,0.851023725067));
#4366 = CARTESIAN_POINT('',(9.980039900055E-004,1.679658949251));
#4367 = CARTESIAN_POINT('',(9.980039900001E-004,2.488775839249));
#4368 = CARTESIAN_POINT('',(9.980039899943E-004,3.27835738369));
#4369 = CARTESIAN_POINT('',(9.980039900016E-004,4.048590079679));
#4370 = CARTESIAN_POINT('',(9.980039899995E-004,4.79987353786));
#4371 = CARTESIAN_POINT('',(9.980039900006E-004,5.532780961866));
#4372 = CARTESIAN_POINT('',(9.980039899984E-004,6.248020897187));
#4373 = CARTESIAN_POINT('',(9.980039900062E-004,6.946360561602));
#4374 = CARTESIAN_POINT('',(9.980039899985E-004,7.628688622213));
#4375 = CARTESIAN_POINT('',(9.980039900005E-004,8.296073959795));
#4376 = CARTESIAN_POINT('',(9.980039900005E-004,8.949683930198));
#4377 = CARTESIAN_POINT('',(9.980039899986E-004,9.590744767127));
#4378 = CARTESIAN_POINT('',(9.980039900063E-004,10.220499172478));
#4379 = CARTESIAN_POINT('',(9.980039899989E-004,10.840182507808));
#4380 = CARTESIAN_POINT('',(9.980039899995E-004,11.450961979492));
#4381 = CARTESIAN_POINT('',(9.980039900047E-004,12.054057822195));
#4382 = CARTESIAN_POINT('',(9.980039900049E-004,12.650784944821));
#4383 = CARTESIAN_POINT('',(9.980039899992E-004,13.242437000851));
#4384 = CARTESIAN_POINT('',(9.980039900006E-004,13.830311315814));
#4385 = CARTESIAN_POINT('',(9.980039900007E-004,14.415700439734));
#4386 = CARTESIAN_POINT('',(9.980039899992E-004,14.999897612483));
#4387 = CARTESIAN_POINT('',(9.980039900055E-004,15.584089011206));
#4388 = CARTESIAN_POINT('',(9.980039900034E-004,16.169496121161));
#4389 = CARTESIAN_POINT('',(9.980039900056E-004,16.757374011576));
#4390 = CARTESIAN_POINT('',(9.980039899988E-004,17.349001918072));
#4391 = CARTESIAN_POINT('',(9.980039900027E-004,17.945677527575));
#4392 = CARTESIAN_POINT('',(9.980039899943E-004,18.548712222154));
#4393 = CARTESIAN_POINT('',(9.980039900028E-004,19.159406299081));
#4394 = CARTESIAN_POINT('',(9.980039899987E-004,19.779034544911));
#4395 = CARTESIAN_POINT('',(9.980039900069E-004,20.408844116443));
#4396 = CARTESIAN_POINT('',(9.980039899995E-004,21.050050720802));
#4397 = CARTESIAN_POINT('',(9.9800399E-004,21.703821245659));
#4398 = CARTESIAN_POINT('',(9.980039900057E-004,22.371286813055));
#4399 = CARTESIAN_POINT('',(9.980039900038E-004,23.053580538057));
#4400 = CARTESIAN_POINT('',(9.980039900059E-004,23.751780894188));
#4401 = CARTESIAN_POINT('',(9.980039899997E-004,24.466876472869));
#4402 = CARTESIAN_POINT('',(9.980039900014E-004,25.199732657463));
#4403 = CARTESIAN_POINT('',(9.98003990001E-004,25.951064422964));
#4404 = CARTESIAN_POINT('',(9.980039900011E-004,26.721413690527));
#4405 = CARTESIAN_POINT('',(9.980039900011E-004,27.511129458051));
#4406 = CARTESIAN_POINT('',(9.980039900011E-004,28.320321956023));
#4407 = CARTESIAN_POINT('',(9.980039900014E-004,29.148977247686));
#4408 = CARTESIAN_POINT('',(9.980039900007E-004,29.71421380249));
#4409 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4410 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4411 = FACE_BOUND('',#4412,.T.);
#4412 = EDGE_LOOP('',(#4413,#4533));
#4413 = ORIENTED_EDGE('',*,*,#4414,.T.);
#4414 = EDGE_CURVE('',#4415,#4417,#4419,.T.);
#4415 = VERTEX_POINT('',#4416);
#4416 = CARTESIAN_POINT('',(127.5,62.0096189398,20.));
#4417 = VERTEX_POINT('',#4418);
#4418 = CARTESIAN_POINT('',(137.5,62.0096189398,20.));
#4419 = SURFACE_CURVE('',#4420,(#4445,#4473),.PCURVE_S1.);
#4420 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4421,#4422,#4423,#4424,#4425,
    #4426,#4427,#4428,#4429,#4430,#4431,#4432,#4433,#4434,#4435,#4436,
    #4437,#4438,#4439,#4440,#4441,#4442,#4443,#4444),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163359,7.85828165183,
    10.7238180689,13.5836590139,16.4911855248,20.3877608811,
    22.3658107236),.UNSPECIFIED.);
#4421 = CARTESIAN_POINT('',(127.5,62.0096189398,20.));
#4422 = CARTESIAN_POINT('',(127.5,62.476817191,20.));
#4423 = CARTESIAN_POINT('',(127.55456968,62.9776044013,20.));
#4424 = CARTESIAN_POINT('',(127.679582259,63.5007419779,20.));
#4425 = CARTESIAN_POINT('',(128.072686127,64.4896803774,20.));
#4426 = CARTESIAN_POINT('',(128.758014639,65.390523683,20.));
#4427 = CARTESIAN_POINT('',(129.145236192,65.7782452702,20.));
#4428 = CARTESIAN_POINT('',(129.932508627,66.3717069689,20.));
#4429 = CARTESIAN_POINT('',(130.854810743,66.7614593064,20.));
#4430 = CARTESIAN_POINT('',(131.276778553,66.8875382755,20.));
#4431 = CARTESIAN_POINT('',(132.143712962,67.0450999313,20.));
#4432 = CARTESIAN_POINT('',(133.026400303,67.0110197017,20.));
#4433 = CARTESIAN_POINT('',(133.463050674,66.9453675059,20.));
#4434 = CARTESIAN_POINT('',(134.31864212,66.718464728,20.));
#4435 = CARTESIAN_POINT('',(135.09575462,66.3167496104,20.));
#4436 = CARTESIAN_POINT('',(135.460313186,66.0738380209,20.));
#4437 = CARTESIAN_POINT('',(136.235549037,65.4259202334,20.));
#4438 = CARTESIAN_POINT('',(136.809522598,64.6342745955,20.));
#4439 = CARTESIAN_POINT('',(137.063750023,64.1520671352,20.));
#4440 = CARTESIAN_POINT('',(137.336292434,63.4144378745,20.));
#4441 = CARTESIAN_POINT('',(137.461218769,62.6984740442,20.));
#4442 = CARTESIAN_POINT('',(137.487633229,62.4620503115,20.));
#4443 = CARTESIAN_POINT('',(137.5,62.232028603,20.));
#4444 = CARTESIAN_POINT('',(137.5,62.0096189398,20.));
#4445 = PCURVE('',#3830,#4446);
#4446 = DEFINITIONAL_REPRESENTATION('',(#4447),#4472);
#4447 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4448,#4449,#4450,#4451,#4452,
    #4453,#4454,#4455,#4456,#4457,#4458,#4459,#4460,#4461,#4462,#4463,
    #4464,#4465,#4466,#4467,#4468,#4469,#4470,#4471),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163359,7.85828165183,
    10.7238180689,13.5836590139,16.4911855248,20.3877608811,
    22.3658107236),.UNSPECIFIED.);
#4448 = CARTESIAN_POINT('',(37.5,-12.9903810602));
#4449 = CARTESIAN_POINT('',(37.5,-12.523182809));
#4450 = CARTESIAN_POINT('',(37.55456968,-12.0223955987));
#4451 = CARTESIAN_POINT('',(37.679582259,-11.4992580221));
#4452 = CARTESIAN_POINT('',(38.072686127,-10.5103196226));
#4453 = CARTESIAN_POINT('',(38.758014639,-9.609476317));
#4454 = CARTESIAN_POINT('',(39.145236192,-9.2217547298));
#4455 = CARTESIAN_POINT('',(39.932508627,-8.6282930311));
#4456 = CARTESIAN_POINT('',(40.854810743,-8.2385406936));
#4457 = CARTESIAN_POINT('',(41.276778553,-8.1124617245));
#4458 = CARTESIAN_POINT('',(42.143712962,-7.9549000687));
#4459 = CARTESIAN_POINT('',(43.026400303,-7.9889802983));
#4460 = CARTESIAN_POINT('',(43.463050674,-8.0546324941));
#4461 = CARTESIAN_POINT('',(44.31864212,-8.281535272));
#4462 = CARTESIAN_POINT('',(45.09575462,-8.6832503896));
#4463 = CARTESIAN_POINT('',(45.460313186,-8.9261619791));
#4464 = CARTESIAN_POINT('',(46.235549037,-9.5740797666));
#4465 = CARTESIAN_POINT('',(46.809522598,-10.3657254045));
#4466 = CARTESIAN_POINT('',(47.063750023,-10.8479328648));
#4467 = CARTESIAN_POINT('',(47.336292434,-11.5855621255));
#4468 = CARTESIAN_POINT('',(47.461218769,-12.3015259558));
#4469 = CARTESIAN_POINT('',(47.487633229,-12.5379496885));
#4470 = CARTESIAN_POINT('',(47.5,-12.767971397));
#4471 = CARTESIAN_POINT('',(47.5,-12.9903810602));
#4472 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4473 = PCURVE('',#4474,#4483);
#4474 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4475,#4476,#4477,#4478)
    ,(#4479,#4480,#4481,#4482
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4475 = CARTESIAN_POINT('',(127.5,62.00961894,20.));
#4476 = CARTESIAN_POINT('',(127.5,72.00961894,20.));
#4477 = CARTESIAN_POINT('',(137.5,72.00961894,20.));
#4478 = CARTESIAN_POINT('',(137.5,62.00961894,20.));
#4479 = CARTESIAN_POINT('',(127.5,62.00961894,0.E+000));
#4480 = CARTESIAN_POINT('',(127.5,72.00961894,0.E+000));
#4481 = CARTESIAN_POINT('',(137.5,72.00961894,0.E+000));
#4482 = CARTESIAN_POINT('',(137.5,62.00961894,0.E+000));
#4483 = DEFINITIONAL_REPRESENTATION('',(#4484),#4532);
#4484 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4485,#4486,#4487,#4488,#4489,
    #4490,#4491,#4492,#4493,#4494,#4495,#4496,#4497,#4498,#4499,#4500,
    #4501,#4502,#4503,#4504,#4505,#4506,#4507,#4508,#4509,#4510,#4511,
    #4512,#4513,#4514,#4515,#4516,#4517,#4518,#4519,#4520,#4521,#4522,
    #4523,#4524,#4525,#4526,#4527,#4528,#4529,#4530,#4531),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880082,
    1.016627760164,1.524941640245,2.033255520327,2.541569400409,
    3.049883280491,3.558197160573,4.066511040655,4.574824920736,
    5.083138800818,5.5914526809,6.099766560982,6.608080441064,
    7.116394321145,7.624708201227,8.133022081309,8.641335961391,
    9.149649841473,9.657963721555,10.166277601636,10.674591481718,
    11.1829053618,11.691219241882,12.199533121964,12.707847002045,
    13.216160882127,13.724474762209,14.232788642291,14.741102522373,
    15.249416402455,15.757730282536,16.266044162618,16.7743580427,
    17.282671922782,17.790985802864,18.299299682945,18.807613563027,
    19.315927443109,19.824241323191,20.332555203273,20.840869083355,
    21.349182963436,21.857496843518,22.3658107236),
  .QUASI_UNIFORM_KNOTS.);
#4485 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4486 = CARTESIAN_POINT('',(9.980039900008E-004,0.28578613343));
#4487 = CARTESIAN_POINT('',(9.980039900006E-004,0.851023723438));
#4488 = CARTESIAN_POINT('',(9.980039899967E-004,1.679658950391));
#4489 = CARTESIAN_POINT('',(9.980039900128E-004,2.48877584581));
#4490 = CARTESIAN_POINT('',(9.980039899952E-004,3.278357397118));
#4491 = CARTESIAN_POINT('',(9.98003990007E-004,4.048590099459));
#4492 = CARTESIAN_POINT('',(9.980039899987E-004,4.799873561925));
#4493 = CARTESIAN_POINT('',(9.980039899991E-004,5.532780987459));
#4494 = CARTESIAN_POINT('',(9.980039900061E-004,6.248020921925));
#4495 = CARTESIAN_POINT('',(9.980039899991E-004,6.946360585173));
#4496 = CARTESIAN_POINT('',(9.980039899989E-004,7.628688643638));
#4497 = CARTESIAN_POINT('',(9.980039900068E-004,8.296073977935));
#4498 = CARTESIAN_POINT('',(9.980039899971E-004,8.949683944617));
#4499 = CARTESIAN_POINT('',(9.980039900068E-004,9.590744778216));
#4500 = CARTESIAN_POINT('',(9.980039899991E-004,10.220499181319));
#4501 = CARTESIAN_POINT('',(9.980039899992E-004,10.840182515446));
#4502 = CARTESIAN_POINT('',(9.980039900065E-004,11.450961987093));
#4503 = CARTESIAN_POINT('',(9.980039899989E-004,12.054057826011));
#4504 = CARTESIAN_POINT('',(9.980039900008E-004,12.650784939501));
#4505 = CARTESIAN_POINT('',(9.98003990001E-004,13.242436985231));
#4506 = CARTESIAN_POINT('',(9.980039899985E-004,13.830311293435));
#4507 = CARTESIAN_POINT('',(9.980039900084E-004,14.415700416456));
#4508 = CARTESIAN_POINT('',(9.980039899928E-004,14.999897589126));
#4509 = CARTESIAN_POINT('',(9.980039900027E-004,15.584088986808));
#4510 = CARTESIAN_POINT('',(9.980039900001E-004,16.169496095104));
#4511 = CARTESIAN_POINT('',(9.980039900009E-004,16.757373983641));
#4512 = CARTESIAN_POINT('',(9.980039900007E-004,17.349001888558));
#4513 = CARTESIAN_POINT('',(9.980039900008E-004,17.945677496913));
#4514 = CARTESIAN_POINT('',(9.980039900008E-004,18.548712190339));
#4515 = CARTESIAN_POINT('',(9.980039900007E-004,19.159406265994));
#4516 = CARTESIAN_POINT('',(9.980039900012E-004,19.779034510528));
#4517 = CARTESIAN_POINT('',(9.980039899996E-004,20.408844080842));
#4518 = CARTESIAN_POINT('',(9.980039900059E-004,21.050050684138));
#4519 = CARTESIAN_POINT('',(9.980039900039E-004,21.703821207655));
#4520 = CARTESIAN_POINT('',(9.980039900059E-004,22.371286774169));
#4521 = CARTESIAN_POINT('',(9.9800399E-004,23.053580499765));
#4522 = CARTESIAN_POINT('',(9.980039900002E-004,23.751780857846));
#4523 = CARTESIAN_POINT('',(9.980039900055E-004,24.466876439143));
#4524 = CARTESIAN_POINT('',(9.980039900057E-004,25.199732625989));
#4525 = CARTESIAN_POINT('',(9.9800399E-004,25.951064392591));
#4526 = CARTESIAN_POINT('',(9.980039900014E-004,26.721413659959));
#4527 = CARTESIAN_POINT('',(9.980039900015E-004,27.511129428057));
#4528 = CARTESIAN_POINT('',(9.980039899999E-004,28.320321940437));
#4529 = CARTESIAN_POINT('',(9.980039900065E-004,29.148977247336));
#4530 = CARTESIAN_POINT('',(9.980039900044E-004,29.714213804951));
#4531 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4532 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4533 = ORIENTED_EDGE('',*,*,#4534,.T.);
#4534 = EDGE_CURVE('',#4417,#4415,#4535,.T.);
#4535 = SURFACE_CURVE('',#4536,(#4561,#4589),.PCURVE_S1.);
#4536 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4537,#4538,#4539,#4540,#4541,
    #4542,#4543,#4544,#4545,#4546,#4547,#4548,#4549,#4550,#4551,#4552,
    #4553,#4554,#4555,#4556,#4557,#4558,#4559,#4560),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164798,7.85828164866,
    10.7238180467,13.583658987,16.4911854966,20.3877608643,22.3658107618
    ),.UNSPECIFIED.);
#4537 = CARTESIAN_POINT('',(137.5,62.0096189398,20.));
#4538 = CARTESIAN_POINT('',(137.5,61.5424206871,20.));
#4539 = CARTESIAN_POINT('',(137.44543032,61.0416334749,20.));
#4540 = CARTESIAN_POINT('',(137.320417741,60.5184959056,20.));
#4541 = CARTESIAN_POINT('',(136.927313875,59.5295575063,20.));
#4542 = CARTESIAN_POINT('',(136.241985364,58.6287142002,20.));
#4543 = CARTESIAN_POINT('',(135.854763806,58.2409926065,20.));
#4544 = CARTESIAN_POINT('',(135.067491375,57.6475309115,20.));
#4545 = CARTESIAN_POINT('',(134.145189264,57.2577785753,20.));
#4546 = CARTESIAN_POINT('',(133.723221441,57.1316996035,20.));
#4547 = CARTESIAN_POINT('',(132.856287036,56.9741379484,20.));
#4548 = CARTESIAN_POINT('',(131.973599699,57.0082181778,20.));
#4549 = CARTESIAN_POINT('',(131.536949325,57.0738703738,20.));
#4550 = CARTESIAN_POINT('',(130.681357879,57.300773152,20.));
#4551 = CARTESIAN_POINT('',(129.90424538,57.7024882694,20.));
#4552 = CARTESIAN_POINT('',(129.539686814,57.945399859,20.));
#4553 = CARTESIAN_POINT('',(128.764450962,58.5933176475,20.));
#4554 = CARTESIAN_POINT('',(128.1904774,59.3849632871,20.));
#4555 = CARTESIAN_POINT('',(127.93624998,59.8671707404,20.));
#4556 = CARTESIAN_POINT('',(127.663707566,60.6048000098,20.));
#4557 = CARTESIAN_POINT('',(127.53878123,61.3207638459,20.));
#4558 = CARTESIAN_POINT('',(127.512366772,61.5571875554,20.));
#4559 = CARTESIAN_POINT('',(127.5,61.7872092705,20.));
#4560 = CARTESIAN_POINT('',(127.5,62.0096189398,20.));
#4561 = PCURVE('',#3830,#4562);
#4562 = DEFINITIONAL_REPRESENTATION('',(#4563),#4588);
#4563 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4564,#4565,#4566,#4567,#4568,
    #4569,#4570,#4571,#4572,#4573,#4574,#4575,#4576,#4577,#4578,#4579,
    #4580,#4581,#4582,#4583,#4584,#4585,#4586,#4587),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164798,7.85828164866,
    10.7238180467,13.583658987,16.4911854966,20.3877608643,22.3658107618
    ),.UNSPECIFIED.);
#4564 = CARTESIAN_POINT('',(47.5,-12.9903810602));
#4565 = CARTESIAN_POINT('',(47.5,-13.4575793129));
#4566 = CARTESIAN_POINT('',(47.44543032,-13.9583665251));
#4567 = CARTESIAN_POINT('',(47.320417741,-14.4815040944));
#4568 = CARTESIAN_POINT('',(46.927313875,-15.4704424937));
#4569 = CARTESIAN_POINT('',(46.241985364,-16.3712857998));
#4570 = CARTESIAN_POINT('',(45.854763806,-16.7590073935));
#4571 = CARTESIAN_POINT('',(45.067491375,-17.3524690885));
#4572 = CARTESIAN_POINT('',(44.145189264,-17.7422214247));
#4573 = CARTESIAN_POINT('',(43.723221441,-17.8683003965));
#4574 = CARTESIAN_POINT('',(42.856287036,-18.0258620516));
#4575 = CARTESIAN_POINT('',(41.973599699,-17.9917818222));
#4576 = CARTESIAN_POINT('',(41.536949325,-17.9261296262));
#4577 = CARTESIAN_POINT('',(40.681357879,-17.699226848));
#4578 = CARTESIAN_POINT('',(39.90424538,-17.2975117306));
#4579 = CARTESIAN_POINT('',(39.539686814,-17.054600141));
#4580 = CARTESIAN_POINT('',(38.764450962,-16.4066823525));
#4581 = CARTESIAN_POINT('',(38.1904774,-15.6150367129));
#4582 = CARTESIAN_POINT('',(37.93624998,-15.1328292596));
#4583 = CARTESIAN_POINT('',(37.663707566,-14.3951999902));
#4584 = CARTESIAN_POINT('',(37.53878123,-13.6792361541));
#4585 = CARTESIAN_POINT('',(37.512366772,-13.4428124446));
#4586 = CARTESIAN_POINT('',(37.5,-13.2127907295));
#4587 = CARTESIAN_POINT('',(37.5,-12.9903810602));
#4588 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4589 = PCURVE('',#4590,#4599);
#4590 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4591,#4592,#4593,#4594)
    ,(#4595,#4596,#4597,#4598
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4591 = CARTESIAN_POINT('',(137.5,62.00961894,20.));
#4592 = CARTESIAN_POINT('',(137.5,52.00961894,20.));
#4593 = CARTESIAN_POINT('',(127.5,52.00961894,20.));
#4594 = CARTESIAN_POINT('',(127.5,62.00961894,20.));
#4595 = CARTESIAN_POINT('',(137.5,62.00961894,0.E+000));
#4596 = CARTESIAN_POINT('',(137.5,52.00961894,0.E+000));
#4597 = CARTESIAN_POINT('',(127.5,52.00961894,0.E+000));
#4598 = CARTESIAN_POINT('',(127.5,62.00961894,0.E+000));
#4599 = DEFINITIONAL_REPRESENTATION('',(#4600),#4648);
#4600 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4601,#4602,#4603,#4604,#4605,
    #4606,#4607,#4608,#4609,#4610,#4611,#4612,#4613,#4614,#4615,#4616,
    #4617,#4618,#4619,#4620,#4621,#4622,#4623,#4624,#4625,#4626,#4627,
    #4628,#4629,#4630,#4631,#4632,#4633,#4634,#4635,#4636,#4637,#4638,
    #4639,#4640,#4641,#4642,#4643,#4644,#4645,#4646,#4647),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.50831388095,
    1.0166277619,1.52494164285,2.0332555238,2.54156940475,3.0498832857,
    3.55819716665,4.0665110476,4.57482492855,5.0831388095,5.59145269045,
    6.0997665714,6.60808045235,7.1163943333,7.62470821425,8.1330220952,
    8.64133597615,9.1496498571,9.65796373805,10.166277619,10.67459149995
    ,11.1829053809,11.69121926185,12.1995331428,12.70784702375,
    13.2161609047,13.72447478565,14.2327886666,14.74110254755,
    15.2494164285,15.75773030945,16.2660441904,16.77435807135,
    17.2826719523,17.79098583325,18.2992997142,18.80761359515,
    19.3159274761,19.82424135705,20.332555238,20.84086911895,
    21.3491829999,21.85749688085,22.3658107618),.QUASI_UNIFORM_KNOTS.);
#4601 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4602 = CARTESIAN_POINT('',(9.9800399E-004,0.285786134697));
#4603 = CARTESIAN_POINT('',(9.980039900001E-004,0.851023725931));
#4604 = CARTESIAN_POINT('',(9.980039899994E-004,1.679658951849));
#4605 = CARTESIAN_POINT('',(9.98003990002E-004,2.488775844557));
#4606 = CARTESIAN_POINT('',(9.980039899921E-004,3.278357392157));
#4607 = CARTESIAN_POINT('',(9.980039900078E-004,4.048590091131));
#4608 = CARTESIAN_POINT('',(9.980039899977E-004,4.799873551566));
#4609 = CARTESIAN_POINT('',(9.98003990001E-004,5.532780976828));
#4610 = CARTESIAN_POINT('',(9.980039899981E-004,6.248020912541));
#4611 = CARTESIAN_POINT('',(9.980039900065E-004,6.946360577195));
#4612 = CARTESIAN_POINT('',(9.98003989997E-004,7.628688638873));
#4613 = CARTESIAN_POINT('',(9.980039900052E-004,8.296073978386));
#4614 = CARTESIAN_POINT('',(9.980039900031E-004,8.949683951118));
#4615 = CARTESIAN_POINT('',(9.980039900033E-004,9.590744790129));
#4616 = CARTESIAN_POINT('',(9.980039900048E-004,10.220499196831));
#4617 = CARTESIAN_POINT('',(9.980039899985E-004,10.840182532611));
#4618 = CARTESIAN_POINT('',(9.980039900009E-004,11.450962004594));
#4619 = CARTESIAN_POINT('',(9.980039899978E-004,12.054057847271));
#4620 = CARTESIAN_POINT('',(9.980039900077E-004,12.650784969126));
#4621 = CARTESIAN_POINT('',(9.980039899927E-004,13.242437024218));
#4622 = CARTESIAN_POINT('',(9.980039900003E-004,13.830311338687));
#4623 = CARTESIAN_POINT('',(9.980039900061E-004,14.415700462863));
#4624 = CARTESIAN_POINT('',(9.980039899967E-004,14.999897636024));
#4625 = CARTESIAN_POINT('',(9.98003990007E-004,15.584089035661));
#4626 = CARTESIAN_POINT('',(9.980039899966E-004,16.169496146983));
#4627 = CARTESIAN_POINT('',(9.980039900067E-004,16.757374038821));
#4628 = CARTESIAN_POINT('',(9.980039899981E-004,17.349001946392));
#4629 = CARTESIAN_POINT('',(9.980039900009E-004,17.945677556594));
#4630 = CARTESIAN_POINT('',(9.980039899983E-004,18.548712251895));
#4631 = CARTESIAN_POINT('',(9.980039900062E-004,19.159406329557));
#4632 = CARTESIAN_POINT('',(9.980039899983E-004,19.779034576268));
#4633 = CARTESIAN_POINT('',(9.980039900008E-004,20.40884414892));
#4634 = CARTESIAN_POINT('',(9.980039899987E-004,21.050050754629));
#4635 = CARTESIAN_POINT('',(9.980039900046E-004,21.703821280962));
#4636 = CARTESIAN_POINT('',(9.980039900046E-004,22.371286849544));
#4637 = CARTESIAN_POINT('',(9.980039899989E-004,23.053580575252));
#4638 = CARTESIAN_POINT('',(9.980039900003E-004,23.751780931797));
#4639 = CARTESIAN_POINT('',(9.980039900005E-004,24.466876510942));
#4640 = CARTESIAN_POINT('',(9.980039899984E-004,25.199732696417));
#4641 = CARTESIAN_POINT('',(9.980039900064E-004,25.951064463423));
#4642 = CARTESIAN_POINT('',(9.98003989998E-004,26.721413733117));
#4643 = CARTESIAN_POINT('',(9.980039900021E-004,27.511129502751));
#4644 = CARTESIAN_POINT('',(9.98003989994E-004,28.320321980919));
#4645 = CARTESIAN_POINT('',(9.980039900011E-004,29.148977250316));
#4646 = CARTESIAN_POINT('',(9.980039900018E-004,29.714213799825));
#4647 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4648 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4649 = FACE_BOUND('',#4650,.T.);
#4650 = EDGE_LOOP('',(#4651,#4771));
#4651 = ORIENTED_EDGE('',*,*,#4652,.T.);
#4652 = EDGE_CURVE('',#4653,#4655,#4657,.T.);
#4653 = VERTEX_POINT('',#4654);
#4654 = CARTESIAN_POINT('',(127.5,87.9903810602,20.));
#4655 = VERTEX_POINT('',#4656);
#4656 = CARTESIAN_POINT('',(137.5,87.9903810602,20.));
#4657 = SURFACE_CURVE('',#4658,(#4683,#4711),.PCURVE_S1.);
#4658 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4659,#4660,#4661,#4662,#4663,
    #4664,#4665,#4666,#4667,#4668,#4669,#4670,#4671,#4672,#4673,#4674,
    #4675,#4676,#4677,#4678,#4679,#4680,#4681,#4682),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164903,7.85828164914,
    10.7238180511,13.5836589913,16.4911854971,20.3877608695,
    22.3658107102),.UNSPECIFIED.);
#4659 = CARTESIAN_POINT('',(127.5,87.9903810602,20.));
#4660 = CARTESIAN_POINT('',(127.5,88.457579313,20.));
#4661 = CARTESIAN_POINT('',(127.55456968,88.9583665253,20.));
#4662 = CARTESIAN_POINT('',(127.679582259,89.4815040941,20.));
#4663 = CARTESIAN_POINT('',(128.072686125,90.4704424936,20.));
#4664 = CARTESIAN_POINT('',(128.758014636,91.3712857996,20.));
#4665 = CARTESIAN_POINT('',(129.145236194,91.7590073937,20.));
#4666 = CARTESIAN_POINT('',(129.932508626,92.3524690889,20.));
#4667 = CARTESIAN_POINT('',(130.854810737,92.7422214252,20.));
#4668 = CARTESIAN_POINT('',(131.276778557,92.8683003963,20.));
#4669 = CARTESIAN_POINT('',(132.143712963,93.0258620515,20.));
#4670 = CARTESIAN_POINT('',(133.026400301,92.9917818222,20.));
#4671 = CARTESIAN_POINT('',(133.463050675,92.9261296262,20.));
#4672 = CARTESIAN_POINT('',(134.31864212,92.6992268482,20.));
#4673 = CARTESIAN_POINT('',(135.095754619,92.2975117311,20.));
#4674 = CARTESIAN_POINT('',(135.460313185,92.0546001422,20.));
#4675 = CARTESIAN_POINT('',(136.235549037,91.4066823535,20.));
#4676 = CARTESIAN_POINT('',(136.809522599,90.6150367138,20.));
#4677 = CARTESIAN_POINT('',(137.063750022,90.1328292589,20.));
#4678 = CARTESIAN_POINT('',(137.336292433,89.3951999961,20.));
#4679 = CARTESIAN_POINT('',(137.461218769,88.679236166,20.));
#4680 = CARTESIAN_POINT('',(137.487633229,88.4428124314,20.));
#4681 = CARTESIAN_POINT('',(137.5,88.2127907231,20.));
#4682 = CARTESIAN_POINT('',(137.5,87.9903810602,20.));
#4683 = PCURVE('',#3830,#4684);
#4684 = DEFINITIONAL_REPRESENTATION('',(#4685),#4710);
#4685 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4686,#4687,#4688,#4689,#4690,
    #4691,#4692,#4693,#4694,#4695,#4696,#4697,#4698,#4699,#4700,#4701,
    #4702,#4703,#4704,#4705,#4706,#4707,#4708,#4709),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164903,7.85828164914,
    10.7238180511,13.5836589913,16.4911854971,20.3877608695,
    22.3658107102),.UNSPECIFIED.);
#4686 = CARTESIAN_POINT('',(37.5,12.9903810602));
#4687 = CARTESIAN_POINT('',(37.5,13.457579313));
#4688 = CARTESIAN_POINT('',(37.55456968,13.9583665253));
#4689 = CARTESIAN_POINT('',(37.679582259,14.4815040941));
#4690 = CARTESIAN_POINT('',(38.072686125,15.4704424936));
#4691 = CARTESIAN_POINT('',(38.758014636,16.3712857996));
#4692 = CARTESIAN_POINT('',(39.145236194,16.7590073937));
#4693 = CARTESIAN_POINT('',(39.932508626,17.3524690889));
#4694 = CARTESIAN_POINT('',(40.854810737,17.7422214252));
#4695 = CARTESIAN_POINT('',(41.276778557,17.8683003963));
#4696 = CARTESIAN_POINT('',(42.143712963,18.0258620515));
#4697 = CARTESIAN_POINT('',(43.026400301,17.9917818222));
#4698 = CARTESIAN_POINT('',(43.463050675,17.9261296262));
#4699 = CARTESIAN_POINT('',(44.31864212,17.6992268482));
#4700 = CARTESIAN_POINT('',(45.095754619,17.2975117311));
#4701 = CARTESIAN_POINT('',(45.460313185,17.0546001422));
#4702 = CARTESIAN_POINT('',(46.235549037,16.4066823535));
#4703 = CARTESIAN_POINT('',(46.809522599,15.6150367138));
#4704 = CARTESIAN_POINT('',(47.063750022,15.1328292589));
#4705 = CARTESIAN_POINT('',(47.336292433,14.3951999961));
#4706 = CARTESIAN_POINT('',(47.461218769,13.679236166));
#4707 = CARTESIAN_POINT('',(47.487633229,13.4428124314));
#4708 = CARTESIAN_POINT('',(47.5,13.2127907231));
#4709 = CARTESIAN_POINT('',(47.5,12.9903810602));
#4710 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4711 = PCURVE('',#4712,#4721);
#4712 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4713,#4714,#4715,#4716)
    ,(#4717,#4718,#4719,#4720
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4713 = CARTESIAN_POINT('',(127.5,87.99038106,20.));
#4714 = CARTESIAN_POINT('',(127.5,97.99038106,20.));
#4715 = CARTESIAN_POINT('',(137.5,97.99038106,20.));
#4716 = CARTESIAN_POINT('',(137.5,87.99038106,20.));
#4717 = CARTESIAN_POINT('',(127.5,87.99038106,0.E+000));
#4718 = CARTESIAN_POINT('',(127.5,97.99038106,0.E+000));
#4719 = CARTESIAN_POINT('',(137.5,97.99038106,0.E+000));
#4720 = CARTESIAN_POINT('',(137.5,87.99038106,0.E+000));
#4721 = DEFINITIONAL_REPRESENTATION('',(#4722),#4770);
#4722 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4723,#4724,#4725,#4726,#4727,
    #4728,#4729,#4730,#4731,#4732,#4733,#4734,#4735,#4736,#4737,#4738,
    #4739,#4740,#4741,#4742,#4743,#4744,#4745,#4746,#4747,#4748,#4749,
    #4750,#4751,#4752,#4753,#4754,#4755,#4756,#4757,#4758,#4759,#4760,
    #4761,#4762,#4763,#4764,#4765,#4766,#4767,#4768,#4769),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313879777,
    1.016627759555,1.524941639332,2.033255519109,2.541569398886,
    3.049883278664,3.558197158441,4.066511038218,4.574824917995,
    5.083138797773,5.59145267755,6.099766557327,6.608080437105,
    7.116394316882,7.624708196659,8.133022076436,8.641335956214,
    9.149649835991,9.657963715768,10.166277595545,10.674591475323,
    11.1829053551,11.691219234877,12.199533114655,12.707846994432,
    13.216160874209,13.724474753986,14.232788633764,14.741102513541,
    15.249416393318,15.757730273095,16.266044152873,16.77435803265,
    17.282671912427,17.790985792205,18.299299671982,18.807613551759,
    19.315927431536,19.824241311314,20.332555191091,20.840869070868,
    21.349182950645,21.857496830423,22.3658107102),
  .QUASI_UNIFORM_KNOTS.);
#4723 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4724 = CARTESIAN_POINT('',(9.980039899968E-004,0.285786134035));
#4725 = CARTESIAN_POINT('',(9.980039899954E-004,0.851023723936));
#4726 = CARTESIAN_POINT('',(9.980039899998E-004,1.679658947836));
#4727 = CARTESIAN_POINT('',(9.980039900059E-004,2.488775838494));
#4728 = CARTESIAN_POINT('',(9.980039899985E-004,3.278357384096));
#4729 = CARTESIAN_POINT('',(9.980039900007E-004,4.048590081208));
#4730 = CARTESIAN_POINT('',(9.980039899996E-004,4.799873539974));
#4731 = CARTESIAN_POINT('',(9.980039900019E-004,5.532780963769));
#4732 = CARTESIAN_POINT('',(9.98003989994E-004,6.248020898176));
#4733 = CARTESIAN_POINT('',(9.980039900024E-004,6.946360561552));
#4734 = CARTESIAN_POINT('',(9.980039899982E-004,7.628688622063));
#4735 = CARTESIAN_POINT('',(9.980039900066E-004,8.296073960544));
#4736 = CARTESIAN_POINT('',(9.980039899987E-004,8.949683932339));
#4737 = CARTESIAN_POINT('',(9.980039900008E-004,9.590744770448));
#4738 = CARTESIAN_POINT('',(9.980039900007E-004,10.220499176237));
#4739 = CARTESIAN_POINT('',(9.980039899992E-004,10.840182511));
#4740 = CARTESIAN_POINT('',(9.980039900055E-004,11.450961981931));
#4741 = CARTESIAN_POINT('',(9.980039900035E-004,12.054057822704));
#4742 = CARTESIAN_POINT('',(9.980039900056E-004,12.650784941612));
#4743 = CARTESIAN_POINT('',(9.980039899994E-004,13.242436993607));
#4744 = CARTESIAN_POINT('',(9.98003990001E-004,13.830311305766));
#4745 = CARTESIAN_POINT('',(9.980039900008E-004,14.415700428839));
#4746 = CARTESIAN_POINT('',(9.980039900004E-004,14.999897601012));
#4747 = CARTESIAN_POINT('',(9.980039900025E-004,15.584088999461));
#4748 = CARTESIAN_POINT('',(9.980039899946E-004,16.169496109395));
#4749 = CARTESIAN_POINT('',(9.980039900031E-004,16.757373999623));
#4750 = CARTESIAN_POINT('',(9.980039899984E-004,17.349001905418));
#4751 = CARTESIAN_POINT('',(9.98003990009E-004,17.945677513811));
#4752 = CARTESIAN_POINT('',(9.980039899931E-004,18.548712207394));
#4753 = CARTESIAN_POINT('',(9.980039900036E-004,19.159406283254));
#4754 = CARTESIAN_POINT('',(9.98003989999E-004,19.779034527962));
#4755 = CARTESIAN_POINT('',(9.98003990007E-004,20.40884409835));
#4756 = CARTESIAN_POINT('',(9.980039900012E-004,21.050050701577));
#4757 = CARTESIAN_POINT('',(9.980039899956E-004,21.703821225222));
#4758 = CARTESIAN_POINT('',(9.980039900029E-004,22.371286791223));
#4759 = CARTESIAN_POINT('',(9.98003990001E-004,23.053580514668));
#4760 = CARTESIAN_POINT('',(9.980039900015E-004,23.751780869104));
#4761 = CARTESIAN_POINT('',(9.980039900016E-004,24.466876446008));
#4762 = CARTESIAN_POINT('',(9.980039900011E-004,25.199732628809));
#4763 = CARTESIAN_POINT('',(9.980039900032E-004,25.951064392582));
#4764 = CARTESIAN_POINT('',(9.980039899956E-004,26.721413658498));
#4765 = CARTESIAN_POINT('',(9.98003990003E-004,27.511129424557));
#4766 = CARTESIAN_POINT('',(9.980039900026E-004,28.320321937829));
#4767 = CARTESIAN_POINT('',(9.980039899974E-004,29.148977246312));
#4768 = CARTESIAN_POINT('',(9.980039899976E-004,29.71421380479));
#4769 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4770 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4771 = ORIENTED_EDGE('',*,*,#4772,.T.);
#4772 = EDGE_CURVE('',#4655,#4653,#4773,.T.);
#4773 = SURFACE_CURVE('',#4774,(#4799,#4827),.PCURVE_S1.);
#4774 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4775,#4776,#4777,#4778,#4779,
    #4780,#4781,#4782,#4783,#4784,#4785,#4786,#4787,#4788,#4789,#4790,
    #4791,#4792,#4793,#4794,#4795,#4796,#4797,#4798),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163251,7.85828164883,
    10.7238180658,13.583659012,16.491185527,20.3877608853,22.3658107303)
  ,.UNSPECIFIED.);
#4775 = CARTESIAN_POINT('',(137.5,87.9903810602,20.));
#4776 = CARTESIAN_POINT('',(137.5,87.5231828091,20.));
#4777 = CARTESIAN_POINT('',(137.44543032,87.022395599,20.));
#4778 = CARTESIAN_POINT('',(137.320417741,86.4992580219,20.));
#4779 = CARTESIAN_POINT('',(136.927313873,85.5103196227,20.));
#4780 = CARTESIAN_POINT('',(136.241985361,84.6094763174,20.));
#4781 = CARTESIAN_POINT('',(135.854763808,84.2217547294,20.));
#4782 = CARTESIAN_POINT('',(135.067491372,83.628293031,20.));
#4783 = CARTESIAN_POINT('',(134.145189257,83.2385406936,20.));
#4784 = CARTESIAN_POINT('',(133.723221447,83.1124617245,20.));
#4785 = CARTESIAN_POINT('',(132.856287038,82.9549000687,20.));
#4786 = CARTESIAN_POINT('',(131.973599697,82.9889802983,20.));
#4787 = CARTESIAN_POINT('',(131.536949326,83.0546324941,20.));
#4788 = CARTESIAN_POINT('',(130.681357879,83.2815352721,20.));
#4789 = CARTESIAN_POINT('',(129.904245379,83.6832503902,20.));
#4790 = CARTESIAN_POINT('',(129.539686815,83.9261619783,20.));
#4791 = CARTESIAN_POINT('',(128.764450964,84.5740797663,20.));
#4792 = CARTESIAN_POINT('',(128.190477401,85.365725405,20.));
#4793 = CARTESIAN_POINT('',(127.936249977,85.8479328643,20.));
#4794 = CARTESIAN_POINT('',(127.663707566,86.5855621255,20.));
#4795 = CARTESIAN_POINT('',(127.538781231,87.3015259565,20.));
#4796 = CARTESIAN_POINT('',(127.512366771,87.5379496879,20.));
#4797 = CARTESIAN_POINT('',(127.5,87.7679713967,20.));
#4798 = CARTESIAN_POINT('',(127.5,87.9903810602,20.));
#4799 = PCURVE('',#3830,#4800);
#4800 = DEFINITIONAL_REPRESENTATION('',(#4801),#4826);
#4801 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4802,#4803,#4804,#4805,#4806,
    #4807,#4808,#4809,#4810,#4811,#4812,#4813,#4814,#4815,#4816,#4817,
    #4818,#4819,#4820,#4821,#4822,#4823,#4824,#4825),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513163251,7.85828164883,
    10.7238180658,13.583659012,16.491185527,20.3877608853,22.3658107303)
  ,.UNSPECIFIED.);
#4802 = CARTESIAN_POINT('',(47.5,12.9903810602));
#4803 = CARTESIAN_POINT('',(47.5,12.5231828091));
#4804 = CARTESIAN_POINT('',(47.44543032,12.022395599));
#4805 = CARTESIAN_POINT('',(47.320417741,11.4992580219));
#4806 = CARTESIAN_POINT('',(46.927313873,10.5103196227));
#4807 = CARTESIAN_POINT('',(46.241985361,9.6094763174));
#4808 = CARTESIAN_POINT('',(45.854763808,9.2217547294));
#4809 = CARTESIAN_POINT('',(45.067491372,8.628293031));
#4810 = CARTESIAN_POINT('',(44.145189257,8.2385406936));
#4811 = CARTESIAN_POINT('',(43.723221447,8.1124617245));
#4812 = CARTESIAN_POINT('',(42.856287038,7.9549000687));
#4813 = CARTESIAN_POINT('',(41.973599697,7.9889802983));
#4814 = CARTESIAN_POINT('',(41.536949326,8.0546324941));
#4815 = CARTESIAN_POINT('',(40.681357879,8.2815352721));
#4816 = CARTESIAN_POINT('',(39.904245379,8.6832503902));
#4817 = CARTESIAN_POINT('',(39.539686815,8.9261619783));
#4818 = CARTESIAN_POINT('',(38.764450964,9.5740797663));
#4819 = CARTESIAN_POINT('',(38.190477401,10.365725405));
#4820 = CARTESIAN_POINT('',(37.936249977,10.8479328643));
#4821 = CARTESIAN_POINT('',(37.663707566,11.5855621255));
#4822 = CARTESIAN_POINT('',(37.538781231,12.3015259565));
#4823 = CARTESIAN_POINT('',(37.512366771,12.5379496879));
#4824 = CARTESIAN_POINT('',(37.5,12.7679713967));
#4825 = CARTESIAN_POINT('',(37.5,12.9903810602));
#4826 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4827 = PCURVE('',#4828,#4837);
#4828 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4829,#4830,#4831,#4832)
    ,(#4833,#4834,#4835,#4836
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4829 = CARTESIAN_POINT('',(137.5,87.99038106,20.));
#4830 = CARTESIAN_POINT('',(137.5,77.99038106,20.));
#4831 = CARTESIAN_POINT('',(127.5,77.99038106,20.));
#4832 = CARTESIAN_POINT('',(127.5,87.99038106,20.));
#4833 = CARTESIAN_POINT('',(137.5,87.99038106,0.E+000));
#4834 = CARTESIAN_POINT('',(137.5,77.99038106,0.E+000));
#4835 = CARTESIAN_POINT('',(127.5,77.99038106,0.E+000));
#4836 = CARTESIAN_POINT('',(127.5,87.99038106,0.E+000));
#4837 = DEFINITIONAL_REPRESENTATION('',(#4838),#4886);
#4838 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4839,#4840,#4841,#4842,#4843,
    #4844,#4845,#4846,#4847,#4848,#4849,#4850,#4851,#4852,#4853,#4854,
    #4855,#4856,#4857,#4858,#4859,#4860,#4861,#4862,#4863,#4864,#4865,
    #4866,#4867,#4868,#4869,#4870,#4871,#4872,#4873,#4874,#4875,#4876,
    #4877,#4878,#4879,#4880,#4881,#4882,#4883,#4884,#4885),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880234,
    1.016627760468,1.524941640702,2.033255520936,2.54156940117,
    3.049883281405,3.558197161639,4.066511041873,4.574824922107,
    5.083138802341,5.591452682575,6.099766562809,6.608080443043,
    7.116394323277,7.624708203511,8.133022083745,8.64133596398,
    9.149649844214,9.657963724448,10.166277604682,10.674591484916,
    11.18290536515,11.691219245384,12.199533125618,12.707847005852,
    13.216160886086,13.72447476632,14.232788646555,14.741102526789,
    15.249416407023,15.757730287257,16.266044167491,16.774358047725,
    17.282671927959,17.790985808193,18.299299688427,18.807613568661,
    19.315927448895,19.82424132913,20.332555209364,20.840869089598,
    21.349182969832,21.857496850066,22.3658107303),
  .QUASI_UNIFORM_KNOTS.);
#4839 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4840 = CARTESIAN_POINT('',(9.980039900018E-004,0.285786133517));
#4841 = CARTESIAN_POINT('',(9.980039900009E-004,0.851023723715));
#4842 = CARTESIAN_POINT('',(9.98003989994E-004,1.679658951021));
#4843 = CARTESIAN_POINT('',(9.980039900015E-004,2.488775846926));
#4844 = CARTESIAN_POINT('',(9.980039899995E-004,3.278357398785));
#4845 = CARTESIAN_POINT('',(9.980039899999E-004,4.048590101665));
#4846 = CARTESIAN_POINT('',(9.980039900002E-004,4.799873564588));
#4847 = CARTESIAN_POINT('',(9.980039899985E-004,5.532780990465));
#4848 = CARTESIAN_POINT('',(9.980039900048E-004,6.248020925181));
#4849 = CARTESIAN_POINT('',(9.980039900026E-004,6.946360588667));
#4850 = CARTESIAN_POINT('',(9.980039900051E-004,7.628688647535));
#4851 = CARTESIAN_POINT('',(9.980039899972E-004,8.296073982405));
#4852 = CARTESIAN_POINT('',(9.98003990005E-004,8.949683949752));
#4853 = CARTESIAN_POINT('',(9.980039900029E-004,9.590744784014));
#4854 = CARTESIAN_POINT('',(9.980039900036E-004,10.220499187688));
#4855 = CARTESIAN_POINT('',(9.980039900029E-004,10.840182522235));
#4856 = CARTESIAN_POINT('',(9.980039900052E-004,11.450961994024));
#4857 = CARTESIAN_POINT('',(9.980039899965E-004,12.054057832898));
#4858 = CARTESIAN_POINT('',(9.980039900077E-004,12.650784946353));
#4859 = CARTESIAN_POINT('',(9.980039899928E-004,13.242436992094));
#4860 = CARTESIAN_POINT('',(9.980039899987E-004,13.830311300344));
#4861 = CARTESIAN_POINT('',(9.980039900115E-004,14.415700423411));
#4862 = CARTESIAN_POINT('',(9.980039899968E-004,14.999897596134));
#4863 = CARTESIAN_POINT('',(9.980039900002E-004,15.584088993867));
#4864 = CARTESIAN_POINT('',(9.980039900013E-004,16.169496102237));
#4865 = CARTESIAN_POINT('',(9.980039899938E-004,16.757373990909));
#4866 = CARTESIAN_POINT('',(9.980039900015E-004,17.349001895997));
#4867 = CARTESIAN_POINT('',(9.980039899995E-004,17.945677504543));
#4868 = CARTESIAN_POINT('',(9.980039899999E-004,18.548712197726));
#4869 = CARTESIAN_POINT('',(9.980039900003E-004,19.159406272192));
#4870 = CARTESIAN_POINT('',(9.980039899982E-004,19.779034515055));
#4871 = CARTESIAN_POINT('',(9.980039900061E-004,20.408844084014));
#4872 = CARTESIAN_POINT('',(9.980039899981E-004,21.050050686982));
#4873 = CARTESIAN_POINT('',(9.980039900008E-004,21.703821211017));
#4874 = CARTESIAN_POINT('',(9.980039899981E-004,22.371286777985));
#4875 = CARTESIAN_POINT('',(9.980039900061E-004,23.053580503695));
#4876 = CARTESIAN_POINT('',(9.980039899981E-004,23.751780861504));
#4877 = CARTESIAN_POINT('',(9.980039900007E-004,24.466876442315));
#4878 = CARTESIAN_POINT('',(9.980039899984E-004,25.199732628727));
#4879 = CARTESIAN_POINT('',(9.98003990005E-004,25.951064395187));
#4880 = CARTESIAN_POINT('',(9.980039900023E-004,26.721413662813));
#4881 = CARTESIAN_POINT('',(9.980039900068E-004,27.511129431342));
#4882 = CARTESIAN_POINT('',(9.980039899914E-004,28.320321942215));
#4883 = CARTESIAN_POINT('',(9.980039900061E-004,29.148977247338));
#4884 = CARTESIAN_POINT('',(9.980039900057E-004,29.714213804631));
#4885 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#4886 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4887 = FACE_BOUND('',#4888,.T.);
#4888 = EDGE_LOOP('',(#4889,#5009));
#4889 = ORIENTED_EDGE('',*,*,#4890,.T.);
#4890 = EDGE_CURVE('',#4891,#4893,#4895,.T.);
#4891 = VERTEX_POINT('',#4892);
#4892 = CARTESIAN_POINT('',(20.,75.,20.));
#4893 = VERTEX_POINT('',#4894);
#4894 = CARTESIAN_POINT('',(30.,75.,20.));
#4895 = SURFACE_CURVE('',#4896,(#4921,#4949),.PCURVE_S1.);
#4896 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4897,#4898,#4899,#4900,#4901,
    #4902,#4903,#4904,#4905,#4906,#4907,#4908,#4909,#4910,#4911,#4912,
    #4913,#4914,#4915,#4916,#4917,#4918,#4919,#4920),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164484,7.85828164824,
    10.7238180433,13.5836589908,16.4911854976,20.3877608637,22.365810724
    ),.UNSPECIFIED.);
#4897 = CARTESIAN_POINT('',(20.,75.,20.));
#4898 = CARTESIAN_POINT('',(20.,75.4671982525,20.));
#4899 = CARTESIAN_POINT('',(20.0545696798,75.9679854641,20.));
#4900 = CARTESIAN_POINT('',(20.1795822587,76.4911230351,20.));
#4901 = CARTESIAN_POINT('',(20.5726861255,77.4800614343,20.));
#4902 = CARTESIAN_POINT('',(21.2580146363,78.38090474,20.));
#4903 = CARTESIAN_POINT('',(21.6452361932,78.7686263329,20.));
#4904 = CARTESIAN_POINT('',(22.4325086248,79.3620880279,20.));
#4905 = CARTESIAN_POINT('',(23.3548107347,79.7518403641,20.));
#4906 = CARTESIAN_POINT('',(23.7767785579,79.8779193366,20.));
#4907 = CARTESIAN_POINT('',(24.6437129635,80.0354809915,20.));
#4908 = CARTESIAN_POINT('',(25.5264003015,80.0014007619,20.));
#4909 = CARTESIAN_POINT('',(25.9630506731,79.9357485663,20.));
#4910 = CARTESIAN_POINT('',(26.8186421194,79.7088457885,20.));
#4911 = CARTESIAN_POINT('',(27.595754619,79.307130671,20.));
#4912 = CARTESIAN_POINT('',(27.9603131851,79.0642190821,20.));
#4913 = CARTESIAN_POINT('',(28.7355490362,78.416301294,20.));
#4914 = CARTESIAN_POINT('',(29.309522598,77.6246556552,20.));
#4915 = CARTESIAN_POINT('',(29.5637500221,77.1424481994,20.));
#4916 = CARTESIAN_POINT('',(29.8362924346,76.4048189351,20.));
#4917 = CARTESIAN_POINT('',(29.9612187699,75.6888551024,20.));
#4918 = CARTESIAN_POINT('',(29.9876332288,75.4524313758,20.));
#4919 = CARTESIAN_POINT('',(30.,75.2224096652,20.));
#4920 = CARTESIAN_POINT('',(30.,75.,20.));
#4921 = PCURVE('',#3830,#4922);
#4922 = DEFINITIONAL_REPRESENTATION('',(#4923),#4948);
#4923 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#4924,#4925,#4926,#4927,#4928,
    #4929,#4930,#4931,#4932,#4933,#4934,#4935,#4936,#4937,#4938,#4939,
    #4940,#4941,#4942,#4943,#4944,#4945,#4946,#4947),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164484,7.85828164824,
    10.7238180433,13.5836589908,16.4911854976,20.3877608637,22.365810724
    ),.UNSPECIFIED.);
#4924 = CARTESIAN_POINT('',(-70.,0.E+000));
#4925 = CARTESIAN_POINT('',(-70.,0.4671982525));
#4926 = CARTESIAN_POINT('',(-69.9454303202,0.9679854641));
#4927 = CARTESIAN_POINT('',(-69.8204177413,1.4911230351));
#4928 = CARTESIAN_POINT('',(-69.4273138745,2.4800614343));
#4929 = CARTESIAN_POINT('',(-68.7419853637,3.38090474));
#4930 = CARTESIAN_POINT('',(-68.3547638068,3.7686263329));
#4931 = CARTESIAN_POINT('',(-67.5674913752,4.3620880279));
#4932 = CARTESIAN_POINT('',(-66.6451892653,4.7518403641));
#4933 = CARTESIAN_POINT('',(-66.2232214421,4.8779193366));
#4934 = CARTESIAN_POINT('',(-65.3562870365,5.0354809915));
#4935 = CARTESIAN_POINT('',(-64.4735996985,5.0014007619));
#4936 = CARTESIAN_POINT('',(-64.0369493269,4.9357485663));
#4937 = CARTESIAN_POINT('',(-63.1813578806,4.7088457885));
#4938 = CARTESIAN_POINT('',(-62.404245381,4.307130671));
#4939 = CARTESIAN_POINT('',(-62.0396868149,4.0642190821));
#4940 = CARTESIAN_POINT('',(-61.2644509638,3.416301294));
#4941 = CARTESIAN_POINT('',(-60.690477402,2.6246556552));
#4942 = CARTESIAN_POINT('',(-60.4362499779,2.1424481994));
#4943 = CARTESIAN_POINT('',(-60.1637075654,1.4048189351));
#4944 = CARTESIAN_POINT('',(-60.0387812301,0.6888551024));
#4945 = CARTESIAN_POINT('',(-60.0123667712,0.4524313758));
#4946 = CARTESIAN_POINT('',(-60.,0.2224096652));
#4947 = CARTESIAN_POINT('',(-60.,0.E+000));
#4948 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#4949 = PCURVE('',#4950,#4959);
#4950 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#4951,#4952,#4953,#4954)
    ,(#4955,#4956,#4957,#4958
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#4951 = CARTESIAN_POINT('',(20.,75.,20.));
#4952 = CARTESIAN_POINT('',(20.,85.,20.));
#4953 = CARTESIAN_POINT('',(30.,85.,20.));
#4954 = CARTESIAN_POINT('',(30.,75.,20.));
#4955 = CARTESIAN_POINT('',(20.,75.,0.E+000));
#4956 = CARTESIAN_POINT('',(20.,85.,0.E+000));
#4957 = CARTESIAN_POINT('',(30.,85.,0.E+000));
#4958 = CARTESIAN_POINT('',(30.,75.,0.E+000));
#4959 = DEFINITIONAL_REPRESENTATION('',(#4960),#5008);
#4960 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#4961,#4962,#4963,#4964,#4965,
    #4966,#4967,#4968,#4969,#4970,#4971,#4972,#4973,#4974,#4975,#4976,
    #4977,#4978,#4979,#4980,#4981,#4982,#4983,#4984,#4985,#4986,#4987,
    #4988,#4989,#4990,#4991,#4992,#4993,#4994,#4995,#4996,#4997,#4998,
    #4999,#5000,#5001,#5002,#5003,#5004,#5005,#5006,#5007),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880091,
    1.016627760182,1.524941640273,2.033255520364,2.541569400455,
    3.049883280545,3.558197160636,4.066511040727,4.574824920818,
    5.083138800909,5.591452681,6.099766561091,6.608080441182,
    7.116394321273,7.624708201364,8.133022081455,8.641335961545,
    9.149649841636,9.657963721727,10.166277601818,10.674591481909,
    11.182905362,11.691219242091,12.199533122182,12.707847002273,
    13.216160882364,13.724474762455,14.232788642545,14.741102522636,
    15.249416402727,15.757730282818,16.266044162909,16.774358043,
    17.282671923091,17.790985803182,18.299299683273,18.807613563364,
    19.315927443455,19.824241323545,20.332555203636,20.840869083727,
    21.349182963818,21.857496843909,22.365810724),
  .QUASI_UNIFORM_KNOTS.);
#4961 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#4962 = CARTESIAN_POINT('',(9.980039900004E-004,0.285786133916));
#4963 = CARTESIAN_POINT('',(9.980039900034E-004,0.85102372403));
#4964 = CARTESIAN_POINT('',(9.980039900089E-004,1.679658949168));
#4965 = CARTESIAN_POINT('',(9.980039900038E-004,2.488775841388));
#4966 = CARTESIAN_POINT('',(9.980039899976E-004,3.278357388811));
#4967 = CARTESIAN_POINT('',(9.980039900065E-004,4.048590087612));
#4968 = CARTESIAN_POINT('',(9.980039899984E-004,4.799873547661));
#4969 = CARTESIAN_POINT('',(9.980039900006E-004,5.532780972207));
#4970 = CARTESIAN_POINT('',(9.980039900001E-004,6.248020906897));
#4971 = CARTESIAN_POINT('',(9.980039900003E-004,6.946360570455));
#4972 = CARTESIAN_POINT('',(9.980039900002E-004,7.628688630766));
#4973 = CARTESIAN_POINT('',(9.980039900008E-004,8.296073968645));
#4974 = CARTESIAN_POINT('',(9.980039899987E-004,8.949683939675));
#4975 = CARTESIAN_POINT('',(9.980039900069E-004,9.590744777157));
#4976 = CARTESIAN_POINT('',(9.980039899976E-004,10.220499182656));
#4977 = CARTESIAN_POINT('',(9.980039900055E-004,10.840182517569));
#4978 = CARTESIAN_POINT('',(9.980039900049E-004,11.450961988571));
#4979 = CARTESIAN_POINT('',(9.980039899999E-004,12.054057830263));
#4980 = CARTESIAN_POINT('',(9.980039899994E-004,12.650784951439));
#4981 = CARTESIAN_POINT('',(9.980039900069E-004,13.242437005955));
#4982 = CARTESIAN_POINT('',(9.980039899991E-004,13.830311319745));
#4983 = CARTESIAN_POINT('',(9.980039900015E-004,14.41570044305));
#4984 = CARTESIAN_POINT('',(9.9800399E-004,14.999897615392));
#4985 = CARTESIAN_POINT('',(9.980039900039E-004,15.584089013162));
#4986 = CARTESIAN_POINT('',(9.980039900116E-004,16.169496121671));
#4987 = CARTESIAN_POINT('',(9.980039899987E-004,16.757374010561));
#4988 = CARTESIAN_POINT('',(9.980039900002E-004,17.349001915896));
#4989 = CARTESIAN_POINT('',(9.980039900074E-004,17.945677524637));
#4990 = CARTESIAN_POINT('',(9.980039899987E-004,18.548712218422));
#4991 = CARTESIAN_POINT('',(9.98003990005E-004,19.159406294572));
#4992 = CARTESIAN_POINT('',(9.980039899888E-004,19.779034539644));
#4993 = CARTESIAN_POINT('',(9.980039900052E-004,20.408844110465));
#4994 = CARTESIAN_POINT('',(9.980039899989E-004,21.050050714187));
#4995 = CARTESIAN_POINT('',(9.98003990008E-004,21.703821238354));
#4996 = CARTESIAN_POINT('',(9.980039899994E-004,22.371286805171));
#4997 = CARTESIAN_POINT('',(9.980039900037E-004,23.053580529958));
#4998 = CARTESIAN_POINT('',(9.980039899955E-004,23.751780886188));
#4999 = CARTESIAN_POINT('',(9.980039900031E-004,24.466876465107));
#5000 = CARTESIAN_POINT('',(9.980039900025E-004,25.199732649846));
#5001 = CARTESIAN_POINT('',(9.980039899974E-004,25.951064415264));
#5002 = CARTESIAN_POINT('',(9.980039899973E-004,26.721413682561));
#5003 = CARTESIAN_POINT('',(9.980039900032E-004,27.511129450274));
#5004 = CARTESIAN_POINT('',(9.980039900015E-004,28.320321952371));
#5005 = CARTESIAN_POINT('',(9.980039900025E-004,29.148977248229));
#5006 = CARTESIAN_POINT('',(9.980039900013E-004,29.714213803472));
#5007 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#5008 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5009 = ORIENTED_EDGE('',*,*,#5010,.T.);
#5010 = EDGE_CURVE('',#4893,#4891,#5011,.T.);
#5011 = SURFACE_CURVE('',#5012,(#5037,#5065),.PCURVE_S1.);
#5012 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#5013,#5014,#5015,#5016,#5017,
    #5018,#5019,#5020,#5021,#5022,#5023,#5024,#5025,#5026,#5027,#5028,
    #5029,#5030,#5031,#5032,#5033,#5034,#5035,#5036),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164535,7.85828164977,
    10.7238180444,13.583658992,16.4911854986,20.3877608659,22.3658107305
    ),.UNSPECIFIED.);
#5013 = CARTESIAN_POINT('',(30.,75.,20.));
#5014 = CARTESIAN_POINT('',(30.,74.5328017475,20.));
#5015 = CARTESIAN_POINT('',(29.9454303202,74.0320145358,20.));
#5016 = CARTESIAN_POINT('',(29.8204177413,73.5088769651,20.));
#5017 = CARTESIAN_POINT('',(29.4273138745,72.5199385656,20.));
#5018 = CARTESIAN_POINT('',(28.7419853635,71.6190952598,20.));
#5019 = CARTESIAN_POINT('',(28.354763807,71.2313736673,20.));
#5020 = CARTESIAN_POINT('',(27.5674913754,70.6379119722,20.));
#5021 = CARTESIAN_POINT('',(26.6451892654,70.2481596359,20.));
#5022 = CARTESIAN_POINT('',(26.2232214419,70.1220806634,20.));
#5023 = CARTESIAN_POINT('',(25.3562870364,69.9645190085,20.));
#5024 = CARTESIAN_POINT('',(24.4735996985,69.9985992381,20.));
#5025 = CARTESIAN_POINT('',(24.0369493269,70.0642514337,20.));
#5026 = CARTESIAN_POINT('',(23.1813578806,70.2911542115,20.));
#5027 = CARTESIAN_POINT('',(22.4042453811,70.6928693289,20.));
#5028 = CARTESIAN_POINT('',(22.0396868149,70.9357809179,20.));
#5029 = CARTESIAN_POINT('',(21.2644509637,71.5836987061,20.));
#5030 = CARTESIAN_POINT('',(20.6904774017,72.3753443451,20.));
#5031 = CARTESIAN_POINT('',(20.4362499781,72.8575518004,20.));
#5032 = CARTESIAN_POINT('',(20.1637075652,73.5951810654,20.));
#5033 = CARTESIAN_POINT('',(20.03878123,74.3111448986,20.));
#5034 = CARTESIAN_POINT('',(20.0123667712,74.5475686232,20.));
#5035 = CARTESIAN_POINT('',(20.,74.7775903343,20.));
#5036 = CARTESIAN_POINT('',(20.,75.,20.));
#5037 = PCURVE('',#3830,#5038);
#5038 = DEFINITIONAL_REPRESENTATION('',(#5039),#5064);
#5039 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#5040,#5041,#5042,#5043,#5044,
    #5045,#5046,#5047,#5048,#5049,#5050,#5051,#5052,#5053,#5054,#5055,
    #5056,#5057,#5058,#5059,#5060,#5061,#5062,#5063),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164535,7.85828164977,
    10.7238180444,13.583658992,16.4911854986,20.3877608659,22.3658107305
    ),.UNSPECIFIED.);
#5040 = CARTESIAN_POINT('',(-60.,0.E+000));
#5041 = CARTESIAN_POINT('',(-60.,-0.4671982525));
#5042 = CARTESIAN_POINT('',(-60.0545696798,-0.9679854642));
#5043 = CARTESIAN_POINT('',(-60.1795822587,-1.4911230349));
#5044 = CARTESIAN_POINT('',(-60.5726861255,-2.4800614344));
#5045 = CARTESIAN_POINT('',(-61.2580146365,-3.3809047402));
#5046 = CARTESIAN_POINT('',(-61.645236193,-3.7686263327));
#5047 = CARTESIAN_POINT('',(-62.4325086246,-4.3620880278));
#5048 = CARTESIAN_POINT('',(-63.3548107346,-4.7518403641));
#5049 = CARTESIAN_POINT('',(-63.7767785581,-4.8779193366));
#5050 = CARTESIAN_POINT('',(-64.6437129636,-5.0354809915));
#5051 = CARTESIAN_POINT('',(-65.5264003015,-5.0014007619));
#5052 = CARTESIAN_POINT('',(-65.9630506731,-4.9357485663));
#5053 = CARTESIAN_POINT('',(-66.8186421194,-4.7088457885));
#5054 = CARTESIAN_POINT('',(-67.5957546189,-4.3071306711));
#5055 = CARTESIAN_POINT('',(-67.9603131851,-4.0642190821));
#5056 = CARTESIAN_POINT('',(-68.7355490363,-3.4163012939));
#5057 = CARTESIAN_POINT('',(-69.3095225983,-2.6246556549));
#5058 = CARTESIAN_POINT('',(-69.5637500219,-2.1424481996));
#5059 = CARTESIAN_POINT('',(-69.8362924348,-1.4048189346));
#5060 = CARTESIAN_POINT('',(-69.96121877,-0.6888551014));
#5061 = CARTESIAN_POINT('',(-69.9876332288,-0.4524313768));
#5062 = CARTESIAN_POINT('',(-70.,-0.2224096657));
#5063 = CARTESIAN_POINT('',(-70.,0.E+000));
#5064 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5065 = PCURVE('',#5066,#5075);
#5066 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#5067,#5068,#5069,#5070)
    ,(#5071,#5072,#5073,#5074
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#5067 = CARTESIAN_POINT('',(30.,75.,20.));
#5068 = CARTESIAN_POINT('',(30.,65.,20.));
#5069 = CARTESIAN_POINT('',(20.,65.,20.));
#5070 = CARTESIAN_POINT('',(20.,75.,20.));
#5071 = CARTESIAN_POINT('',(30.,75.,0.E+000));
#5072 = CARTESIAN_POINT('',(30.,65.,0.E+000));
#5073 = CARTESIAN_POINT('',(20.,65.,0.E+000));
#5074 = CARTESIAN_POINT('',(20.,75.,0.E+000));
#5075 = DEFINITIONAL_REPRESENTATION('',(#5076),#5124);
#5076 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#5077,#5078,#5079,#5080,#5081,
    #5082,#5083,#5084,#5085,#5086,#5087,#5088,#5089,#5090,#5091,#5092,
    #5093,#5094,#5095,#5096,#5097,#5098,#5099,#5100,#5101,#5102,#5103,
    #5104,#5105,#5106,#5107,#5108,#5109,#5110,#5111,#5112,#5113,#5114,
    #5115,#5116,#5117,#5118,#5119,#5120,#5121,#5122,#5123),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313880239,
    1.016627760477,1.524941640716,2.033255520955,2.541569401193,
    3.049883281432,3.55819716167,4.066511041909,4.574824922148,
    5.083138802386,5.591452682625,6.099766562864,6.608080443102,
    7.116394323341,7.62470820358,8.133022083818,8.641335964057,
    9.149649844295,9.657963724534,10.166277604773,10.674591485011,
    11.18290536525,11.691219245489,12.199533125727,12.707847005966,
    13.216160886205,13.724474766443,14.232788646682,14.74110252692,
    15.249416407159,15.757730287398,16.266044167636,16.774358047875,
    17.282671928114,17.790985808352,18.299299688591,18.80761356883,
    19.315927449068,19.824241329307,20.332555209545,20.840869089784,
    21.349182970023,21.857496850261,22.3658107305),
  .QUASI_UNIFORM_KNOTS.);
#5077 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#5078 = CARTESIAN_POINT('',(9.980039900022E-004,0.285786133971));
#5079 = CARTESIAN_POINT('',(9.980039900043E-004,0.851023724195));
#5080 = CARTESIAN_POINT('',(9.980039900031E-004,1.679658949476));
#5081 = CARTESIAN_POINT('',(9.980039900048E-004,2.488775841771));
#5082 = CARTESIAN_POINT('',(9.98003989999E-004,3.278357389236));
#5083 = CARTESIAN_POINT('',(9.980039899992E-004,4.048590088088));
#5084 = CARTESIAN_POINT('',(9.980039900044E-004,4.799873548236));
#5085 = CARTESIAN_POINT('',(9.980039900046E-004,5.53278097294));
#5086 = CARTESIAN_POINT('',(9.980039899987E-004,6.248020907828));
#5087 = CARTESIAN_POINT('',(9.980039900008E-004,6.946360571572));
#5088 = CARTESIAN_POINT('',(9.980039899982E-004,7.628688631929));
#5089 = CARTESIAN_POINT('',(9.980039900067E-004,8.296073969708));
#5090 = CARTESIAN_POINT('',(9.980039899968E-004,8.949683940569));
#5091 = CARTESIAN_POINT('',(9.980039900066E-004,9.590744777915));
#5092 = CARTESIAN_POINT('',(9.980039899988E-004,10.220499183388));
#5093 = CARTESIAN_POINT('',(9.980039899989E-004,10.840182518419));
#5094 = CARTESIAN_POINT('',(9.980039900062E-004,11.450961989592));
#5095 = CARTESIAN_POINT('',(9.980039899985E-004,12.054057831516));
#5096 = CARTESIAN_POINT('',(9.980039900007E-004,12.650784953019));
#5097 = CARTESIAN_POINT('',(9.980039899996E-004,13.242437007878));
#5098 = CARTESIAN_POINT('',(9.980039900019E-004,13.830311321935));
#5099 = CARTESIAN_POINT('',(9.98003989994E-004,14.415700445387));
#5100 = CARTESIAN_POINT('',(9.98003990002E-004,14.99989761786));
#5101 = CARTESIAN_POINT('',(9.980039899994E-004,15.584089015774));
#5102 = CARTESIAN_POINT('',(9.980039900019E-004,16.169496124441));
#5103 = CARTESIAN_POINT('',(9.980039899944E-004,16.757374013504));
#5104 = CARTESIAN_POINT('',(9.980039900008E-004,17.349001919024));
#5105 = CARTESIAN_POINT('',(9.980039900043E-004,17.945677527939));
#5106 = CARTESIAN_POINT('',(9.980039900054E-004,18.548712221896));
#5107 = CARTESIAN_POINT('',(9.980039899975E-004,19.159406298292));
#5108 = CARTESIAN_POINT('',(9.980039900068E-004,19.779034543671));
#5109 = CARTESIAN_POINT('',(9.980039899986E-004,20.408844114822));
#5110 = CARTESIAN_POINT('',(9.980039900009E-004,21.050050718852));
#5111 = CARTESIAN_POINT('',(9.980039900003E-004,21.703821243354));
#5112 = CARTESIAN_POINT('',(9.980039900004E-004,22.371286810459));
#5113 = CARTESIAN_POINT('',(9.980039900006E-004,23.053580535369));
#5114 = CARTESIAN_POINT('',(9.9800399E-004,23.751780891585));
#5115 = CARTESIAN_POINT('',(9.980039900024E-004,24.466876470443));
#5116 = CARTESIAN_POINT('',(9.980039899936E-004,25.199732655186));
#5117 = CARTESIAN_POINT('',(9.980039900052E-004,25.951064420751));
#5118 = CARTESIAN_POINT('',(9.980039900104E-004,26.721413688344));
#5119 = CARTESIAN_POINT('',(9.980039899995E-004,27.511129456288));
#5120 = CARTESIAN_POINT('',(9.980039899954E-004,28.320321955614));
#5121 = CARTESIAN_POINT('',(9.980039900015E-004,29.148977248431));
#5122 = CARTESIAN_POINT('',(9.980039900022E-004,29.714213803035));
#5123 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#5124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5125 = FACE_BOUND('',#5126,.T.);
#5126 = EDGE_LOOP('',(#5127,#5247));
#5127 = ORIENTED_EDGE('',*,*,#5128,.T.);
#5128 = EDGE_CURVE('',#5129,#5131,#5133,.T.);
#5129 = VERTEX_POINT('',#5130);
#5130 = CARTESIAN_POINT('',(150.,75.,20.));
#5131 = VERTEX_POINT('',#5132);
#5132 = CARTESIAN_POINT('',(160.,75.,20.));
#5133 = SURFACE_CURVE('',#5134,(#5159,#5187),.PCURVE_S1.);
#5134 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#5135,#5136,#5137,#5138,#5139,
    #5140,#5141,#5142,#5143,#5144,#5145,#5146,#5147,#5148,#5149,#5150,
    #5151,#5152,#5153,#5154,#5155,#5156,#5157,#5158),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164503,7.85828164656,
    10.7238180501,13.5836589945,16.4911855081,20.3877608582,
    22.3658107087),.UNSPECIFIED.);
#5135 = CARTESIAN_POINT('',(150.,75.,20.));
#5136 = CARTESIAN_POINT('',(150.,75.4671982525,20.));
#5137 = CARTESIAN_POINT('',(150.05456968,75.9679854642,20.));
#5138 = CARTESIAN_POINT('',(150.179582259,76.491123035,20.));
#5139 = CARTESIAN_POINT('',(150.572686125,77.4800614342,20.));
#5140 = CARTESIAN_POINT('',(151.258014636,78.3809047395,20.));
#5141 = CARTESIAN_POINT('',(151.645236194,78.7686263332,20.));
#5142 = CARTESIAN_POINT('',(152.432508626,79.3620880288,20.));
#5143 = CARTESIAN_POINT('',(153.354810737,79.7518403651,20.));
#5144 = CARTESIAN_POINT('',(153.776778557,79.8779193362,20.));
#5145 = CARTESIAN_POINT('',(154.643712964,80.0354809914,20.));
#5146 = CARTESIAN_POINT('',(155.526400302,80.0014007619,20.));
#5147 = CARTESIAN_POINT('',(155.963050674,79.9357485661,20.));
#5148 = CARTESIAN_POINT('',(156.818642121,79.708845788,20.));
#5149 = CARTESIAN_POINT('',(157.595754621,79.30713067,20.));
#5150 = CARTESIAN_POINT('',(157.960313185,79.0642190816,20.));
#5151 = CARTESIAN_POINT('',(158.735549036,78.4163012945,20.));
#5152 = CARTESIAN_POINT('',(159.309522597,77.624655657,20.));
#5153 = CARTESIAN_POINT('',(159.563750023,77.1424481935,20.));
#5154 = CARTESIAN_POINT('',(159.836292434,76.4048189324,20.));
#5155 = CARTESIAN_POINT('',(159.961218769,75.688855103,20.));
#5156 = CARTESIAN_POINT('',(159.987633229,75.4524313736,20.));
#5157 = CARTESIAN_POINT('',(160.,75.2224096641,20.));
#5158 = CARTESIAN_POINT('',(160.,75.,20.));
#5159 = PCURVE('',#3830,#5160);
#5160 = DEFINITIONAL_REPRESENTATION('',(#5161),#5186);
#5161 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#5162,#5163,#5164,#5165,#5166,
    #5167,#5168,#5169,#5170,#5171,#5172,#5173,#5174,#5175,#5176,#5177,
    #5178,#5179,#5180,#5181,#5182,#5183,#5184,#5185),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164503,7.85828164656,
    10.7238180501,13.5836589945,16.4911855081,20.3877608582,
    22.3658107087),.UNSPECIFIED.);
#5162 = CARTESIAN_POINT('',(60.,0.E+000));
#5163 = CARTESIAN_POINT('',(60.,0.4671982525));
#5164 = CARTESIAN_POINT('',(60.05456968,0.9679854642));
#5165 = CARTESIAN_POINT('',(60.179582259,1.491123035));
#5166 = CARTESIAN_POINT('',(60.572686125,2.4800614342));
#5167 = CARTESIAN_POINT('',(61.258014636,3.3809047395));
#5168 = CARTESIAN_POINT('',(61.645236194,3.7686263332));
#5169 = CARTESIAN_POINT('',(62.432508626,4.3620880288));
#5170 = CARTESIAN_POINT('',(63.354810737,4.7518403651));
#5171 = CARTESIAN_POINT('',(63.776778557,4.8779193362));
#5172 = CARTESIAN_POINT('',(64.643712964,5.0354809914));
#5173 = CARTESIAN_POINT('',(65.526400302,5.0014007619));
#5174 = CARTESIAN_POINT('',(65.963050674,4.9357485661));
#5175 = CARTESIAN_POINT('',(66.818642121,4.708845788));
#5176 = CARTESIAN_POINT('',(67.595754621,4.30713067));
#5177 = CARTESIAN_POINT('',(67.960313185,4.0642190816));
#5178 = CARTESIAN_POINT('',(68.735549036,3.4163012945));
#5179 = CARTESIAN_POINT('',(69.309522597,2.624655657));
#5180 = CARTESIAN_POINT('',(69.563750023,2.1424481935));
#5181 = CARTESIAN_POINT('',(69.836292434,1.4048189324));
#5182 = CARTESIAN_POINT('',(69.961218769,0.688855103));
#5183 = CARTESIAN_POINT('',(69.987633229,0.4524313736));
#5184 = CARTESIAN_POINT('',(70.,0.2224096641));
#5185 = CARTESIAN_POINT('',(70.,0.E+000));
#5186 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5187 = PCURVE('',#5188,#5197);
#5188 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#5189,#5190,#5191,#5192)
    ,(#5193,#5194,#5195,#5196
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#5189 = CARTESIAN_POINT('',(150.,75.,20.));
#5190 = CARTESIAN_POINT('',(150.,85.,20.));
#5191 = CARTESIAN_POINT('',(160.,85.,20.));
#5192 = CARTESIAN_POINT('',(160.,75.,20.));
#5193 = CARTESIAN_POINT('',(150.,75.,0.E+000));
#5194 = CARTESIAN_POINT('',(150.,85.,0.E+000));
#5195 = CARTESIAN_POINT('',(160.,85.,0.E+000));
#5196 = CARTESIAN_POINT('',(160.,75.,0.E+000));
#5197 = DEFINITIONAL_REPRESENTATION('',(#5198),#5246);
#5198 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#5199,#5200,#5201,#5202,#5203,
    #5204,#5205,#5206,#5207,#5208,#5209,#5210,#5211,#5212,#5213,#5214,
    #5215,#5216,#5217,#5218,#5219,#5220,#5221,#5222,#5223,#5224,#5225,
    #5226,#5227,#5228,#5229,#5230,#5231,#5232,#5233,#5234,#5235,#5236,
    #5237,#5238,#5239,#5240,#5241,#5242,#5243,#5244,#5245),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.508313879743,
    1.016627759486,1.52494163923,2.033255518973,2.541569398716,
    3.049883278459,3.558197158202,4.066511037945,4.574824917689,
    5.083138797432,5.591452677175,6.099766556918,6.608080436661,
    7.116394316405,7.624708196148,8.133022075891,8.641335955634,
    9.149649835377,9.65796371512,10.166277594864,10.674591474607,
    11.18290535435,11.691219234093,12.199533113836,12.70784699358,
    13.216160873323,13.724474753066,14.232788632809,14.741102512552,
    15.249416392295,15.757730272039,16.266044151782,16.774358031525,
    17.282671911268,17.790985791011,18.299299670755,18.807613550498,
    19.315927430241,19.824241309984,20.332555189727,20.84086906947,
    21.349182949214,21.857496828957,22.3658107087),
  .QUASI_UNIFORM_KNOTS.);
#5199 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#5200 = CARTESIAN_POINT('',(9.980039900036E-004,0.285786133711));
#5201 = CARTESIAN_POINT('',(9.980039900051E-004,0.85102372344));
#5202 = CARTESIAN_POINT('',(9.980039899982E-004,1.679658948044));
#5203 = CARTESIAN_POINT('',(9.980039900024E-004,2.488775839735));
#5204 = CARTESIAN_POINT('',(9.980039899926E-004,3.278357386635));
#5205 = CARTESIAN_POINT('',(9.980039900063E-004,4.048590084919));
#5206 = CARTESIAN_POINT('',(9.980039900041E-004,4.799873544464));
#5207 = CARTESIAN_POINT('',(9.980039899994E-004,5.532780968517));
#5208 = CARTESIAN_POINT('',(9.980039899993E-004,6.248020902737));
#5209 = CARTESIAN_POINT('',(9.980039900045E-004,6.94636056585));
#5210 = CARTESIAN_POINT('',(9.980039900052E-004,7.628688626046));
#5211 = CARTESIAN_POINT('',(9.980039899973E-004,8.296073964156));
#5212 = CARTESIAN_POINT('',(9.980039900067E-004,8.94968393557));
#5213 = CARTESIAN_POINT('',(9.980039899985E-004,9.590744773333));
#5214 = CARTESIAN_POINT('',(9.980039900006E-004,10.220499178825));
#5215 = CARTESIAN_POINT('',(9.980039900006E-004,10.840182513329));
#5216 = CARTESIAN_POINT('',(9.980039899987E-004,11.450961983983));
#5217 = CARTESIAN_POINT('',(9.980039900065E-004,12.054057824443));
#5218 = CARTESIAN_POINT('',(9.980039899987E-004,12.650784943057));
#5219 = CARTESIAN_POINT('',(9.980039900009E-004,13.24243699482));
#5220 = CARTESIAN_POINT('',(9.9800399E-004,13.830311306814));
#5221 = CARTESIAN_POINT('',(9.980039900017E-004,14.415700429728));
#5222 = CARTESIAN_POINT('',(9.980039899959E-004,14.999897601529));
#5223 = CARTESIAN_POINT('',(9.980039899963E-004,15.584088998856));
#5224 = CARTESIAN_POINT('',(9.980039900007E-004,16.169496107211));
#5225 = CARTESIAN_POINT('',(9.980039900043E-004,16.757373996043));
#5226 = CARTESIAN_POINT('',(9.980039900068E-004,17.349001901135));
#5227 = CARTESIAN_POINT('',(9.980039899934E-004,17.945677509452));
#5228 = CARTESIAN_POINT('',(9.980039900022E-004,18.548712202426));
#5229 = CARTESIAN_POINT('',(9.980039900019E-004,19.159406276733));
#5230 = CARTESIAN_POINT('',(9.980039899946E-004,19.779034519507));
#5231 = CARTESIAN_POINT('',(9.980039900029E-004,20.408844088363));
#5232 = CARTESIAN_POINT('',(9.980039899986E-004,21.050050691109));
#5233 = CARTESIAN_POINT('',(9.980039900075E-004,21.703821214481));
#5234 = CARTESIAN_POINT('',(9.980039899975E-004,22.371286781119));
#5235 = CARTESIAN_POINT('',(9.980039900075E-004,23.053580507691));
#5236 = CARTESIAN_POINT('',(9.98003989999E-004,23.751780867479));
#5237 = CARTESIAN_POINT('',(9.980039900017E-004,24.466876450808));
#5238 = CARTESIAN_POINT('',(9.980039899996E-004,25.199732639387));
#5239 = CARTESIAN_POINT('',(9.980039900057E-004,25.951064406832));
#5240 = CARTESIAN_POINT('',(9.980039900049E-004,26.721413673865));
#5241 = CARTESIAN_POINT('',(9.980039900022E-004,27.511129440899));
#5242 = CARTESIAN_POINT('',(9.980039899927E-004,28.320321947345));
#5243 = CARTESIAN_POINT('',(9.98003990012E-004,29.148977247975));
#5244 = CARTESIAN_POINT('',(9.980039900095E-004,29.714213804197));
#5245 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#5246 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5247 = ORIENTED_EDGE('',*,*,#5248,.T.);
#5248 = EDGE_CURVE('',#5131,#5129,#5249,.T.);
#5249 = SURFACE_CURVE('',#5250,(#5275,#5303),.PCURVE_S1.);
#5250 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#5251,#5252,#5253,#5254,#5255,
    #5256,#5257,#5258,#5259,#5260,#5261,#5262,#5263,#5264,#5265,#5266,
    #5267,#5268,#5269,#5270,#5271,#5272,#5273,#5274),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164529,7.85828164788,
    10.7238180522,13.5836589941,16.4911855053,20.3877608633,
    22.3658107059),.UNSPECIFIED.);
#5251 = CARTESIAN_POINT('',(160.,75.,20.));
#5252 = CARTESIAN_POINT('',(160.,74.5328017475,20.));
#5253 = CARTESIAN_POINT('',(159.94543032,74.0320145358,20.));
#5254 = CARTESIAN_POINT('',(159.820417741,73.5088769651,20.));
#5255 = CARTESIAN_POINT('',(159.427313875,72.5199385658,20.));
#5256 = CARTESIAN_POINT('',(158.741985364,71.6190952602,20.));
#5257 = CARTESIAN_POINT('',(158.354763806,71.2313736669,20.));
#5258 = CARTESIAN_POINT('',(157.567491374,70.6379119712,20.));
#5259 = CARTESIAN_POINT('',(156.645189263,70.2481596349,20.));
#5260 = CARTESIAN_POINT('',(156.223221443,70.1220806638,20.));
#5261 = CARTESIAN_POINT('',(155.356287037,69.9645190086,20.));
#5262 = CARTESIAN_POINT('',(154.473599699,69.9985992381,20.));
#5263 = CARTESIAN_POINT('',(154.036949325,70.064251434,20.));
#5264 = CARTESIAN_POINT('',(153.181357879,70.2911542121,20.));
#5265 = CARTESIAN_POINT('',(152.40424538,70.6928693296,20.));
#5266 = CARTESIAN_POINT('',(152.039686814,70.9357809188,20.));
#5267 = CARTESIAN_POINT('',(151.264450963,71.5836987066,20.));
#5268 = CARTESIAN_POINT('',(150.690477401,72.3753443448,20.));
#5269 = CARTESIAN_POINT('',(150.436249977,72.8575518045,20.));
#5270 = CARTESIAN_POINT('',(150.163707566,73.5951810656,20.));
#5271 = CARTESIAN_POINT('',(150.038781231,74.3111448952,20.));
#5272 = CARTESIAN_POINT('',(150.012366771,74.5475686283,20.));
#5273 = CARTESIAN_POINT('',(150.,74.7775903368,20.));
#5274 = CARTESIAN_POINT('',(150.,75.,20.));
#5275 = PCURVE('',#3830,#5276);
#5276 = DEFINITIONAL_REPRESENTATION('',(#5277),#5302);
#5277 = B_SPLINE_CURVE_WITH_KNOTS('',5,(#5278,#5279,#5280,#5281,#5282,
    #5283,#5284,#5285,#5286,#5287,#5288,#5289,#5290,#5291,#5292,#5293,
    #5294,#5295,#5296,#5297,#5298,#5299,#5300,#5301),.UNSPECIFIED.,.F.,
  .F.,(6,3,3,3,3,3,3,6),(0.E+000,4.15513164529,7.85828164788,
    10.7238180522,13.5836589941,16.4911855053,20.3877608633,
    22.3658107059),.UNSPECIFIED.);
#5278 = CARTESIAN_POINT('',(70.,0.E+000));
#5279 = CARTESIAN_POINT('',(70.,-0.4671982525));
#5280 = CARTESIAN_POINT('',(69.94543032,-0.9679854642));
#5281 = CARTESIAN_POINT('',(69.820417741,-1.4911230349));
#5282 = CARTESIAN_POINT('',(69.427313875,-2.4800614342));
#5283 = CARTESIAN_POINT('',(68.741985364,-3.3809047398));
#5284 = CARTESIAN_POINT('',(68.354763806,-3.7686263331));
#5285 = CARTESIAN_POINT('',(67.567491374,-4.3620880288));
#5286 = CARTESIAN_POINT('',(66.645189263,-4.7518403651));
#5287 = CARTESIAN_POINT('',(66.223221443,-4.8779193362));
#5288 = CARTESIAN_POINT('',(65.356287037,-5.0354809914));
#5289 = CARTESIAN_POINT('',(64.473599699,-5.0014007619));
#5290 = CARTESIAN_POINT('',(64.036949325,-4.935748566));
#5291 = CARTESIAN_POINT('',(63.181357879,-4.7088457879));
#5292 = CARTESIAN_POINT('',(62.40424538,-4.3071306704));
#5293 = CARTESIAN_POINT('',(62.039686814,-4.0642190812));
#5294 = CARTESIAN_POINT('',(61.264450963,-3.4163012934));
#5295 = CARTESIAN_POINT('',(60.690477401,-2.6246556552));
#5296 = CARTESIAN_POINT('',(60.436249977,-2.1424481955));
#5297 = CARTESIAN_POINT('',(60.163707566,-1.4048189344));
#5298 = CARTESIAN_POINT('',(60.038781231,-0.6888551048));
#5299 = CARTESIAN_POINT('',(60.012366771,-0.4524313717));
#5300 = CARTESIAN_POINT('',(60.,-0.2224096632));
#5301 = CARTESIAN_POINT('',(60.,0.E+000));
#5302 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5303 = PCURVE('',#5304,#5313);
#5304 = ( BOUNDED_SURFACE() B_SPLINE_SURFACE(1,3,(
    (#5305,#5306,#5307,#5308)
    ,(#5309,#5310,#5311,#5312
)),.UNSPECIFIED.,.F.,.F.,.F.) B_SPLINE_SURFACE_WITH_KNOTS((2,2),(4,4),(
    9.9800399E-004,20.000998004),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_SURFACE((
    (1.,0.33333333333,0.33333333333,1.)
,(1.,0.33333333333,0.33333333333,1.
  ))) REPRESENTATION_ITEM('') SURFACE() );
#5305 = CARTESIAN_POINT('',(160.,75.,20.));
#5306 = CARTESIAN_POINT('',(160.,65.,20.));
#5307 = CARTESIAN_POINT('',(150.,65.,20.));
#5308 = CARTESIAN_POINT('',(150.,75.,20.));
#5309 = CARTESIAN_POINT('',(160.,75.,0.E+000));
#5310 = CARTESIAN_POINT('',(160.,65.,0.E+000));
#5311 = CARTESIAN_POINT('',(150.,65.,0.E+000));
#5312 = CARTESIAN_POINT('',(150.,75.,0.E+000));
#5313 = DEFINITIONAL_REPRESENTATION('',(#5314),#5362);
#5314 = B_SPLINE_CURVE_WITH_KNOTS('',3,(#5315,#5316,#5317,#5318,#5319,
    #5320,#5321,#5322,#5323,#5324,#5325,#5326,#5327,#5328,#5329,#5330,
    #5331,#5332,#5333,#5334,#5335,#5336,#5337,#5338,#5339,#5340,#5341,
    #5342,#5343,#5344,#5345,#5346,#5347,#5348,#5349,#5350,#5351,#5352,
    #5353,#5354,#5355,#5356,#5357,#5358,#5359,#5360,#5361),
  .UNSPECIFIED.,.F.,.F.,(4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
    ,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,4),(0.E+000,0.50831387968,
    1.016627759359,1.524941639039,2.033255518718,2.541569398398,
    3.049883278077,3.558197157757,4.066511037436,4.574824917116,
    5.083138796795,5.591452676475,6.099766556155,6.608080435834,
    7.116394315514,7.624708195193,8.133022074873,8.641335954552,
    9.149649834232,9.657963713911,10.166277593591,10.67459147327,
    11.18290535295,11.69121923263,12.199533112309,12.707846991989,
    13.216160871668,13.724474751348,14.232788631027,14.741102510707,
    15.249416390386,15.757730270066,16.266044149745,16.774358029425,
    17.282671909105,17.790985788784,18.299299668464,18.807613548143,
    19.315927427823,19.824241307502,20.332555187182,20.840869066861,
    21.349182946541,21.85749682622,22.3658107059),
  .QUASI_UNIFORM_KNOTS.);
#5315 = CARTESIAN_POINT('',(9.9800399E-004,0.E+000));
#5316 = CARTESIAN_POINT('',(9.980039899982E-004,0.285786133659));
#5317 = CARTESIAN_POINT('',(9.980039899991E-004,0.85102372328));
#5318 = CARTESIAN_POINT('',(9.980039900059E-004,1.679658947713));
#5319 = CARTESIAN_POINT('',(9.980039899988E-004,2.488775839219));
#5320 = CARTESIAN_POINT('',(9.980039899993E-004,3.278357385934));
#5321 = CARTESIAN_POINT('',(9.980039900046E-004,4.048590084048));
#5322 = CARTESIAN_POINT('',(9.980039900041E-004,4.799873543449));
#5323 = CARTESIAN_POINT('',(9.980039900009E-004,5.53278096739));
#5324 = CARTESIAN_POINT('',(9.98003989993E-004,6.248020901528));
#5325 = CARTESIAN_POINT('',(9.980039900066E-004,6.946360564584));
#5326 = CARTESIAN_POINT('',(9.980039900029E-004,7.628688624637));
#5327 = CARTESIAN_POINT('',(9.98003990004E-004,8.296073962504));
#5328 = CARTESIAN_POINT('',(9.980039900034E-004,8.949683933624));
#5329 = CARTESIAN_POINT('',(9.980039900051E-004,9.590744771096));
#5330 = CARTESIAN_POINT('',(9.98003989999E-004,10.220499176339));
#5331 = CARTESIAN_POINT('',(9.980039900006E-004,10.840182510642));
#5332 = CARTESIAN_POINT('',(9.980039900003E-004,11.450961981105));
#5333 = CARTESIAN_POINT('',(9.980039900003E-004,12.054057821357));
#5334 = CARTESIAN_POINT('',(9.980039900008E-004,12.65078493973));
#5335 = CARTESIAN_POINT('',(9.980039899988E-004,13.242436991189));
#5336 = CARTESIAN_POINT('',(9.980039900065E-004,13.830311302823));
#5337 = CARTESIAN_POINT('',(9.980039899992E-004,14.415700425386));
#5338 = CARTESIAN_POINT('',(9.980039899994E-004,14.999897597052));
#5339 = CARTESIAN_POINT('',(9.980039900061E-004,15.584088995026));
#5340 = CARTESIAN_POINT('',(9.980039900006E-004,16.169496104535));
#5341 = CARTESIAN_POINT('',(9.98003989995E-004,16.757373994389));
#5342 = CARTESIAN_POINT('',(9.980039900016E-004,17.349001899823));
#5343 = CARTESIAN_POINT('',(9.980039900023E-004,17.945677507762));
#5344 = CARTESIAN_POINT('',(9.98003989993E-004,18.548712200658));
#5345 = CARTESIAN_POINT('',(9.980039900086E-004,19.159406275716));
#5346 = CARTESIAN_POINT('',(9.980039899984E-004,19.779034519667));
#5347 = CARTESIAN_POINT('',(9.980039900025E-004,20.408844089475));
#5348 = CARTESIAN_POINT('',(9.980039899962E-004,21.050050692392));
#5349 = CARTESIAN_POINT('',(9.980039899963E-004,21.703821215721));
#5350 = CARTESIAN_POINT('',(9.980039900025E-004,22.371286781966));
#5351 = CARTESIAN_POINT('',(9.98003989999E-004,23.05358050698));
#5352 = CARTESIAN_POINT('',(9.980039900072E-004,23.751780864192));
#5353 = CARTESIAN_POINT('',(9.980039899992E-004,24.466876444464));
#5354 = CARTESIAN_POINT('',(9.980039900019E-004,25.199732630295));
#5355 = CARTESIAN_POINT('',(9.980039899993E-004,25.951064395972));
#5356 = CARTESIAN_POINT('',(9.980039900073E-004,26.721413662433));
#5357 = CARTESIAN_POINT('',(9.980039899995E-004,27.511129429173));
#5358 = CARTESIAN_POINT('',(9.980039900016E-004,28.320321940868));
#5359 = CARTESIAN_POINT('',(9.980039900014E-004,29.148977247292));
#5360 = CARTESIAN_POINT('',(9.980039900008E-004,29.714213804884));
#5361 = CARTESIAN_POINT('',(9.9800399E-004,30.));
#5362 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5363 = ADVANCED_FACE('',(#5364),#3842,.T.);
#5364 = FACE_BOUND('',#5365,.T.);
#5365 = EDGE_LOOP('',(#5366,#5389,#5390,#5413));
#5366 = ORIENTED_EDGE('',*,*,#5367,.T.);
#5367 = EDGE_CURVE('',#5368,#3820,#5370,.T.);
#5368 = VERTEX_POINT('',#5369);
#5369 = CARTESIAN_POINT('',(180.,0.E+000,0.E+000));
#5370 = SURFACE_CURVE('',#5371,(#5375,#5382),.PCURVE_S1.);
#5371 = LINE('',#5372,#5373);
#5372 = CARTESIAN_POINT('',(180.,0.E+000,10.));
#5373 = VECTOR('',#5374,1.);
#5374 = DIRECTION('',(0.E+000,0.E+000,1.));
#5375 = PCURVE('',#3842,#5376);
#5376 = DEFINITIONAL_REPRESENTATION('',(#5377),#5381);
#5377 = LINE('',#5378,#5379);
#5378 = CARTESIAN_POINT('',(-10.,90.));
#5379 = VECTOR('',#5380,1.);
#5380 = DIRECTION('',(-1.,0.E+000));
#5381 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5382 = PCURVE('',#3870,#5383);
#5383 = DEFINITIONAL_REPRESENTATION('',(#5384),#5388);
#5384 = LINE('',#5385,#5386);
#5385 = CARTESIAN_POINT('',(-10.,-75.));
#5386 = VECTOR('',#5387,1.);
#5387 = DIRECTION('',(-1.,0.E+000));
#5388 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5389 = ORIENTED_EDGE('',*,*,#3819,.T.);
#5390 = ORIENTED_EDGE('',*,*,#5391,.F.);
#5391 = EDGE_CURVE('',#5392,#3822,#5394,.T.);
#5392 = VERTEX_POINT('',#5393);
#5393 = CARTESIAN_POINT('',(0.E+000,0.E+000,0.E+000));
#5394 = SURFACE_CURVE('',#5395,(#5399,#5406),.PCURVE_S1.);
#5395 = LINE('',#5396,#5397);
#5396 = CARTESIAN_POINT('',(0.E+000,0.E+000,10.));
#5397 = VECTOR('',#5398,1.);
#5398 = DIRECTION('',(0.E+000,0.E+000,1.));
#5399 = PCURVE('',#3842,#5400);
#5400 = DEFINITIONAL_REPRESENTATION('',(#5401),#5405);
#5401 = LINE('',#5402,#5403);
#5402 = CARTESIAN_POINT('',(-10.,-90.));
#5403 = VECTOR('',#5404,1.);
#5404 = DIRECTION('',(-1.,0.E+000));
#5405 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5406 = PCURVE('',#3924,#5407);
#5407 = DEFINITIONAL_REPRESENTATION('',(#5408),#5412);
#5408 = LINE('',#5409,#5410);
#5409 = CARTESIAN_POINT('',(10.,-75.));
#5410 = VECTOR('',#5411,1.);
#5411 = DIRECTION('',(1.,0.E+000));
#5412 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5413 = ORIENTED_EDGE('',*,*,#5414,.T.);
#5414 = EDGE_CURVE('',#5392,#5368,#5415,.T.);
#5415 = SURFACE_CURVE('',#5416,(#5420,#5427),.PCURVE_S1.);
#5416 = LINE('',#5417,#5418);
#5417 = CARTESIAN_POINT('',(90.,0.E+000,0.E+000));
#5418 = VECTOR('',#5419,1.);
#5419 = DIRECTION('',(1.,0.E+000,0.E+000));
#5420 = PCURVE('',#3842,#5421);
#5421 = DEFINITIONAL_REPRESENTATION('',(#5422),#5426);
#5422 = LINE('',#5423,#5424);
#5423 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5424 = VECTOR('',#5425,1.);
#5425 = DIRECTION('',(0.E+000,1.));
#5426 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5427 = PCURVE('',#5428,#5433);
#5428 = PLANE('',#5429);
#5429 = AXIS2_PLACEMENT_3D('',#5430,#5431,#5432);
#5430 = CARTESIAN_POINT('',(90.,75.,0.E+000));
#5431 = DIRECTION('',(0.E+000,0.E+000,-1.));
#5432 = DIRECTION('',(-1.,0.E+000,0.E+000));
#5433 = DEFINITIONAL_REPRESENTATION('',(#5434),#5438);
#5434 = LINE('',#5435,#5436);
#5435 = CARTESIAN_POINT('',(0.E+000,-75.));
#5436 = VECTOR('',#5437,1.);
#5437 = DIRECTION('',(-1.,0.E+000));
#5438 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5439 = ADVANCED_FACE('',(#5440),#3870,.T.);
#5440 = FACE_BOUND('',#5441,.T.);
#5441 = EDGE_LOOP('',(#5442,#5465,#5486,#5487));
#5442 = ORIENTED_EDGE('',*,*,#5443,.T.);
#5443 = EDGE_CURVE('',#5368,#5444,#5446,.T.);
#5444 = VERTEX_POINT('',#5445);
#5445 = CARTESIAN_POINT('',(180.,150.,0.E+000));
#5446 = SURFACE_CURVE('',#5447,(#5451,#5458),.PCURVE_S1.);
#5447 = LINE('',#5448,#5449);
#5448 = CARTESIAN_POINT('',(180.,75.,0.E+000));
#5449 = VECTOR('',#5450,1.);
#5450 = DIRECTION('',(0.E+000,1.,0.E+000));
#5451 = PCURVE('',#3870,#5452);
#5452 = DEFINITIONAL_REPRESENTATION('',(#5453),#5457);
#5453 = LINE('',#5454,#5455);
#5454 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5455 = VECTOR('',#5456,1.);
#5456 = DIRECTION('',(0.E+000,1.));
#5457 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5458 = PCURVE('',#5428,#5459);
#5459 = DEFINITIONAL_REPRESENTATION('',(#5460),#5464);
#5460 = LINE('',#5461,#5462);
#5461 = CARTESIAN_POINT('',(-90.,0.E+000));
#5462 = VECTOR('',#5463,1.);
#5463 = DIRECTION('',(0.E+000,1.));
#5464 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5465 = ORIENTED_EDGE('',*,*,#5466,.T.);
#5466 = EDGE_CURVE('',#5444,#3855,#5467,.T.);
#5467 = SURFACE_CURVE('',#5468,(#5472,#5479),.PCURVE_S1.);
#5468 = LINE('',#5469,#5470);
#5469 = CARTESIAN_POINT('',(180.,150.,10.));
#5470 = VECTOR('',#5471,1.);
#5471 = DIRECTION('',(0.E+000,0.E+000,1.));
#5472 = PCURVE('',#3870,#5473);
#5473 = DEFINITIONAL_REPRESENTATION('',(#5474),#5478);
#5474 = LINE('',#5475,#5476);
#5475 = CARTESIAN_POINT('',(-10.,75.));
#5476 = VECTOR('',#5477,1.);
#5477 = DIRECTION('',(-1.,0.E+000));
#5478 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5479 = PCURVE('',#3898,#5480);
#5480 = DEFINITIONAL_REPRESENTATION('',(#5481),#5485);
#5481 = LINE('',#5482,#5483);
#5482 = CARTESIAN_POINT('',(10.,90.));
#5483 = VECTOR('',#5484,1.);
#5484 = DIRECTION('',(1.,0.E+000));
#5485 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5486 = ORIENTED_EDGE('',*,*,#3854,.T.);
#5487 = ORIENTED_EDGE('',*,*,#5367,.F.);
#5488 = ADVANCED_FACE('',(#5489),#3898,.T.);
#5489 = FACE_BOUND('',#5490,.T.);
#5490 = EDGE_LOOP('',(#5491,#5514,#5515,#5516));
#5491 = ORIENTED_EDGE('',*,*,#5492,.T.);
#5492 = EDGE_CURVE('',#5493,#3883,#5495,.T.);
#5493 = VERTEX_POINT('',#5494);
#5494 = CARTESIAN_POINT('',(0.E+000,150.,0.E+000));
#5495 = SURFACE_CURVE('',#5496,(#5500,#5507),.PCURVE_S1.);
#5496 = LINE('',#5497,#5498);
#5497 = CARTESIAN_POINT('',(0.E+000,150.,10.));
#5498 = VECTOR('',#5499,1.);
#5499 = DIRECTION('',(0.E+000,0.E+000,1.));
#5500 = PCURVE('',#3898,#5501);
#5501 = DEFINITIONAL_REPRESENTATION('',(#5502),#5506);
#5502 = LINE('',#5503,#5504);
#5503 = CARTESIAN_POINT('',(10.,-90.));
#5504 = VECTOR('',#5505,1.);
#5505 = DIRECTION('',(1.,0.E+000));
#5506 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5507 = PCURVE('',#3924,#5508);
#5508 = DEFINITIONAL_REPRESENTATION('',(#5509),#5513);
#5509 = LINE('',#5510,#5511);
#5510 = CARTESIAN_POINT('',(10.,75.));
#5511 = VECTOR('',#5512,1.);
#5512 = DIRECTION('',(1.,0.E+000));
#5513 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5514 = ORIENTED_EDGE('',*,*,#3882,.T.);
#5515 = ORIENTED_EDGE('',*,*,#5466,.F.);
#5516 = ORIENTED_EDGE('',*,*,#5517,.T.);
#5517 = EDGE_CURVE('',#5444,#5493,#5518,.T.);
#5518 = SURFACE_CURVE('',#5519,(#5523,#5530),.PCURVE_S1.);
#5519 = LINE('',#5520,#5521);
#5520 = CARTESIAN_POINT('',(90.,150.,0.E+000));
#5521 = VECTOR('',#5522,1.);
#5522 = DIRECTION('',(-1.,0.E+000,0.E+000));
#5523 = PCURVE('',#3898,#5524);
#5524 = DEFINITIONAL_REPRESENTATION('',(#5525),#5529);
#5525 = LINE('',#5526,#5527);
#5526 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5527 = VECTOR('',#5528,1.);
#5528 = DIRECTION('',(0.E+000,-1.));
#5529 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5530 = PCURVE('',#5428,#5531);
#5531 = DEFINITIONAL_REPRESENTATION('',(#5532),#5536);
#5532 = LINE('',#5533,#5534);
#5533 = CARTESIAN_POINT('',(0.E+000,75.));
#5534 = VECTOR('',#5535,1.);
#5535 = DIRECTION('',(1.,0.E+000));
#5536 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5537 = ADVANCED_FACE('',(#5538),#3924,.T.);
#5538 = FACE_BOUND('',#5539,.T.);
#5539 = EDGE_LOOP('',(#5540,#5541,#5542,#5543));
#5540 = ORIENTED_EDGE('',*,*,#5391,.T.);
#5541 = ORIENTED_EDGE('',*,*,#3910,.T.);
#5542 = ORIENTED_EDGE('',*,*,#5492,.F.);
#5543 = ORIENTED_EDGE('',*,*,#5544,.T.);
#5544 = EDGE_CURVE('',#5493,#5392,#5545,.T.);
#5545 = SURFACE_CURVE('',#5546,(#5550,#5557),.PCURVE_S1.);
#5546 = LINE('',#5547,#5548);
#5547 = CARTESIAN_POINT('',(0.E+000,75.,0.E+000));
#5548 = VECTOR('',#5549,1.);
#5549 = DIRECTION('',(0.E+000,-1.,0.E+000));
#5550 = PCURVE('',#3924,#5551);
#5551 = DEFINITIONAL_REPRESENTATION('',(#5552),#5556);
#5552 = LINE('',#5553,#5554);
#5553 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5554 = VECTOR('',#5555,1.);
#5555 = DIRECTION('',(0.E+000,-1.));
#5556 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5557 = PCURVE('',#5428,#5558);
#5558 = DEFINITIONAL_REPRESENTATION('',(#5559),#5563);
#5559 = LINE('',#5560,#5561);
#5560 = CARTESIAN_POINT('',(90.,0.E+000));
#5561 = VECTOR('',#5562,1.);
#5562 = DIRECTION('',(0.E+000,-1.));
#5563 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5564 = ADVANCED_FACE('',(#5565),#3998,.T.);
#5565 = FACE_BOUND('',#5566,.T.);
#5566 = EDGE_LOOP('',(#5567,#5594,#5614,#5615));
#5567 = ORIENTED_EDGE('',*,*,#5568,.T.);
#5568 = EDGE_CURVE('',#5569,#5571,#5573,.T.);
#5569 = VERTEX_POINT('',#5570);
#5570 = CARTESIAN_POINT('',(42.5,87.99038106,0.E+000));
#5571 = VERTEX_POINT('',#5572);
#5572 = CARTESIAN_POINT('',(52.5,87.99038106,0.E+000));
#5573 = SURFACE_CURVE('',#5574,(#5579,#5586),.PCURVE_S1.);
#5574 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5575,#5576,#5577,#5578),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5575 = CARTESIAN_POINT('',(42.5,87.99038106,0.E+000));
#5576 = CARTESIAN_POINT('',(42.5,97.99038106,0.E+000));
#5577 = CARTESIAN_POINT('',(52.5,97.99038106,0.E+000));
#5578 = CARTESIAN_POINT('',(52.5,87.99038106,0.E+000));
#5579 = PCURVE('',#3998,#5580);
#5580 = DEFINITIONAL_REPRESENTATION('',(#5581),#5585);
#5581 = LINE('',#5582,#5583);
#5582 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5583 = VECTOR('',#5584,1.);
#5584 = DIRECTION('',(0.E+000,1.));
#5585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5586 = PCURVE('',#5428,#5587);
#5587 = DEFINITIONAL_REPRESENTATION('',(#5588),#5593);
#5588 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5589,#5590,#5591,#5592),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5589 = CARTESIAN_POINT('',(47.5,12.99038106));
#5590 = CARTESIAN_POINT('',(47.5,22.99038106));
#5591 = CARTESIAN_POINT('',(37.5,22.99038106));
#5592 = CARTESIAN_POINT('',(37.5,12.99038106));
#5593 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5594 = ORIENTED_EDGE('',*,*,#5595,.F.);
#5595 = EDGE_CURVE('',#3941,#5571,#5596,.T.);
#5596 = SURFACE_CURVE('',#5597,(#5600,#5607),.PCURVE_S1.);
#5597 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5598,#5599),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5598 = CARTESIAN_POINT('',(52.5,87.99038106,20.));
#5599 = CARTESIAN_POINT('',(52.5,87.99038106,0.E+000));
#5600 = PCURVE('',#3998,#5601);
#5601 = DEFINITIONAL_REPRESENTATION('',(#5602),#5606);
#5602 = LINE('',#5603,#5604);
#5603 = CARTESIAN_POINT('',(0.E+000,30.));
#5604 = VECTOR('',#5605,1.);
#5605 = DIRECTION('',(1.,0.E+000));
#5606 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5607 = PCURVE('',#4114,#5608);
#5608 = DEFINITIONAL_REPRESENTATION('',(#5609),#5613);
#5609 = LINE('',#5610,#5611);
#5610 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5611 = VECTOR('',#5612,1.);
#5612 = DIRECTION('',(1.,0.E+000));
#5613 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5614 = ORIENTED_EDGE('',*,*,#3938,.F.);
#5615 = ORIENTED_EDGE('',*,*,#5616,.T.);
#5616 = EDGE_CURVE('',#3939,#5569,#5617,.T.);
#5617 = SURFACE_CURVE('',#5618,(#5621,#5628),.PCURVE_S1.);
#5618 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5619,#5620),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5619 = CARTESIAN_POINT('',(42.5,87.99038106,20.));
#5620 = CARTESIAN_POINT('',(42.5,87.99038106,0.E+000));
#5621 = PCURVE('',#3998,#5622);
#5622 = DEFINITIONAL_REPRESENTATION('',(#5623),#5627);
#5623 = LINE('',#5624,#5625);
#5624 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5625 = VECTOR('',#5626,1.);
#5626 = DIRECTION('',(1.,0.E+000));
#5627 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5628 = PCURVE('',#4114,#5629);
#5629 = DEFINITIONAL_REPRESENTATION('',(#5630),#5634);
#5630 = LINE('',#5631,#5632);
#5631 = CARTESIAN_POINT('',(0.E+000,30.));
#5632 = VECTOR('',#5633,1.);
#5633 = DIRECTION('',(1.,0.E+000));
#5634 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5635 = ADVANCED_FACE('',(#5636),#4114,.T.);
#5636 = FACE_BOUND('',#5637,.T.);
#5637 = EDGE_LOOP('',(#5638,#5661,#5662,#5663));
#5638 = ORIENTED_EDGE('',*,*,#5639,.T.);
#5639 = EDGE_CURVE('',#5571,#5569,#5640,.T.);
#5640 = SURFACE_CURVE('',#5641,(#5646,#5653),.PCURVE_S1.);
#5641 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5642,#5643,#5644,#5645),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5642 = CARTESIAN_POINT('',(52.5,87.99038106,0.E+000));
#5643 = CARTESIAN_POINT('',(52.5,77.99038106,0.E+000));
#5644 = CARTESIAN_POINT('',(42.5,77.99038106,0.E+000));
#5645 = CARTESIAN_POINT('',(42.5,87.99038106,0.E+000));
#5646 = PCURVE('',#4114,#5647);
#5647 = DEFINITIONAL_REPRESENTATION('',(#5648),#5652);
#5648 = LINE('',#5649,#5650);
#5649 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5650 = VECTOR('',#5651,1.);
#5651 = DIRECTION('',(0.E+000,1.));
#5652 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5653 = PCURVE('',#5428,#5654);
#5654 = DEFINITIONAL_REPRESENTATION('',(#5655),#5660);
#5655 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5656,#5657,#5658,#5659),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5656 = CARTESIAN_POINT('',(37.5,12.99038106));
#5657 = CARTESIAN_POINT('',(37.5,2.99038106));
#5658 = CARTESIAN_POINT('',(47.5,2.99038106));
#5659 = CARTESIAN_POINT('',(47.5,12.99038106));
#5660 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5661 = ORIENTED_EDGE('',*,*,#5616,.F.);
#5662 = ORIENTED_EDGE('',*,*,#4058,.F.);
#5663 = ORIENTED_EDGE('',*,*,#5595,.T.);
#5664 = ADVANCED_FACE('',(#5665),#4236,.T.);
#5665 = FACE_BOUND('',#5666,.T.);
#5666 = EDGE_LOOP('',(#5667,#5694,#5714,#5715));
#5667 = ORIENTED_EDGE('',*,*,#5668,.T.);
#5668 = EDGE_CURVE('',#5669,#5671,#5673,.T.);
#5669 = VERTEX_POINT('',#5670);
#5670 = CARTESIAN_POINT('',(42.5,62.00961894,0.E+000));
#5671 = VERTEX_POINT('',#5672);
#5672 = CARTESIAN_POINT('',(52.5,62.00961894,-1.7763568394E-015));
#5673 = SURFACE_CURVE('',#5674,(#5679,#5686),.PCURVE_S1.);
#5674 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5675,#5676,#5677,#5678),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5675 = CARTESIAN_POINT('',(42.5,62.00961894,0.E+000));
#5676 = CARTESIAN_POINT('',(42.5,72.00961894,0.E+000));
#5677 = CARTESIAN_POINT('',(52.5,72.00961894,0.E+000));
#5678 = CARTESIAN_POINT('',(52.5,62.00961894,0.E+000));
#5679 = PCURVE('',#4236,#5680);
#5680 = DEFINITIONAL_REPRESENTATION('',(#5681),#5685);
#5681 = LINE('',#5682,#5683);
#5682 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5683 = VECTOR('',#5684,1.);
#5684 = DIRECTION('',(0.E+000,1.));
#5685 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5686 = PCURVE('',#5428,#5687);
#5687 = DEFINITIONAL_REPRESENTATION('',(#5688),#5693);
#5688 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5689,#5690,#5691,#5692),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5689 = CARTESIAN_POINT('',(47.5,-12.99038106));
#5690 = CARTESIAN_POINT('',(47.5,-2.99038106));
#5691 = CARTESIAN_POINT('',(37.5,-2.99038106));
#5692 = CARTESIAN_POINT('',(37.5,-12.99038106));
#5693 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5694 = ORIENTED_EDGE('',*,*,#5695,.F.);
#5695 = EDGE_CURVE('',#4179,#5671,#5696,.T.);
#5696 = SURFACE_CURVE('',#5697,(#5700,#5707),.PCURVE_S1.);
#5697 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5698,#5699),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5698 = CARTESIAN_POINT('',(52.5,62.00961894,20.));
#5699 = CARTESIAN_POINT('',(52.5,62.00961894,0.E+000));
#5700 = PCURVE('',#4236,#5701);
#5701 = DEFINITIONAL_REPRESENTATION('',(#5702),#5706);
#5702 = LINE('',#5703,#5704);
#5703 = CARTESIAN_POINT('',(0.E+000,30.));
#5704 = VECTOR('',#5705,1.);
#5705 = DIRECTION('',(1.,0.E+000));
#5706 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5707 = PCURVE('',#4352,#5708);
#5708 = DEFINITIONAL_REPRESENTATION('',(#5709),#5713);
#5709 = LINE('',#5710,#5711);
#5710 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5711 = VECTOR('',#5712,1.);
#5712 = DIRECTION('',(1.,0.E+000));
#5713 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5714 = ORIENTED_EDGE('',*,*,#4176,.F.);
#5715 = ORIENTED_EDGE('',*,*,#5716,.T.);
#5716 = EDGE_CURVE('',#4177,#5669,#5717,.T.);
#5717 = SURFACE_CURVE('',#5718,(#5721,#5728),.PCURVE_S1.);
#5718 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5719,#5720),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5719 = CARTESIAN_POINT('',(42.5,62.00961894,20.));
#5720 = CARTESIAN_POINT('',(42.5,62.00961894,0.E+000));
#5721 = PCURVE('',#4236,#5722);
#5722 = DEFINITIONAL_REPRESENTATION('',(#5723),#5727);
#5723 = LINE('',#5724,#5725);
#5724 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5725 = VECTOR('',#5726,1.);
#5726 = DIRECTION('',(1.,0.E+000));
#5727 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5728 = PCURVE('',#4352,#5729);
#5729 = DEFINITIONAL_REPRESENTATION('',(#5730),#5734);
#5730 = LINE('',#5731,#5732);
#5731 = CARTESIAN_POINT('',(0.E+000,30.));
#5732 = VECTOR('',#5733,1.);
#5733 = DIRECTION('',(1.,0.E+000));
#5734 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5735 = ADVANCED_FACE('',(#5736),#4352,.T.);
#5736 = FACE_BOUND('',#5737,.T.);
#5737 = EDGE_LOOP('',(#5738,#5761,#5762,#5763));
#5738 = ORIENTED_EDGE('',*,*,#5739,.T.);
#5739 = EDGE_CURVE('',#5671,#5669,#5740,.T.);
#5740 = SURFACE_CURVE('',#5741,(#5746,#5753),.PCURVE_S1.);
#5741 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5742,#5743,#5744,#5745),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5742 = CARTESIAN_POINT('',(52.5,62.00961894,0.E+000));
#5743 = CARTESIAN_POINT('',(52.5,52.00961894,0.E+000));
#5744 = CARTESIAN_POINT('',(42.5,52.00961894,0.E+000));
#5745 = CARTESIAN_POINT('',(42.5,62.00961894,0.E+000));
#5746 = PCURVE('',#4352,#5747);
#5747 = DEFINITIONAL_REPRESENTATION('',(#5748),#5752);
#5748 = LINE('',#5749,#5750);
#5749 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5750 = VECTOR('',#5751,1.);
#5751 = DIRECTION('',(0.E+000,1.));
#5752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5753 = PCURVE('',#5428,#5754);
#5754 = DEFINITIONAL_REPRESENTATION('',(#5755),#5760);
#5755 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5756,#5757,#5758,#5759),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5756 = CARTESIAN_POINT('',(37.5,-12.99038106));
#5757 = CARTESIAN_POINT('',(37.5,-22.99038106));
#5758 = CARTESIAN_POINT('',(47.5,-22.99038106));
#5759 = CARTESIAN_POINT('',(47.5,-12.99038106));
#5760 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5761 = ORIENTED_EDGE('',*,*,#5716,.F.);
#5762 = ORIENTED_EDGE('',*,*,#4296,.F.);
#5763 = ORIENTED_EDGE('',*,*,#5695,.T.);
#5764 = ADVANCED_FACE('',(#5765),#4474,.T.);
#5765 = FACE_BOUND('',#5766,.T.);
#5766 = EDGE_LOOP('',(#5767,#5794,#5814,#5815));
#5767 = ORIENTED_EDGE('',*,*,#5768,.T.);
#5768 = EDGE_CURVE('',#5769,#5771,#5773,.T.);
#5769 = VERTEX_POINT('',#5770);
#5770 = CARTESIAN_POINT('',(127.5,62.00961894,0.E+000));
#5771 = VERTEX_POINT('',#5772);
#5772 = CARTESIAN_POINT('',(137.5,62.00961894,-1.7763568394E-015));
#5773 = SURFACE_CURVE('',#5774,(#5779,#5786),.PCURVE_S1.);
#5774 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5775,#5776,#5777,#5778),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5775 = CARTESIAN_POINT('',(127.5,62.00961894,0.E+000));
#5776 = CARTESIAN_POINT('',(127.5,72.00961894,0.E+000));
#5777 = CARTESIAN_POINT('',(137.5,72.00961894,0.E+000));
#5778 = CARTESIAN_POINT('',(137.5,62.00961894,0.E+000));
#5779 = PCURVE('',#4474,#5780);
#5780 = DEFINITIONAL_REPRESENTATION('',(#5781),#5785);
#5781 = LINE('',#5782,#5783);
#5782 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5783 = VECTOR('',#5784,1.);
#5784 = DIRECTION('',(0.E+000,1.));
#5785 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5786 = PCURVE('',#5428,#5787);
#5787 = DEFINITIONAL_REPRESENTATION('',(#5788),#5793);
#5788 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5789,#5790,#5791,#5792),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5789 = CARTESIAN_POINT('',(-37.5,-12.99038106));
#5790 = CARTESIAN_POINT('',(-37.5,-2.99038106));
#5791 = CARTESIAN_POINT('',(-47.5,-2.99038106));
#5792 = CARTESIAN_POINT('',(-47.5,-12.99038106));
#5793 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5794 = ORIENTED_EDGE('',*,*,#5795,.F.);
#5795 = EDGE_CURVE('',#4417,#5771,#5796,.T.);
#5796 = SURFACE_CURVE('',#5797,(#5800,#5807),.PCURVE_S1.);
#5797 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5798,#5799),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5798 = CARTESIAN_POINT('',(137.5,62.00961894,20.));
#5799 = CARTESIAN_POINT('',(137.5,62.00961894,0.E+000));
#5800 = PCURVE('',#4474,#5801);
#5801 = DEFINITIONAL_REPRESENTATION('',(#5802),#5806);
#5802 = LINE('',#5803,#5804);
#5803 = CARTESIAN_POINT('',(0.E+000,30.));
#5804 = VECTOR('',#5805,1.);
#5805 = DIRECTION('',(1.,0.E+000));
#5806 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5807 = PCURVE('',#4590,#5808);
#5808 = DEFINITIONAL_REPRESENTATION('',(#5809),#5813);
#5809 = LINE('',#5810,#5811);
#5810 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5811 = VECTOR('',#5812,1.);
#5812 = DIRECTION('',(1.,0.E+000));
#5813 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5814 = ORIENTED_EDGE('',*,*,#4414,.F.);
#5815 = ORIENTED_EDGE('',*,*,#5816,.T.);
#5816 = EDGE_CURVE('',#4415,#5769,#5817,.T.);
#5817 = SURFACE_CURVE('',#5818,(#5821,#5828),.PCURVE_S1.);
#5818 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5819,#5820),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5819 = CARTESIAN_POINT('',(127.5,62.00961894,20.));
#5820 = CARTESIAN_POINT('',(127.5,62.00961894,0.E+000));
#5821 = PCURVE('',#4474,#5822);
#5822 = DEFINITIONAL_REPRESENTATION('',(#5823),#5827);
#5823 = LINE('',#5824,#5825);
#5824 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5825 = VECTOR('',#5826,1.);
#5826 = DIRECTION('',(1.,0.E+000));
#5827 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5828 = PCURVE('',#4590,#5829);
#5829 = DEFINITIONAL_REPRESENTATION('',(#5830),#5834);
#5830 = LINE('',#5831,#5832);
#5831 = CARTESIAN_POINT('',(0.E+000,30.));
#5832 = VECTOR('',#5833,1.);
#5833 = DIRECTION('',(1.,0.E+000));
#5834 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5835 = ADVANCED_FACE('',(#5836),#4590,.T.);
#5836 = FACE_BOUND('',#5837,.T.);
#5837 = EDGE_LOOP('',(#5838,#5861,#5862,#5863));
#5838 = ORIENTED_EDGE('',*,*,#5839,.T.);
#5839 = EDGE_CURVE('',#5771,#5769,#5840,.T.);
#5840 = SURFACE_CURVE('',#5841,(#5846,#5853),.PCURVE_S1.);
#5841 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5842,#5843,#5844,#5845),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5842 = CARTESIAN_POINT('',(137.5,62.00961894,0.E+000));
#5843 = CARTESIAN_POINT('',(137.5,52.00961894,0.E+000));
#5844 = CARTESIAN_POINT('',(127.5,52.00961894,0.E+000));
#5845 = CARTESIAN_POINT('',(127.5,62.00961894,0.E+000));
#5846 = PCURVE('',#4590,#5847);
#5847 = DEFINITIONAL_REPRESENTATION('',(#5848),#5852);
#5848 = LINE('',#5849,#5850);
#5849 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5850 = VECTOR('',#5851,1.);
#5851 = DIRECTION('',(0.E+000,1.));
#5852 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5853 = PCURVE('',#5428,#5854);
#5854 = DEFINITIONAL_REPRESENTATION('',(#5855),#5860);
#5855 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5856,#5857,#5858,#5859),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5856 = CARTESIAN_POINT('',(-47.5,-12.99038106));
#5857 = CARTESIAN_POINT('',(-47.5,-22.99038106));
#5858 = CARTESIAN_POINT('',(-37.5,-22.99038106));
#5859 = CARTESIAN_POINT('',(-37.5,-12.99038106));
#5860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5861 = ORIENTED_EDGE('',*,*,#5816,.F.);
#5862 = ORIENTED_EDGE('',*,*,#4534,.F.);
#5863 = ORIENTED_EDGE('',*,*,#5795,.T.);
#5864 = ADVANCED_FACE('',(#5865),#4712,.T.);
#5865 = FACE_BOUND('',#5866,.T.);
#5866 = EDGE_LOOP('',(#5867,#5894,#5914,#5915));
#5867 = ORIENTED_EDGE('',*,*,#5868,.T.);
#5868 = EDGE_CURVE('',#5869,#5871,#5873,.T.);
#5869 = VERTEX_POINT('',#5870);
#5870 = CARTESIAN_POINT('',(127.5,87.99038106,0.E+000));
#5871 = VERTEX_POINT('',#5872);
#5872 = CARTESIAN_POINT('',(137.5,87.99038106,0.E+000));
#5873 = SURFACE_CURVE('',#5874,(#5879,#5886),.PCURVE_S1.);
#5874 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5875,#5876,#5877,#5878),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5875 = CARTESIAN_POINT('',(127.5,87.99038106,0.E+000));
#5876 = CARTESIAN_POINT('',(127.5,97.99038106,0.E+000));
#5877 = CARTESIAN_POINT('',(137.5,97.99038106,0.E+000));
#5878 = CARTESIAN_POINT('',(137.5,87.99038106,0.E+000));
#5879 = PCURVE('',#4712,#5880);
#5880 = DEFINITIONAL_REPRESENTATION('',(#5881),#5885);
#5881 = LINE('',#5882,#5883);
#5882 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5883 = VECTOR('',#5884,1.);
#5884 = DIRECTION('',(0.E+000,1.));
#5885 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5886 = PCURVE('',#5428,#5887);
#5887 = DEFINITIONAL_REPRESENTATION('',(#5888),#5893);
#5888 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5889,#5890,#5891,#5892),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5889 = CARTESIAN_POINT('',(-37.5,12.99038106));
#5890 = CARTESIAN_POINT('',(-37.5,22.99038106));
#5891 = CARTESIAN_POINT('',(-47.5,22.99038106));
#5892 = CARTESIAN_POINT('',(-47.5,12.99038106));
#5893 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5894 = ORIENTED_EDGE('',*,*,#5895,.F.);
#5895 = EDGE_CURVE('',#4655,#5871,#5896,.T.);
#5896 = SURFACE_CURVE('',#5897,(#5900,#5907),.PCURVE_S1.);
#5897 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5898,#5899),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5898 = CARTESIAN_POINT('',(137.5,87.99038106,20.));
#5899 = CARTESIAN_POINT('',(137.5,87.99038106,0.E+000));
#5900 = PCURVE('',#4712,#5901);
#5901 = DEFINITIONAL_REPRESENTATION('',(#5902),#5906);
#5902 = LINE('',#5903,#5904);
#5903 = CARTESIAN_POINT('',(0.E+000,30.));
#5904 = VECTOR('',#5905,1.);
#5905 = DIRECTION('',(1.,0.E+000));
#5906 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5907 = PCURVE('',#4828,#5908);
#5908 = DEFINITIONAL_REPRESENTATION('',(#5909),#5913);
#5909 = LINE('',#5910,#5911);
#5910 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5911 = VECTOR('',#5912,1.);
#5912 = DIRECTION('',(1.,0.E+000));
#5913 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5914 = ORIENTED_EDGE('',*,*,#4652,.F.);
#5915 = ORIENTED_EDGE('',*,*,#5916,.T.);
#5916 = EDGE_CURVE('',#4653,#5869,#5917,.T.);
#5917 = SURFACE_CURVE('',#5918,(#5921,#5928),.PCURVE_S1.);
#5918 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5919,#5920),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5919 = CARTESIAN_POINT('',(127.5,87.99038106,20.));
#5920 = CARTESIAN_POINT('',(127.5,87.99038106,0.E+000));
#5921 = PCURVE('',#4712,#5922);
#5922 = DEFINITIONAL_REPRESENTATION('',(#5923),#5927);
#5923 = LINE('',#5924,#5925);
#5924 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#5925 = VECTOR('',#5926,1.);
#5926 = DIRECTION('',(1.,0.E+000));
#5927 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5928 = PCURVE('',#4828,#5929);
#5929 = DEFINITIONAL_REPRESENTATION('',(#5930),#5934);
#5930 = LINE('',#5931,#5932);
#5931 = CARTESIAN_POINT('',(0.E+000,30.));
#5932 = VECTOR('',#5933,1.);
#5933 = DIRECTION('',(1.,0.E+000));
#5934 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5935 = ADVANCED_FACE('',(#5936),#4828,.T.);
#5936 = FACE_BOUND('',#5937,.T.);
#5937 = EDGE_LOOP('',(#5938,#5961,#5962,#5963));
#5938 = ORIENTED_EDGE('',*,*,#5939,.T.);
#5939 = EDGE_CURVE('',#5871,#5869,#5940,.T.);
#5940 = SURFACE_CURVE('',#5941,(#5946,#5953),.PCURVE_S1.);
#5941 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5942,#5943,#5944,#5945),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5942 = CARTESIAN_POINT('',(137.5,87.99038106,0.E+000));
#5943 = CARTESIAN_POINT('',(137.5,77.99038106,0.E+000));
#5944 = CARTESIAN_POINT('',(127.5,77.99038106,0.E+000));
#5945 = CARTESIAN_POINT('',(127.5,87.99038106,0.E+000));
#5946 = PCURVE('',#4828,#5947);
#5947 = DEFINITIONAL_REPRESENTATION('',(#5948),#5952);
#5948 = LINE('',#5949,#5950);
#5949 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5950 = VECTOR('',#5951,1.);
#5951 = DIRECTION('',(0.E+000,1.));
#5952 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5953 = PCURVE('',#5428,#5954);
#5954 = DEFINITIONAL_REPRESENTATION('',(#5955),#5960);
#5955 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5956,#5957,#5958,#5959),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5956 = CARTESIAN_POINT('',(-47.5,12.99038106));
#5957 = CARTESIAN_POINT('',(-47.5,2.99038106));
#5958 = CARTESIAN_POINT('',(-37.5,2.99038106));
#5959 = CARTESIAN_POINT('',(-37.5,12.99038106));
#5960 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5961 = ORIENTED_EDGE('',*,*,#5916,.F.);
#5962 = ORIENTED_EDGE('',*,*,#4772,.F.);
#5963 = ORIENTED_EDGE('',*,*,#5895,.T.);
#5964 = ADVANCED_FACE('',(#5965),#4950,.T.);
#5965 = FACE_BOUND('',#5966,.T.);
#5966 = EDGE_LOOP('',(#5967,#5994,#6014,#6015));
#5967 = ORIENTED_EDGE('',*,*,#5968,.T.);
#5968 = EDGE_CURVE('',#5969,#5971,#5973,.T.);
#5969 = VERTEX_POINT('',#5970);
#5970 = CARTESIAN_POINT('',(20.,75.,0.E+000));
#5971 = VERTEX_POINT('',#5972);
#5972 = CARTESIAN_POINT('',(30.,75.,0.E+000));
#5973 = SURFACE_CURVE('',#5974,(#5979,#5986),.PCURVE_S1.);
#5974 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5975,#5976,#5977,#5978),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5975 = CARTESIAN_POINT('',(20.,75.,0.E+000));
#5976 = CARTESIAN_POINT('',(20.,85.,0.E+000));
#5977 = CARTESIAN_POINT('',(30.,85.,0.E+000));
#5978 = CARTESIAN_POINT('',(30.,75.,0.E+000));
#5979 = PCURVE('',#4950,#5980);
#5980 = DEFINITIONAL_REPRESENTATION('',(#5981),#5985);
#5981 = LINE('',#5982,#5983);
#5982 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#5983 = VECTOR('',#5984,1.);
#5984 = DIRECTION('',(0.E+000,1.));
#5985 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5986 = PCURVE('',#5428,#5987);
#5987 = DEFINITIONAL_REPRESENTATION('',(#5988),#5993);
#5988 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#5989,#5990,#5991,#5992),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#5989 = CARTESIAN_POINT('',(70.,0.E+000));
#5990 = CARTESIAN_POINT('',(70.,10.));
#5991 = CARTESIAN_POINT('',(60.,10.));
#5992 = CARTESIAN_POINT('',(60.,0.E+000));
#5993 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#5994 = ORIENTED_EDGE('',*,*,#5995,.F.);
#5995 = EDGE_CURVE('',#4893,#5971,#5996,.T.);
#5996 = SURFACE_CURVE('',#5997,(#6000,#6007),.PCURVE_S1.);
#5997 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#5998,#5999),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#5998 = CARTESIAN_POINT('',(30.,75.,20.));
#5999 = CARTESIAN_POINT('',(30.,75.,0.E+000));
#6000 = PCURVE('',#4950,#6001);
#6001 = DEFINITIONAL_REPRESENTATION('',(#6002),#6006);
#6002 = LINE('',#6003,#6004);
#6003 = CARTESIAN_POINT('',(0.E+000,30.));
#6004 = VECTOR('',#6005,1.);
#6005 = DIRECTION('',(1.,0.E+000));
#6006 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6007 = PCURVE('',#5066,#6008);
#6008 = DEFINITIONAL_REPRESENTATION('',(#6009),#6013);
#6009 = LINE('',#6010,#6011);
#6010 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#6011 = VECTOR('',#6012,1.);
#6012 = DIRECTION('',(1.,0.E+000));
#6013 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6014 = ORIENTED_EDGE('',*,*,#4890,.F.);
#6015 = ORIENTED_EDGE('',*,*,#6016,.T.);
#6016 = EDGE_CURVE('',#4891,#5969,#6017,.T.);
#6017 = SURFACE_CURVE('',#6018,(#6021,#6028),.PCURVE_S1.);
#6018 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6019,#6020),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#6019 = CARTESIAN_POINT('',(20.,75.,20.));
#6020 = CARTESIAN_POINT('',(20.,75.,0.E+000));
#6021 = PCURVE('',#4950,#6022);
#6022 = DEFINITIONAL_REPRESENTATION('',(#6023),#6027);
#6023 = LINE('',#6024,#6025);
#6024 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#6025 = VECTOR('',#6026,1.);
#6026 = DIRECTION('',(1.,0.E+000));
#6027 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6028 = PCURVE('',#5066,#6029);
#6029 = DEFINITIONAL_REPRESENTATION('',(#6030),#6034);
#6030 = LINE('',#6031,#6032);
#6031 = CARTESIAN_POINT('',(0.E+000,30.));
#6032 = VECTOR('',#6033,1.);
#6033 = DIRECTION('',(1.,0.E+000));
#6034 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6035 = ADVANCED_FACE('',(#6036),#5066,.T.);
#6036 = FACE_BOUND('',#6037,.T.);
#6037 = EDGE_LOOP('',(#6038,#6061,#6062,#6063));
#6038 = ORIENTED_EDGE('',*,*,#6039,.T.);
#6039 = EDGE_CURVE('',#5971,#5969,#6040,.T.);
#6040 = SURFACE_CURVE('',#6041,(#6046,#6053),.PCURVE_S1.);
#6041 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#6042,#6043,#6044,#6045),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#6042 = CARTESIAN_POINT('',(30.,75.,0.E+000));
#6043 = CARTESIAN_POINT('',(30.,65.,0.E+000));
#6044 = CARTESIAN_POINT('',(20.,65.,0.E+000));
#6045 = CARTESIAN_POINT('',(20.,75.,0.E+000));
#6046 = PCURVE('',#5066,#6047);
#6047 = DEFINITIONAL_REPRESENTATION('',(#6048),#6052);
#6048 = LINE('',#6049,#6050);
#6049 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#6050 = VECTOR('',#6051,1.);
#6051 = DIRECTION('',(0.E+000,1.));
#6052 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6053 = PCURVE('',#5428,#6054);
#6054 = DEFINITIONAL_REPRESENTATION('',(#6055),#6060);
#6055 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#6056,#6057,#6058,#6059),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#6056 = CARTESIAN_POINT('',(60.,0.E+000));
#6057 = CARTESIAN_POINT('',(60.,-10.));
#6058 = CARTESIAN_POINT('',(70.,-10.));
#6059 = CARTESIAN_POINT('',(70.,0.E+000));
#6060 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6061 = ORIENTED_EDGE('',*,*,#6016,.F.);
#6062 = ORIENTED_EDGE('',*,*,#5010,.F.);
#6063 = ORIENTED_EDGE('',*,*,#5995,.T.);
#6064 = ADVANCED_FACE('',(#6065),#5188,.T.);
#6065 = FACE_BOUND('',#6066,.T.);
#6066 = EDGE_LOOP('',(#6067,#6094,#6114,#6115));
#6067 = ORIENTED_EDGE('',*,*,#6068,.T.);
#6068 = EDGE_CURVE('',#6069,#6071,#6073,.T.);
#6069 = VERTEX_POINT('',#6070);
#6070 = CARTESIAN_POINT('',(150.,75.,0.E+000));
#6071 = VERTEX_POINT('',#6072);
#6072 = CARTESIAN_POINT('',(160.,75.,0.E+000));
#6073 = SURFACE_CURVE('',#6074,(#6079,#6086),.PCURVE_S1.);
#6074 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#6075,#6076,#6077,#6078),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#6075 = CARTESIAN_POINT('',(150.,75.,0.E+000));
#6076 = CARTESIAN_POINT('',(150.,85.,0.E+000));
#6077 = CARTESIAN_POINT('',(160.,85.,0.E+000));
#6078 = CARTESIAN_POINT('',(160.,75.,0.E+000));
#6079 = PCURVE('',#5188,#6080);
#6080 = DEFINITIONAL_REPRESENTATION('',(#6081),#6085);
#6081 = LINE('',#6082,#6083);
#6082 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#6083 = VECTOR('',#6084,1.);
#6084 = DIRECTION('',(0.E+000,1.));
#6085 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6086 = PCURVE('',#5428,#6087);
#6087 = DEFINITIONAL_REPRESENTATION('',(#6088),#6093);
#6088 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#6089,#6090,#6091,#6092),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#6089 = CARTESIAN_POINT('',(-60.,0.E+000));
#6090 = CARTESIAN_POINT('',(-60.,10.));
#6091 = CARTESIAN_POINT('',(-70.,10.));
#6092 = CARTESIAN_POINT('',(-70.,0.E+000));
#6093 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6094 = ORIENTED_EDGE('',*,*,#6095,.F.);
#6095 = EDGE_CURVE('',#5131,#6071,#6096,.T.);
#6096 = SURFACE_CURVE('',#6097,(#6100,#6107),.PCURVE_S1.);
#6097 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6098,#6099),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#6098 = CARTESIAN_POINT('',(160.,75.,20.));
#6099 = CARTESIAN_POINT('',(160.,75.,0.E+000));
#6100 = PCURVE('',#5188,#6101);
#6101 = DEFINITIONAL_REPRESENTATION('',(#6102),#6106);
#6102 = LINE('',#6103,#6104);
#6103 = CARTESIAN_POINT('',(0.E+000,30.));
#6104 = VECTOR('',#6105,1.);
#6105 = DIRECTION('',(1.,0.E+000));
#6106 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6107 = PCURVE('',#5304,#6108);
#6108 = DEFINITIONAL_REPRESENTATION('',(#6109),#6113);
#6109 = LINE('',#6110,#6111);
#6110 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#6111 = VECTOR('',#6112,1.);
#6112 = DIRECTION('',(1.,0.E+000));
#6113 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6114 = ORIENTED_EDGE('',*,*,#5128,.F.);
#6115 = ORIENTED_EDGE('',*,*,#6116,.T.);
#6116 = EDGE_CURVE('',#5129,#6069,#6117,.T.);
#6117 = SURFACE_CURVE('',#6118,(#6121,#6128),.PCURVE_S1.);
#6118 = B_SPLINE_CURVE_WITH_KNOTS('',1,(#6119,#6120),.UNSPECIFIED.,.F.,
  .F.,(2,2),(9.9800399E-004,20.000998004),.PIECEWISE_BEZIER_KNOTS.);
#6119 = CARTESIAN_POINT('',(150.,75.,20.));
#6120 = CARTESIAN_POINT('',(150.,75.,0.E+000));
#6121 = PCURVE('',#5188,#6122);
#6122 = DEFINITIONAL_REPRESENTATION('',(#6123),#6127);
#6123 = LINE('',#6124,#6125);
#6124 = CARTESIAN_POINT('',(0.E+000,0.E+000));
#6125 = VECTOR('',#6126,1.);
#6126 = DIRECTION('',(1.,0.E+000));
#6127 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6128 = PCURVE('',#5304,#6129);
#6129 = DEFINITIONAL_REPRESENTATION('',(#6130),#6134);
#6130 = LINE('',#6131,#6132);
#6131 = CARTESIAN_POINT('',(0.E+000,30.));
#6132 = VECTOR('',#6133,1.);
#6133 = DIRECTION('',(1.,0.E+000));
#6134 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6135 = ADVANCED_FACE('',(#6136),#5304,.T.);
#6136 = FACE_BOUND('',#6137,.T.);
#6137 = EDGE_LOOP('',(#6138,#6161,#6162,#6163));
#6138 = ORIENTED_EDGE('',*,*,#6139,.T.);
#6139 = EDGE_CURVE('',#6071,#6069,#6140,.T.);
#6140 = SURFACE_CURVE('',#6141,(#6146,#6153),.PCURVE_S1.);
#6141 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#6142,#6143,#6144,#6145),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#6142 = CARTESIAN_POINT('',(160.,75.,0.E+000));
#6143 = CARTESIAN_POINT('',(160.,65.,0.E+000));
#6144 = CARTESIAN_POINT('',(150.,65.,0.E+000));
#6145 = CARTESIAN_POINT('',(150.,75.,0.E+000));
#6146 = PCURVE('',#5304,#6147);
#6147 = DEFINITIONAL_REPRESENTATION('',(#6148),#6152);
#6148 = LINE('',#6149,#6150);
#6149 = CARTESIAN_POINT('',(20.000998004,0.E+000));
#6150 = VECTOR('',#6151,1.);
#6151 = DIRECTION('',(0.E+000,1.));
#6152 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6153 = PCURVE('',#5428,#6154);
#6154 = DEFINITIONAL_REPRESENTATION('',(#6155),#6160);
#6155 = ( BOUNDED_CURVE() B_SPLINE_CURVE(3,(#6156,#6157,#6158,#6159),
.UNSPECIFIED.,.F.,.F.) B_SPLINE_CURVE_WITH_KNOTS((4,4),(0.E+000,30.),
.PIECEWISE_BEZIER_KNOTS.) CURVE() GEOMETRIC_REPRESENTATION_ITEM() 
RATIONAL_B_SPLINE_CURVE((1.,0.33333333333,0.33333333333,1.)) 
REPRESENTATION_ITEM('') );
#6156 = CARTESIAN_POINT('',(-70.,0.E+000));
#6157 = CARTESIAN_POINT('',(-70.,-10.));
#6158 = CARTESIAN_POINT('',(-60.,-10.));
#6159 = CARTESIAN_POINT('',(-60.,0.E+000));
#6160 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#6161 = ORIENTED_EDGE('',*,*,#6116,.F.);
#6162 = ORIENTED_EDGE('',*,*,#5248,.F.);
#6163 = ORIENTED_EDGE('',*,*,#6095,.T.);
#6164 = ADVANCED_FACE('',(#6165,#6171,#6175,#6179,#6183,#6187,#6191),
  #5428,.T.);
#6165 = FACE_BOUND('',#6166,.T.);
#6166 = EDGE_LOOP('',(#6167,#6168,#6169,#6170));
#6167 = ORIENTED_EDGE('',*,*,#5443,.F.);
#6168 = ORIENTED_EDGE('',*,*,#5414,.F.);
#6169 = ORIENTED_EDGE('',*,*,#5544,.F.);
#6170 = ORIENTED_EDGE('',*,*,#5517,.F.);
#6171 = FACE_BOUND('',#6172,.T.);
#6172 = EDGE_LOOP('',(#6173,#6174));
#6173 = ORIENTED_EDGE('',*,*,#5639,.F.);
#6174 = ORIENTED_EDGE('',*,*,#5568,.F.);
#6175 = FACE_BOUND('',#6176,.T.);
#6176 = EDGE_LOOP('',(#6177,#6178));
#6177 = ORIENTED_EDGE('',*,*,#5739,.F.);
#6178 = ORIENTED_EDGE('',*,*,#5668,.F.);
#6179 = FACE_BOUND('',#6180,.T.);
#6180 = EDGE_LOOP('',(#6181,#6182));
#6181 = ORIENTED_EDGE('',*,*,#5839,.F.);
#6182 = ORIENTED_EDGE('',*,*,#5768,.F.);
#6183 = FACE_BOUND('',#6184,.T.);
#6184 = EDGE_LOOP('',(#6185,#6186));
#6185 = ORIENTED_EDGE('',*,*,#5939,.F.);
#6186 = ORIENTED_EDGE('',*,*,#5868,.F.);
#6187 = FACE_BOUND('',#6188,.T.);
#6188 = EDGE_LOOP('',(#6189,#6190));
#6189 = ORIENTED_EDGE('',*,*,#6039,.F.);
#6190 = ORIENTED_EDGE('',*,*,#5968,.F.);
#6191 = FACE_BOUND('',#6192,.T.);
#6192 = EDGE_LOOP('',(#6193,#6194));
#6193 = ORIENTED_EDGE('',*,*,#6139,.F.);
#6194 = ORIENTED_EDGE('',*,*,#6068,.F.);
#6195 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#6199)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#6196,#6197,#6198)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#6196 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6197 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#6198 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#6199 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-005),#6196,
  'distance_accuracy_value','confusion accuracy');
#6200 = SHAPE_DEFINITION_REPRESENTATION(#6201,#3812);
#6201 = PRODUCT_DEFINITION_SHAPE('','',#6202);
#6202 = PRODUCT_DEFINITION('design','',#6203,#6206);
#6203 = PRODUCT_DEFINITION_FORMATION('','',#6204);
#6204 = PRODUCT('plate','plate','',(#6205));
#6205 = PRODUCT_CONTEXT('',#2,'mechanical');
#6206 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#6207 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#6208,#6210);
#6208 = ( REPRESENTATION_RELATIONSHIP('','',#3812,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#6209) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#6209 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#6210 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #6211);
#6211 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('12','plate_1','',#5,#6202,$);
#6212 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#6204));
#6213 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#6214,#6216);
#6214 = ( REPRESENTATION_RELATIONSHIP('','',#1146,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#6215) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#6215 = ITEM_DEFINED_TRANSFORMATION('','',#11,#27);
#6216 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #6217);
#6217 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('13','l-bracket-assembly_2','',#5
  ,#1141,$);
#6218 = PRESENTATION_LAYER_ASSIGNMENT('256','visible',(#63,#759,#1190,
    #1934,#3813));
#6219 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #6220),#6195);
#6220 = STYLED_ITEM('color',(#6221),#3813);
#6221 = PRESENTATION_STYLE_ASSIGNMENT((#6222));
#6222 = SURFACE_STYLE_USAGE(.BOTH.,#6223);
#6223 = SURFACE_SIDE_STYLE('',(#6224));
#6224 = SURFACE_STYLE_FILL_AREA(#6225);
#6225 = FILL_AREA_STYLE('',(#6226));
#6226 = FILL_AREA_STYLE_COLOUR('',#6227);
#6227 = COLOUR_RGB('',0.8,1.,0.E+000);
#6228 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #6229),#3788);
#6229 = STYLED_ITEM('color',(#6230),#1934);
#6230 = PRESENTATION_STYLE_ASSIGNMENT((#6231));
#6231 = SURFACE_STYLE_USAGE(.BOTH.,#6232);
#6232 = SURFACE_SIDE_STYLE('',(#6233));
#6233 = SURFACE_STYLE_FILL_AREA(#6234);
#6234 = FILL_AREA_STYLE('',(#6235));
#6235 = FILL_AREA_STYLE_COLOUR('',#6236);
#6236 = DRAUGHTING_PRE_DEFINED_COLOUR('green');
#6237 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #6238),#1894);
#6238 = STYLED_ITEM('color',(#6239),#1190);
#6239 = PRESENTATION_STYLE_ASSIGNMENT((#6240));
#6240 = SURFACE_STYLE_USAGE(.BOTH.,#6241);
#6241 = SURFACE_SIDE_STYLE('',(#6242));
#6242 = SURFACE_STYLE_FILL_AREA(#6243);
#6243 = FILL_AREA_STYLE('',(#6244));
#6244 = FILL_AREA_STYLE_COLOUR('',#6245);
#6245 = DRAUGHTING_PRE_DEFINED_COLOUR('blue');
#6246 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #6247),#735);
#6247 = STYLED_ITEM('color',(#6248),#63);
#6248 = PRESENTATION_STYLE_ASSIGNMENT((#6249));
#6249 = SURFACE_STYLE_USAGE(.BOTH.,#6250);
#6250 = SURFACE_SIDE_STYLE('',(#6251));
#6251 = SURFACE_STYLE_FILL_AREA(#6252);
#6252 = FILL_AREA_STYLE('',(#6253));
#6253 = FILL_AREA_STYLE_COLOUR('',#6254);
#6254 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#6255 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #6256),#1115);
#6256 = STYLED_ITEM('color',(#6257),#759);
#6257 = PRESENTATION_STYLE_ASSIGNMENT((#6258));
#6258 = SURFACE_STYLE_USAGE(.BOTH.,#6259);
#6259 = SURFACE_SIDE_STYLE('',(#6260));
#6260 = SURFACE_STYLE_FILL_AREA(#6261);
#6261 = FILL_AREA_STYLE('',(#6262));
#6262 = FILL_AREA_STYLE_COLOUR('',#6263);
#6263 = COLOUR_RGB('',1.,0.5,0.E+000);
#6264 = PROPERTY_DEFINITION_REPRESENTATION(#6265,#6266);
#6265 = PROPERTY_DEFINITION('geometric validation property','volume',
  #741);
#6266 = REPRESENTATION('volume',(#6267),#735);
#6267 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    664.37421974184),#6268);
#6268 = DERIVED_UNIT((#6269));
#6269 = DERIVED_UNIT_ELEMENT(#6270,3.);
#6270 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6271 = PROPERTY_DEFINITION_REPRESENTATION(#6272,#6273);
#6272 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#741);
#6273 = REPRESENTATION('surface area',(#6274),#735);
#6274 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    747.02478901525),#6275);
#6275 = DERIVED_UNIT((#6276));
#6276 = DERIVED_UNIT_ELEMENT(#6277,2.);
#6277 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6278 = PROPERTY_DEFINITION_REPRESENTATION(#6279,#6280);
#6279 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #741);
#6280 = REPRESENTATION('centroid',(#6281),#735);
#6281 = CARTESIAN_POINT('centre point',(9.999998287573,7.500001815529,
    1.500011022837));
#6282 = PROPERTY_DEFINITION_REPRESENTATION(#6283,#6284);
#6283 = PROPERTY_DEFINITION('geometric validation property','volume',
  #1121);
#6284 = REPRESENTATION('volume',(#6285),#1115);
#6285 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    1.570837382832E+004),#6286);
#6286 = DERIVED_UNIT((#6287));
#6287 = DERIVED_UNIT_ELEMENT(#6288,3.);
#6288 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6289 = PROPERTY_DEFINITION_REPRESENTATION(#6290,#6291);
#6290 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#1121);
#6291 = REPRESENTATION('surface area',(#6292),#1115);
#6292 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    6.431602661948E+003),#6293);
#6293 = DERIVED_UNIT((#6294));
#6294 = DERIVED_UNIT_ELEMENT(#6295,2.);
#6295 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6296 = PROPERTY_DEFINITION_REPRESENTATION(#6297,#6298);
#6297 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #1121);
#6298 = REPRESENTATION('centroid',(#6299),#1115);
#6299 = CARTESIAN_POINT('centre point',(0.E+000,0.E+000,99.997966412822)
  );
#6300 = PROPERTY_DEFINITION_REPRESENTATION(#6301,#6302);
#6301 = PROPERTY_DEFINITION('geometric validation property','volume',#38
  );
#6302 = REPRESENTATION('volume',(#6303),#57);
#6303 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    1.703713409008E+004),#6304);
#6304 = DERIVED_UNIT((#6305));
#6305 = DERIVED_UNIT_ELEMENT(#6306,3.);
#6306 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6307 = PROPERTY_DEFINITION_REPRESENTATION(#6308,#6309);
#6308 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#38);
#6309 = REPRESENTATION('surface area',(#6310),#57);
#6310 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    7.925652239978E+003),#6311);
#6311 = DERIVED_UNIT((#6312));
#6312 = DERIVED_UNIT_ELEMENT(#6313,2.);
#6313 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6314 = PROPERTY_DEFINITION_REPRESENTATION(#6315,#6316);
#6315 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #38);
#6316 = REPRESENTATION('centroid',(#6317),#57);
#6317 = CARTESIAN_POINT('centre point',(0.E+000,0.E+000,99.998177633943)
  );
#6318 = PROPERTY_DEFINITION_REPRESENTATION(#6319,#6320);
#6319 = PROPERTY_DEFINITION('geometric validation property','volume',
  #1900);
#6320 = REPRESENTATION('volume',(#6321),#1894);
#6321 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    3.200717242138E+003),#6322);
#6322 = DERIVED_UNIT((#6323));
#6323 = DERIVED_UNIT_ELEMENT(#6324,3.);
#6324 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6325 = PROPERTY_DEFINITION_REPRESENTATION(#6326,#6327);
#6326 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#1900);
#6327 = REPRESENTATION('surface area',(#6328),#1894);
#6328 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    1.562789760368E+003),#6329);
#6329 = DERIVED_UNIT((#6330));
#6330 = DERIVED_UNIT_ELEMENT(#6331,2.);
#6331 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6332 = PROPERTY_DEFINITION_REPRESENTATION(#6333,#6334);
#6333 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #1900);
#6334 = REPRESENTATION('centroid',(#6335),#1894);
#6335 = CARTESIAN_POINT('centre point',(0.E+000,0.E+000,16.935607701573)
  );
#6336 = PROPERTY_DEFINITION_REPRESENTATION(#6337,#6338);
#6337 = PROPERTY_DEFINITION('geometric validation property','volume',
  #1169);
#6338 = REPRESENTATION('volume',(#6339),#1184);
#6339 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    3.865094121568E+003),#6340);
#6340 = DERIVED_UNIT((#6341));
#6341 = DERIVED_UNIT_ELEMENT(#6342,3.);
#6342 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6343 = PROPERTY_DEFINITION_REPRESENTATION(#6344,#6345);
#6344 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#1169);
#6345 = REPRESENTATION('surface area',(#6346),#1184);
#6346 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    2.309814549384E+003),#6347);
#6347 = DERIVED_UNIT((#6348));
#6348 = DERIVED_UNIT_ELEMENT(#6349,2.);
#6349 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6350 = PROPERTY_DEFINITION_REPRESENTATION(#6351,#6352);
#6351 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #1169);
#6352 = REPRESENTATION('centroid',(#6353),#1184);
#6353 = CARTESIAN_POINT('centre point',(-7.499996680667,-9.999998648448,
    -6.954762707546));
#6354 = PROPERTY_DEFINITION_REPRESENTATION(#6355,#6356);
#6355 = PROPERTY_DEFINITION('geometric validation property','volume',
  #3794);
#6356 = REPRESENTATION('volume',(#6357),#3788);
#6357 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    9.685857103522E+004),#6358);
#6358 = DERIVED_UNIT((#6359));
#6359 = DERIVED_UNIT_ELEMENT(#6360,3.);
#6360 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6361 = PROPERTY_DEFINITION_REPRESENTATION(#6362,#6363);
#6362 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#3794);
#6363 = REPRESENTATION('surface area',(#6364),#3788);
#6364 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    2.462826538146E+004),#6365);
#6365 = DERIVED_UNIT((#6366));
#6366 = DERIVED_UNIT_ELEMENT(#6367,2.);
#6367 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6368 = PROPERTY_DEFINITION_REPRESENTATION(#6369,#6370);
#6369 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #3794);
#6370 = REPRESENTATION('centroid',(#6371),#3788);
#6371 = CARTESIAN_POINT('centre point',(14.594581738821,20.202718603421,
    49.999999875298));
#6372 = PROPERTY_DEFINITION_REPRESENTATION(#6373,#6374);
#6373 = PROPERTY_DEFINITION('geometric validation property','volume',
  #1140);
#6374 = REPRESENTATION('volume',(#6375),#1163);
#6375 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    1.084538533999E+005),#6376);
#6376 = DERIVED_UNIT((#6377));
#6377 = DERIVED_UNIT_ELEMENT(#6378,3.);
#6378 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6379 = PROPERTY_DEFINITION_REPRESENTATION(#6380,#6381);
#6380 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#1140);
#6381 = REPRESENTATION('surface area',(#6382),#1163);
#6382 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    3.155770902961E+004),#6383);
#6383 = DERIVED_UNIT((#6384));
#6384 = DERIVED_UNIT_ELEMENT(#6385,2.);
#6385 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6386 = PROPERTY_DEFINITION_REPRESENTATION(#6387,#6388);
#6387 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #1140);
#6388 = REPRESENTATION('centroid',(#6389),#1163);
#6389 = CARTESIAN_POINT('centre point',(16.776213828681,-49.99999916789,
    17.299191856855));
#6390 = PROPERTY_DEFINITION_REPRESENTATION(#6391,#6392);
#6391 = PROPERTY_DEFINITION('geometric validation property','volume',
  #6201);
#6392 = REPRESENTATION('volume',(#6393),#6195);
#6393 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    5.30574966551E+005),#6394);
#6394 = DERIVED_UNIT((#6395));
#6395 = DERIVED_UNIT_ELEMENT(#6396,3.);
#6396 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6397 = PROPERTY_DEFINITION_REPRESENTATION(#6398,#6399);
#6398 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#6201);
#6399 = REPRESENTATION('surface area',(#6400),#6195);
#6400 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    7.002214873411E+004),#6401);
#6401 = DERIVED_UNIT((#6402));
#6402 = DERIVED_UNIT_ELEMENT(#6403,2.);
#6403 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6404 = PROPERTY_DEFINITION_REPRESENTATION(#6405,#6406);
#6405 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #6201);
#6406 = REPRESENTATION('centroid',(#6407),#6195);
#6407 = CARTESIAN_POINT('centre point',(89.999999708518,75.000000349373,
    10.000003702419));
#6408 = PROPERTY_DEFINITION_REPRESENTATION(#6409,#6410);
#6409 = PROPERTY_DEFINITION('geometric validation property','volume',#4
  );
#6410 = REPRESENTATION('volume',(#6411),#31);
#6411 = MEASURE_REPRESENTATION_ITEM('volume measure',VOLUME_MEASURE(
    7.645198155597E+005),#6412);
#6412 = DERIVED_UNIT((#6413));
#6413 = DERIVED_UNIT_ELEMENT(#6414,3.);
#6414 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6415 = PROPERTY_DEFINITION_REPRESENTATION(#6416,#6417);
#6416 = PROPERTY_DEFINITION('geometric validation property',
  'surface area',#4);
#6417 = REPRESENTATION('surface area',(#6418),#31);
#6418 = MEASURE_REPRESENTATION_ITEM('surface area measure',AREA_MEASURE(
    1.410632190333E+005),#6419);
#6419 = DERIVED_UNIT((#6420));
#6420 = DERIVED_UNIT_ELEMENT(#6421,2.);
#6421 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#6422 = PROPERTY_DEFINITION_REPRESENTATION(#6423,#6424);
#6423 = PROPERTY_DEFINITION('geometric validation property','centroid',
  #4);
#6424 = REPRESENTATION('centroid',(#6425),#31);
#6425 = CARTESIAN_POINT('centre point',(89.999958232116,74.999996882312,
    18.859503194781));
ENDSEC;
END-ISO-10303-21;
