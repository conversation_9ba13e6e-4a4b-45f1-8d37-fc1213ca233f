ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: using custom schema-name function */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'part_parametric.stp',
/* time_stamp */ '2017-11-23T15:24:29+01:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v15',
/* originating_system */ 'SIEMENS PLM Software NX 9.0',
/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN { 1 0 10303 214 3 1 1 1 }'));
ENDSEC;

DATA;
#10=CONSTRUCTIVE_GEOMETRY_REPRESENTATION_RELATIONSHIP(
'supplemental geometry','',#26,#11);
#11=CONSTRUCTIVE_GEOMETRY_REPRESENTATION('supplemental geometry',(#559),
#865);
#12=GEOMETRICALLY_BOUNDED_WIREFRAME_SHAPE_REPRESENTATION(
'part_parametric-None',(#27),#865);
#13=SHAPE_REPRESENTATION_RELATIONSHIP('None',
'relationship between part_parametric-None and part_parametric-None',#26,
#15);
#14=SHAPE_REPRESENTATION_RELATIONSHIP('None',
'relationship between part_parametric-None and part_parametric-None',#26,
#12);
#15=ADVANCED_BREP_SHAPE_REPRESENTATION('part_parametric-None',(#33),#865);
#16=SHAPE_DEFINITION_REPRESENTATION(#17,#26);
#17=PRODUCT_DEFINITION_SHAPE('','',#18);
#18=PRODUCT_DEFINITION(' ','',#20,#19);
#19=PRODUCT_DEFINITION_CONTEXT('part definition',#25,'design');
#20=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE(' ',' ',#22,
 .NOT_KNOWN.);
#21=PRODUCT_RELATED_PRODUCT_CATEGORY('part','',(#22));
#22=PRODUCT('part_parametric','part_parametric',' ',(#23));
#23=PRODUCT_CONTEXT(' ',#25,'mechanical');
#24=APPLICATION_PROTOCOL_DEFINITION('international standard',
'automotive_design',2010,#25);
#25=APPLICATION_CONTEXT(
'core data for automotive mechanical design processes');
#26=SHAPE_REPRESENTATION('part_parametric-None',(#557),#865);
#27=GEOMETRIC_CURVE_SET('None',(#730,#384,#385,#386,#387,#388,#389,#390,
#391,#392,#393,#394,#395,#396,#397,#339));
#28=SURFACE_STYLE_USAGE(.BOTH.,#29);
#29=SURFACE_SIDE_STYLE('',(#30));
#30=SURFACE_STYLE_FILL_AREA(#31);
#31=FILL_AREA_STYLE('',(#32));
#32=FILL_AREA_STYLE_COLOUR('',#556);
#33=MANIFOLD_SOLID_BREP('',#34);
#34=CLOSED_SHELL('',(#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,#72,#73,
#74,#75,#76,#77,#78,#79,#80,#81,#82,#83));
#35=CYLINDRICAL_SURFACE('',#579,23.1283236048185);
#36=CYLINDRICAL_SURFACE('',#581,5.);
#37=CYLINDRICAL_SURFACE('',#591,5.);
#38=CYLINDRICAL_SURFACE('',#592,5.);
#39=CYLINDRICAL_SURFACE('',#593,5.);
#40=CYLINDRICAL_SURFACE('',#594,5.);
#41=FACE_OUTER_BOUND('',#109,.T.);
#42=FACE_OUTER_BOUND('',#110,.T.);
#43=FACE_OUTER_BOUND('',#111,.T.);
#44=FACE_OUTER_BOUND('',#112,.T.);
#45=FACE_OUTER_BOUND('',#113,.T.);
#46=FACE_OUTER_BOUND('',#114,.T.);
#47=FACE_OUTER_BOUND('',#117,.T.);
#48=FACE_OUTER_BOUND('',#118,.T.);
#49=FACE_OUTER_BOUND('',#119,.T.);
#50=FACE_OUTER_BOUND('',#122,.T.);
#51=FACE_OUTER_BOUND('',#123,.T.);
#52=FACE_OUTER_BOUND('',#124,.T.);
#53=FACE_OUTER_BOUND('',#125,.T.);
#54=FACE_OUTER_BOUND('',#126,.T.);
#55=FACE_OUTER_BOUND('',#127,.T.);
#56=FACE_OUTER_BOUND('',#128,.T.);
#57=FACE_OUTER_BOUND('',#129,.T.);
#58=FACE_OUTER_BOUND('',#130,.T.);
#59=FACE_OUTER_BOUND('',#131,.T.);
#60=FACE_OUTER_BOUND('',#132,.T.);
#61=ADVANCED_FACE('',(#101,#102),#84,.F.);
#62=ADVANCED_FACE('',(#41),#85,.T.);
#63=ADVANCED_FACE('',(#42),#86,.T.);
#64=ADVANCED_FACE('',(#43),#87,.T.);
#65=ADVANCED_FACE('',(#44),#88,.T.);
#66=ADVANCED_FACE('',(#45),#89,.T.);
#67=ADVANCED_FACE('',(#46),#90,.F.);
#68=ADVANCED_FACE('',(#103,#104),#91,.F.);
#69=ADVANCED_FACE('',(#47),#92,.F.);
#70=ADVANCED_FACE('',(#48),#93,.F.);
#71=ADVANCED_FACE('',(#49),#94,.F.);
#72=ADVANCED_FACE('',(#105,#106),#35,.T.);
#73=ADVANCED_FACE('',(#50),#95,.T.);
#74=ADVANCED_FACE('',(#51),#36,.T.);
#75=ADVANCED_FACE('',(#52),#96,.T.);
#76=ADVANCED_FACE('',(#53),#97,.T.);
#77=ADVANCED_FACE('',(#54),#98,.T.);
#78=ADVANCED_FACE('',(#55),#99,.T.);
#79=ADVANCED_FACE('',(#56),#100,.F.);
#80=ADVANCED_FACE('',(#57),#37,.F.);
#81=ADVANCED_FACE('',(#58),#38,.F.);
#82=ADVANCED_FACE('',(#59),#39,.F.);
#83=ADVANCED_FACE('',(#60),#40,.F.);
#84=PLANE('',#564);
#85=PLANE('',#566);
#86=PLANE('',#567);
#87=PLANE('',#568);
#88=PLANE('',#569);
#89=PLANE('',#570);
#90=PLANE('',#572);
#91=PLANE('',#574);
#92=PLANE('',#575);
#93=PLANE('',#576);
#94=PLANE('',#577);
#95=PLANE('',#580);
#96=PLANE('',#582);
#97=PLANE('',#583);
#98=PLANE('',#584);
#99=PLANE('',#585);
#100=PLANE('',#590);
#101=FACE_BOUND('',#107,.T.);
#102=FACE_BOUND('',#108,.T.);
#103=FACE_BOUND('',#115,.T.);
#104=FACE_BOUND('',#116,.T.);
#105=FACE_BOUND('',#120,.T.);
#106=FACE_BOUND('',#121,.T.);
#107=EDGE_LOOP('',(#133,#134,#135,#136,#137,#138,#139,#140));
#108=EDGE_LOOP('',(#141,#142,#143,#144,#145,#146,#147,#148));
#109=EDGE_LOOP('',(#149,#150,#151,#152,#153,#154,#155));
#110=EDGE_LOOP('',(#156,#157,#158,#159,#160,#161));
#111=EDGE_LOOP('',(#162,#163,#164,#165));
#112=EDGE_LOOP('',(#166,#167,#168,#169));
#113=EDGE_LOOP('',(#170,#171,#172,#173));
#114=EDGE_LOOP('',(#174,#175,#176,#177,#178));
#115=EDGE_LOOP('',(#179));
#116=EDGE_LOOP('',(#180,#181,#182,#183));
#117=EDGE_LOOP('',(#184,#185,#186,#187));
#118=EDGE_LOOP('',(#188,#189,#190,#191));
#119=EDGE_LOOP('',(#192,#193,#194,#195,#196,#197));
#120=EDGE_LOOP('',(#198));
#121=EDGE_LOOP('',(#199));
#122=EDGE_LOOP('',(#200));
#123=EDGE_LOOP('',(#201,#202,#203,#204));
#124=EDGE_LOOP('',(#205,#206,#207,#208));
#125=EDGE_LOOP('',(#209,#210,#211,#212));
#126=EDGE_LOOP('',(#213,#214,#215,#216));
#127=EDGE_LOOP('',(#217,#218,#219,#220));
#128=EDGE_LOOP('',(#221,#222,#223,#224,#225,#226,#227,#228));
#129=EDGE_LOOP('',(#229,#230,#231,#232));
#130=EDGE_LOOP('',(#233,#234,#235,#236));
#131=EDGE_LOOP('',(#237,#238,#239,#240));
#132=EDGE_LOOP('',(#241,#242,#243,#244));
#133=ORIENTED_EDGE('',*,*,#283,.T.);
#134=ORIENTED_EDGE('',*,*,#284,.T.);
#135=ORIENTED_EDGE('',*,*,#285,.T.);
#136=ORIENTED_EDGE('',*,*,#286,.T.);
#137=ORIENTED_EDGE('',*,*,#287,.T.);
#138=ORIENTED_EDGE('',*,*,#288,.T.);
#139=ORIENTED_EDGE('',*,*,#289,.T.);
#140=ORIENTED_EDGE('',*,*,#290,.T.);
#141=ORIENTED_EDGE('',*,*,#291,.F.);
#142=ORIENTED_EDGE('',*,*,#292,.F.);
#143=ORIENTED_EDGE('',*,*,#293,.F.);
#144=ORIENTED_EDGE('',*,*,#294,.F.);
#145=ORIENTED_EDGE('',*,*,#295,.F.);
#146=ORIENTED_EDGE('',*,*,#296,.F.);
#147=ORIENTED_EDGE('',*,*,#297,.F.);
#148=ORIENTED_EDGE('',*,*,#298,.F.);
#149=ORIENTED_EDGE('',*,*,#299,.F.);
#150=ORIENTED_EDGE('',*,*,#300,.T.);
#151=ORIENTED_EDGE('',*,*,#301,.F.);
#152=ORIENTED_EDGE('',*,*,#302,.T.);
#153=ORIENTED_EDGE('',*,*,#303,.F.);
#154=ORIENTED_EDGE('',*,*,#304,.F.);
#155=ORIENTED_EDGE('',*,*,#292,.T.);
#156=ORIENTED_EDGE('',*,*,#305,.F.);
#157=ORIENTED_EDGE('',*,*,#302,.F.);
#158=ORIENTED_EDGE('',*,*,#306,.F.);
#159=ORIENTED_EDGE('',*,*,#307,.T.);
#160=ORIENTED_EDGE('',*,*,#295,.T.);
#161=ORIENTED_EDGE('',*,*,#308,.T.);
#162=ORIENTED_EDGE('',*,*,#309,.F.);
#163=ORIENTED_EDGE('',*,*,#308,.F.);
#164=ORIENTED_EDGE('',*,*,#294,.T.);
#165=ORIENTED_EDGE('',*,*,#310,.T.);
#166=ORIENTED_EDGE('',*,*,#311,.F.);
#167=ORIENTED_EDGE('',*,*,#310,.F.);
#168=ORIENTED_EDGE('',*,*,#293,.T.);
#169=ORIENTED_EDGE('',*,*,#304,.T.);
#170=ORIENTED_EDGE('',*,*,#303,.T.);
#171=ORIENTED_EDGE('',*,*,#305,.T.);
#172=ORIENTED_EDGE('',*,*,#309,.T.);
#173=ORIENTED_EDGE('',*,*,#311,.T.);
#174=ORIENTED_EDGE('',*,*,#312,.F.);
#175=ORIENTED_EDGE('',*,*,#313,.T.);
#176=ORIENTED_EDGE('',*,*,#314,.T.);
#177=ORIENTED_EDGE('',*,*,#298,.T.);
#178=ORIENTED_EDGE('',*,*,#315,.F.);
#179=ORIENTED_EDGE('',*,*,#316,.F.);
#180=ORIENTED_EDGE('',*,*,#297,.T.);
#181=ORIENTED_EDGE('',*,*,#317,.F.);
#182=ORIENTED_EDGE('',*,*,#318,.F.);
#183=ORIENTED_EDGE('',*,*,#315,.T.);
#184=ORIENTED_EDGE('',*,*,#296,.T.);
#185=ORIENTED_EDGE('',*,*,#307,.F.);
#186=ORIENTED_EDGE('',*,*,#319,.F.);
#187=ORIENTED_EDGE('',*,*,#317,.T.);
#188=ORIENTED_EDGE('',*,*,#314,.F.);
#189=ORIENTED_EDGE('',*,*,#320,.T.);
#190=ORIENTED_EDGE('',*,*,#299,.T.);
#191=ORIENTED_EDGE('',*,*,#291,.T.);
#192=ORIENTED_EDGE('',*,*,#301,.T.);
#193=ORIENTED_EDGE('',*,*,#321,.T.);
#194=ORIENTED_EDGE('',*,*,#312,.T.);
#195=ORIENTED_EDGE('',*,*,#318,.T.);
#196=ORIENTED_EDGE('',*,*,#319,.T.);
#197=ORIENTED_EDGE('',*,*,#306,.T.);
#198=ORIENTED_EDGE('',*,*,#322,.F.);
#199=ORIENTED_EDGE('',*,*,#316,.T.);
#200=ORIENTED_EDGE('',*,*,#322,.T.);
#201=ORIENTED_EDGE('',*,*,#300,.F.);
#202=ORIENTED_EDGE('',*,*,#320,.F.);
#203=ORIENTED_EDGE('',*,*,#313,.F.);
#204=ORIENTED_EDGE('',*,*,#321,.F.);
#205=ORIENTED_EDGE('',*,*,#323,.F.);
#206=ORIENTED_EDGE('',*,*,#324,.T.);
#207=ORIENTED_EDGE('',*,*,#289,.F.);
#208=ORIENTED_EDGE('',*,*,#325,.T.);
#209=ORIENTED_EDGE('',*,*,#326,.F.);
#210=ORIENTED_EDGE('',*,*,#327,.T.);
#211=ORIENTED_EDGE('',*,*,#283,.F.);
#212=ORIENTED_EDGE('',*,*,#328,.T.);
#213=ORIENTED_EDGE('',*,*,#285,.F.);
#214=ORIENTED_EDGE('',*,*,#329,.T.);
#215=ORIENTED_EDGE('',*,*,#330,.F.);
#216=ORIENTED_EDGE('',*,*,#331,.T.);
#217=ORIENTED_EDGE('',*,*,#287,.F.);
#218=ORIENTED_EDGE('',*,*,#332,.T.);
#219=ORIENTED_EDGE('',*,*,#333,.F.);
#220=ORIENTED_EDGE('',*,*,#334,.T.);
#221=ORIENTED_EDGE('',*,*,#330,.T.);
#222=ORIENTED_EDGE('',*,*,#335,.T.);
#223=ORIENTED_EDGE('',*,*,#326,.T.);
#224=ORIENTED_EDGE('',*,*,#336,.T.);
#225=ORIENTED_EDGE('',*,*,#323,.T.);
#226=ORIENTED_EDGE('',*,*,#337,.T.);
#227=ORIENTED_EDGE('',*,*,#333,.T.);
#228=ORIENTED_EDGE('',*,*,#338,.T.);
#229=ORIENTED_EDGE('',*,*,#288,.F.);
#230=ORIENTED_EDGE('',*,*,#334,.F.);
#231=ORIENTED_EDGE('',*,*,#337,.F.);
#232=ORIENTED_EDGE('',*,*,#325,.F.);
#233=ORIENTED_EDGE('',*,*,#290,.F.);
#234=ORIENTED_EDGE('',*,*,#324,.F.);
#235=ORIENTED_EDGE('',*,*,#336,.F.);
#236=ORIENTED_EDGE('',*,*,#328,.F.);
#237=ORIENTED_EDGE('',*,*,#286,.F.);
#238=ORIENTED_EDGE('',*,*,#331,.F.);
#239=ORIENTED_EDGE('',*,*,#338,.F.);
#240=ORIENTED_EDGE('',*,*,#332,.F.);
#241=ORIENTED_EDGE('',*,*,#284,.F.);
#242=ORIENTED_EDGE('',*,*,#327,.F.);
#243=ORIENTED_EDGE('',*,*,#335,.F.);
#244=ORIENTED_EDGE('',*,*,#329,.F.);
#245=VERTEX_POINT('',#748);
#246=VERTEX_POINT('',#749);
#247=VERTEX_POINT('',#751);
#248=VERTEX_POINT('',#753);
#249=VERTEX_POINT('',#755);
#250=VERTEX_POINT('',#757);
#251=VERTEX_POINT('',#759);
#252=VERTEX_POINT('',#761);
#253=VERTEX_POINT('',#764);
#254=VERTEX_POINT('',#765);
#255=VERTEX_POINT('',#767);
#256=VERTEX_POINT('',#769);
#257=VERTEX_POINT('',#771);
#258=VERTEX_POINT('',#773);
#259=VERTEX_POINT('',#775);
#260=VERTEX_POINT('',#777);
#261=VERTEX_POINT('',#781);
#262=VERTEX_POINT('',#783);
#263=VERTEX_POINT('',#785);
#264=VERTEX_POINT('',#787);
#265=VERTEX_POINT('',#789);
#266=VERTEX_POINT('',#793);
#267=VERTEX_POINT('',#795);
#268=VERTEX_POINT('',#800);
#269=VERTEX_POINT('',#807);
#270=VERTEX_POINT('',#808);
#271=VERTEX_POINT('',#810);
#272=VERTEX_POINT('',#815);
#273=VERTEX_POINT('',#817);
#274=VERTEX_POINT('',#827);
#275=VERTEX_POINT('',#832);
#276=VERTEX_POINT('',#833);
#277=VERTEX_POINT('',#838);
#278=VERTEX_POINT('',#839);
#279=VERTEX_POINT('',#844);
#280=VERTEX_POINT('',#846);
#281=VERTEX_POINT('',#850);
#282=VERTEX_POINT('',#852);
#283=EDGE_CURVE('',#245,#246,#412,.T.);
#284=EDGE_CURVE('',#246,#247,#340,.T.);
#285=EDGE_CURVE('',#247,#248,#413,.T.);
#286=EDGE_CURVE('',#248,#249,#341,.T.);
#287=EDGE_CURVE('',#249,#250,#414,.T.);
#288=EDGE_CURVE('',#250,#251,#342,.T.);
#289=EDGE_CURVE('',#251,#252,#415,.T.);
#290=EDGE_CURVE('',#252,#245,#343,.T.);
#291=EDGE_CURVE('',#253,#254,#416,.T.);
#292=EDGE_CURVE('',#255,#253,#417,.T.);
#293=EDGE_CURVE('',#256,#255,#418,.T.);
#294=EDGE_CURVE('',#257,#256,#419,.T.);
#295=EDGE_CURVE('',#258,#257,#420,.T.);
#296=EDGE_CURVE('',#259,#258,#421,.T.);
#297=EDGE_CURVE('',#260,#259,#422,.T.);
#298=EDGE_CURVE('',#254,#260,#423,.T.);
#299=EDGE_CURVE('',#261,#253,#424,.T.);
#300=EDGE_CURVE('',#261,#262,#344,.T.);
#301=EDGE_CURVE('',#263,#262,#425,.T.);
#302=EDGE_CURVE('',#263,#264,#426,.T.);
#303=EDGE_CURVE('',#265,#264,#427,.T.);
#304=EDGE_CURVE('',#255,#265,#428,.T.);
#305=EDGE_CURVE('',#264,#266,#429,.T.);
#306=EDGE_CURVE('',#267,#263,#430,.T.);
#307=EDGE_CURVE('',#267,#258,#431,.T.);
#308=EDGE_CURVE('',#257,#266,#432,.T.);
#309=EDGE_CURVE('',#266,#268,#433,.T.);
#310=EDGE_CURVE('',#256,#268,#434,.T.);
#311=EDGE_CURVE('',#268,#265,#435,.T.);
#312=EDGE_CURVE('',#269,#270,#436,.T.);
#313=EDGE_CURVE('',#269,#271,#345,.T.);
#314=EDGE_CURVE('',#271,#254,#437,.T.);
#315=EDGE_CURVE('',#270,#260,#438,.T.);
#316=EDGE_CURVE('',#272,#272,#346,.T.);
#317=EDGE_CURVE('',#273,#259,#439,.T.);
#318=EDGE_CURVE('',#270,#273,#440,.T.);
#319=EDGE_CURVE('',#273,#267,#441,.T.);
#320=EDGE_CURVE('',#271,#261,#442,.T.);
#321=EDGE_CURVE('',#262,#269,#443,.T.);
#322=EDGE_CURVE('',#274,#274,#347,.T.);
#323=EDGE_CURVE('',#275,#276,#444,.T.);
#324=EDGE_CURVE('',#275,#252,#445,.T.);
#325=EDGE_CURVE('',#251,#276,#446,.T.);
#326=EDGE_CURVE('',#277,#278,#447,.T.);
#327=EDGE_CURVE('',#277,#246,#448,.T.);
#328=EDGE_CURVE('',#245,#278,#449,.T.);
#329=EDGE_CURVE('',#247,#279,#450,.T.);
#330=EDGE_CURVE('',#280,#279,#451,.T.);
#331=EDGE_CURVE('',#280,#248,#452,.T.);
#332=EDGE_CURVE('',#249,#281,#453,.T.);
#333=EDGE_CURVE('',#282,#281,#454,.T.);
#334=EDGE_CURVE('',#282,#250,#455,.T.);
#335=EDGE_CURVE('',#279,#277,#348,.T.);
#336=EDGE_CURVE('',#278,#275,#349,.T.);
#337=EDGE_CURVE('',#276,#282,#350,.T.);
#338=EDGE_CURVE('',#281,#280,#351,.T.);
#339=CIRCLE('Arc1',#558,23.1283236048185);
#340=CIRCLE('',#560,5.);
#341=CIRCLE('',#561,5.);
#342=CIRCLE('',#562,5.);
#343=CIRCLE('',#563,5.);
#344=CIRCLE('',#565,5.);
#345=CIRCLE('',#571,5.);
#346=CIRCLE('',#573,23.1283236048185);
#347=CIRCLE('',#578,23.1283236048185);
#348=CIRCLE('',#586,5.);
#349=CIRCLE('',#587,5.);
#350=CIRCLE('',#588,5.);
#351=CIRCLE('',#589,5.);
#352=CURVE_STYLE('',#368,POSITIVE_LENGTH_MEASURE(0.18),#555);
#353=CURVE_STYLE('',#369,POSITIVE_LENGTH_MEASURE(0.18),#555);
#354=CURVE_STYLE('',#370,POSITIVE_LENGTH_MEASURE(0.18),#555);
#355=CURVE_STYLE('',#371,POSITIVE_LENGTH_MEASURE(0.18),#555);
#356=CURVE_STYLE('',#372,POSITIVE_LENGTH_MEASURE(0.18),#555);
#357=CURVE_STYLE('',#373,POSITIVE_LENGTH_MEASURE(0.18),#555);
#358=CURVE_STYLE('',#374,POSITIVE_LENGTH_MEASURE(0.18),#555);
#359=CURVE_STYLE('',#375,POSITIVE_LENGTH_MEASURE(0.18),#555);
#360=CURVE_STYLE('',#376,POSITIVE_LENGTH_MEASURE(0.18),#555);
#361=CURVE_STYLE('',#377,POSITIVE_LENGTH_MEASURE(0.18),#555);
#362=CURVE_STYLE('',#378,POSITIVE_LENGTH_MEASURE(0.18),#555);
#363=CURVE_STYLE('',#379,POSITIVE_LENGTH_MEASURE(0.18),#555);
#364=CURVE_STYLE('',#380,POSITIVE_LENGTH_MEASURE(0.18),#555);
#365=CURVE_STYLE('',#381,POSITIVE_LENGTH_MEASURE(0.18),#555);
#366=CURVE_STYLE('',#382,POSITIVE_LENGTH_MEASURE(0.18),#555);
#367=CURVE_STYLE('',#383,POSITIVE_LENGTH_MEASURE(0.18),#554);
#368=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#369=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#370=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#371=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#372=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#373=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#374=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#375=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#376=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#377=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#378=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#379=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#380=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#381=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#382=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#383=DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#384=TRIMMED_CURVE('Line1',#398,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#385=TRIMMED_CURVE('Line2',#399,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#386=TRIMMED_CURVE('Line3',#400,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#387=TRIMMED_CURVE('Line4',#401,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#388=TRIMMED_CURVE('Line5',#402,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#389=TRIMMED_CURVE('Line6',#403,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#390=TRIMMED_CURVE('Line7',#404,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#391=TRIMMED_CURVE('Line8',#405,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#392=TRIMMED_CURVE('Line9',#406,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#393=TRIMMED_CURVE('Line10',#407,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#394=TRIMMED_CURVE('Line11',#408,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#395=TRIMMED_CURVE('Line12',#409,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#396=TRIMMED_CURVE('Line13',#410,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#397=TRIMMED_CURVE('Line14',#411,(PARAMETER_VALUE(0.)),(
PARAMETER_VALUE(1.)),.T.,.PARAMETER.);
#398=LINE('Line1',#731,#456);
#399=LINE('Line2',#732,#457);
#400=LINE('Line3',#733,#458);
#401=LINE('Line4',#734,#459);
#402=LINE('Line5',#735,#460);
#403=LINE('Line6',#736,#461);
#404=LINE('Line7',#737,#462);
#405=LINE('Line8',#738,#463);
#406=LINE('Line9',#739,#464);
#407=LINE('Line10',#740,#465);
#408=LINE('Line11',#741,#466);
#409=LINE('Line12',#742,#467);
#410=LINE('Line13',#743,#468);
#411=LINE('Line14',#744,#469);
#412=LINE('',#747,#470);
#413=LINE('',#752,#471);
#414=LINE('',#756,#472);
#415=LINE('',#760,#473);
#416=LINE('',#763,#474);
#417=LINE('',#766,#475);
#418=LINE('',#768,#476);
#419=LINE('',#770,#477);
#420=LINE('',#772,#478);
#421=LINE('',#774,#479);
#422=LINE('',#776,#480);
#423=LINE('',#778,#481);
#424=LINE('',#780,#482);
#425=LINE('',#784,#483);
#426=LINE('',#786,#484);
#427=LINE('',#788,#485);
#428=LINE('',#790,#486);
#429=LINE('',#792,#487);
#430=LINE('',#794,#488);
#431=LINE('',#796,#489);
#432=LINE('',#797,#490);
#433=LINE('',#799,#491);
#434=LINE('',#801,#492);
#435=LINE('',#803,#493);
#436=LINE('',#806,#494);
#437=LINE('',#811,#495);
#438=LINE('',#812,#496);
#439=LINE('',#816,#497);
#440=LINE('',#818,#498);
#441=LINE('',#820,#499);
#442=LINE('',#822,#500);
#443=LINE('',#824,#501);
#444=LINE('',#831,#502);
#445=LINE('',#834,#503);
#446=LINE('',#835,#504);
#447=LINE('',#837,#505);
#448=LINE('',#840,#506);
#449=LINE('',#841,#507);
#450=LINE('',#843,#508);
#451=LINE('',#845,#509);
#452=LINE('',#847,#510);
#453=LINE('',#849,#511);
#454=LINE('',#851,#512);
#455=LINE('',#853,#513);
#456=VECTOR('',#597,315.);
#457=VECTOR('',#598,225.);
#458=VECTOR('',#599,315.);
#459=VECTOR('',#600,225.);
#460=VECTOR('',#601,178.985428527387);
#461=VECTOR('',#602,40.);
#462=VECTOR('',#603,138.985428527387);
#463=VECTOR('',#604,40.1507853183055);
#464=VECTOR('',#605,196.4002269787);
#465=VECTOR('',#606,236.551012297005);
#466=VECTOR('',#607,92.);
#467=VECTOR('',#608,100.);
#468=VECTOR('',#609,92.);
#469=VECTOR('',#610,100.);
#470=VECTOR('',#615,1.);
#471=VECTOR('',#618,1.);
#472=VECTOR('',#621,1.);
#473=VECTOR('',#624,1.);
#474=VECTOR('',#627,1.);
#475=VECTOR('',#628,1.);
#476=VECTOR('',#629,1.);
#477=VECTOR('',#630,1.);
#478=VECTOR('',#631,1.);
#479=VECTOR('',#632,1.);
#480=VECTOR('',#633,1.);
#481=VECTOR('',#634,1.);
#482=VECTOR('',#637,1.);
#483=VECTOR('',#640,1.);
#484=VECTOR('',#641,1.);
#485=VECTOR('',#642,1.);
#486=VECTOR('',#643,1.);
#487=VECTOR('',#646,1.);
#488=VECTOR('',#647,1.);
#489=VECTOR('',#648,1.);
#490=VECTOR('',#649,1.);
#491=VECTOR('',#652,1.);
#492=VECTOR('',#653,1.);
#493=VECTOR('',#656,1.);
#494=VECTOR('',#661,1.);
#495=VECTOR('',#664,1.);
#496=VECTOR('',#665,1.);
#497=VECTOR('',#670,1.);
#498=VECTOR('',#671,1.);
#499=VECTOR('',#674,1.);
#500=VECTOR('',#677,1.);
#501=VECTOR('',#680,1.);
#502=VECTOR('',#691,1.);
#503=VECTOR('',#692,1.);
#504=VECTOR('',#693,1.);
#505=VECTOR('',#696,1.);
#506=VECTOR('',#697,1.);
#507=VECTOR('',#698,1.);
#508=VECTOR('',#701,1.);
#509=VECTOR('',#702,1.);
#510=VECTOR('',#703,1.);
#511=VECTOR('',#706,1.);
#512=VECTOR('',#707,1.);
#513=VECTOR('',#708,1.);
#514=PRESENTATION_LAYER_ASSIGNMENT('61','Layer 61',(#730,#559));
#515=PRESENTATION_LAYER_ASSIGNMENT('1','Layer 1',(#384,#385,#386,#387,#388,
#389,#390,#391,#392,#393,#394,#395,#396,#397,#339,#33));
#516=STYLED_ITEM('',(#534),#730);
#517=STYLED_ITEM('',(#535),#384);
#518=STYLED_ITEM('',(#536),#385);
#519=STYLED_ITEM('',(#537),#386);
#520=STYLED_ITEM('',(#538),#387);
#521=STYLED_ITEM('',(#539),#388);
#522=STYLED_ITEM('',(#540),#389);
#523=STYLED_ITEM('',(#541),#390);
#524=STYLED_ITEM('',(#542),#391);
#525=STYLED_ITEM('',(#543),#392);
#526=STYLED_ITEM('',(#544),#393);
#527=STYLED_ITEM('',(#545),#394);
#528=STYLED_ITEM('',(#546),#395);
#529=STYLED_ITEM('',(#547),#396);
#530=STYLED_ITEM('',(#548),#397);
#531=STYLED_ITEM('',(#549),#339);
#532=STYLED_ITEM('',(#550),#559);
#533=STYLED_ITEM('',(#551),#33);
#534=PRESENTATION_STYLE_ASSIGNMENT((#552));
#535=PRESENTATION_STYLE_ASSIGNMENT((#352));
#536=PRESENTATION_STYLE_ASSIGNMENT((#353));
#537=PRESENTATION_STYLE_ASSIGNMENT((#354));
#538=PRESENTATION_STYLE_ASSIGNMENT((#355));
#539=PRESENTATION_STYLE_ASSIGNMENT((#356));
#540=PRESENTATION_STYLE_ASSIGNMENT((#357));
#541=PRESENTATION_STYLE_ASSIGNMENT((#358));
#542=PRESENTATION_STYLE_ASSIGNMENT((#359));
#543=PRESENTATION_STYLE_ASSIGNMENT((#360));
#544=PRESENTATION_STYLE_ASSIGNMENT((#361));
#545=PRESENTATION_STYLE_ASSIGNMENT((#362));
#546=PRESENTATION_STYLE_ASSIGNMENT((#363));
#547=PRESENTATION_STYLE_ASSIGNMENT((#364));
#548=PRESENTATION_STYLE_ASSIGNMENT((#365));
#549=PRESENTATION_STYLE_ASSIGNMENT((#366));
#550=PRESENTATION_STYLE_ASSIGNMENT((#367));
#551=PRESENTATION_STYLE_ASSIGNMENT((#28));
#552=POINT_STYLE('',#553,POSITIVE_LENGTH_MEASURE(3.),#554);
#553=PRE_DEFINED_POINT_MARKER_SYMBOL('plus');
#554=COLOUR_RGB('Medium Maroon',0.6,0.4,0.4);
#555=COLOUR_RGB('Medium Royal',0.2,0.4,0.8);
#556=COLOUR_RGB('Medium Steel',0.596063172350652,0.666666666666667,0.686259250782025);
#557=AXIS2_PLACEMENT_3D('',#729,#595,#596);
#558=AXIS2_PLACEMENT_3D('',#745,#611,#612);
#559=AXIS2_PLACEMENT_3D('',#746,#613,#614);
#560=AXIS2_PLACEMENT_3D('',#750,#616,#617);
#561=AXIS2_PLACEMENT_3D('',#754,#619,#620);
#562=AXIS2_PLACEMENT_3D('',#758,#622,#623);
#563=AXIS2_PLACEMENT_3D('',#762,#625,#626);
#564=AXIS2_PLACEMENT_3D('',#779,#635,#636);
#565=AXIS2_PLACEMENT_3D('',#782,#638,#639);
#566=AXIS2_PLACEMENT_3D('',#791,#644,#645);
#567=AXIS2_PLACEMENT_3D('',#798,#650,#651);
#568=AXIS2_PLACEMENT_3D('',#802,#654,#655);
#569=AXIS2_PLACEMENT_3D('',#804,#657,#658);
#570=AXIS2_PLACEMENT_3D('',#805,#659,#660);
#571=AXIS2_PLACEMENT_3D('',#809,#662,#663);
#572=AXIS2_PLACEMENT_3D('',#813,#666,#667);
#573=AXIS2_PLACEMENT_3D('',#814,#668,#669);
#574=AXIS2_PLACEMENT_3D('',#819,#672,#673);
#575=AXIS2_PLACEMENT_3D('',#821,#675,#676);
#576=AXIS2_PLACEMENT_3D('',#823,#678,#679);
#577=AXIS2_PLACEMENT_3D('',#825,#681,#682);
#578=AXIS2_PLACEMENT_3D('',#826,#683,#684);
#579=AXIS2_PLACEMENT_3D('',#828,#685,#686);
#580=AXIS2_PLACEMENT_3D('',#829,#687,#688);
#581=AXIS2_PLACEMENT_3D('',#830,#689,#690);
#582=AXIS2_PLACEMENT_3D('',#836,#694,#695);
#583=AXIS2_PLACEMENT_3D('',#842,#699,#700);
#584=AXIS2_PLACEMENT_3D('',#848,#704,#705);
#585=AXIS2_PLACEMENT_3D('',#854,#709,#710);
#586=AXIS2_PLACEMENT_3D('',#855,#711,#712);
#587=AXIS2_PLACEMENT_3D('',#856,#713,#714);
#588=AXIS2_PLACEMENT_3D('',#857,#715,#716);
#589=AXIS2_PLACEMENT_3D('',#858,#717,#718);
#590=AXIS2_PLACEMENT_3D('',#859,#719,#720);
#591=AXIS2_PLACEMENT_3D('',#860,#721,#722);
#592=AXIS2_PLACEMENT_3D('',#861,#723,#724);
#593=AXIS2_PLACEMENT_3D('',#862,#725,#726);
#594=AXIS2_PLACEMENT_3D('',#863,#727,#728);
#595=DIRECTION('',(0.,0.,1.));
#596=DIRECTION('',(1.,0.,0.));
#597=DIRECTION('',(1.,0.,0.));
#598=DIRECTION('',(0.,0.,1.));
#599=DIRECTION('',(-1.,0.,0.));
#600=DIRECTION('',(0.,0.,-1.));
#601=DIRECTION('',(6.35173704680774E-016,0.,1.));
#602=DIRECTION('',(-3.5527136788005E-015,0.,-1.));
#603=DIRECTION('',(0.,0.,-1.));
#604=DIRECTION('',(-1.,0.,7.07874309433396E-016));
#605=DIRECTION('',(-1.,0.,1.01299254625813E-015));
#606=DIRECTION('',(1.,0.,0.));
#607=DIRECTION('',(-3.39824786667874E-015,0.,-1.));
#608=DIRECTION('',(-1.,0.,3.48165940522449E-015));
#609=DIRECTION('',(3.47548077273962E-015,0.,1.));
#610=DIRECTION('',(1.,0.,-3.69482222595252E-015));
#611=DIRECTION('',(-1.,0.,0.));
#612=DIRECTION('',(0.,0.,-1.));
#613=DIRECTION('',(0.,0.,1.));
#614=DIRECTION('',(1.,0.,0.));
#615=DIRECTION('',(-1.,0.,3.53883589099269E-015));
#616=DIRECTION('',(0.,1.,0.));
#617=DIRECTION('',(0.,0.,1.));
#618=DIRECTION('',(3.46944695195361E-015,0.,1.));
#619=DIRECTION('',(0.,1.,0.));
#620=DIRECTION('',(0.,0.,1.));
#621=DIRECTION('',(1.,0.,-3.60822483003176E-015));
#622=DIRECTION('',(0.,1.,0.));
#623=DIRECTION('',(0.,0.,1.));
#624=DIRECTION('',(-3.62029247160377E-015,0.,-1.));
#625=DIRECTION('',(0.,1.,0.));
#626=DIRECTION('',(0.,0.,1.));
#627=DIRECTION('',(-3.46944695195361E-015,0.,-1.));
#628=DIRECTION('',(1.,0.,0.));
#629=DIRECTION('',(0.,0.,1.));
#630=DIRECTION('',(-1.,0.,0.));
#631=DIRECTION('',(0.,0.,-1.));
#632=DIRECTION('',(1.,0.,-6.91283505306051E-016));
#633=DIRECTION('',(0.,0.,-1.));
#634=DIRECTION('',(1.,0.,-9.89250533455206E-016));
#635=DIRECTION('',(0.,1.,0.));
#636=DIRECTION('',(0.,0.,1.));
#637=DIRECTION('',(0.,1.,0.));
#638=DIRECTION('',(0.,0.,1.));
#639=DIRECTION('',(1.,0.,0.));
#640=DIRECTION('',(-1.,0.,0.));
#641=DIRECTION('',(0.,1.,0.));
#642=DIRECTION('',(1.,0.,0.));
#643=DIRECTION('',(0.,1.,0.));
#644=DIRECTION('',(0.,0.,1.));
#645=DIRECTION('',(1.,0.,0.));
#646=DIRECTION('',(0.,0.,-1.));
#647=DIRECTION('',(6.20286820977318E-016,0.,1.));
#648=DIRECTION('',(0.,1.,0.));
#649=DIRECTION('',(0.,1.,0.));
#650=DIRECTION('',(1.,0.,0.));
#651=DIRECTION('',(0.,0.,-1.));
#652=DIRECTION('',(-1.,0.,0.));
#653=DIRECTION('',(0.,1.,0.));
#654=DIRECTION('',(0.,0.,-1.));
#655=DIRECTION('',(-1.,0.,0.));
#656=DIRECTION('',(0.,0.,1.));
#657=DIRECTION('',(-1.,0.,0.));
#658=DIRECTION('',(0.,0.,1.));
#659=DIRECTION('',(0.,1.,0.));
#660=DIRECTION('',(0.,0.,1.));
#661=DIRECTION('',(1.,0.,-9.89250533455206E-016));
#662=DIRECTION('',(-9.89250533455206E-016,0.,-1.));
#663=DIRECTION('',(1.,0.,0.));
#664=DIRECTION('',(0.,1.,0.));
#665=DIRECTION('',(0.,1.,0.));
#666=DIRECTION('',(9.89250533455206E-016,0.,1.));
#667=DIRECTION('',(1.,0.,-9.99200722162641E-016));
#668=DIRECTION('',(-1.,0.,0.));
#669=DIRECTION('',(0.,0.,-1.));
#670=DIRECTION('',(0.,1.,0.));
#671=DIRECTION('',(0.,0.,-1.));
#672=DIRECTION('',(1.,0.,0.));
#673=DIRECTION('',(0.,0.,-1.));
#674=DIRECTION('',(1.,0.,-6.91283505306051E-016));
#675=DIRECTION('',(6.91283505306051E-016,0.,1.));
#676=DIRECTION('',(1.,0.,-6.93889390390723E-016));
#677=DIRECTION('',(3.46944695195361E-015,0.,1.));
#678=DIRECTION('',(1.,0.,-3.46944695195361E-015));
#679=DIRECTION('',(-3.46944695195361E-015,0.,-1.));
#680=DIRECTION('',(-3.46944695195361E-015,0.,-1.));
#681=DIRECTION('',(0.,1.,0.));
#682=DIRECTION('',(0.,0.,1.));
#683=DIRECTION('',(-1.,0.,0.));
#684=DIRECTION('',(0.,0.,-1.));
#685=DIRECTION('',(1.,0.,0.));
#686=DIRECTION('',(0.,0.,-1.));
#687=DIRECTION('',(-1.,0.,0.));
#688=DIRECTION('',(0.,0.,1.));
#689=DIRECTION('',(-3.46944695195361E-015,0.,-1.));
#690=DIRECTION('',(-1.,0.,3.46944695195361E-015));
#691=DIRECTION('',(3.62029247160377E-015,0.,1.));
#692=DIRECTION('',(0.,-1.,0.));
#693=DIRECTION('',(0.,1.,0.));
#694=DIRECTION('',(-1.,0.,3.62029247160377E-015));
#695=DIRECTION('',(3.60822483003176E-015,0.,1.));
#696=DIRECTION('',(1.,0.,-3.53883589099269E-015));
#697=DIRECTION('',(0.,-1.,0.));
#698=DIRECTION('',(0.,1.,0.));
#699=DIRECTION('',(3.53883589099269E-015,0.,1.));
#700=DIRECTION('',(1.,0.,-3.53883589099269E-015));
#701=DIRECTION('',(0.,1.,0.));
#702=DIRECTION('',(-3.46944695195361E-015,0.,-1.));
#703=DIRECTION('',(0.,-1.,0.));
#704=DIRECTION('',(1.,0.,-3.46944695195361E-015));
#705=DIRECTION('',(-3.46944695195361E-015,0.,-1.));
#706=DIRECTION('',(0.,1.,0.));
#707=DIRECTION('',(-1.,0.,3.60822483003176E-015));
#708=DIRECTION('',(0.,-1.,0.));
#709=DIRECTION('',(-3.60822483003176E-015,0.,-1.));
#710=DIRECTION('',(-1.,0.,3.60822483003176E-015));
#711=DIRECTION('',(0.,-1.,0.));
#712=DIRECTION('',(0.,0.,1.));
#713=DIRECTION('',(0.,-1.,0.));
#714=DIRECTION('',(0.,0.,1.));
#715=DIRECTION('',(0.,-1.,0.));
#716=DIRECTION('',(0.,0.,1.));
#717=DIRECTION('',(0.,-1.,0.));
#718=DIRECTION('',(0.,0.,1.));
#719=DIRECTION('',(0.,1.,0.));
#720=DIRECTION('',(0.,0.,1.));
#721=DIRECTION('',(0.,-1.,0.));
#722=DIRECTION('',(0.,0.,-1.));
#723=DIRECTION('',(0.,-1.,0.));
#724=DIRECTION('',(0.,0.,-0.999999999999999));
#725=DIRECTION('',(0.,-1.,0.));
#726=DIRECTION('',(0.,0.,-1.));
#727=DIRECTION('',(0.,-1.,0.));
#728=DIRECTION('',(0.,0.,-0.999999999999999));
#729=CARTESIAN_POINT('',(0.,0.,0.));
#730=CARTESIAN_POINT('',(0.,0.,0.));
#731=CARTESIAN_POINT('',(0.,0.,0.));
#732=CARTESIAN_POINT('',(315.,0.,0.));
#733=CARTESIAN_POINT('',(315.,0.,225.));
#734=CARTESIAN_POINT('',(0.,0.,225.));
#735=CARTESIAN_POINT('',(315.,-25.,46.0145714726135));
#736=CARTESIAN_POINT('',(78.4489877029947,-25.,225.));
#737=CARTESIAN_POINT('',(274.849214681695,-25.,185.));
#738=CARTESIAN_POINT('',(315.,-25.,46.0145714726135));
#739=CARTESIAN_POINT('',(274.849214681695,-25.,185.));
#740=CARTESIAN_POINT('',(78.4489877029947,-25.,225.));
#741=CARTESIAN_POINT('',(153.000000000001,-25.,145.999999999999));
#742=CARTESIAN_POINT('',(153.,-25.,53.9999999999995));
#743=CARTESIAN_POINT('',(53.0000000000002,-25.,53.9999999999998));
#744=CARTESIAN_POINT('',(53.0000000000005,-25.,146.));
#745=CARTESIAN_POINT('',(274.849214681695,-73.297326908187,87.8720070391476));
#746=CARTESIAN_POINT('',(0.,0.,0.));
#747=CARTESIAN_POINT('',(153.,-25.,53.9999999999995));
#748=CARTESIAN_POINT('',(148.,-25.,53.9999999999995));
#749=CARTESIAN_POINT('',(58.0000000000002,-25.,53.9999999999998));
#750=CARTESIAN_POINT('',(58.0000000000002,-25.,58.9999999999998));
#751=CARTESIAN_POINT('',(53.0000000000002,-25.,58.9999999999998));
#752=CARTESIAN_POINT('',(53.0000000000002,-25.,53.9999999999998));
#753=CARTESIAN_POINT('',(53.0000000000005,-25.,141.));
#754=CARTESIAN_POINT('',(58.0000000000005,-25.,141.));
#755=CARTESIAN_POINT('',(58.0000000000005,-25.,146.));
#756=CARTESIAN_POINT('',(53.0000000000005,-25.,146.));
#757=CARTESIAN_POINT('',(148.000000000001,-25.,145.999999999999));
#758=CARTESIAN_POINT('',(148.,-25.,140.999999999999));
#759=CARTESIAN_POINT('',(153.,-25.,140.999999999999));
#760=CARTESIAN_POINT('',(153.000000000001,-25.,145.999999999999));
#761=CARTESIAN_POINT('',(153.,-25.,58.9999999999995));
#762=CARTESIAN_POINT('',(148.,-25.,58.9999999999995));
#763=CARTESIAN_POINT('',(78.4489877029947,-25.,225.));
#764=CARTESIAN_POINT('',(78.4489877029947,-25.,225.));
#765=CARTESIAN_POINT('',(78.4489877029947,-25.,185.));
#766=CARTESIAN_POINT('',(0.,-25.,225.));
#767=CARTESIAN_POINT('',(0.,-25.,225.));
#768=CARTESIAN_POINT('',(0.,-25.,0.));
#769=CARTESIAN_POINT('',(0.,-25.,0.));
#770=CARTESIAN_POINT('',(315.,-25.,0.));
#771=CARTESIAN_POINT('',(315.,-25.,0.));
#772=CARTESIAN_POINT('',(315.,-25.,225.));
#773=CARTESIAN_POINT('',(315.,-25.,46.0145714726135));
#774=CARTESIAN_POINT('',(274.849214681695,-25.,46.0145714726135));
#775=CARTESIAN_POINT('',(274.849214681695,-25.,46.0145714726135));
#776=CARTESIAN_POINT('',(274.849214681695,-25.,185.));
#777=CARTESIAN_POINT('',(274.849214681695,-25.,185.));
#778=CARTESIAN_POINT('',(78.4489877029947,-25.,185.));
#779=CARTESIAN_POINT('',(0.,-25.,0.));
#780=CARTESIAN_POINT('',(78.4489877029947,-105.,225.));
#781=CARTESIAN_POINT('',(78.4489877029947,-100.,225.));
#782=CARTESIAN_POINT('',(83.4489877029947,-100.,225.));
#783=CARTESIAN_POINT('',(83.4489877029947,-105.,225.));
#784=CARTESIAN_POINT('',(315.,-105.,225.));
#785=CARTESIAN_POINT('',(315.,-105.,225.));
#786=CARTESIAN_POINT('',(315.,-25.,225.));
#787=CARTESIAN_POINT('',(315.,0.,225.));
#788=CARTESIAN_POINT('',(0.,0.,225.));
#789=CARTESIAN_POINT('',(0.,0.,225.));
#790=CARTESIAN_POINT('',(0.,-25.,225.));
#791=CARTESIAN_POINT('',(0.,-25.,225.));
#792=CARTESIAN_POINT('',(315.,0.,225.));
#793=CARTESIAN_POINT('',(315.,0.,0.));
#794=CARTESIAN_POINT('',(315.,-105.,46.0145714726135));
#795=CARTESIAN_POINT('',(315.,-105.,46.0145714726135));
#796=CARTESIAN_POINT('',(315.,-105.,46.0145714726135));
#797=CARTESIAN_POINT('',(315.,-25.,0.));
#798=CARTESIAN_POINT('',(315.,-25.,225.));
#799=CARTESIAN_POINT('',(315.,0.,0.));
#800=CARTESIAN_POINT('',(0.,0.,0.));
#801=CARTESIAN_POINT('',(0.,-25.,0.));
#802=CARTESIAN_POINT('',(315.,-25.,0.));
#803=CARTESIAN_POINT('',(0.,0.,0.));
#804=CARTESIAN_POINT('',(0.,-25.,0.));
#805=CARTESIAN_POINT('',(0.,0.,0.));
#806=CARTESIAN_POINT('',(78.4489877029947,-105.,185.));
#807=CARTESIAN_POINT('',(83.4489877029946,-105.,185.));
#808=CARTESIAN_POINT('',(274.849214681695,-105.,185.));
#809=CARTESIAN_POINT('',(83.4489877029946,-100.,185.));
#810=CARTESIAN_POINT('',(78.4489877029947,-100.,185.));
#811=CARTESIAN_POINT('',(78.4489877029947,-105.,185.));
#812=CARTESIAN_POINT('',(274.849214681695,-105.,185.));
#813=CARTESIAN_POINT('',(78.4489877029947,-105.,185.));
#814=CARTESIAN_POINT('',(274.849214681695,-73.297326908187,87.8720070391476));
#815=CARTESIAN_POINT('',(274.849214681695,-73.297326908187,64.7436834343291));
#816=CARTESIAN_POINT('',(274.849214681695,-105.,46.0145714726135));
#817=CARTESIAN_POINT('',(274.849214681695,-105.,46.0145714726135));
#818=CARTESIAN_POINT('',(274.849214681695,-105.,185.));
#819=CARTESIAN_POINT('',(274.849214681695,-105.,185.));
#820=CARTESIAN_POINT('',(274.849214681695,-105.,46.0145714726135));
#821=CARTESIAN_POINT('',(274.849214681695,-105.,46.0145714726135));
#822=CARTESIAN_POINT('',(78.4489877029947,-100.,225.));
#823=CARTESIAN_POINT('',(78.4489877029947,-105.,225.));
#824=CARTESIAN_POINT('',(83.448987702994,-105.,-2.89521836029767E-013));
#825=CARTESIAN_POINT('',(0.,-105.,0.));
#826=CARTESIAN_POINT('',(194.849214681695,-73.297326908187,87.8720070391476));
#827=CARTESIAN_POINT('',(194.849214681695,-73.297326908187,64.7436834343291));
#828=CARTESIAN_POINT('',(194.849214681695,-73.297326908187,87.8720070391476));
#829=CARTESIAN_POINT('',(194.849214681695,-73.297326908187,87.8720070391476));
#830=CARTESIAN_POINT('',(83.448987702994,-100.,-2.89521836029767E-013));
#831=CARTESIAN_POINT('',(153.000000000001,-20.,145.999999999999));
#832=CARTESIAN_POINT('',(153.,-20.,58.9999999999995));
#833=CARTESIAN_POINT('',(153.,-20.,140.999999999999));
#834=CARTESIAN_POINT('',(153.,-25.,58.9999999999995));
#835=CARTESIAN_POINT('',(153.,-20.,140.999999999999));
#836=CARTESIAN_POINT('',(153.000000000001,-20.,145.999999999999));
#837=CARTESIAN_POINT('',(153.,-20.,53.9999999999995));
#838=CARTESIAN_POINT('',(58.0000000000002,-20.,53.9999999999998));
#839=CARTESIAN_POINT('',(148.,-20.,53.9999999999995));
#840=CARTESIAN_POINT('',(58.0000000000002,-25.,53.9999999999998));
#841=CARTESIAN_POINT('',(148.,-20.,53.9999999999995));
#842=CARTESIAN_POINT('',(153.,-20.,53.9999999999995));
#843=CARTESIAN_POINT('',(53.0000000000002,-20.,58.9999999999998));
#844=CARTESIAN_POINT('',(53.0000000000002,-20.,58.9999999999998));
#845=CARTESIAN_POINT('',(53.0000000000002,-20.,53.9999999999998));
#846=CARTESIAN_POINT('',(53.0000000000005,-20.,141.));
#847=CARTESIAN_POINT('',(53.0000000000005,-25.,141.));
#848=CARTESIAN_POINT('',(53.0000000000002,-20.,53.9999999999998));
#849=CARTESIAN_POINT('',(58.0000000000005,-20.,146.));
#850=CARTESIAN_POINT('',(58.0000000000005,-20.,146.));
#851=CARTESIAN_POINT('',(53.0000000000005,-20.,146.));
#852=CARTESIAN_POINT('',(148.000000000001,-20.,145.999999999999));
#853=CARTESIAN_POINT('',(148.000000000001,-25.,145.999999999999));
#854=CARTESIAN_POINT('',(53.0000000000005,-20.,146.));
#855=CARTESIAN_POINT('',(58.0000000000002,-20.,58.9999999999998));
#856=CARTESIAN_POINT('',(148.,-20.,58.9999999999995));
#857=CARTESIAN_POINT('',(148.,-20.,140.999999999999));
#858=CARTESIAN_POINT('',(58.0000000000005,-20.,141.));
#859=CARTESIAN_POINT('',(0.,-20.,0.));
#860=CARTESIAN_POINT('',(148.,-20.,140.999999999999));
#861=CARTESIAN_POINT('',(148.,-20.,58.9999999999995));
#862=CARTESIAN_POINT('',(58.0000000000005,-20.,141.));
#863=CARTESIAN_POINT('',(58.0000000000002,-20.,58.9999999999998));
#864=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#516,#517,
#518,#519,#520,#521,#522,#523,#524,#525,#526,#527,#528,#529,#530,#531,#532,
#533),#865);
#865=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#866))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#872,#868,#867))
REPRESENTATION_CONTEXT('part_parametric','TOP_LEVEL_ASSEMBLY_PART')
);
#866=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(2.E-005),#872,
'DISTANCE_ACCURACY_VALUE','Maximum Tolerance applied to model');
#867=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#868=(
CONVERSION_BASED_UNIT('DEGREE',#870)
NAMED_UNIT(#869)
PLANE_ANGLE_UNIT()
);
#869=DIMENSIONAL_EXPONENTS(0.,0.,0.,0.,0.,0.,0.);
#870=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(0.0174532925),#871);
#871=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#872=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
ENDSEC;
END-ISO-10303-21;
