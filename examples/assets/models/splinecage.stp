ISO-10303-21;
HEADER;
/* Generated by software containing ST-Developer
 * from STEP Tools, Inc. (www.steptools.com) 
 */
/* OPTION: using custom schema-name function */

FILE_DESCRIPTION(
/* description */ (''),
/* implementation_level */ '2;1');

FILE_NAME(
/* name */ 'splinecage',
/* time_stamp */ '2014-11-25T23:28:15+01:00',
/* author */ (''),
/* organization */ (''),
/* preprocessor_version */ 'ST-DEVELOPER v15',
/* originating_system */ '',
/* authorisation */ '');

FILE_SCHEMA (('AUTOMOTIVE_DESIGN_CC2'));
ENDSEC;

DATA;
#10=SHAPE_REPRESENTATION_RELATIONSHIP('','',#258,#13);
#11=SHAPE_REPRESENTATION_RELATIONSHIP('','',#258,#85);
#12=GEOMETRIC_CURVE_SET('curve_set_0',(#214,#215,#216,#217,#218,#219));
#13=GEOMETRICALLY_BOUNDED_WIREFRAME_SHAPE_REPRESENTATION(
'wireframe_rep_0',(#12,#260),#257);
#14=CURVE_STYLE('3d BsCrv',$,POSITIVE_LENGTH_MEASURE(0.02),#24);
#15=CURVE_STYLE('3d BsCrv',$,POSITIVE_LENGTH_MEASURE(0.02),#25);
#16=CURVE_STYLE('3d BsCrv',$,POSITIVE_LENGTH_MEASURE(0.02),#26);
#17=CURVE_STYLE('3d BsCrv',$,POSITIVE_LENGTH_MEASURE(0.02),#27);
#18=CURVE_STYLE('3d BsCrv',$,POSITIVE_LENGTH_MEASURE(0.02),#28);
#19=CURVE_STYLE('3d BsCrv',$,POSITIVE_LENGTH_MEASURE(0.02),#29);
#20=COLOUR_RGB('',0.,0.,0.);
#21=COLOUR_RGB('',0.,0.,0.);
#22=COLOUR_RGB('',0.,0.,0.);
#23=COLOUR_RGB('',0.,0.,0.);
#24=COLOUR_RGB('',0.,0.,0.);
#25=COLOUR_RGB('',0.,0.,0.);
#26=COLOUR_RGB('',0.,0.,0.);
#27=COLOUR_RGB('',0.,0.,0.);
#28=COLOUR_RGB('',0.,0.,0.);
#29=COLOUR_RGB('',0.,0.,0.);
#30=FILL_AREA_STYLE_COLOUR('',#20);
#31=FILL_AREA_STYLE_COLOUR('',#21);
#32=FILL_AREA_STYLE_COLOUR('',#22);
#33=FILL_AREA_STYLE_COLOUR('',#23);
#34=FILL_AREA_STYLE('',(#30));
#35=FILL_AREA_STYLE('',(#31));
#36=FILL_AREA_STYLE('',(#32));
#37=FILL_AREA_STYLE('',(#33));
#38=SURFACE_STYLE_FILL_AREA(#34);
#39=SURFACE_STYLE_FILL_AREA(#35);
#40=SURFACE_STYLE_FILL_AREA(#36);
#41=SURFACE_STYLE_FILL_AREA(#37);
#42=SURFACE_SIDE_STYLE('',(#38));
#43=SURFACE_SIDE_STYLE('',(#39));
#44=SURFACE_SIDE_STYLE('',(#40));
#45=SURFACE_SIDE_STYLE('',(#41));
#46=SURFACE_STYLE_USAGE(.BOTH.,#42);
#47=SURFACE_STYLE_USAGE(.BOTH.,#43);
#48=SURFACE_STYLE_USAGE(.BOTH.,#44);
#49=SURFACE_STYLE_USAGE(.BOTH.,#45);
#50=MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#61,#62,
#63,#64,#65,#66,#67,#68,#69,#70),#257);
#51=PRESENTATION_STYLE_ASSIGNMENT((#46));
#52=PRESENTATION_STYLE_ASSIGNMENT((#47));
#53=PRESENTATION_STYLE_ASSIGNMENT((#48));
#54=PRESENTATION_STYLE_ASSIGNMENT((#49));
#55=PRESENTATION_STYLE_ASSIGNMENT((#14));
#56=PRESENTATION_STYLE_ASSIGNMENT((#15));
#57=PRESENTATION_STYLE_ASSIGNMENT((#16));
#58=PRESENTATION_STYLE_ASSIGNMENT((#17));
#59=PRESENTATION_STYLE_ASSIGNMENT((#18));
#60=PRESENTATION_STYLE_ASSIGNMENT((#19));
#61=STYLED_ITEM('',(#51),#81);
#62=STYLED_ITEM('',(#52),#82);
#63=STYLED_ITEM('',(#53),#83);
#64=STYLED_ITEM('',(#54),#84);
#65=STYLED_ITEM('',(#55),#214);
#66=STYLED_ITEM('',(#56),#215);
#67=STYLED_ITEM('',(#57),#216);
#68=STYLED_ITEM('',(#58),#217);
#69=STYLED_ITEM('',(#59),#218);
#70=STYLED_ITEM('',(#60),#219);
#71=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#81));
#72=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#82));
#73=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#83));
#74=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#84));
#75=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#214));
#76=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#215));
#77=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#216));
#78=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#217));
#79=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#218));
#80=PRESENTATION_LAYER_ASSIGNMENT('Default','',(#219));
#81=SHELL_BASED_SURFACE_MODEL('shell_1',(#86));
#82=SHELL_BASED_SURFACE_MODEL('shell_2',(#87));
#83=SHELL_BASED_SURFACE_MODEL('shell_3',(#88));
#84=SHELL_BASED_SURFACE_MODEL('shell_4',(#89));
#85=MANIFOLD_SURFACE_SHAPE_REPRESENTATION('shell_rep_0',(#81,#82,#83,#84,
#261),#257);
#86=OPEN_SHELL('',(#90));
#87=OPEN_SHELL('',(#91));
#88=OPEN_SHELL('',(#92));
#89=OPEN_SHELL('',(#93));
#90=ADVANCED_FACE('',(#94),#236,.T.);
#91=ADVANCED_FACE('',(#95),#237,.T.);
#92=ADVANCED_FACE('',(#96),#238,.T.);
#93=ADVANCED_FACE('',(#97),#239,.T.);
#94=FACE_OUTER_BOUND('',#98,.T.);
#95=FACE_OUTER_BOUND('',#99,.T.);
#96=FACE_OUTER_BOUND('',#100,.T.);
#97=FACE_OUTER_BOUND('',#101,.T.);
#98=EDGE_LOOP('',(#102,#103,#104,#105));
#99=EDGE_LOOP('',(#106,#107,#108,#109));
#100=EDGE_LOOP('',(#110,#111,#112,#113));
#101=EDGE_LOOP('',(#114,#115,#116,#117));
#102=ORIENTED_EDGE('',*,*,#166,.T.);
#103=ORIENTED_EDGE('',*,*,#167,.T.);
#104=ORIENTED_EDGE('',*,*,#168,.T.);
#105=ORIENTED_EDGE('',*,*,#169,.T.);
#106=ORIENTED_EDGE('',*,*,#170,.T.);
#107=ORIENTED_EDGE('',*,*,#171,.T.);
#108=ORIENTED_EDGE('',*,*,#172,.T.);
#109=ORIENTED_EDGE('',*,*,#173,.T.);
#110=ORIENTED_EDGE('',*,*,#174,.T.);
#111=ORIENTED_EDGE('',*,*,#175,.T.);
#112=ORIENTED_EDGE('',*,*,#176,.T.);
#113=ORIENTED_EDGE('',*,*,#177,.T.);
#114=ORIENTED_EDGE('',*,*,#178,.T.);
#115=ORIENTED_EDGE('',*,*,#179,.T.);
#116=ORIENTED_EDGE('',*,*,#180,.T.);
#117=ORIENTED_EDGE('',*,*,#181,.T.);
#118=PCURVE('',#236,#134);
#119=PCURVE('',#236,#135);
#120=PCURVE('',#236,#136);
#121=PCURVE('',#236,#137);
#122=PCURVE('',#237,#138);
#123=PCURVE('',#237,#139);
#124=PCURVE('',#237,#140);
#125=PCURVE('',#237,#141);
#126=PCURVE('',#238,#142);
#127=PCURVE('',#238,#143);
#128=PCURVE('',#238,#144);
#129=PCURVE('',#238,#145);
#130=PCURVE('',#239,#146);
#131=PCURVE('',#239,#147);
#132=PCURVE('',#239,#148);
#133=PCURVE('',#239,#149);
#134=DEFINITIONAL_REPRESENTATION('',(#183),#466);
#135=DEFINITIONAL_REPRESENTATION('',(#185),#466);
#136=DEFINITIONAL_REPRESENTATION('',(#187),#466);
#137=DEFINITIONAL_REPRESENTATION('',(#189),#466);
#138=DEFINITIONAL_REPRESENTATION('',(#191),#466);
#139=DEFINITIONAL_REPRESENTATION('',(#193),#466);
#140=DEFINITIONAL_REPRESENTATION('',(#195),#466);
#141=DEFINITIONAL_REPRESENTATION('',(#197),#466);
#142=DEFINITIONAL_REPRESENTATION('',(#199),#466);
#143=DEFINITIONAL_REPRESENTATION('',(#201),#466);
#144=DEFINITIONAL_REPRESENTATION('',(#203),#466);
#145=DEFINITIONAL_REPRESENTATION('',(#205),#466);
#146=DEFINITIONAL_REPRESENTATION('',(#207),#466);
#147=DEFINITIONAL_REPRESENTATION('',(#209),#466);
#148=DEFINITIONAL_REPRESENTATION('',(#211),#466);
#149=DEFINITIONAL_REPRESENTATION('',(#213),#466);
#150=SURFACE_CURVE('',#182,(#118),.PCURVE_S1.);
#151=SURFACE_CURVE('',#184,(#119),.PCURVE_S1.);
#152=SURFACE_CURVE('',#186,(#120),.PCURVE_S1.);
#153=SURFACE_CURVE('',#188,(#121),.PCURVE_S1.);
#154=SURFACE_CURVE('',#190,(#122),.PCURVE_S1.);
#155=SURFACE_CURVE('',#192,(#123),.PCURVE_S1.);
#156=SURFACE_CURVE('',#194,(#124),.PCURVE_S1.);
#157=SURFACE_CURVE('',#196,(#125),.PCURVE_S1.);
#158=SURFACE_CURVE('',#198,(#126),.PCURVE_S1.);
#159=SURFACE_CURVE('',#200,(#127),.PCURVE_S1.);
#160=SURFACE_CURVE('',#202,(#128),.PCURVE_S1.);
#161=SURFACE_CURVE('',#204,(#129),.PCURVE_S1.);
#162=SURFACE_CURVE('',#206,(#130),.PCURVE_S1.);
#163=SURFACE_CURVE('',#208,(#131),.PCURVE_S1.);
#164=SURFACE_CURVE('',#210,(#132),.PCURVE_S1.);
#165=SURFACE_CURVE('',#212,(#133),.PCURVE_S1.);
#166=EDGE_CURVE('',#220,#221,#150,.T.);
#167=EDGE_CURVE('',#221,#222,#151,.T.);
#168=EDGE_CURVE('',#222,#223,#152,.T.);
#169=EDGE_CURVE('',#223,#220,#153,.T.);
#170=EDGE_CURVE('',#224,#225,#154,.T.);
#171=EDGE_CURVE('',#225,#226,#155,.T.);
#172=EDGE_CURVE('',#226,#227,#156,.T.);
#173=EDGE_CURVE('',#227,#224,#157,.T.);
#174=EDGE_CURVE('',#228,#229,#158,.T.);
#175=EDGE_CURVE('',#229,#230,#159,.T.);
#176=EDGE_CURVE('',#230,#231,#160,.T.);
#177=EDGE_CURVE('',#231,#228,#161,.T.);
#178=EDGE_CURVE('',#232,#233,#162,.T.);
#179=EDGE_CURVE('',#233,#234,#163,.T.);
#180=EDGE_CURVE('',#234,#235,#164,.T.);
#181=EDGE_CURVE('',#235,#232,#165,.T.);
#182=B_SPLINE_CURVE_WITH_KNOTS('',1,(#283,#284),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,4.01188035713928),.UNSPECIFIED.);
#183=B_SPLINE_CURVE_WITH_KNOTS('',1,(#285,#286),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,4.01188035713928),.UNSPECIFIED.);
#184=B_SPLINE_CURVE_WITH_KNOTS('',3,(#287,#288,#289,#290,#291),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,3.51465881598738,6.4250404627203),
 .UNSPECIFIED.);
#185=B_SPLINE_CURVE_WITH_KNOTS('',1,(#292,#293),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,6.4250404627203),.UNSPECIFIED.);
#186=B_SPLINE_CURVE_WITH_KNOTS('',1,(#294,#295),.UNSPECIFIED.,.F.,.F.,(2,
2),(-4.01188035713928,0.),.UNSPECIFIED.);
#187=B_SPLINE_CURVE_WITH_KNOTS('',1,(#296,#297),.UNSPECIFIED.,.F.,.F.,(2,
2),(-4.01188035713928,0.),.UNSPECIFIED.);
#188=B_SPLINE_CURVE_WITH_KNOTS('',3,(#298,#299,#300,#301,#302),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-6.4250404627203,-3.51465881598738,0.),
 .UNSPECIFIED.);
#189=B_SPLINE_CURVE_WITH_KNOTS('',1,(#303,#304),.UNSPECIFIED.,.F.,.F.,(2,
2),(-6.4250404627203,0.),.UNSPECIFIED.);
#190=B_SPLINE_CURVE_WITH_KNOTS('',1,(#323,#324),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,10.4393985806995),.UNSPECIFIED.);
#191=B_SPLINE_CURVE_WITH_KNOTS('',1,(#325,#326),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,10.4393985806995),.UNSPECIFIED.);
#192=B_SPLINE_CURVE_WITH_KNOTS('',3,(#327,#328,#329,#330,#331,#332,#333),
 .UNSPECIFIED.,.F.,.F.,(4,1,1,1,4),(-34.5185246164679,-28.5099736460139,
-21.3814859881661,-9.74845583339916,0.),.UNSPECIFIED.);
#193=B_SPLINE_CURVE_WITH_KNOTS('',1,(#334,#335),.UNSPECIFIED.,.F.,.F.,(2,
2),(-34.5185246164679,0.),.UNSPECIFIED.);
#194=B_SPLINE_CURVE_WITH_KNOTS('',1,(#336,#337),.UNSPECIFIED.,.F.,.F.,(2,
2),(-10.4393985806995,0.),.UNSPECIFIED.);
#195=B_SPLINE_CURVE_WITH_KNOTS('',1,(#338,#339),.UNSPECIFIED.,.F.,.F.,(2,
2),(-10.4393985806995,0.),.UNSPECIFIED.);
#196=B_SPLINE_CURVE_WITH_KNOTS('',3,(#340,#341,#342,#343,#344,#345,#346),
 .UNSPECIFIED.,.F.,.F.,(4,1,1,1,4),(0.,9.74845583339916,21.3814859881661,
28.5099736460139,34.5185246164679),.UNSPECIFIED.);
#197=B_SPLINE_CURVE_WITH_KNOTS('',1,(#347,#348),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,34.5185246164679),.UNSPECIFIED.);
#198=B_SPLINE_CURVE_WITH_KNOTS('',1,(#367,#368),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,6.45149286157901),.UNSPECIFIED.);
#199=B_SPLINE_CURVE_WITH_KNOTS('',1,(#369,#370),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,6.45149286157901),.UNSPECIFIED.);
#200=B_SPLINE_CURVE_WITH_KNOTS('',3,(#371,#372,#373,#374,#375,#376,#377),
 .UNSPECIFIED.,.F.,.F.,(4,1,1,1,4),(-32.0644920415908,-30.5240348658636,
-20.1743991984232,-12.0778348300855,0.),.UNSPECIFIED.);
#201=B_SPLINE_CURVE_WITH_KNOTS('',1,(#378,#379),.UNSPECIFIED.,.F.,.F.,(2,
2),(-32.0644920415908,0.),.UNSPECIFIED.);
#202=B_SPLINE_CURVE_WITH_KNOTS('',1,(#380,#381),.UNSPECIFIED.,.F.,.F.,(2,
2),(-6.45149286157901,0.),.UNSPECIFIED.);
#203=B_SPLINE_CURVE_WITH_KNOTS('',1,(#382,#383),.UNSPECIFIED.,.F.,.F.,(2,
2),(-6.45149286157901,0.),.UNSPECIFIED.);
#204=B_SPLINE_CURVE_WITH_KNOTS('',3,(#384,#385,#386,#387,#388,#389,#390),
 .UNSPECIFIED.,.F.,.F.,(4,1,1,1,4),(0.,12.0778348300855,20.1743991984232,
30.5240348658636,32.0644920415908),.UNSPECIFIED.);
#205=B_SPLINE_CURVE_WITH_KNOTS('',1,(#391,#392),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,32.0644920415908),.UNSPECIFIED.);
#206=B_SPLINE_CURVE_WITH_KNOTS('',1,(#407,#408),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,3.02899786916609),.UNSPECIFIED.);
#207=B_SPLINE_CURVE_WITH_KNOTS('',1,(#409,#410),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,3.02899786916609),.UNSPECIFIED.);
#208=B_SPLINE_CURVE_WITH_KNOTS('',3,(#411,#412,#413,#414,#415),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(-8.5628600707263,-3.36421332586669,0.),
 .UNSPECIFIED.);
#209=B_SPLINE_CURVE_WITH_KNOTS('',1,(#416,#417),.UNSPECIFIED.,.F.,.F.,(2,
2),(-8.5628600707263,0.),.UNSPECIFIED.);
#210=B_SPLINE_CURVE_WITH_KNOTS('',1,(#418,#419),.UNSPECIFIED.,.F.,.F.,(2,
2),(-3.02899786916609,0.),.UNSPECIFIED.);
#211=B_SPLINE_CURVE_WITH_KNOTS('',1,(#420,#421),.UNSPECIFIED.,.F.,.F.,(2,
2),(-3.02899786916609,0.),.UNSPECIFIED.);
#212=B_SPLINE_CURVE_WITH_KNOTS('',3,(#422,#423,#424,#425,#426),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,3.36421332586669,8.5628600707263),
 .UNSPECIFIED.);
#213=B_SPLINE_CURVE_WITH_KNOTS('',1,(#427,#428),.UNSPECIFIED.,.F.,.F.,(2,
2),(0.,8.5628600707263),.UNSPECIFIED.);
#214=B_SPLINE_CURVE_WITH_KNOTS('3d BsCrv',3,(#429,#430,#431,#432,#433,#434),
 .UNSPECIFIED.,.F.,.F.,(4,1,1,4),(0.,6.69683613629596,12.5300430152841,
16.4856623501837),.UNSPECIFIED.);
#215=B_SPLINE_CURVE_WITH_KNOTS('3d BsCrv',3,(#435,#436,#437,#438,#439),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,6.99986449329835,8.85919276450691),
 .UNSPECIFIED.);
#216=B_SPLINE_CURVE_WITH_KNOTS('3d BsCrv',3,(#440,#441,#442,#443,#444),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,3.36421332586669,8.5628600707263),
 .UNSPECIFIED.);
#217=B_SPLINE_CURVE_WITH_KNOTS('3d BsCrv',3,(#445,#446,#447,#448,#449),
 .UNSPECIFIED.,.F.,.F.,(4,1,4),(0.,3.51465881598738,6.4250404627203),
 .UNSPECIFIED.);
#218=B_SPLINE_CURVE_WITH_KNOTS('3d BsCrv',3,(#450,#451,#452,#453,#454,#455,
#456),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,4),(0.,12.0778348300855,20.1743991984232,
30.5240348658636,32.0644920415908),.UNSPECIFIED.);
#219=B_SPLINE_CURVE_WITH_KNOTS('3d BsCrv',3,(#457,#458,#459,#460,#461,#462,
#463),.UNSPECIFIED.,.F.,.F.,(4,1,1,1,4),(0.,9.74845583339916,21.3814859881661,
28.5099736460139,34.5185246164679),.UNSPECIFIED.);
#220=VERTEX_POINT('',#279);
#221=VERTEX_POINT('',#280);
#222=VERTEX_POINT('',#281);
#223=VERTEX_POINT('',#282);
#224=VERTEX_POINT('',#319);
#225=VERTEX_POINT('',#320);
#226=VERTEX_POINT('',#321);
#227=VERTEX_POINT('',#322);
#228=VERTEX_POINT('',#363);
#229=VERTEX_POINT('',#364);
#230=VERTEX_POINT('',#365);
#231=VERTEX_POINT('',#366);
#232=VERTEX_POINT('',#403);
#233=VERTEX_POINT('',#404);
#234=VERTEX_POINT('',#405);
#235=VERTEX_POINT('',#406);
#236=B_SPLINE_SURFACE_WITH_KNOTS('',1,3,((#269,#270,#271,#272,#273),(#274,
#275,#276,#277,#278)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(4,1,4),(0.,4.01188035713928),
(0.,3.51465881598738,6.4250404627203),.UNSPECIFIED.);
#237=B_SPLINE_SURFACE_WITH_KNOTS('',1,3,((#305,#306,#307,#308,#309,#310,
#311),(#312,#313,#314,#315,#316,#317,#318)),.UNSPECIFIED.,.F.,.F.,.F.,(2,
2),(4,1,1,1,4),(0.,10.4393985806995),(-34.5185246164679,-28.5099736460139,
-21.3814859881661,-9.74845583339916,0.),.UNSPECIFIED.);
#238=B_SPLINE_SURFACE_WITH_KNOTS('',1,3,((#349,#350,#351,#352,#353,#354,
#355),(#356,#357,#358,#359,#360,#361,#362)),.UNSPECIFIED.,.F.,.F.,.F.,(2,
2),(4,1,1,1,4),(0.,6.45149286157901),(-32.0644920415908,-30.5240348658636,
-20.1743991984232,-12.0778348300855,0.),.UNSPECIFIED.);
#239=B_SPLINE_SURFACE_WITH_KNOTS('',1,3,((#393,#394,#395,#396,#397),(#398,
#399,#400,#401,#402)),.UNSPECIFIED.,.F.,.F.,.F.,(2,2),(4,1,4),(0.,3.02899786916609),
(-8.5628600707263,-3.36421332586669,0.),.UNSPECIFIED.);
#240=SHAPE_DEFINITION_REPRESENTATION(#241,#258);
#241=PRODUCT_DEFINITION_SHAPE('Document','',#243);
#242=PRODUCT_DEFINITION_CONTEXT('3D Mechanical Parts',#247,'design');
#243=PRODUCT_DEFINITION('A','First version',#244,#242);
#244=PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('A',
'First version',#249,.MADE.);
#245=PRODUCT_RELATED_PRODUCT_CATEGORY('tool','tool',(#249));
#246=APPLICATION_PROTOCOL_DEFINITION('Draft International Standard',
'automotive_design',1999,#247);
#247=APPLICATION_CONTEXT(
'data for automotive mechanical design processes');
#248=PRODUCT_CONTEXT('3D Mechanical Parts',#247,'mechanical');
#249=PRODUCT('Document','Document','Rhino converted to STEP',(#248));
#250=(
LENGTH_UNIT()
NAMED_UNIT(*)
SI_UNIT(.MILLI.,.METRE.)
);
#251=(
NAMED_UNIT(*)
PLANE_ANGLE_UNIT()
SI_UNIT($,.RADIAN.)
);
#252=DIMENSIONAL_EXPONENTS(0.,0.,0.,0.,0.,0.,0.);
#253=PLANE_ANGLE_MEASURE_WITH_UNIT(PLANE_ANGLE_MEASURE(0.01745329252),#251);
#254=(
CONVERSION_BASED_UNIT('DEGREES',#253)
NAMED_UNIT(#252)
PLANE_ANGLE_UNIT()
);
#255=(
NAMED_UNIT(*)
SI_UNIT($,.STERADIAN.)
SOLID_ANGLE_UNIT()
);
#256=UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(0.001),#250,
'DISTANCE_ACCURACY_VALUE',
'Maximum model space distance between geometric entities at asserted c
onnectivities');
#257=(
GEOMETRIC_REPRESENTATION_CONTEXT(3)
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#256))
GLOBAL_UNIT_ASSIGNED_CONTEXT((#255,#254,#250))
REPRESENTATION_CONTEXT('ID1','3D')
);
#258=SHAPE_REPRESENTATION('Document',(#259,#260,#261),#257);
#259=AXIS2_PLACEMENT_3D('',#268,#262,#263);
#260=AXIS2_PLACEMENT_3D('',#464,#264,#265);
#261=AXIS2_PLACEMENT_3D('',#465,#266,#267);
#262=DIRECTION('',(0.,0.,1.));
#263=DIRECTION('',(1.,0.,0.));
#264=DIRECTION('',(0.,0.,1.));
#265=DIRECTION('',(1.,0.,0.));
#266=DIRECTION('',(0.,0.,1.));
#267=DIRECTION('',(1.,0.,0.));
#268=CARTESIAN_POINT('',(0.,0.,0.));
#269=CARTESIAN_POINT('',(-11.5505976355345,3.02023905421381,0.));
#270=CARTESIAN_POINT('',(-11.9665215938126,2.03452597491268,0.));
#271=CARTESIAN_POINT('',(-12.3033866175916,0.00916111538683565,0.));
#272=CARTESIAN_POINT('',(-11.3752819594064,-1.81722642336829,0.));
#273=CARTESIAN_POINT('',(-10.8198946385473,-2.533103722889,0.));
#274=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#275=CARTESIAN_POINT('',(-7.96652159381264,2.23352134260716,0.));
#276=CARTESIAN_POINT('',(-8.30338661759158,-0.0168840568660054,0.));
#277=CARTESIAN_POINT('',(-7.37528195940638,-2.0462035443717,0.));
#278=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#279=CARTESIAN_POINT('',(-11.5505976355345,3.02023905421381,0.));
#280=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#281=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#282=CARTESIAN_POINT('',(-10.8198946385473,-2.533103722889,0.));
#283=CARTESIAN_POINT('',(-11.5505976355345,3.02023905421381,0.));
#284=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#285=CARTESIAN_POINT('',(0.,0.));
#286=CARTESIAN_POINT('',(4.01188035713928,0.));
#287=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#288=CARTESIAN_POINT('',(-7.96652159381264,2.23352134260716,0.));
#289=CARTESIAN_POINT('',(-8.30338661759158,-0.0168840568660054,0.));
#290=CARTESIAN_POINT('',(-7.37528195940638,-2.0462035443717,0.));
#291=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#292=CARTESIAN_POINT('',(4.01188035713928,0.));
#293=CARTESIAN_POINT('',(4.01188035713928,6.4250404627203));
#294=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#295=CARTESIAN_POINT('',(-10.8198946385473,-2.533103722889,0.));
#296=CARTESIAN_POINT('',(4.01188035713928,6.4250404627203));
#297=CARTESIAN_POINT('',(0.,6.4250404627203));
#298=CARTESIAN_POINT('',(-10.8198946385473,-2.533103722889,0.));
#299=CARTESIAN_POINT('',(-11.3752819594064,-1.81722642336829,0.));
#300=CARTESIAN_POINT('',(-12.3033866175916,0.00916111538683565,0.));
#301=CARTESIAN_POINT('',(-11.9665215938126,2.03452597491268,0.));
#302=CARTESIAN_POINT('',(-11.5505976355345,3.02023905421381,0.));
#303=CARTESIAN_POINT('',(0.,6.4250404627203));
#304=CARTESIAN_POINT('',(0.,0.));
#305=CARTESIAN_POINT('',(21.2715761345166,3.56832740187496,0.));
#306=CARTESIAN_POINT('',(20.3978756391004,3.32053926557098,0.));
#307=CARTESIAN_POINT('',(18.4172484374869,5.55690032851121,0.));
#308=CARTESIAN_POINT('',(13.0325216360608,12.9041138426155,0.));
#309=CARTESIAN_POINT('',(1.46979686458558,10.2769613006003,0.));
#310=CARTESIAN_POINT('',(-4.56014000187896,9.38903694204547,0.));
#311=CARTESIAN_POINT('',(-7.55059763553453,8.24482658259311,0.));
#312=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#313=CARTESIAN_POINT('',(19.3613256907258,-2.46930946093757,0.));
#314=CARTESIAN_POINT('',(13.1960277309483,-3.48300548185671,0.));
#315=CARTESIAN_POINT('',(15.4693959036192,9.12094307461637,0.));
#316=CARTESIAN_POINT('',(1.46979686458558,5.58668556183866,0.));
#317=CARTESIAN_POINT('',(-4.56014000187896,4.60010294122214,0.));
#318=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#319=CARTESIAN_POINT('',(21.2715761345166,3.56832740187496,0.));
#320=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#321=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#322=CARTESIAN_POINT('',(-7.55059763553453,8.24482658259311,0.));
#323=CARTESIAN_POINT('',(21.2715761345166,3.56832740187496,0.));
#324=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#325=CARTESIAN_POINT('',(0.,-34.5185246164679));
#326=CARTESIAN_POINT('',(10.4393985806995,-34.5185246164679));
#327=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#328=CARTESIAN_POINT('',(19.3613256907258,-2.46930946093757,0.));
#329=CARTESIAN_POINT('',(13.1960277309483,-3.48300548185671,0.));
#330=CARTESIAN_POINT('',(15.4693959036192,9.12094307461637,0.));
#331=CARTESIAN_POINT('',(1.46979686458558,5.58668556183866,0.));
#332=CARTESIAN_POINT('',(-4.56014000187896,4.60010294122214,0.));
#333=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#334=CARTESIAN_POINT('',(10.4393985806995,-34.5185246164679));
#335=CARTESIAN_POINT('',(10.4393985806995,0.));
#336=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#337=CARTESIAN_POINT('',(-7.55059763553453,8.24482658259311,0.));
#338=CARTESIAN_POINT('',(10.4393985806995,0.));
#339=CARTESIAN_POINT('',(0.,0.));
#340=CARTESIAN_POINT('',(-7.55059763553453,8.24482658259311,0.));
#341=CARTESIAN_POINT('',(-4.56014000187896,9.38903694204547,0.));
#342=CARTESIAN_POINT('',(1.46979686458558,10.2769613006003,0.));
#343=CARTESIAN_POINT('',(13.0325216360608,12.9041138426155,0.));
#344=CARTESIAN_POINT('',(18.4172484374869,5.55690032851121,0.));
#345=CARTESIAN_POINT('',(20.3978756391004,3.32053926557098,0.));
#346=CARTESIAN_POINT('',(21.2715761345166,3.56832740187496,0.));
#347=CARTESIAN_POINT('',(0.,0.));
#348=CARTESIAN_POINT('',(0.,-34.5185246164679));
#349=CARTESIAN_POINT('',(22.2458467971662,-15.942512900224,0.));
#350=CARTESIAN_POINT('',(21.7699233043508,-15.7690102218546,0.));
#351=CARTESIAN_POINT('',(18.0184859736771,-14.4302164570008,0.));
#352=CARTESIAN_POINT('',(10.368095369636,-20.3899875248848,0.));
#353=CARTESIAN_POINT('',(4.62472217259556,-9.8799011255195,0.));
#354=CARTESIAN_POINT('',(-2.79509781405342,-9.37964307360312,0.));
#355=CARTESIAN_POINT('',(-6.81989463854732,-9.29311562764039,0.));
#356=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#357=CARTESIAN_POINT('',(21.7699233043508,-10.0370612040771,0.));
#358=CARTESIAN_POINT('',(18.0184859736771,-8.54951257646188,0.));
#359=CARTESIAN_POINT('',(10.368095369636,-15.1714804296663,0.));
#360=CARTESIAN_POINT('',(4.62472217259556,-3.49360665259373,0.));
#361=CARTESIAN_POINT('',(-2.79509781405342,-2.93776437268664,0.));
#362=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#363=CARTESIAN_POINT('',(22.2458467971662,-15.942512900224,0.));
#364=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#365=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#366=CARTESIAN_POINT('',(-6.81989463854732,-9.29311562764039,0.));
#367=CARTESIAN_POINT('',(22.2458467971662,-15.942512900224,0.));
#368=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#369=CARTESIAN_POINT('',(0.,-32.0644920415908));
#370=CARTESIAN_POINT('',(6.45149286157901,-32.0644920415908));
#371=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#372=CARTESIAN_POINT('',(21.7699233043508,-10.0370612040771,0.));
#373=CARTESIAN_POINT('',(18.0184859736771,-8.54951257646188,0.));
#374=CARTESIAN_POINT('',(10.368095369636,-15.1714804296663,0.));
#375=CARTESIAN_POINT('',(4.62472217259556,-3.49360665259373,0.));
#376=CARTESIAN_POINT('',(-2.79509781405342,-2.93776437268664,0.));
#377=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#378=CARTESIAN_POINT('',(6.45149286157901,-32.0644920415908));
#379=CARTESIAN_POINT('',(6.45149286157901,0.));
#380=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#381=CARTESIAN_POINT('',(-6.81989463854732,-9.29311562764039,0.));
#382=CARTESIAN_POINT('',(6.45149286157901,0.));
#383=CARTESIAN_POINT('',(0.,0.));
#384=CARTESIAN_POINT('',(-6.81989463854732,-9.29311562764039,0.));
#385=CARTESIAN_POINT('',(-2.79509781405342,-9.37964307360312,0.));
#386=CARTESIAN_POINT('',(4.62472217259556,-9.8799011255195,0.));
#387=CARTESIAN_POINT('',(10.368095369636,-20.3899875248848,0.));
#388=CARTESIAN_POINT('',(18.0184859736771,-14.4302164570008,0.));
#389=CARTESIAN_POINT('',(21.7699233043508,-15.7690102218546,0.));
#390=CARTESIAN_POINT('',(22.2458467971662,-15.942512900224,0.));
#391=CARTESIAN_POINT('',(0.,0.));
#392=CARTESIAN_POINT('',(0.,-32.0644920415908));
#393=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#394=CARTESIAN_POINT('',(22.660915266739,-8.54740353339258,1.29113577526191));
#395=CARTESIAN_POINT('',(22.8014214254268,-5.57641846128921,-2.59014246113745));
#396=CARTESIAN_POINT('',(21.7935035196958,-2.85989415644413,0.));
#397=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#398=CARTESIAN_POINT('',(25.2458467971662,-9.81171746510051,0.));
#399=CARTESIAN_POINT('',(25.660915266739,-8.29752288311496,1.29113577526191));
#400=CARTESIAN_POINT('',(25.8014214254268,-5.62363631822192,-2.59014246113745));
#401=CARTESIAN_POINT('',(24.7935035196958,-3.17876444386135,0.));
#402=CARTESIAN_POINT('',(24.2715761345166,-2.28547659613222,0.));
#403=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#404=CARTESIAN_POINT('',(25.2458467971662,-9.81171746510051,0.));
#405=CARTESIAN_POINT('',(24.2715761345166,-2.28547659613222,0.));
#406=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#407=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#408=CARTESIAN_POINT('',(25.2458467971662,-9.81171746510051,0.));
#409=CARTESIAN_POINT('',(0.,-8.5628600707263));
#410=CARTESIAN_POINT('',(3.02899786916609,-8.5628600707263));
#411=CARTESIAN_POINT('',(25.2458467971662,-9.81171746510051,0.));
#412=CARTESIAN_POINT('',(25.660915266739,-8.29752288311496,1.29113577526191));
#413=CARTESIAN_POINT('',(25.8014214254268,-5.62363631822192,-2.59014246113745));
#414=CARTESIAN_POINT('',(24.7935035196958,-3.17876444386135,0.));
#415=CARTESIAN_POINT('',(24.2715761345166,-2.28547659613222,0.));
#416=CARTESIAN_POINT('',(3.02899786916609,-8.5628600707263));
#417=CARTESIAN_POINT('',(3.02899786916609,0.));
#418=CARTESIAN_POINT('',(24.2715761345166,-2.28547659613222,0.));
#419=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#420=CARTESIAN_POINT('',(3.02899786916609,0.));
#421=CARTESIAN_POINT('',(0.,0.));
#422=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#423=CARTESIAN_POINT('',(21.7935035196958,-2.85989415644413,0.));
#424=CARTESIAN_POINT('',(22.8014214254268,-5.57641846128921,-2.59014246113745));
#425=CARTESIAN_POINT('',(22.660915266739,-8.54740353339258,1.29113577526191));
#426=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#427=CARTESIAN_POINT('',(0.,0.));
#428=CARTESIAN_POINT('',(0.,-8.5628600707263));
#429=CARTESIAN_POINT('',(12.0510433556488,6.51116885597537,0.));
#430=CARTESIAN_POINT('',(10.0163694995085,5.59292567052402,0.));
#431=CARTESIAN_POINT('',(5.19789433585724,2.56274052590848,0.));
#432=CARTESIAN_POINT('',(9.72859134296392,-3.66499892700204,-3.84038624405277));
#433=CARTESIAN_POINT('',(6.43271484572794,-6.01903711646199,0.));
#434=CARTESIAN_POINT('',(5.27051614267851,-6.64180619211469,0.));
#435=CARTESIAN_POINT('',(16.864887796244,-2.43806166412921,0.));
#436=CARTESIAN_POINT('',(15.3329386679503,-4.19799505110792,5.12074987100736));
#437=CARTESIAN_POINT('',(13.9016373674019,-7.50582374999729,5.12074987100736));
#438=CARTESIAN_POINT('',(15.0718602833927,-10.2818625416299,0.));
#439=CARTESIAN_POINT('',(15.3664860767554,-10.8271313231683,0.));
#440=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#441=CARTESIAN_POINT('',(21.7935035196958,-2.85989415644413,0.));
#442=CARTESIAN_POINT('',(22.8014214254268,-5.57641846128921,-2.59014246113745));
#443=CARTESIAN_POINT('',(22.660915266739,-8.54740353339258,1.29113577526191));
#444=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#445=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#446=CARTESIAN_POINT('',(-7.96652159381264,2.23352134260716,0.));
#447=CARTESIAN_POINT('',(-8.30338661759158,-0.0168840568660054,0.));
#448=CARTESIAN_POINT('',(-7.37528195940638,-2.0462035443717,0.));
#449=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#450=CARTESIAN_POINT('',(-6.81989463854732,-2.84162276606138,0.));
#451=CARTESIAN_POINT('',(-2.79509781405342,-2.93776437268664,0.));
#452=CARTESIAN_POINT('',(4.62472217259556,-3.49360665259373,0.));
#453=CARTESIAN_POINT('',(10.368095369636,-15.1714804296663,0.));
#454=CARTESIAN_POINT('',(18.0184859736771,-8.54951257646188,0.));
#455=CARTESIAN_POINT('',(21.7699233043508,-10.0370612040771,0.));
#456=CARTESIAN_POINT('',(22.2458467971662,-10.229841957821,0.));
#457=CARTESIAN_POINT('',(-7.55059763553453,3.32875809738619,0.));
#458=CARTESIAN_POINT('',(-4.56014000187896,4.60010294122214,0.));
#459=CARTESIAN_POINT('',(1.46979686458558,5.58668556183866,0.));
#460=CARTESIAN_POINT('',(15.4693959036192,9.12094307461637,0.));
#461=CARTESIAN_POINT('',(13.1960277309483,-3.48300548185671,0.));
#462=CARTESIAN_POINT('',(19.3613256907258,-2.46930946093757,0.));
#463=CARTESIAN_POINT('',(21.2715761345166,-1.86735210341176,0.));
#464=CARTESIAN_POINT('',(0.,0.,0.));
#465=CARTESIAN_POINT('',(0.,0.,0.));
#466=(
GEOMETRIC_REPRESENTATION_CONTEXT(2)
PARAMETRIC_REPRESENTATION_CONTEXT()
REPRESENTATION_CONTEXT('pspace','')
);
ENDSEC;
END-ISO-10303-21;
