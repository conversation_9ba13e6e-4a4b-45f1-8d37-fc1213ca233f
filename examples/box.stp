ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('Open CASCADE Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2024-03-17T01:34:49',('Author'),(
    'Open CASCADE'),'Open CASCADE STEP processor 7.7','Open CASCADE 7.7'
  ,'Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'config_control_design',1994,#2);
#2 = APPLICATION_CONTEXT(
'configuration controlled 3D designs of mechanical parts and assemblies'
  );
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION_WITH_SPECIFIED_SOURCE('','',#7,
  .NOT_KNOWN.);
#7 = PRODUCT('Open CASCADE STEP translator 7.7 1',
  'Open CASCADE STEP translator 7.7 1','',(#8));
#8 = MECHANICAL_CONTEXT('',#2,'mechanical');
#9 = DESIGN_CONTEXT('',#2,'design');
#10 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#15),#345);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = MANIFOLD_SOLID_BREP('',#16);
#16 = CLOSED_SHELL('',(#17,#137,#237,#284,#331,#338));
#17 = ADVANCED_FACE('',(#18),#32,.F.);
#18 = FACE_BOUND('',#19,.F.);
#19 = EDGE_LOOP('',(#20,#55,#83,#111));
#20 = ORIENTED_EDGE('',*,*,#21,.F.);
#21 = EDGE_CURVE('',#22,#24,#26,.T.);
#22 = VERTEX_POINT('',#23);
#23 = CARTESIAN_POINT('',(0.,0.,0.));
#24 = VERTEX_POINT('',#25);
#25 = CARTESIAN_POINT('',(0.,0.,30.));
#26 = SURFACE_CURVE('',#27,(#31,#43),.PCURVE_S1.);
#27 = LINE('',#28,#29);
#28 = CARTESIAN_POINT('',(0.,0.,0.));
#29 = VECTOR('',#30,1.);
#30 = DIRECTION('',(0.,0.,1.));
#31 = PCURVE('',#32,#37);
#32 = PLANE('',#33);
#33 = AXIS2_PLACEMENT_3D('',#34,#35,#36);
#34 = CARTESIAN_POINT('',(0.,0.,0.));
#35 = DIRECTION('',(1.,0.,-0.));
#36 = DIRECTION('',(0.,0.,1.));
#37 = DEFINITIONAL_REPRESENTATION('',(#38),#42);
#38 = LINE('',#39,#40);
#39 = CARTESIAN_POINT('',(0.,0.));
#40 = VECTOR('',#41,1.);
#41 = DIRECTION('',(1.,0.));
#42 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#43 = PCURVE('',#44,#49);
#44 = PLANE('',#45);
#45 = AXIS2_PLACEMENT_3D('',#46,#47,#48);
#46 = CARTESIAN_POINT('',(0.,0.,0.));
#47 = DIRECTION('',(-0.,1.,0.));
#48 = DIRECTION('',(0.,0.,1.));
#49 = DEFINITIONAL_REPRESENTATION('',(#50),#54);
#50 = LINE('',#51,#52);
#51 = CARTESIAN_POINT('',(0.,0.));
#52 = VECTOR('',#53,1.);
#53 = DIRECTION('',(1.,0.));
#54 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#55 = ORIENTED_EDGE('',*,*,#56,.T.);
#56 = EDGE_CURVE('',#22,#57,#59,.T.);
#57 = VERTEX_POINT('',#58);
#58 = CARTESIAN_POINT('',(0.,20.,0.));
#59 = SURFACE_CURVE('',#60,(#64,#71),.PCURVE_S1.);
#60 = LINE('',#61,#62);
#61 = CARTESIAN_POINT('',(0.,0.,0.));
#62 = VECTOR('',#63,1.);
#63 = DIRECTION('',(-0.,1.,0.));
#64 = PCURVE('',#32,#65);
#65 = DEFINITIONAL_REPRESENTATION('',(#66),#70);
#66 = LINE('',#67,#68);
#67 = CARTESIAN_POINT('',(0.,0.));
#68 = VECTOR('',#69,1.);
#69 = DIRECTION('',(0.,-1.));
#70 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#71 = PCURVE('',#72,#77);
#72 = PLANE('',#73);
#73 = AXIS2_PLACEMENT_3D('',#74,#75,#76);
#74 = CARTESIAN_POINT('',(0.,0.,0.));
#75 = DIRECTION('',(0.,0.,1.));
#76 = DIRECTION('',(1.,0.,-0.));
#77 = DEFINITIONAL_REPRESENTATION('',(#78),#82);
#78 = LINE('',#79,#80);
#79 = CARTESIAN_POINT('',(0.,0.));
#80 = VECTOR('',#81,1.);
#81 = DIRECTION('',(0.,1.));
#82 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#83 = ORIENTED_EDGE('',*,*,#84,.T.);
#84 = EDGE_CURVE('',#57,#85,#87,.T.);
#85 = VERTEX_POINT('',#86);
#86 = CARTESIAN_POINT('',(0.,20.,30.));
#87 = SURFACE_CURVE('',#88,(#92,#99),.PCURVE_S1.);
#88 = LINE('',#89,#90);
#89 = CARTESIAN_POINT('',(0.,20.,0.));
#90 = VECTOR('',#91,1.);
#91 = DIRECTION('',(0.,0.,1.));
#92 = PCURVE('',#32,#93);
#93 = DEFINITIONAL_REPRESENTATION('',(#94),#98);
#94 = LINE('',#95,#96);
#95 = CARTESIAN_POINT('',(0.,-20.));
#96 = VECTOR('',#97,1.);
#97 = DIRECTION('',(1.,0.));
#98 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#99 = PCURVE('',#100,#105);
#100 = PLANE('',#101);
#101 = AXIS2_PLACEMENT_3D('',#102,#103,#104);
#102 = CARTESIAN_POINT('',(0.,20.,0.));
#103 = DIRECTION('',(-0.,1.,0.));
#104 = DIRECTION('',(0.,0.,1.));
#105 = DEFINITIONAL_REPRESENTATION('',(#106),#110);
#106 = LINE('',#107,#108);
#107 = CARTESIAN_POINT('',(0.,0.));
#108 = VECTOR('',#109,1.);
#109 = DIRECTION('',(1.,0.));
#110 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#111 = ORIENTED_EDGE('',*,*,#112,.F.);
#112 = EDGE_CURVE('',#24,#85,#113,.T.);
#113 = SURFACE_CURVE('',#114,(#118,#125),.PCURVE_S1.);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(0.,0.,30.));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(-0.,1.,0.));
#118 = PCURVE('',#32,#119);
#119 = DEFINITIONAL_REPRESENTATION('',(#120),#124);
#120 = LINE('',#121,#122);
#121 = CARTESIAN_POINT('',(30.,0.));
#122 = VECTOR('',#123,1.);
#123 = DIRECTION('',(0.,-1.));
#124 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#125 = PCURVE('',#126,#131);
#126 = PLANE('',#127);
#127 = AXIS2_PLACEMENT_3D('',#128,#129,#130);
#128 = CARTESIAN_POINT('',(0.,0.,30.));
#129 = DIRECTION('',(0.,0.,1.));
#130 = DIRECTION('',(1.,0.,-0.));
#131 = DEFINITIONAL_REPRESENTATION('',(#132),#136);
#132 = LINE('',#133,#134);
#133 = CARTESIAN_POINT('',(0.,0.));
#134 = VECTOR('',#135,1.);
#135 = DIRECTION('',(0.,1.));
#136 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#137 = ADVANCED_FACE('',(#138),#152,.T.);
#138 = FACE_BOUND('',#139,.T.);
#139 = EDGE_LOOP('',(#140,#170,#193,#216));
#140 = ORIENTED_EDGE('',*,*,#141,.F.);
#141 = EDGE_CURVE('',#142,#144,#146,.T.);
#142 = VERTEX_POINT('',#143);
#143 = CARTESIAN_POINT('',(10.,0.,0.));
#144 = VERTEX_POINT('',#145);
#145 = CARTESIAN_POINT('',(10.,0.,30.));
#146 = SURFACE_CURVE('',#147,(#151,#163),.PCURVE_S1.);
#147 = LINE('',#148,#149);
#148 = CARTESIAN_POINT('',(10.,0.,0.));
#149 = VECTOR('',#150,1.);
#150 = DIRECTION('',(0.,0.,1.));
#151 = PCURVE('',#152,#157);
#152 = PLANE('',#153);
#153 = AXIS2_PLACEMENT_3D('',#154,#155,#156);
#154 = CARTESIAN_POINT('',(10.,0.,0.));
#155 = DIRECTION('',(1.,0.,-0.));
#156 = DIRECTION('',(0.,0.,1.));
#157 = DEFINITIONAL_REPRESENTATION('',(#158),#162);
#158 = LINE('',#159,#160);
#159 = CARTESIAN_POINT('',(0.,0.));
#160 = VECTOR('',#161,1.);
#161 = DIRECTION('',(1.,0.));
#162 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#163 = PCURVE('',#44,#164);
#164 = DEFINITIONAL_REPRESENTATION('',(#165),#169);
#165 = LINE('',#166,#167);
#166 = CARTESIAN_POINT('',(0.,10.));
#167 = VECTOR('',#168,1.);
#168 = DIRECTION('',(1.,0.));
#169 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#170 = ORIENTED_EDGE('',*,*,#171,.T.);
#171 = EDGE_CURVE('',#142,#172,#174,.T.);
#172 = VERTEX_POINT('',#173);
#173 = CARTESIAN_POINT('',(10.,20.,0.));
#174 = SURFACE_CURVE('',#175,(#179,#186),.PCURVE_S1.);
#175 = LINE('',#176,#177);
#176 = CARTESIAN_POINT('',(10.,0.,0.));
#177 = VECTOR('',#178,1.);
#178 = DIRECTION('',(-0.,1.,0.));
#179 = PCURVE('',#152,#180);
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(0.,0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,-1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#72,#187);
#187 = DEFINITIONAL_REPRESENTATION('',(#188),#192);
#188 = LINE('',#189,#190);
#189 = CARTESIAN_POINT('',(10.,0.));
#190 = VECTOR('',#191,1.);
#191 = DIRECTION('',(0.,1.));
#192 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#193 = ORIENTED_EDGE('',*,*,#194,.T.);
#194 = EDGE_CURVE('',#172,#195,#197,.T.);
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(10.,20.,30.));
#197 = SURFACE_CURVE('',#198,(#202,#209),.PCURVE_S1.);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(10.,20.,0.));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.,0.,1.));
#202 = PCURVE('',#152,#203);
#203 = DEFINITIONAL_REPRESENTATION('',(#204),#208);
#204 = LINE('',#205,#206);
#205 = CARTESIAN_POINT('',(0.,-20.));
#206 = VECTOR('',#207,1.);
#207 = DIRECTION('',(1.,0.));
#208 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#209 = PCURVE('',#100,#210);
#210 = DEFINITIONAL_REPRESENTATION('',(#211),#215);
#211 = LINE('',#212,#213);
#212 = CARTESIAN_POINT('',(0.,10.));
#213 = VECTOR('',#214,1.);
#214 = DIRECTION('',(1.,0.));
#215 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#216 = ORIENTED_EDGE('',*,*,#217,.F.);
#217 = EDGE_CURVE('',#144,#195,#218,.T.);
#218 = SURFACE_CURVE('',#219,(#223,#230),.PCURVE_S1.);
#219 = LINE('',#220,#221);
#220 = CARTESIAN_POINT('',(10.,0.,30.));
#221 = VECTOR('',#222,1.);
#222 = DIRECTION('',(-0.,1.,0.));
#223 = PCURVE('',#152,#224);
#224 = DEFINITIONAL_REPRESENTATION('',(#225),#229);
#225 = LINE('',#226,#227);
#226 = CARTESIAN_POINT('',(30.,0.));
#227 = VECTOR('',#228,1.);
#228 = DIRECTION('',(0.,-1.));
#229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#230 = PCURVE('',#126,#231);
#231 = DEFINITIONAL_REPRESENTATION('',(#232),#236);
#232 = LINE('',#233,#234);
#233 = CARTESIAN_POINT('',(10.,0.));
#234 = VECTOR('',#235,1.);
#235 = DIRECTION('',(0.,1.));
#236 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#237 = ADVANCED_FACE('',(#238),#44,.F.);
#238 = FACE_BOUND('',#239,.F.);
#239 = EDGE_LOOP('',(#240,#261,#262,#283));
#240 = ORIENTED_EDGE('',*,*,#241,.F.);
#241 = EDGE_CURVE('',#22,#142,#242,.T.);
#242 = SURFACE_CURVE('',#243,(#247,#254),.PCURVE_S1.);
#243 = LINE('',#244,#245);
#244 = CARTESIAN_POINT('',(0.,0.,0.));
#245 = VECTOR('',#246,1.);
#246 = DIRECTION('',(1.,0.,-0.));
#247 = PCURVE('',#44,#248);
#248 = DEFINITIONAL_REPRESENTATION('',(#249),#253);
#249 = LINE('',#250,#251);
#250 = CARTESIAN_POINT('',(0.,0.));
#251 = VECTOR('',#252,1.);
#252 = DIRECTION('',(0.,1.));
#253 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#254 = PCURVE('',#72,#255);
#255 = DEFINITIONAL_REPRESENTATION('',(#256),#260);
#256 = LINE('',#257,#258);
#257 = CARTESIAN_POINT('',(0.,0.));
#258 = VECTOR('',#259,1.);
#259 = DIRECTION('',(1.,0.));
#260 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#261 = ORIENTED_EDGE('',*,*,#21,.T.);
#262 = ORIENTED_EDGE('',*,*,#263,.T.);
#263 = EDGE_CURVE('',#24,#144,#264,.T.);
#264 = SURFACE_CURVE('',#265,(#269,#276),.PCURVE_S1.);
#265 = LINE('',#266,#267);
#266 = CARTESIAN_POINT('',(0.,0.,30.));
#267 = VECTOR('',#268,1.);
#268 = DIRECTION('',(1.,0.,-0.));
#269 = PCURVE('',#44,#270);
#270 = DEFINITIONAL_REPRESENTATION('',(#271),#275);
#271 = LINE('',#272,#273);
#272 = CARTESIAN_POINT('',(30.,0.));
#273 = VECTOR('',#274,1.);
#274 = DIRECTION('',(0.,1.));
#275 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#276 = PCURVE('',#126,#277);
#277 = DEFINITIONAL_REPRESENTATION('',(#278),#282);
#278 = LINE('',#279,#280);
#279 = CARTESIAN_POINT('',(0.,0.));
#280 = VECTOR('',#281,1.);
#281 = DIRECTION('',(1.,0.));
#282 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#283 = ORIENTED_EDGE('',*,*,#141,.F.);
#284 = ADVANCED_FACE('',(#285),#100,.T.);
#285 = FACE_BOUND('',#286,.T.);
#286 = EDGE_LOOP('',(#287,#308,#309,#330));
#287 = ORIENTED_EDGE('',*,*,#288,.F.);
#288 = EDGE_CURVE('',#57,#172,#289,.T.);
#289 = SURFACE_CURVE('',#290,(#294,#301),.PCURVE_S1.);
#290 = LINE('',#291,#292);
#291 = CARTESIAN_POINT('',(0.,20.,0.));
#292 = VECTOR('',#293,1.);
#293 = DIRECTION('',(1.,0.,-0.));
#294 = PCURVE('',#100,#295);
#295 = DEFINITIONAL_REPRESENTATION('',(#296),#300);
#296 = LINE('',#297,#298);
#297 = CARTESIAN_POINT('',(0.,0.));
#298 = VECTOR('',#299,1.);
#299 = DIRECTION('',(0.,1.));
#300 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#301 = PCURVE('',#72,#302);
#302 = DEFINITIONAL_REPRESENTATION('',(#303),#307);
#303 = LINE('',#304,#305);
#304 = CARTESIAN_POINT('',(0.,20.));
#305 = VECTOR('',#306,1.);
#306 = DIRECTION('',(1.,0.));
#307 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#308 = ORIENTED_EDGE('',*,*,#84,.T.);
#309 = ORIENTED_EDGE('',*,*,#310,.T.);
#310 = EDGE_CURVE('',#85,#195,#311,.T.);
#311 = SURFACE_CURVE('',#312,(#316,#323),.PCURVE_S1.);
#312 = LINE('',#313,#314);
#313 = CARTESIAN_POINT('',(0.,20.,30.));
#314 = VECTOR('',#315,1.);
#315 = DIRECTION('',(1.,0.,-0.));
#316 = PCURVE('',#100,#317);
#317 = DEFINITIONAL_REPRESENTATION('',(#318),#322);
#318 = LINE('',#319,#320);
#319 = CARTESIAN_POINT('',(30.,0.));
#320 = VECTOR('',#321,1.);
#321 = DIRECTION('',(0.,1.));
#322 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#323 = PCURVE('',#126,#324);
#324 = DEFINITIONAL_REPRESENTATION('',(#325),#329);
#325 = LINE('',#326,#327);
#326 = CARTESIAN_POINT('',(0.,20.));
#327 = VECTOR('',#328,1.);
#328 = DIRECTION('',(1.,0.));
#329 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#330 = ORIENTED_EDGE('',*,*,#194,.F.);
#331 = ADVANCED_FACE('',(#332),#72,.F.);
#332 = FACE_BOUND('',#333,.F.);
#333 = EDGE_LOOP('',(#334,#335,#336,#337));
#334 = ORIENTED_EDGE('',*,*,#56,.F.);
#335 = ORIENTED_EDGE('',*,*,#241,.T.);
#336 = ORIENTED_EDGE('',*,*,#171,.T.);
#337 = ORIENTED_EDGE('',*,*,#288,.F.);
#338 = ADVANCED_FACE('',(#339),#126,.T.);
#339 = FACE_BOUND('',#340,.T.);
#340 = EDGE_LOOP('',(#341,#342,#343,#344));
#341 = ORIENTED_EDGE('',*,*,#112,.F.);
#342 = ORIENTED_EDGE('',*,*,#263,.T.);
#343 = ORIENTED_EDGE('',*,*,#217,.T.);
#344 = ORIENTED_EDGE('',*,*,#310,.F.);
#345 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#349)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#346,#347,#348)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#346 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#347 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#348 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#349 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#346,
  'distance_accuracy_value','confusion accuracy');
#350 = PRODUCT_RELATED_PRODUCT_CATEGORY('detail',$,(#7));
#351 = PRODUCT_CATEGORY_RELATIONSHIP('','',#352,#350);
#352 = PRODUCT_CATEGORY('part',$);
#353 = CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#354,#357,(#6,#5));
#354 = PERSON_AND_ORGANIZATION(#355,#356);
#355 = PERSON('IP10.10.10,Administrator','','Administrator',$,$,$);
#356 = ORGANIZATION('IP10.10.10','Unspecified','');
#357 = PERSON_AND_ORGANIZATION_ROLE('creator');
#358 = CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#354,#359,(#7));
#359 = PERSON_AND_ORGANIZATION_ROLE('design_owner');
#360 = CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#354,#361,(#6));
#361 = PERSON_AND_ORGANIZATION_ROLE('design_supplier');
#362 = CC_DESIGN_PERSON_AND_ORGANIZATION_ASSIGNMENT(#354,#363,(#364));
#363 = PERSON_AND_ORGANIZATION_ROLE('classification_officer');
#364 = SECURITY_CLASSIFICATION('','',#365);
#365 = SECURITY_CLASSIFICATION_LEVEL('unclassified');
#366 = CC_DESIGN_SECURITY_CLASSIFICATION(#364,(#6));
#367 = CC_DESIGN_DATE_AND_TIME_ASSIGNMENT(#368,#372,(#5));
#368 = DATE_AND_TIME(#369,#370);
#369 = CALENDAR_DATE(2024,17,3);
#370 = LOCAL_TIME(1,34,$,#371);
#371 = COORDINATED_UNIVERSAL_TIME_OFFSET(8,$,.AHEAD.);
#372 = DATE_TIME_ROLE('creation_date');
#373 = CC_DESIGN_DATE_AND_TIME_ASSIGNMENT(#368,#374,(#364));
#374 = DATE_TIME_ROLE('classification_date');
#375 = CC_DESIGN_APPROVAL(#376,(#6,#5,#364));
#376 = APPROVAL(#377,'');
#377 = APPROVAL_STATUS('not_yet_approved');
#378 = APPROVAL_PERSON_ORGANIZATION(#354,#376,#379);
#379 = APPROVAL_ROLE('approver');
#380 = APPROVAL_DATE_TIME(#368,#376);
ENDSEC;
END-ISO-10303-21;
