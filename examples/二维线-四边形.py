from OCC.Core.BRepBuilderAPI import BRep<PERSON><PERSON>erAPI_MakeWire, BRepBuilderAPI_MakeEdge
from OCC.Core.GCE2d import  GCE2d_MakeSegment
from OCC.Core.GeomAPI import geomapi_To3d
from OCC.Core.gp import gp_Pnt2d, gp_Pln, gp_Pnt, gp_Dir
from OCC.Display.SimpleGui import init_display

sideLength=20
l1 = GCE2d_MakeSegment(gp_Pnt2d(-sideLength / 2, sideLength / 2), gp_Pnt2d(sideLength / 2, sideLength / 2))
l2 = GCE2d_MakeSegment(gp_Pnt2d(sideLength / 2, sideLength / 2), gp_Pnt2d(sideLength / 2, -sideLength / 2))
l3 = GCE2d_MakeSegment(gp_Pnt2d(sideLength / 2, -sideLength / 2), gp_Pnt2d(-sideLength / 2, -sideLength / 2))
l4 = GCE2d_MakeSegment(gp_Pnt2d(-sideLength / 2, -sideLength / 2), gp_Pnt2d(-sideLength / 2, sideLength / 2))

e1=BRepBuilderAPI_MakeEdge(geomapi_To3d(l1.Value(),gp_Pln(gp_Pnt(0,0,20),gp_Dir(0,0,1)))).Edge()
e2=BRepBuilderAPI_MakeEdge(geomapi_To3d(l2.Value(),gp_Pln(gp_Pnt(0,0,20),gp_Dir(0,0,1)))).Edge()
e3=BRepBuilderAPI_MakeEdge(geomapi_To3d(l3.Value(),gp_Pln(gp_Pnt(0,0,20),gp_Dir(0,0,1)))).Edge()
e4=BRepBuilderAPI_MakeEdge(geomapi_To3d(l4.Value(),gp_Pln(gp_Pnt(0,0,20),gp_Dir(0,0,1)))).Edge()

display, start_display, add_menu, add_function_to_menu = init_display()
square= BRepBuilderAPI_MakeWire(e1,e2,e3,e4)
display.DisplayShape(square.Wire(), update=True)
start_display()
