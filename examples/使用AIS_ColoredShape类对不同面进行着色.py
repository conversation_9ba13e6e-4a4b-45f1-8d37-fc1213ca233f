from __future__ import print_function

from random import random

from OCC.Core.AIS import AIS_ColoredShape
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox
from OCC.Display.OCCViewer import rgb_color
from OCC.Display.SimpleGui import init_display
from OCC.Extend.TopologyUtils import TopologyExplorer
display, start_display, add_menu, add_function_to_menu = init_display()
my_box = BRepPrimAPI_MakeBox(10., 20., 30.).Shape()
ais_shp = AIS_ColoredShape(my_box)
for fc in TopologyExplorer(my_box).faces():
    # set a custom color per-face
    ais_shp.SetCustomColor(fc, rgb_color(random(), random(), random()))
display.Context.Display(ais_shp, True)
display.FitAll()

start_display()
