from random import random
from OCC.Core.BRepBuilderAPI import <PERSON><PERSON><PERSON><PERSON>erAP<PERSON>_MakePolygon, BRepBuilderAPI_MakeFace
from OCC.Core.gp import gp_Pnt, gp_Dir, gp_Pln
from OCC.Core.TopExp import TopExp_Explorer
from OCC.Core.TopAbs import TopAbs_EDGE, TopAbs_FACE
from OCC.Core.TopoDS import topods
from OCC.Core.Geom import Geom_Line
from OCC.Core.gp import gp
from OCC.Core.BRep import BRep_Tool
from OCC.Core.GeomAPI import GeomAPI_ExtremaCurveCurve
from OCC.Core.BRepOffsetAPI import BRep<PERSON><PERSON>etAPI_MakeOffset
from OCC.Core.BRepAlgoAPI import BRepAlgoAPI_Cut, BRepAlgoAPI_Fuse
from OCC.Display.SimpleGui import init_display
from OCC.Core.GeomAbs import GeomAbs_Intersection, GeomAbs_Arc, GeomAbs_Tangent
from OCC.Core.AIS import AIS_ColoredShape
from OCC.Display.OCCViewer import rgb_color

display, start_display, add_menu, add_functionto_menu = init_display()

p0 = gp_Pnt()
vnorm = gp_Dir(0, 1, 0)
aPlane = gp_Pln(p0, vnorm)

bcd = 150
tcd = 50
ht = 50
offset = 0
z_of_trapezoid = 0

x0 = -0.5 * bcd + offset
x1 = 0.5 * bcd + offset
x2 = 0.5 * tcd + offset
x3 = -0.5 * tcd + offset
z0 = z_of_trapezoid  # bottom line
z1 = z_of_trapezoid + ht  # top line
aP1 = gp_Pnt(x0, 0.0, z0)
aP2 = gp_Pnt(x1, 0.0, z0)
aP3 = gp_Pnt(x2, 0.0, z1)
aP4 = gp_Pnt(x3, 0.0, z1)
aPolygon = BRepBuilderAPI_MakePolygon(aP1, aP2, aP3, aP4, True)
aTrapezoid = BRepBuilderAPI_MakeFace(aPlane, aPolygon.Wire()).Shape()

ais_shp = AIS_ColoredShape(aTrapezoid)
ais_shp.SetCustomColor(aTrapezoid, rgb_color(random(), random(), random()))
display.Context.Display(ais_shp, True)

anFaceExplorer = TopExp_Explorer(aTrapezoid, TopAbs_FACE)

aReMerge = False
coating_thickness = 10
counter = 0
while anFaceExplorer.More():  # 有更多子形状去挖掘
    aFace = topods.Face(anFaceExplorer.Current())  # 当前被探索到的子形状是哪一个
    offset = BRepOffsetAPI_MakeOffset(aFace, GeomAbs_Intersection)
    offset.Perform(coating_thickness)

    if offset.Shape().IsNull():
        continue
    elif not aReMerge:
        aReMerge = BRepBuilderAPI_MakeFace(topods.Wire(offset.Shape())).Shape()
    else:
        aReMerge = BRepAlgoAPI_Fuse(aReMerge, BRepBuilderAPI_MakeFace(topods.Wire(offset.Shape())).Shape()).Shape()

    anFaceExplorer.Next()  # 下一个子形状

    counter += 1
    print("while counter: ", counter)

offset = BRepAlgoAPI_Cut(aReMerge, aTrapezoid).Shape()  # 从aReMerge中把里面的梯形aTrapezoid挖去

ais_shp = AIS_ColoredShape(offset)
ais_shp.SetCustomColor(offset, rgb_color(1, 0, 0))
display.Context.Display(ais_shp, True)
display.FitAll()
start_display()
