from OCC.Core.gp import gp_Pnt, gp_Dir, gp_Pln
from OCC.Display.SimpleGui import init_display
from OCC.Core.BRepAlgoAPI import BRepAlgoAP<PERSON>_Cut
from OCC.Core.BRepBuilderAPI import BRep<PERSON>uilderAPI_MakePolygon, BRepBuilderAPI_MakeFace

display, start_display, add_menu, add_function_to_menu = init_display()

p0 = gp_Pnt()
vnorm = gp_Dir(0, 1, 0)
aPlane = gp_Pln(p0, vnorm)
tcd = 40
bcd = 60
ht = 30
x0 = -0.5 * bcd
x1 = 0.5 * bcd
x2 = 0.5 * tcd
x3 = -0.5 * tcd
z0 = 0
z1 = 0 + ht
aP1 = gp_Pnt(x0, 0.0, z0)
aP2 = gp_Pnt(x1, 0.0, z0)
aP3 = gp_Pnt(x2, 0.0, z1)
aP4 = gp_Pnt(x3, 0.0, z1)
aPolygon = BRepBuilderAPI_MakePolygon(aP1, aP2, aP3, aP4, True)
aTrapezoid = BRepBuilderAPI_MakeFace(aPlane, aPolygon.Wire()).Shape()

p0 = gp_Pnt()
vnorm = gp_Dir(0, 1, 0)
aPlane = gp_Pln(p0, vnorm)
tcd = 30
bcd = 10
ht = 10
x0 = -0.5 * bcd
x1 = 0.5 * bcd
x2 = 0.5 * tcd
x3 = -0.5 * tcd
z0 = 30 - ht  # down line
z1 = 30  # up line
aP1 = gp_Pnt(x0, 0.0, z0)
aP2 = gp_Pnt(x1, 0.0, z0)
aP3 = gp_Pnt(x2, 0.0, z1)
aP4 = gp_Pnt(x3, 0.0, z1)
aPolygon = BRepBuilderAPI_MakePolygon(aP1, aP2, aP3, aP4, True)
aCutTrapezoid = BRepBuilderAPI_MakeFace(aPlane, aPolygon.Wire()).Shape()

ShapeCut = BRepAlgoAPI_Cut(aTrapezoid, aCutTrapezoid).Shape()

display.DisplayShape(ShapeCut, update=True)
start_display()
