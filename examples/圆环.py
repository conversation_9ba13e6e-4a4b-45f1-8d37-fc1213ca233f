# 学习圆柱  https://zhuanlan.zhihu.com/p/382708320
from OCC.Core.math import math
from OCC.Display.SimpleGui import init_display
from OCC.Core.gp import gp_Pnt
# BRepPrimAPI_MakeBox
from OCC.Core.BRepPrimAPI import BRep<PERSON><PERSON><PERSON><PERSON>_MakeTorus
from OCC.Display.OCCViewer import rgb_color
from OCC.Core.gp import gp_Ax2
from OCC.Core.gp import gp_Dir
import math

if __name__ == '__main__':
    display, start_display, add_menu, add_function_to_menu = init_display()
torus1 = BRepPrimAPI_MakeTorus(gp_Ax2(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1), gp_Dir(1, 0, 0)), 10, 2)
torus2 = BRepPrimAPI_MakeTorus(gp_Ax2(gp_Pnt(30, 0, 0), gp_Dir(0, 0, 1), gp_Dir(1, 0, 0)), 10, 2, 0.5 * math.pi)
torus3 = BRepPrimAPI_MakeTorus(gp_Ax2(gp_Pnt(60, 0, 0), gp_Dir(0, 0, 1), gp_Dir(1, 0, 0)), 10, 2, -0.5 * math.pi,
                               0.75 * math.pi)
torus4 = BRepPrimAPI_MakeTorus(gp_Ax2(gp_Pnt(90, 0, 0), gp_Dir(0, 0, 1), gp_Dir(1, 0, 0)), 10, 2, -0.75 * math.pi,
                               0.75 * math.pi, 0.5 * math.pi)
display.DisplayShape(torus1.Shape(), update=True, color=rgb_color(0, 1, 1))
display.DisplayShape(torus2.Shape(), update=True, color=rgb_color(0.5, 0.5, 1))
display.DisplayShape(torus3.Shape(), update=True, color=rgb_color(0.5, 0.1, 0))
display.DisplayShape(torus4.Shape(), update=True, color=rgb_color(0.2, 0.7, 0.2))

start_display()