from OCC.Core.gp import gp_Pnt
from OCC.Core.BRepBuilderAPI import BRep<PERSON><PERSON>erAPI_MakePolygon
from OCC.Core.GeomAbs import <PERSON>eomAbs_Arc
from OCC.Core.BRepOffsetAPI import BRep<PERSON><PERSON>etAPI_MakeEvolved

from OCC.Display.SimpleGui import init_display
display, start_display, add_menu, add_function_to_menu = init_display()
def evolved_shape():
    P = BRepBuilderAPI_MakePolygon()
    P.Add(gp_Pnt(0., 0., 0.))
    P.Add(gp_Pnt(200., 0., 0.))
    P.Add(gp_Pnt(200., 200., 0.))
    P.Add(gp_Pnt(0., 200., 0.))
    P.Add(gp_Pnt(0., 0., 0.))
    wprof = BRepBuilderAPI_MakePolygon(gp_Pnt(0., 0., 0.), gp_Pnt(-60., -60., -200.))
    S = BRepOffsetAPI_MakeEvolved(P.Wire(),
                                  wprof.Wire(),
                                  GeomAbs_Arc,
                                  True,
                                  False,
                                  True,
                                  0.0001)
    S.Build()
    display.DisplayShape(S.Shape(), update=True)
if __name__ == '__main__':
    evolved_shape()
    start_display()
