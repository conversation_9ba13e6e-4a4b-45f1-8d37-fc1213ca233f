import sys
import time

from OCC.Core.BRepAlgoAPI import BRepAlgoAPI_Fuse, BRepAlgoAPI_Common, BRepAlgoAPI_Section, BRepAlgoAPI_Cut
from OCC.Core.BRepBuilderAPI import BRep<PERSON><PERSON>erAP<PERSON>_MakeFace, BRepBuilderAPI_Transform
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox, BRepPrimAPI_MakeWedge, BRepPrimAPI_MakeSphere, BRepPrimAPI_MakeTorus
from OCC.Display.SimpleGui import init_display
from OCC.Core.gp import gp_Vec, gp_Ax2, gp_Pnt, gp_Dir, gp_Pln, gp_Trsf
display, start_display, add_menu, add_function_to_menu = init_display()
def translate_topods_from_vector(brep_or_iterable, vec, copy=False):
    trns = gp_Trsf()
    trns.SetTranslation(vec)
    brep_trns = BRepBuilderAPI_Transform(brep_or_iterable, trns, copy)
    brep_trns.Build()
    return brep_trns.Shape()
def fuse(event=None):
    display.EraseAll()
    box1 = BRepPrimAPI_MakeBox(2, 1, 1).Shape()
    box2 = BRepPrimAPI_MakeBox(2, 1, 1).Shape()
    box1 = translate_topods_from_vector(box1, gp_Vec(.5, .5, 0))
    fuse_shp = BRepAlgoAPI_Fuse(box1, box2).Shape()
    display.DisplayShape(fuse_shp)
    display.FitAll()
def common(event=None):
    # Create Box
    axe = gp_Ax2(gp_Pnt(10, 10, 10), gp_Dir(1, 2, 1))
    Box = BRepPrimAPI_MakeBox(axe, 60, 80, 100).Shape()
    # Create wedge
    Wedge = BRepPrimAPI_MakeWedge(60., 100., 80., 20.).Shape()
    # Common surface
    CommonSurface = BRepAlgoAPI_Common(Box, Wedge).Shape()

    display.EraseAll()
    ais_box = display.DisplayShape(Box)[0]
    ais_wedge = display.DisplayShape(Wedge)[0]
    display.Context.SetTransparency(ais_box, 0.8, True)
    display.Context.SetTransparency(ais_wedge, 0.8, True)
    display.DisplayShape(CommonSurface)
    display.FitAll()
def slicer(event=None):
    # Param
    Zmin, Zmax, deltaZ = -100, 100, 5
    # Note: the shape can also come from a shape selected from InteractiveViewer
    if 'display' in dir():
        shape = display.GetSelectedShape()
    else:
        # Create the shape to slice
        shape = BRepPrimAPI_MakeSphere(60.).Shape()
    # Define the direction
    D = gp_Dir(0., 0., 1.)  # the z direction
    # Perform slice
    sections = []
    init_time = time.time()  # for total time computation
    for z in range(Zmin, Zmax, deltaZ):
        # Create Plane defined by a point and the perpendicular direction
        P = gp_Pnt(0, 0, z)
        Pln = gp_Pln(P, D)
        face = BRepBuilderAPI_MakeFace(Pln).Shape()
        # Computes Shape/Plane intersection
        section_shp = BRepAlgoAPI_Section(shape, face)
        if section_shp.IsDone():
            sections.append(section_shp)
    total_time = time.time() - init_time
    print("%.3fs necessary to perform slice." % total_time)

    display.EraseAll()
    display.DisplayShape(shape)
    for section_ in sections:
        display.DisplayShape(section_.Shape())
    display.FitAll()
def section(event=None):
    torus = BRepPrimAPI_MakeTorus(120, 20).Shape()
    radius = 120.0
    sections = []
    for i in range(-3, 4):
        # Create Sphere
        sphere = BRepPrimAPI_MakeSphere(gp_Pnt(26 * 3 * i, 0, 0), radius).Shape()
        # Computes Torus/Sphere section
        section_shp = BRepAlgoAPI_Section(torus, sphere, False)
        section_shp.ComputePCurveOn1(True)
        section_shp.Approximation(True)
        section_shp.Build()
        sections.append(section_shp)

    display.EraseAll()
    display.DisplayShape(torus)
    for section_ in sections:
        display.DisplayShape(section_.Shape())
    display.FitAll()
def cut(event=None):
    # Create Box
    Box = BRepPrimAPI_MakeBox(200, 60, 60).Shape()
    # Create Sphere
    Sphere = BRepPrimAPI_MakeSphere(gp_Pnt(100, 20, 20), 80).Shape()
    # Cut: the shere is cut 'by' the box
    Cut = BRepAlgoAPI_Cut(Sphere, Box).Shape()
    display.EraseAll()
    ais_box = display.DisplayShape(Box)[0]
    display.Context.SetTransparency(ais_box, 0.8, True)
    display.DisplayShape(Cut)
    display.FitAll()
def exit(event=None):
    sys.exit()
if __name__ == '__main__':
    add_menu('topology boolean operations')
    add_function_to_menu('topology boolean operations', fuse)
    add_function_to_menu('topology boolean operations', common)
    add_function_to_menu('topology boolean operations', cut)
    add_function_to_menu('topology boolean operations', section)
    add_function_to_menu('topology boolean operations', slicer)
    add_function_to_menu('topology boolean operations', exit)
    start_display()
