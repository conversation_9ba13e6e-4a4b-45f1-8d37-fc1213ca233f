
from OCC.Display.SimpleGui import init_display
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox
from OCC.Core.BOPAlgo import BOPAlgo_Builder

display, start_display, add_menu, add_function_to_menu = init_display()
my_box1 = BRep<PERSON>rim<PERSON>I_MakeBox(10., 20., 30.).Shape()
my_box2 = BRepPrimAPI_MakeBox(20., 1., 30.).Shape()

# use the General Fuse Algorithm
builder = BOPAlgo_Builder()
builder.AddArgument(my_box1)
builder.AddArgument(my_box2)
builder.SetRunParallel(True)
builder.Perform()  # or .PerformWithFiller(a_filler)

if builder.HasErrors():
    raise AssertionError("Failed with error: " % builder.DumpErrorsToString())
result = builder.Shape()
display.DisplayShape(result, update=True)
start_display()
