from OCC.Core.BRepBuilderAPI import BRep<PERSON><PERSON>erAPI_MakePolygon, BRepBuilderAPI_MakeFace
from OCC.Core.gp import gp_Pnt, gp_Dir, gp_Pln
from OCC.Display.SimpleGui import init_display
from OCC.Core.TopExp import TopExp_Explorer
from OCC.Core.TopAbs import TopAbs_VERTEX
from OCC.Core.TopoDS import topods
from OCC.Core.gp import gp
from OCC.Core.BRep import BRep_Tool
from OCC.Display.OCCViewer import rgb_color
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox, BRepPrimAPI_MakeCylinder

def GetTrapezoidShape(x0, x1, x2, x3, z0, z1, aPlane):

    def duobianxing(x0, x1, x2, x3, z0, z1, aPlane):
        # ZOX plane
        # x0 is bottom left, x1 is bottom right, x2 is top right, x3 is top left
        # z0 is bottom, z1 is top

        aP1 = gp_Pnt(x0, 0.0, z0)
        aP2 = gp_Pnt(x1, 0.0, z0)
        aP3 = gp_Pnt(x2, 0.0, z1)
        aP4 = gp_Pnt(x3, 0.0, z1)
        # 多边形
        aPolygon = BRepBuilderAPI_MakePolygon(aP1, aP2, aP3, aP4, True)
        shape=BRepBuilderAPI_MakeFace(aPlane, aPolygon.Wire()).Shape()
        return  shape
    #调用多边形
    #  shape=duobianxing(x0, x1, x2, x3, z0, z1, aPlane)
    #立体坐标
    shape = BRepPrimAPI_MakeBox(gp_Pnt(0, 0, 0), 3, 3, 3).Shape()
    return  shape

def shibie():

    gp_Pnt_1 = gp_Pnt()
    gp_Dir_1 = gp_Dir(0, 1, 0)
    gp_Pln_1 = gp_Pln(gp_Pnt_1 , gp_Dir_1)
    #图形长宽高
    bcd1,tcd1,ht1 = 40,60,70
    # bottom line
    x0, x1, x2, x3 = -0.5 * bcd1, 0.5 * bcd1, 0.5 * tcd1, -0.5 * tcd1
    # top line
    z0, z1 = 0, ht1
    aTrapezoid1Shape_1 = GetTrapezoidShape(x0, x1, x2, x3, z0, z1, gp_Pln_1)#获得一个图形或文件
    #xianshi
    display.DisplayShape(aTrapezoid1Shape_1, update=True)

    #对文件定点查找
    anVertexExplorer_1 = TopExp_Explorer(aTrapezoid1Shape_1, TopAbs_VERTEX)
    vertex_1 = []#定义空数据
    while anVertexExplorer_1.More():  # 有更多子形状去挖掘
        anVertex_1 = topods.Vertex(anVertexExplorer_1.Current())  # 当前被探索到的子形状是哪一个， topods_Vertex
        aPnt_1 = BRep_Tool.Pnt(anVertex_1)  # 转换成gp_Pnt

        vertex_1.append(aPnt_1) #加入数据
        display.DisplayShape(anVertex_1, update=True)#顯示
        anVertexExplorer_1.Next()  # 下一个子形状

    pnts = [] #建立坐標空數組
    for v in vertex_1: #對顶点遍历
        coordinate = (v.X(), v.Y(), v.Z()) #逐个获得坐标点数据
        if coordinate not in pnts: #
            pnts.append(coordinate) #坐标点数据加入数据
    print(pnts)#显示数组
    display.FitAll()
    start_display()
if __name__ == '__main__':

    display, start_display, add_menu, add_function_to_menu = init_display()
    shibie()
