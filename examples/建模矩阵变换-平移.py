from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeCone  # 导入创建圆锥体的API
from OCC.Core.TopLoc import TopLoc_Location  # 导入用于定位的类
from OCC.Core.TopoDS import TopoDS_Shape  # 导入拓扑形状类
from OCC.Core.gp import gp_Pnt, gp_Trsf, gp_Vec, gp_Ax1, gp_Dir  # 导入几何基础类
from OCC.Display.OCCViewer import rgb_color  # 导入颜色设置函数

# 创建一个底面半径为1，顶面半径为0，高度为4的圆锥体
my_cone = BRepPrimAPI_MakeCone(1, 0, 4).Shape()
# 复制圆锥体形状用于后续变换
cone = TopoDS_Shape(my_cone)
# 创建一个变换对象
T = gp_Trsf()
# 设置平移变换，沿Y轴平移5个单位
T.SetTranslation(gp_Vec(0, 5, 0))
# 创建一个位置对象，基于之前定义的变换
loc = TopLoc_Location(T)
# 将圆锥体应用位置变换
cone.Location(loc)

if __name__ == "__main__":
    # 初始化显示环境
    from OCC.Display.SimpleGui import init_display
    display, start_display, add_menu, add_function_to_menu = init_display()
    # 显示原始圆锥体，设置为红色
    display.DisplayShape(my_cone, update=True, color=rgb_color(1, 0, 0))
    # 显示平移后的圆锥体，设置为蓝色
    display.DisplayShape(cone, update=True, color=rgb_color(0, 0, 1))
    # 启动显示
    start_display()
