from OCC.Core.Geom2dAPI import  Geom2dAPI_PointsToBSpline
from OCC.Core.TColgp import TColgp_Array1OfPnt2d
from OCC.Core.gp import  gp_Pnt2d
from OCC.Display.SimpleGui import init_display

points=TColgp_Array1OfPnt2d(1,3)
p1=gp_Pnt2d(0,1)
p2=gp_Pnt2d(2,1)
p3=gp_Pnt2d(6,-3)

points.SetValue(1,p1)
points.SetValue(2,p2)
points.SetValue(3,p3)
approx=Geom2dAPI_PointsToBSpline(points)


display, start_display, add_menu, add_function_to_menu = init_display()

display.DisplayShape(approx.Curve(),update=True)
display.DisplayShape(p1)
display.DisplayShape(p2)
display.DisplayShape(p3)

start_display()
