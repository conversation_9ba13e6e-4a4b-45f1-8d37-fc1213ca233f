from OCC.Core.GeomAPI import GeomAPI_Interpolate
from OCC.Core.TColgp import TColgp_HArray1OfPnt
from OCC.Core.gp import gp_Pnt
from OCC.Display.SimpleGui import init_display

points=[]

points=TColgp_HArray1OfPnt(1,3)
p1=gp_Pnt(0,1,2)
p2=gp_Pnt(2,1,3)
p3=gp_Pnt(6,2,1)

points.SetValue(1,p1)
points.SetValue(2,p2)
points.SetValue(3,p3)
interp=GeomAPI_Interpolate(points,False,0.0001)
interp.Perform()

display, start_display, add_menu, add_function_to_menu = init_display()

display.DisplayShape(interp.Curve(),update=True)
display.DisplayShape(p1)
display.DisplayShape(p2)
display.DisplayShape(p3)

start_display()
