from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
from OCC.Core.TColgp import  TColgp_Array1OfPnt
from OCC.Core.gp import gp_Pnt
from OCC.Display.SimpleGui import init_display

points=TColgp_Array1OfPnt(1,3)
p1=gp_Pnt(0,1,2)
p2=gp_Pnt(2,1,3)
p3=gp_Pnt(6,2,1)

points.SetValue(1,p1)
points.SetValue(2,p2)
points.SetValue(3,p3)
approx=GeomAPI_PointsToBSpline(points)


display, start_display, add_menu, add_function_to_menu = init_display()

display.DisplayShape(approx.Curve(),update=True)
display.DisplayShape(p1)
display.DisplayShape(p2)
display.DisplayShape(p3)

start_display()
