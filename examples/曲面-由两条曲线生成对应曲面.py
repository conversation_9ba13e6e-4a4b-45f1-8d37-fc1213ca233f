from OCC.Core.GeomFill import GeomFill_Pipe, geomfill_Surface
from OCC.Core.gp import gp_Pnt
from OCC.Core.BRepBuilderAPI import  BRep<PERSON><PERSON>erAP<PERSON>_MakeEdge, BRepBuilderAPI_MakeWire
from OCC.Core.TColgp import TColgp_HArray1OfPnt
from OCC.Core.GeomAPI import GeomAPI_Interpolate



# 生成第一个曲线
Linearray1 = TColgp_HArray1OfPnt(1, 5)
Linearray1.SetValue(1, gp_Pnt(5, 0, 0))
Linearray1.SetValue(2, gp_Pnt(4, 1.5, 0))
Linearray1.SetValue(3, gp_Pnt(3, 2, 0))
Linearray1.SetValue(4, gp_Pnt(2, 3, 0))
Linearray1.SetValue(5, gp_Pnt(0, 4.5, 0))
bspline_11 = GeomAPI_Interpolate(Linearray1, False, 0.00001)
bspline_11.Perform()
bspline_21 = bspline_11.Curve()
bspline_31 = BRepBuilderAPI_MakeEdge(bspline_21)
bspline1 = BRepBuilderAPI_MakeWire(bspline_31.Edge()).Wire()

# 生成第二个曲线
Linearray2 = TColgp_HArray1OfPnt(1, 5)
Linearray2.SetValue(1, gp_Pnt(8, 0, 5))
Linearray2.SetValue(2, gp_Pnt(7, 1.7, 5))
Linearray2.SetValue(3, gp_Pnt(6, 2.4, 5))
Linearray2.SetValue(4, gp_Pnt(3, 3.2, 5))
Linearray2.SetValue(5, gp_Pnt(1, 5, 5))
bspline_12 = GeomAPI_Interpolate(Linearray2, False, 0.00001)
bspline_12.Perform()
bspline_22 = bspline_12.Curve()
bspline_32 = BRepBuilderAPI_MakeEdge(bspline_22)
bspline2 = BRepBuilderAPI_MakeWire(bspline_32.Edge()).Wire()

# 二个曲线生成曲面
surface=geomfill_Surface(bspline_21,bspline_22)


if __name__ == "__main__":
    from OCC.Display.SimpleGui import init_display
    display, start_display, add_menu, add_function_to_menu = init_display()
    display.DisplayShape(bspline1, update=True)
    display.DisplayShape(bspline2, update=True)
    display.DisplayShape(surface, update=True)


    start_display()
