from OCC.Core.GeomFill import Geom<PERSON>ill_Pipe
from OCC.Core.gp import gp_Pnt
from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeFace, BRepBuilderAPI_MakeEdge, BRepBuilderAPI_MakeWire
from OCC.Core.TColgp import TColgp_Array2OfPnt, TColgp_HArray1OfPnt
from OCC.Core.GeomAPI import GeomAPI_PointsToBSplineSurface, GeomAPI_PointsToBSpline, GeomAPI_Interpolate
from OCC.Core.GeomAbs import GeomAbs_C2


Linearray2 = TColgp_HArray1OfPnt(1, 5)
Linearray2.SetValue(1, gp_Pnt(8, 0, 5))
Linearray2.SetValue(2, gp_Pnt(7, 1.7, 5))
Linearray2.SetValue(3, gp_Pnt(6, 2.4, 5))
Linearray2.SetValue(4, gp_Pnt(3, 3.2, 5))
Linearray2.SetValue(5, gp_Pnt(1, 5, 5))
bspline_12 = GeomAPI_Interpolate(Linearray2, False, 0.00001)
bspline_12.Perform()
bspline_22 = bspline_12.Curve()
bspline_32 = BRepBuilderAPI_MakeEdge(bspline_22)
bspline2 = BRepBuilderAPI_MakeWire(bspline_32.Edge()).Wire()

tube=GeomFill_Pipe(bspline_22,1)
tube.Perform()

if __name__ == "__main__":
    from OCC.Display.SimpleGui import init_display
    display, start_display, add_menu, add_function_to_menu = init_display()
    display.DisplayShape(tube.Surface(), update=True)

    start_display()
