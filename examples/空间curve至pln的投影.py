from OCC.Core import Geom<PERSON><PERSON>jLib
from OCC.Core.Geom import Geom_Plane

from OCC.Core.gp import gp_Pnt, gp_Trsf, gp_Dir,gp_OX
from OCC.Display.SimpleGui import init_display
from OCC.Core.GeomAPI import GeomAPI_Interpolate
from OCC.Core.TColgp import TColgp_HArray1OfPnt
from OCC.Core.gp import gp_Pnt

display, start_display, add_menu, add_function_to_menu = init_display()


print("creating Plane")
plane = Geom_Plane(gp_Pnt(0,0,0),gp_Dir(0,0,1))

print("creating bspline")
points = []
points=TColgp_HArray1OfPnt(1,3)
p1=gp_Pnt(0,6,2)
p2=gp_Pnt(2,1,3)
p3=gp_Pnt(6,2,1)

points.SetValue(1,p1)
points.SetValue(2,p2)
points.SetValue(3,p3)
interp=GeomAPI_Interpolate(points,False,0.0001)
interp.Perform()
curve=interp.Curve() #Geom_BsplineCurve

# project
proj=GeomProjLib.geomprojlib.ProjectOnPlane(curve,plane, gp_Dir(0, 0, 1),True)

# display original curve and projected curve
display.DisplayShape(curve,color='blue',update=True)
display.DisplayShape(proj, color='red', update=True)

start_display()

