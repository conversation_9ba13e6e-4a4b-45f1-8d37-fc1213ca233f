
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeCylinder
from OCC.Display.SimpleGui import init_display

display, start_display, add_menu, add_function_to_menu = init_display()
display.SetModeHLR()
# Get Context
ais_context = display.GetContext()
# Get Prs3d_drawer from previous context
drawer = ais_context.DefaultDrawer()
drawer.SetIsoOnPlane(True)
la = drawer.LineAspect()
la.SetWidth(4)
# increase line width in the current viewer
# This is only viewed in the HLR mode (hit 'e' key for instance)
line_aspect = drawer.SeenLineAspect()
drawer.EnableDrawHiddenLine()
line_aspect.SetWidth(4)
drawer.SetWireAspect(line_aspect)
# Displays a cylinder
s = BRepPrimAPI_MakeCylinder(50., 50.).Shape()
display.DisplayShape(s)
# Display settings and display loop
display.View_Iso()
display.FitAll()
start_display()
