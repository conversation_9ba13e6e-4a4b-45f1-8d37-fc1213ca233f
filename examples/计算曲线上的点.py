
from OCC.Core.GCPnts import GCPnts_UniformDeflection
from OCC.Core.GeomAPI import GeomAPI_Interpolate
from OCC.Core.GeomAdaptor import GeomAdaptor_Curve
from OCC.Core.TColgp import TColgp_HArray1OfPnt
from OCC.Core.gp import gp_Pnt
from OCC.Display.OCCViewer import rgb_color
from OCC.Display.SimpleGui import init_display



display, start_display, add_menu, add_function_to_menu = init_display()


mycurve=TColgp_HArray1OfPnt(1,3)
p1=gp_Pnt(0,1,2)
p2=gp_Pnt(2,1,3)
p3=gp_Pnt(6,2,1)

mycurve.SetValue(1,p1)
mycurve.SetValue(2,p2)
mycurve.SetValue(3,p3)
mycurve=GeomAPI_Interpolate(mycurve,False,0.0001)
mycurve.Perform()
display.DisplayShape(mycurve.Curve())
display.DisplayShape(p1,update=True)
display.DisplayShape(p2,update=True)
display.DisplayShape(p3,update=True)
# C=Geom2dAdaptor_Curve(mycurve) #二维曲线采用此条代码
C=GeomAdaptor_Curve(mycurve.Curve())
Deflection=0.1
myAlgo=GCPnts_UniformDeflection()
myAlgo.Initialize ( C , Deflection )

if  myAlgo.IsDone() :
    nbr = myAlgo.NbPoints()
    print(nbr)
    for i in range(1,nbr+1):
        point=myAlgo.Value(i)
        display.DisplayShape(point,update=True,color=rgb_color(0,0,1))

start_display()

