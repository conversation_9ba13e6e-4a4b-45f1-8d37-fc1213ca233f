
from OCC.Display.SimpleGui import init_display
from OCC.Core.Addons import text_to_brep, Font_FontAspect_Bold
display, start_display, add_menu, add_function_to_menu = init_display()
## create a basic string
arialbold_brep_string = text_to_brep("pythonocc rocks !", "Arial", Font_FontAspect_Bold, 12., True)
## Then display the string
display.DisplayShape(arialbold_brep_string, update=True)
start_display()
