from OCC.Core.AIS import <PERSON><PERSON>_Shape
from OCC.Core.BRepPrimAPI import BRepPrimAPI_MakeBox
from OCC.Display.SimpleGui import init_display
display, start_display, add_menu, add_function_to_menu = init_display()
# Create a box
s = BRepPrimAPI_MakeBox(200, 100, 50).Shape()
# Create an AIS_Shape from the previous shape
ais_shp = AIS_Shape(s)
ais_shp.SetWidth(4)
ais_shp.SetTransparency(0.10)
# Get context and display shape
# Get Context
ais_context = display.GetContext()
ais_context.Display(ais_shp, True)

display.View_Iso()
display.FitAll()
start_display()
