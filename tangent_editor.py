#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样条线编辑器
用于编辑样条线控制点的坐标和切矢方向
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QDoubleSpinBox, QPushButton, QTableWidget,
                             QTableWidgetItem, QGroupBox, QCheckBox,
                             QSlider, QSpinBox, QMessageBox, QAbstractItemView,
                             QDesktopWidget, QHeaderView, QSizePolicy, QFormLayout, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
import math

class TangentEditor(QDialog):
    """样条线编辑对话框"""
    
    # 信号：当切矢方向改变时发出
    tangent_changed = pyqtSignal(int, float, float, float, float)  # 点索引, x, y, z, 长度
    # 信号：当控制点坐标改变时发出
    point_coordinate_changed = pyqtSignal(int, float, float, float)  # 点索引, x, y, z
    # 信号：清除预览
    clear_preview_signal = pyqtSignal()
    
    def __init__(self, points_data, parent=None):
        """
        初始化切矢编辑器

        Args:
            points_data: 点数据列表，每个元素包含坐标信息
            parent: 父窗口
        """
        super().__init__(parent)
        self.points_data = points_data
        self.tangent_data = []  # 存储每个点的切矢数据

        # 🔧 新增：选中控制点管理
        self.selected_control_points = set()  # 存储选中的控制点索引
        self.reference_curve = None  # 参考曲线
        self.constraints = None  # 约束信息

        # 🔧 新增：样条线分割功能相关属性
        self.spline_split_enabled = False  # 是否启用样条线分割功能
        self.endpoint_split_data = {}  # 存储端点分割相关数据

        self.init_ui()
        self.init_tangent_data()
        self.detect_and_display_constraints()  # 检测并显示约束
        self.setup_connections()
        self.adjust_window_size()  # 自动调整窗口大小

    def update_selected_points_display(self):
        """更新选中控制点的显示"""
        if not self.selected_control_points:
            self.selected_points_text.setPlainText("未选择任何控制点")
            self.selected_points_text.setStyleSheet("""
                QTextEdit {
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                    background-color: #f9f9f9;
                    font-size: 12px;
                    color: #999;
                }
            """)
        else:
            selected_list = sorted(list(self.selected_control_points))
            points_text = "、".join([f"点{i+1}" for i in selected_list])
            self.selected_points_text.setPlainText(f"已选择: {points_text}")
            self.selected_points_text.setStyleSheet("""
                QTextEdit {
                    border: 2px solid #4CAF50;
                    border-radius: 5px;
                    padding: 5px;
                    background-color: #E8F5E8;
                    font-size: 12px;
                    color: #2E7D32;
                    font-weight: bold;
                }
            """)

    def toggle_point_selection(self, row):
        """切换控制点的选择状态"""
        if row in self.selected_control_points:
            self.selected_control_points.remove(row)
            print(f"取消选择点{row+1}")
        else:
            self.selected_control_points.add(row)
            print(f"选择点{row+1}")

        self.update_selected_points_display()
        self.update_table_selection_display()

    def select_all_points(self):
        """选择所有控制点"""
        self.selected_control_points = set(range(len(self.points_data)))
        print(f"已选择所有{len(self.points_data)}个控制点")
        self.update_selected_points_display()
        self.update_table_selection_display()

    def clear_point_selection(self):
        """清除所有选择的控制点"""
        self.selected_control_points.clear()
        print("已清除所有选择")
        self.update_selected_points_display()
        self.update_table_selection_display()

    def update_table_selection_display(self):
        """更新表格中的选择显示"""
        for row in range(self.tangent_table.rowCount()):
            if row in self.selected_control_points:
                # 🔧 改进：更明显的选中显示
                for col in range(self.tangent_table.columnCount()):
                    item = self.tangent_table.item(row, col)
                    if item:
                        if col == 0:  # 点编号列使用更明显的颜色
                            item.setBackground(QColor(100, 200, 100))  # 深绿色背景
                            item.setForeground(QColor(255, 255, 255))  # 白色文字
                        else:
                            item.setBackground(QColor(200, 255, 200))  # 浅绿色背景
                            item.setForeground(QColor(0, 0, 0))  # 黑色文字
            else:
                # 恢复默认背景
                for col in range(self.tangent_table.columnCount()):
                    item = self.tangent_table.item(row, col)
                    if item:
                        item.setBackground(QColor(255, 255, 255))  # 白色背景
                        item.setForeground(QColor(0, 0, 0))  # 黑色文字

    def highlight_point_selection_column(self):
        """高亮显示控制点选择列，引导用户操作"""
        try:
            print("🎨 高亮显示控制点选择列，引导用户操作")

            # 高亮显示第一列（控制点编号列）
            for row in range(self.tangent_table.rowCount()):
                item = self.tangent_table.item(row, 0)
                if item:
                    item.setBackground(QColor(255, 255, 150))  # 黄色背景
                    item.setForeground(QColor(0, 0, 0))  # 黑色文字

            print("✅ 控制点选择列高亮显示完成")

            # 3秒后恢复正常样式
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(3000, self.restore_table_normal_style)

        except Exception as e:
            print(f"❌ 高亮显示控制点选择列失败: {e}")

    def restore_table_normal_style(self):
        """恢复表格正常样式"""
        try:
            print("🎨 恢复表格正常样式")
            self.update_table_selection_display()  # 这会恢复正常的选择显示
            print("✅ 表格样式恢复完成")
        except Exception as e:
            print(f"❌ 恢复表格样式失败: {e}")

    def init_ui(self):
        """初始化用户界面"""
        # 🔧 优化：设置窗口标题和属性
        self.setWindowTitle("样条线切矢编辑器 - 智能切矢设置")
        # 🔧 修复：设置为非模态窗口，允许与画布交互
        self.setModal(False)

        # 🔧 优化：设置窗口图标（如果有的话）
        try:
            from PyQt5.QtGui import QIcon
            # 可以设置一个图标，如果有图标文件的话
            # self.setWindowIcon(QIcon("path/to/icon.png"))
        except:
            pass

        # 🔧 优化：设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #333333;
                font-size: 14px;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
            }
        """)

        # 设置初始大小
        self.resize(1200, 700)  # 增加宽度到1200，高度700，确保能看到所有内容

        # 设置窗口位置到右上角
        self.position_window_top_right()

        # 设置窗口属性
        self.setup_window_properties()
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("样条线编辑器")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("编辑样条线控制点的坐标和切矢方向。双击坐标可直接修改，切矢长度影响曲线的弯曲程度。")
        info_label.setWordWrap(True)
        main_layout.addWidget(info_label)

        # 约束检测和显示
        self.create_constraint_info()
        main_layout.addWidget(self.constraint_info_group)
        
        # 切矢表格 - 设置为可扩展
        self.create_tangent_table()
        main_layout.addWidget(self.tangent_table_group, 1)  # 设置拉伸因子为1，让表格占据主要空间

        # 全局控制 - 固定高度
        self.create_global_controls()
        main_layout.addWidget(self.global_controls_group, 0)  # 设置拉伸因子为0，保持固定高度

        # 样条线分割控制区域
        self.create_spline_split_controls()
        main_layout.addWidget(self.spline_split_group, 0)  # 设置拉伸因子为0，保持固定高度

        # 按钮区域 - 固定高度
        self.create_buttons()
        main_layout.addLayout(self.buttons_layout, 0)  # 设置拉伸因子为0，保持固定高度
        
    def create_tangent_table(self):
        """创建样条线编辑表格"""
        self.tangent_table_group = QGroupBox("控制点坐标与切矢设置")
        layout = QVBoxLayout(self.tangent_table_group)

        # 🔧 重新设计：简化表格，移除X、Y、Z轴方向列
        self.tangent_table = QTableWidget()
        self.tangent_table.setColumnCount(6)
        self.tangent_table.setHorizontalHeaderLabels([
            "点", "X坐标", "Y坐标", "Z坐标", "编辑坐标", "启用切矢"
        ])

        # 设置表格行数
        self.tangent_table.setRowCount(len(self.points_data))

        # 🔧 修复：设置正确的表格列宽（只有6列）
        self.tangent_table.setColumnWidth(0, 60)   # 点编号
        self.tangent_table.setColumnWidth(1, 100)  # X坐标
        self.tangent_table.setColumnWidth(2, 100)  # Y坐标
        self.tangent_table.setColumnWidth(3, 100)  # Z坐标
        self.tangent_table.setColumnWidth(4, 100)  # 编辑坐标按钮
        self.tangent_table.setColumnWidth(5, 100)  # 启用切矢复选框

        # 🔧 优化：设置表格的最小高度，确保能显示所有行
        min_height = max(350, (len(self.points_data) + 3) * 40)  # 每行约40像素高度，增加行高
        self.tangent_table.setMinimumHeight(min_height)

        # 🔧 修复：设置表格的最小宽度，确保所有列都能显示
        min_table_width = 60 + 100 + 100 + 100 + 100 + 100 + 30  # 包含滚动条和边距
        self.tangent_table.setMinimumWidth(min_table_width)

        # 🔧 优化：设置表格选择行为和外观
        self.tangent_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.tangent_table.setAlternatingRowColors(True)  # 交替行颜色，便于查看
        self.tangent_table.setShowGrid(True)  # 显示网格线
        self.tangent_table.setGridStyle(Qt.SolidLine)  # 实线网格

        # 🔧 优化：设置表格自动调整策略
        header = self.tangent_table.horizontalHeader()
        header.setStretchLastSection(True)  # 最后一列自动拉伸填充剩余空间
        header.setDefaultSectionSize(100)  # 设置默认列宽

        # 设置垂直表头
        v_header = self.tangent_table.verticalHeader()
        v_header.setDefaultSectionSize(40)  # 设置行高为40像素

        # 🔧 优化：设置表格大小策略
        self.tangent_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 🔧 新增：添加表格点击事件处理
        self.tangent_table.cellClicked.connect(self.on_table_cell_clicked)

        layout.addWidget(self.tangent_table)

    def create_spline_split_controls(self):
        """创建样条线分割控制区域"""
        self.spline_split_group = QGroupBox("样条线分割控制")
        layout = QVBoxLayout(self.spline_split_group)

        # 分割功能说明
        info_layout = QHBoxLayout()
        info_label = QLabel("当在端点设置切矢时，可选择将样条线分割为两条新样条线")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: #666; font-size: 11px;")
        info_layout.addWidget(info_label)
        layout.addLayout(info_layout)

        # 分割状态显示
        status_layout = QHBoxLayout()
        self.split_status_label = QLabel("分割状态: 未检测到端点切矢设置")
        self.split_status_label.setStyleSheet("font-weight: bold; color: #333;")
        status_layout.addWidget(self.split_status_label)
        status_layout.addStretch()
        layout.addLayout(status_layout)

        # 分割选项控制
        options_layout = QHBoxLayout()

        # 自动分割选项
        self.auto_split_checkbox = QCheckBox("自动提示分割选项")
        self.auto_split_checkbox.setChecked(True)
        self.auto_split_checkbox.setToolTip("当在端点设置切矢时，自动提示是否要分割样条线")
        options_layout.addWidget(self.auto_split_checkbox)

        # 手动分割按钮
        self.manual_split_btn = QPushButton("手动检查分割")
        self.manual_split_btn.setToolTip("手动检查当前是否可以进行样条线分割")
        self.manual_split_btn.clicked.connect(self.manual_check_split)
        options_layout.addWidget(self.manual_split_btn)

        options_layout.addStretch()
        layout.addLayout(options_layout)

        # 分割预览信息
        preview_layout = QVBoxLayout()
        self.split_preview_text = QTextEdit()
        self.split_preview_text.setMaximumHeight(80)
        self.split_preview_text.setPlainText("暂无分割预览信息")
        self.split_preview_text.setReadOnly(True)
        self.split_preview_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                background-color: #f9f9f9;
                font-size: 11px;
                color: #666;
            }
        """)
        preview_layout.addWidget(QLabel("分割预览:"))
        preview_layout.addWidget(self.split_preview_text)
        layout.addLayout(preview_layout)

    def on_table_cell_clicked(self, row, column):
        """处理表格单元格点击事件"""
        # 只有点击点编号列（第0列）时才切换选择状态
        if column == 0:
            self.toggle_point_selection(row)
            print(f"点击了点{row+1}，切换选择状态")

            # 🔧 改进：显示更明显的选择反馈
            selected_count = len(self.selected_control_points)
            if selected_count > 0:
                selected_list = sorted(list(self.selected_control_points))
                selected_text = ", ".join([f"点{i+1}" for i in selected_list])
                print(f"📍 当前已选择 {selected_count} 个控制点: {selected_text}")
            else:
                print("📍 当前没有选择任何控制点")
        
    def create_global_controls(self):
        """创建全局控制"""
        self.global_controls_group = QGroupBox("切矢设置")
        self.global_controls_group.setMinimumHeight(280)  # 🔧 增加最小高度以容纳新的选择区域
        self.global_controls_group.setMaximumHeight(350)  # 🔧 增加最大高度
        layout = QVBoxLayout(self.global_controls_group)

        # 🔧 重新设计：创建水平布局，分为两个主要区域
        main_horizontal_layout = QHBoxLayout()

        # 左侧：控制点选择区域
        self.create_control_points_selection_area(main_horizontal_layout)

        # 右侧：参考曲线选择区域
        self.create_reference_curve_selection_area(main_horizontal_layout)

        layout.addLayout(main_horizontal_layout)

    def create_control_points_selection_area(self, parent_layout):
        """创建控制点选择区域"""
        # 控制点选择组
        control_points_group = QGroupBox("选择控制点")
        control_points_layout = QVBoxLayout(control_points_group)

        # 选择按钮行
        buttons_layout = QHBoxLayout()

        # 🔧 修改：画布选取按钮
        self.select_all_points_btn = QPushButton("鼠标画布中选取")
        self.select_all_points_btn.setToolTip("点击后在画布中用鼠标选择控制点")
        self.select_all_points_btn.setMinimumHeight(35)
        self.select_all_points_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        self.select_all_points_btn.clicked.connect(self.start_canvas_point_selection)
        buttons_layout.addWidget(self.select_all_points_btn)

        # 🔧 优化：清除选择按钮
        self.clear_points_selection_btn = QPushButton("清除")
        self.clear_points_selection_btn.setToolTip("清除所有控制点选择")
        self.clear_points_selection_btn.setMinimumHeight(35)
        self.clear_points_selection_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)
        self.clear_points_selection_btn.clicked.connect(self.clear_control_points_selection)
        buttons_layout.addWidget(self.clear_points_selection_btn)

        # 🔧 优化：反选按钮
        self.invert_points_selection_btn = QPushButton("反选")
        self.invert_points_selection_btn.setToolTip("反选控制点")
        self.invert_points_selection_btn.setMinimumHeight(35)
        self.invert_points_selection_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        self.invert_points_selection_btn.clicked.connect(self.invert_control_points_selection)
        buttons_layout.addWidget(self.invert_points_selection_btn)

        # 🧪 测试按钮：测试控制点选择功能
        self.test_selection_btn = QPushButton("测试选择点1")
        self.test_selection_btn.setToolTip("测试控制点选择功能")
        self.test_selection_btn.setMinimumHeight(35)
        self.test_selection_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:pressed {
                background-color: #6A1B9A;
            }
        """)
        self.test_selection_btn.clicked.connect(lambda: self.test_point_selection(0))
        buttons_layout.addWidget(self.test_selection_btn)

        control_points_layout.addLayout(buttons_layout)

        # 🔧 优化：已选控制点显示文本框
        self.selected_points_text = QTextEdit()
        self.selected_points_text.setMinimumHeight(80)  # 增加最小高度
        self.selected_points_text.setMaximumHeight(100)  # 增加最大高度
        self.selected_points_text.setPlaceholderText("已选择的控制点将显示在这里...")
        self.selected_points_text.setReadOnly(True)
        self.selected_points_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                background-color: #f9f9f9;
                font-size: 12px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
        """)
        control_points_layout.addWidget(self.selected_points_text)

        # 说明标签
        info_label = QLabel("提示：在上方表格中点击控制点编号来选择")
        info_label.setStyleSheet("color: gray; font-size: 10px;")
        control_points_layout.addWidget(info_label)

        parent_layout.addWidget(control_points_group)

    def create_reference_curve_selection_area(self, parent_layout):
        """创建参考曲线选择区域"""
        # 参考曲线选择组
        reference_curve_group = QGroupBox("选择参考样条曲线")
        reference_curve_layout = QVBoxLayout(reference_curve_group)

        # 🔧 优化：选择按钮
        self.select_reference_curve_btn = QPushButton("选择参考曲线")
        self.select_reference_curve_btn.setToolTip("点击选择作为切矢方向的参考样条曲线")
        self.select_reference_curve_btn.setMinimumHeight(40)
        self.select_reference_curve_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        self.select_reference_curve_btn.clicked.connect(self.start_reference_curve_selection)
        reference_curve_layout.addWidget(self.select_reference_curve_btn)

        # 🔧 优化：当前选中曲线显示文本框
        self.reference_curve_text = QTextEdit()
        self.reference_curve_text.setMinimumHeight(80)  # 增加最小高度
        self.reference_curve_text.setMaximumHeight(100)  # 增加最大高度
        self.reference_curve_text.setPlaceholderText("选择的参考曲线信息将显示在这里...")
        self.reference_curve_text.setReadOnly(True)
        self.reference_curve_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                background-color: #f9f9f9;
                font-size: 12px;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
            }
        """)
        reference_curve_layout.addWidget(self.reference_curve_text)

        # 操作按钮行
        curve_buttons_layout = QHBoxLayout()

        # 🔧 优化：清除选择按钮
        self.clear_reference_curve_btn = QPushButton("清除选择")
        self.clear_reference_curve_btn.setToolTip("清除当前选择的参考曲线")
        self.clear_reference_curve_btn.setMinimumHeight(35)
        self.clear_reference_curve_btn.setStyleSheet("""
            QPushButton {
                background-color: #9E9E9E;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #757575;
            }
            QPushButton:pressed {
                background-color: #616161;
            }
            QPushButton:disabled {
                background-color: #E0E0E0;
                color: #9E9E9E;
            }
        """)
        self.clear_reference_curve_btn.clicked.connect(self.clear_reference_curve_selection)
        self.clear_reference_curve_btn.setEnabled(False)  # 初始状态禁用
        curve_buttons_layout.addWidget(self.clear_reference_curve_btn)

        # 🔧 优化：应用切矢按钮
        self.apply_tangent_btn = QPushButton("应用切矢")
        self.apply_tangent_btn.setToolTip("将参考曲线的切矢方向应用到选中的控制点")
        self.apply_tangent_btn.setMinimumHeight(35)
        self.apply_tangent_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #E0E0E0;
                color: #9E9E9E;
            }
        """)
        self.apply_tangent_btn.clicked.connect(self.apply_reference_curve_tangent)
        self.apply_tangent_btn.setEnabled(False)  # 初始状态禁用
        curve_buttons_layout.addWidget(self.apply_tangent_btn)

        reference_curve_layout.addLayout(curve_buttons_layout)

        # 说明标签
        info_label = QLabel("提示：选择一条样条曲线作为切矢方向参考")
        info_label.setStyleSheet("color: gray; font-size: 10px;")
        reference_curve_layout.addWidget(info_label)

        parent_layout.addWidget(reference_curve_group)

    def start_canvas_point_selection(self):
        """开始画布中的控制点选择模式"""
        try:
            print("🎯 开始画布控制点选择模式")

            # 检查是否有父窗口支持画布选择
            if not hasattr(self.parent(), 'start_point_selection_mode'):
                print("❌ 父窗口不支持控制点选择模式，使用全选替代")
                # 如果不支持画布选择，则使用全选作为备用方案
                self.select_all_control_points_fallback()
                return

            # 更新按钮状态
            self.select_all_points_btn.setText("点击画布选择控制点...")
            self.select_all_points_btn.setEnabled(False)
            self.select_all_points_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 12px;
                    font-weight: bold;
                    padding: 5px 10px;
                }
            """)

            # 通知父窗口进入控制点选择模式
            self.parent().start_point_selection_mode(self.on_canvas_point_selected)
            print("✅ 已通知父窗口进入控制点选择模式")

            # 最小化编辑器窗口，让画布可见
            self.showMinimized()
            print("✅ 编辑器已最小化，画布可见")

        except Exception as e:
            print(f"❌ 开始画布控制点选择失败: {e}")
            # 出错时恢复按钮状态
            self.reset_canvas_selection_ui()

    def select_all_control_points_fallback(self):
        """备用方案：全选所有控制点"""
        try:
            print("🔧 使用备用方案：全选所有控制点")
            self.selected_control_points = set(range(len(self.points_data)))
            self.update_table_selection_display()
            self.update_selected_points_display()
            print(f"✅ 已选择所有 {len(self.selected_control_points)} 个控制点")
        except Exception as e:
            print(f"❌ 全选控制点失败: {e}")

    def on_canvas_point_selected(self, point_data):
        """当在画布中选择控制点时的回调"""
        try:
            print(f"🎯 画布控制点选择完成: {point_data}")
            print(f"🔍 调试信息:")
            print(f"   point_data类型: {type(point_data)}")
            print(f"   point_data内容: {point_data}")
            print(f"   points_data长度: {len(self.points_data) if hasattr(self, 'points_data') else 'N/A'}")
            print(f"   selected_control_points存在: {hasattr(self, 'selected_control_points')}")

            # 恢复编辑器窗口
            self.showNormal()
            self.raise_()
            self.activateWindow()
            print("✅ 编辑器窗口已恢复")

            # 重置按钮状态
            self.reset_canvas_selection_ui()
            print("✅ 按钮状态已重置")

            # 处理选择的控制点数据
            if point_data and 'point_index' in point_data:
                point_index = point_data['point_index']
                print(f"🔍 提取的控制点索引: {point_index}")

                # 检查点索引是否有效
                if 0 <= point_index < len(self.points_data):
                    print(f"✅ 控制点索引{point_index}有效")

                    # 添加到选中的控制点集合
                    if not hasattr(self, 'selected_control_points'):
                        self.selected_control_points = set()
                        print("🔧 初始化selected_control_points")

                    print(f"🔍 当前选中状态: {self.selected_control_points}")

                    if point_index in self.selected_control_points:
                        # 如果已经选中，则取消选择
                        self.selected_control_points.remove(point_index)
                        print(f"🔄 取消选择控制点{point_index+1}")
                    else:
                        # 如果未选中，则添加选择
                        self.selected_control_points.add(point_index)
                        print(f"✅ 选择控制点{point_index+1}")

                    print(f"🔍 更新后选中状态: {self.selected_control_points}")

                    # 更新界面显示
                    print("🔧 开始更新界面显示...")
                    self.update_table_selection_display()
                    print("✅ 表格选择显示已更新")

                    self.update_selected_points_display()
                    print("✅ 选中控制点显示已更新")

                    print(f"📍 当前已选择 {len(self.selected_control_points)} 个控制点")
                    print(f"📍 选中的控制点列表: {sorted(list(self.selected_control_points))}")

                    # 🔧 验证文本框内容
                    if hasattr(self, 'selected_points_text'):
                        current_text = self.selected_points_text.toPlainText()
                        print(f"📝 文本框当前内容: '{current_text}'")
                    else:
                        print("❌ selected_points_text不存在")

                else:
                    print(f"❌ 控制点索引{point_index}超出范围(0-{len(self.points_data)-1})")
            else:
                print("❌ 无效的控制点数据")
                if point_data:
                    print(f"   point_data键: {list(point_data.keys()) if isinstance(point_data, dict) else 'Not a dict'}")
                else:
                    print("   point_data为None")

        except Exception as e:
            print(f"❌ 处理画布控制点选择失败: {e}")
            import traceback
            traceback.print_exc()
            self.reset_canvas_selection_ui()

    def reset_canvas_selection_ui(self):
        """重置画布选择UI状态"""
        try:
            self.select_all_points_btn.setText("鼠标画布中选取")
            self.select_all_points_btn.setEnabled(True)
            self.select_all_points_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 12px;
                    font-weight: bold;
                    padding: 5px 10px;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
                QPushButton:pressed {
                    background-color: #E65100;
                }
            """)
            print("✅ 画布选择UI状态已重置")
        except Exception as e:
            print(f"❌ 重置画布选择UI失败: {e}")

    def test_point_selection(self, point_index=0):
        """测试控制点选择功能"""
        try:
            print(f"🧪 测试选择控制点{point_index+1}")

            # 模拟选择控制点
            if not hasattr(self, 'selected_control_points'):
                self.selected_control_points = set()

            if point_index in self.selected_control_points:
                self.selected_control_points.remove(point_index)
                print(f"🔄 取消选择控制点{point_index+1}")
            else:
                self.selected_control_points.add(point_index)
                print(f"✅ 选择控制点{point_index+1}")

            # 更新界面显示
            self.update_table_selection_display()
            self.update_selected_points_display()

            print(f"📍 当前已选择 {len(self.selected_control_points)} 个控制点")
            print(f"📍 选中的控制点: {sorted(list(self.selected_control_points))}")

        except Exception as e:
            print(f"❌ 测试控制点选择失败: {e}")
            import traceback
            traceback.print_exc()

    def clear_control_points_selection(self):
        """清除所有控制点选择"""
        try:
            print("🔧 清除所有控制点选择")
            self.selected_control_points.clear()
            self.update_table_selection_display()
            self.update_selected_points_display()
            print("✅ 已清除所有控制点选择")
        except Exception as e:
            print(f"❌ 清除控制点选择失败: {e}")

    def invert_control_points_selection(self):
        """反选控制点"""
        try:
            print("🔧 反选控制点")
            all_points = set(range(len(self.points_data)))
            self.selected_control_points = all_points - self.selected_control_points
            self.update_table_selection_display()
            self.update_selected_points_display()
            print(f"✅ 反选完成，当前选择 {len(self.selected_control_points)} 个控制点")
        except Exception as e:
            print(f"❌ 反选控制点失败: {e}")

    def start_reference_curve_selection(self):
        """开始参考曲线选择模式"""
        try:
            print("🎯 开始参考曲线选择模式")

            # 🔧 首先检查是否有选中的控制点
            if not hasattr(self, 'selected_control_points'):
                self.selected_control_points = set()

            if not self.selected_control_points:
                print("❌ 没有选中任何控制点")
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("需要选择控制点")
                msg.setText("请先选择要应用切矢的控制点")
                msg.setInformativeText("使用上方的控制点选择按钮或在表格中点击控制点编号")
                msg.setStandardButtons(QMessageBox.Ok)
                msg.exec_()

                # 高亮显示控制点选择区域
                self.highlight_control_points_area()
                return

            # 更新按钮状态
            self.select_reference_curve_btn.setText("点击画布选择曲线...")
            self.select_reference_curve_btn.setEnabled(False)
            self.reference_curve_text.setText("请在画布中点击选择参考样条曲线")

            # 通知父窗口进入曲线选择模式
            if hasattr(self.parent(), 'start_curve_selection_mode'):
                self.parent().start_curve_selection_mode(self.on_reference_curve_selected)
                print("✅ 已通知父窗口进入曲线选择模式")

                # 最小化编辑器窗口，让画布可见
                self.showMinimized()
                print("✅ 编辑器已最小化，画布可见")

            else:
                print("❌ 父窗口不支持曲线选择模式")
                self.reset_reference_curve_selection_ui()

        except Exception as e:
            print(f"❌ 开始参考曲线选择失败: {e}")
            import traceback
            traceback.print_exc()
            self.reset_reference_curve_selection_ui()

    def clear_reference_curve_selection(self):
        """清除参考曲线选择"""
        try:
            print("🔧 清除参考曲线选择")
            self.reference_curve_data = None
            self.reference_curve_text.setText("")
            self.clear_reference_curve_btn.setEnabled(False)
            self.apply_tangent_btn.setEnabled(False)
            print("✅ 已清除参考曲线选择")
        except Exception as e:
            print(f"❌ 清除参考曲线选择失败: {e}")

    def highlight_control_points_area(self):
        """高亮显示控制点选择区域，引导用户操作"""
        try:
            print("🎨 高亮显示控制点选择区域")
            # 这里可以添加视觉高亮效果
            # 暂时通过改变按钮样式来实现
            original_style = self.select_all_points_btn.styleSheet()
            self.select_all_points_btn.setStyleSheet("background-color: yellow; font-weight: bold;")

            # 3秒后恢复正常样式
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(3000, lambda: self.select_all_points_btn.setStyleSheet(original_style))

        except Exception as e:
            print(f"❌ 高亮显示控制点选择区域失败: {e}")

    def reset_reference_curve_selection_ui(self):
        """重置参考曲线选择界面"""
        try:
            self.select_reference_curve_btn.setText("选择参考曲线")
            self.select_reference_curve_btn.setEnabled(True)
            self.reference_curve_text.setText("")
        except Exception as e:
            print(f"❌ 重置参考曲线选择界面失败: {e}")

    def on_reference_curve_selected(self, curve_data):
        """处理参考曲线选择完成"""
        try:
            print("🎯 参考曲线选择完成")
            print(f"选择的曲线数据: {curve_data}")

            # 保存参考曲线数据
            self.reference_curve_data = curve_data

            # 更新界面显示
            curve_name = curve_data.get('name', '未知曲线')
            curve_type = curve_data.get('type', '未知类型')
            curve_info = f"已选择: {curve_name} ({curve_type})"

            if 'points' in curve_data:
                point_count = len(curve_data['points'])
                curve_info += f"\n控制点数: {point_count}"

            self.reference_curve_text.setText(curve_info)

            # 恢复按钮状态
            self.select_reference_curve_btn.setText("选择参考曲线")
            self.select_reference_curve_btn.setEnabled(True)
            self.clear_reference_curve_btn.setEnabled(True)
            self.apply_tangent_btn.setEnabled(True)

            # 恢复编辑器窗口
            self.showNormal()
            self.raise_()
            self.activateWindow()

            print("✅ 参考曲线选择界面更新完成")

        except Exception as e:
            print(f"❌ 处理参考曲线选择失败: {e}")
            import traceback
            traceback.print_exc()
            self.reset_reference_curve_selection_ui()

    def apply_reference_curve_tangent(self):
        """应用参考曲线的切矢方向"""
        try:
            print("🔧 开始应用参考曲线的切矢方向")

            # 🔧 调试：显示当前数据结构状态
            self.debug_data_structures()

            # 检查必要条件
            if not hasattr(self, 'selected_control_points') or not self.selected_control_points:
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("需要选择控制点")
                msg.setText("请先选择要应用切矢的控制点")
                msg.setStandardButtons(QMessageBox.Ok)
                msg.exec_()
                return False

            if not hasattr(self, 'reference_curve_data') or not self.reference_curve_data:
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("需要选择参考曲线")
                msg.setText("请先选择参考样条曲线")
                msg.setStandardButtons(QMessageBox.Ok)
                msg.exec_()
                return False

            print(f"📍 准备为{len(self.selected_control_points)}个选中控制点应用切矢")

            # 🔧 增强调试：详细检查应用过程
            success_count = 0
            selected_points = sorted(list(self.selected_control_points))

            print(f"🔍 开始为{len(selected_points)}个控制点应用切矢")
            print(f"   选中的控制点索引: {selected_points}")
            print(f"   参考曲线数据类型: {type(self.reference_curve_data)}")
            print(f"   参考曲线数据键: {list(self.reference_curve_data.keys()) if isinstance(self.reference_curve_data, dict) else 'Not a dict'}")
            print(f"   points_data长度: {len(self.points_data) if hasattr(self, 'points_data') else 'N/A'}")
            print(f"   tangent_data长度: {len(self.tangent_data) if hasattr(self, 'tangent_data') else 'N/A'}")

            for point_index in selected_points:
                try:
                    print(f"\n🎯 处理控制点{point_index+1} (索引{point_index})")

                    # 检查数据结构
                    if point_index >= len(self.points_data):
                        print(f"❌ 控制点索引{point_index}超出points_data范围({len(self.points_data)})")
                        continue

                    if point_index >= len(self.tangent_data):
                        print(f"❌ 控制点索引{point_index}超出tangent_data范围({len(self.tangent_data)})")
                        continue

                    print(f"   points_data[{point_index}]: {self.points_data[point_index]}")
                    print(f"   tangent_data[{point_index}]: {self.tangent_data[point_index]}")

                    # 为每个控制点计算合适的切矢方向
                    tangent_direction = self.calculate_tangent_for_point(point_index, self.reference_curve_data)

                    if tangent_direction:
                        print(f"🎯 为控制点{point_index+1}计算得到切矢方向: {tangent_direction}")

                        # 🔧 修复：应用切矢方向到正确的数据结构
                        success = self.apply_tangent_direction_to_point(point_index, tangent_direction)
                        if success:
                            success_count += 1
                            print(f"✅ 成功应用切矢到控制点{point_index+1}")

                            # 验证应用结果
                            if point_index < len(self.tangent_data):
                                applied_data = self.tangent_data[point_index]
                                print(f"   验证: 启用状态={applied_data.get('enabled', 'N/A')}")
                                print(f"   验证: 方向={applied_data.get('direction', 'N/A')}")
                                print(f"   验证: 长度={applied_data.get('length', 'N/A')}")
                        else:
                            print(f"❌ 应用切矢到控制点{point_index+1}失败")
                    else:
                        print(f"❌ 无法为控制点{point_index+1}计算切矢方向")

                except Exception as point_error:
                    print(f"❌ 应用切矢到控制点{point_index+1}时出错: {point_error}")
                    import traceback
                    traceback.print_exc()
                    continue

            # 更新表格显示
            self.populate_table()

            # 触发预览更新
            if hasattr(self, 'preview_enabled') and self.preview_enabled:
                self.update_preview()

            print(f"📊 应用结果: {success_count}/{len(selected_points)} 个控制点成功")

            if success_count > 0:
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("应用成功")
                msg.setText(f"成功应用切矢到 {success_count} 个控制点")
                msg.setStandardButtons(QMessageBox.Ok)
                msg.exec_()
                return True
            else:
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("应用失败")
                msg.setText("没有成功应用任何切矢")
                msg.setInformativeText("请检查控制点选择和参考曲线")
                msg.setStandardButtons(QMessageBox.Ok)
                msg.exec_()
                return False

        except Exception as e:
            print(f"❌ 应用参考曲线切矢失败: {e}")
            import traceback
            traceback.print_exc()

            try:
                from PyQt5.QtWidgets import QMessageBox
                msg = QMessageBox(self)
                msg.setWindowTitle("程序错误")
                msg.setText("应用切矢时发生未预期的错误")
                msg.setInformativeText(f"错误详情: {str(e)}")
                msg.setStandardButtons(QMessageBox.Ok)
                msg.exec_()
            except:
                pass

            return False

    def debug_data_structures(self):
        """调试数据结构状态"""
        try:
            print("\n🔍 调试数据结构状态:")
            print(f"   selected_control_points: {getattr(self, 'selected_control_points', 'N/A')}")
            print(f"   reference_curve_data存在: {hasattr(self, 'reference_curve_data')}")
            if hasattr(self, 'reference_curve_data') and self.reference_curve_data:
                print(f"   reference_curve_data类型: {type(self.reference_curve_data)}")
                if isinstance(self.reference_curve_data, dict):
                    print(f"   reference_curve_data键: {list(self.reference_curve_data.keys())}")

            print(f"   points_data长度: {len(getattr(self, 'points_data', []))}")
            print(f"   tangent_data长度: {len(getattr(self, 'tangent_data', []))}")

            if hasattr(self, 'points_data') and self.points_data:
                for i, point_data in enumerate(self.points_data[:3]):  # 只显示前3个
                    print(f"   points_data[{i}]: {point_data}")

            if hasattr(self, 'tangent_data') and self.tangent_data:
                for i, tangent_info in enumerate(self.tangent_data[:3]):  # 只显示前3个
                    print(f"   tangent_data[{i}]: {tangent_info}")

        except Exception as e:
            print(f"❌ 调试数据结构失败: {e}")

    def populate_table(self):
        """填充表格数据"""
        try:
            print("🔧 开始填充表格数据")

            if not hasattr(self, 'tangent_table') or not hasattr(self, 'points_data'):
                print("❌ 表格或数据不存在")
                return

            # 设置表格行数
            self.tangent_table.setRowCount(len(self.points_data))

            # 填充每一行
            for row in range(len(self.points_data)):
                self.populate_table_row(row)

            print(f"✅ 表格填充完成，共{len(self.points_data)}行")

        except Exception as e:
            print(f"❌ 填充表格失败: {e}")
            import traceback
            traceback.print_exc()

    def update_preview(self):
        """更新预览（兼容性方法）"""
        try:
            print("🎨 更新预览（兼容性方法）")

            # 如果有预览功能，可以在这里实现
            # 目前只是一个占位方法，避免调用错误
            if hasattr(self, 'preview_enabled') and self.preview_enabled:
                print("🎨 预览功能已启用，但具体实现待完善")
            else:
                print("ℹ️ 预览功能未启用")

        except Exception as e:
            print(f"❌ 更新预览失败: {e}")

    def calculate_tangent_for_point(self, point_index, reference_curve_data):
        """
        为指定控制点基于参考曲线计算切矢方向

        Args:
            point_index: 控制点索引
            reference_curve_data: 参考曲线数据

        Returns:
            list: 归一化的切矢方向向量 [x, y, z]，失败时返回None
        """
        try:
            print(f"🎯 为控制点{point_index+1}基于参考曲线计算切矢方向")

            # 🔧 修复：获取控制点坐标，适配实际的数据结构
            if isinstance(self.points_data[point_index], dict):
                # 如果是字典结构，尝试多种可能的键名
                if 'coords' in self.points_data[point_index]:
                    point_coords = self.points_data[point_index]['coords']
                elif 'coordinates' in self.points_data[point_index]:
                    point_coords = self.points_data[point_index]['coordinates']
                else:
                    # 如果没有找到坐标键，使用整个字典
                    point_coords = self.points_data[point_index]
            else:
                # 如果是简单的坐标列表
                point_coords = self.points_data[point_index]

            print(f"   控制点坐标: {point_coords}")

            # 🔧 新算法：多种策略计算切矢方向
            tangent_methods = []

            # 策略1：基于最近点的切矢方向
            tangent_by_closest = self.calculate_tangent_by_closest_point_new(reference_curve_data, point_coords)
            if tangent_by_closest:
                tangent_methods.append(("最近点法", tangent_by_closest))
                print(f"   ✅ 最近点法计算成功: {tangent_by_closest}")

            # 策略2：基于相对位置的切矢方向
            tangent_by_position = self.calculate_tangent_by_relative_position(reference_curve_data, point_coords, point_index)
            if tangent_by_position:
                tangent_methods.append(("相对位置法", tangent_by_position))
                print(f"   ✅ 相对位置法计算成功: {tangent_by_position}")

            # 策略3：基于曲线段的切矢方向
            tangent_by_segment = self.calculate_tangent_by_curve_segment_new(reference_curve_data, point_coords, point_index)
            if tangent_by_segment:
                tangent_methods.append(("曲线段法", tangent_by_segment))
                print(f"   ✅ 曲线段法计算成功: {tangent_by_segment}")

            # 选择最佳的切矢方向
            if tangent_methods:
                best_tangent = self.select_best_tangent_method(tangent_methods, point_coords, point_index)
                print(f"   🎯 最终选择的切矢方向: {best_tangent}")
                return best_tangent
            else:
                print(f"   ❌ 所有方法都无法计算切矢方向")
                return None

        except Exception as e:
            print(f"❌ 为控制点{point_index+1}计算切矢失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_tangent_by_closest_point_new(self, curve_data, point_coords):
        """
        基于最近点计算切矢方向（新版本）

        Args:
            curve_data: 参考曲线数据
            point_coords: 控制点坐标

        Returns:
            list: 切矢方向向量
        """
        try:
            print(f"   🔧 使用最近点法计算切矢方向...")

            # 如果有边缘信息，使用边缘计算
            if 'edge' in curve_data:
                return self.calculate_tangent_from_edge_new(curve_data['edge'], point_coords)

            # 如果有控制点信息，使用控制点计算
            if 'points' in curve_data:
                curve_points = curve_data['points']
                if len(curve_points) >= 2:
                    # 找到最近的曲线段
                    min_distance = float('inf')
                    best_segment_index = 0

                    for i in range(len(curve_points) - 1):
                        # 计算到线段的距离（简化为到中点的距离）
                        p1 = curve_points[i]
                        p2 = curve_points[i + 1]
                        mid_point = [(p1[j] + p2[j]) / 2 for j in range(3)]
                        distance = sum((point_coords[j] - mid_point[j]) ** 2 for j in range(3)) ** 0.5

                        if distance < min_distance:
                            min_distance = distance
                            best_segment_index = i

                    # 使用最近段计算方向
                    p1 = curve_points[best_segment_index]
                    p2 = curve_points[best_segment_index + 1]
                    direction = [p2[i] - p1[i] for i in range(3)]

                    # 归一化
                    import math
                    length = math.sqrt(sum(d * d for d in direction))
                    if length > 1e-6:
                        direction = [d / length for d in direction]
                        print(f"   ✅ 最近点法成功: 使用段{best_segment_index}-{best_segment_index+1}")
                        return direction

            return None

        except Exception as e:
            print(f"   ❌ 最近点法计算失败: {e}")
            return None

    def calculate_tangent_by_relative_position(self, curve_data, point_coords, point_index):
        """
        基于相对位置计算切矢方向

        Args:
            curve_data: 参考曲线数据
            point_coords: 控制点坐标
            point_index: 控制点索引

        Returns:
            list: 切矢方向向量
        """
        try:
            print(f"   🔧 使用相对位置法计算切矢方向...")

            if 'points' in curve_data:
                curve_points = curve_data['points']
                if len(curve_points) >= 2:
                    # 根据控制点在样条线中的相对位置选择参考曲线段
                    total_points = len(self.points_data)
                    curve_point_count = len(curve_points)

                    # 计算相对位置（0.0 到 1.0）
                    relative_pos = point_index / max(1, total_points - 1)

                    # 映射到参考曲线的位置
                    curve_pos = relative_pos * (curve_point_count - 1)
                    curve_index = int(curve_pos)
                    curve_index = max(0, min(curve_index, curve_point_count - 2))

                    # 计算该段的方向向量
                    p1 = curve_points[curve_index]
                    p2 = curve_points[curve_index + 1]
                    direction = [p2[i] - p1[i] for i in range(3)]

                    # 归一化
                    import math
                    length = math.sqrt(sum(d * d for d in direction))
                    if length > 1e-6:
                        direction = [d / length for d in direction]
                        print(f"   ✅ 相对位置法成功: 相对位置{relative_pos:.2f}, 使用段{curve_index}-{curve_index+1}")
                        return direction

            return None

        except Exception as e:
            print(f"   ❌ 相对位置法计算失败: {e}")
            return None

    def calculate_tangent_by_curve_segment_new(self, curve_data, point_coords, point_index):
        """
        基于曲线段计算切矢方向（新版本）

        Args:
            curve_data: 参考曲线数据
            point_coords: 控制点坐标
            point_index: 控制点索引

        Returns:
            list: 切矢方向向量
        """
        try:
            print(f"   🔧 使用曲线段法计算切矢方向...")

            # 如果有边缘信息，使用更精确的计算
            if 'edge' in curve_data:
                return self.calculate_tangent_from_edge_sampling_new(curve_data['edge'], point_coords)

            # 使用控制点信息计算平均方向
            if 'points' in curve_data:
                curve_points = curve_data['points']
                if len(curve_points) >= 3:
                    # 计算多个段的平均方向
                    directions = []

                    for i in range(len(curve_points) - 1):
                        p1 = curve_points[i]
                        p2 = curve_points[i + 1]
                        direction = [p2[j] - p1[j] for j in range(3)]

                        # 归一化单个方向
                        import math
                        length = math.sqrt(sum(d * d for d in direction))
                        if length > 1e-6:
                            direction = [d / length for d in direction]
                            directions.append(direction)

                    if directions:
                        # 计算平均方向
                        avg_direction = [0, 0, 0]
                        for direction in directions:
                            for i in range(3):
                                avg_direction[i] += direction[i]

                        # 归一化平均方向
                        import math
                        length = math.sqrt(sum(d * d for d in avg_direction))
                        if length > 1e-6:
                            avg_direction = [d / length for d in avg_direction]
                            print(f"   ✅ 曲线段法成功: 使用{len(directions)}个段的平均方向")
                            return avg_direction

            return None

        except Exception as e:
            print(f"   ❌ 曲线段法计算失败: {e}")
            return None

    def calculate_tangent_from_edge_new(self, edge, point_coords):
        """
        从边缘计算切矢方向（新版本）

        Args:
            edge: OpenCASCADE边缘对象
            point_coords: 控制点坐标

        Returns:
            list: 切矢方向向量
        """
        try:
            print(f"   🔧 开始边缘切矢计算，坐标: {point_coords}")

            # 🔧 修复：添加更详细的错误检查
            try:
                from OCC.Core.BRep import BRep_Tool
                from OCC.Core.gp import gp_Pnt, gp_Vec
                from OCC.Core.GeomAPI import GeomAPI_ProjectPointOnCurve
                print(f"   ✅ OpenCASCADE模块导入成功")
            except ImportError as import_error:
                print(f"   ❌ OpenCASCADE模块导入失败: {import_error}")
                return None

            # 获取曲线
            try:
                curve, first, last = BRep_Tool.Curve(edge)
                print(f"   📐 获取曲线参数: first={first}, last={last}")
                if not curve:
                    print(f"   ❌ 无法从边缘获取曲线")
                    return None
            except Exception as curve_error:
                print(f"   ❌ 获取曲线失败: {curve_error}")
                return None

            # 创建投影点
            try:
                point = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])
                projector = GeomAPI_ProjectPointOnCurve(point, curve, first, last)
                print(f"   📍 创建投影点成功，投影点数: {projector.NbPoints()}")
            except Exception as projection_error:
                print(f"   ❌ 创建投影点失败: {projection_error}")
                return None

            if projector.NbPoints() > 0:
                try:
                    # 获取最近点的参数
                    param = projector.LowerDistanceParameter()
                    print(f"   📊 最近点参数: {param}")

                    # 计算该点的切矢
                    curve_point = gp_Pnt()
                    tangent_vec = gp_Vec()
                    curve.D1(param, curve_point, tangent_vec)

                    # 转换为列表
                    direction = [tangent_vec.X(), tangent_vec.Y(), tangent_vec.Z()]
                    print(f"   📐 原始切矢方向: {direction}")

                    # 归一化
                    import math
                    length = math.sqrt(sum(d * d for d in direction))
                    print(f"   📏 切矢长度: {length}")

                    if length > 1e-6:
                        direction = [d / length for d in direction]
                        print(f"   ✅ 边缘切矢计算成功: {direction}")
                        return direction
                    else:
                        print(f"   ❌ 切矢长度太小，无法归一化")
                        return None

                except Exception as calculation_error:
                    print(f"   ❌ 切矢计算失败: {calculation_error}")
                    return None
            else:
                print(f"   ❌ 没有找到投影点")
                return None

        except Exception as e:
            print(f"   ❌ 边缘切矢计算失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_tangent_from_edge_sampling_new(self, edge, point_coords):
        """
        从边缘采样计算切矢方向（新版本）

        Args:
            edge: OpenCASCADE边缘对象
            point_coords: 控制点坐标

        Returns:
            list: 切矢方向向量
        """
        try:
            print(f"   🔧 使用边缘采样法计算切矢方向...")

            # 这是一个更精确的边缘采样方法
            # 目前使用简化实现，可以后续完善
            return self.calculate_tangent_from_edge_new(edge, point_coords)

        except Exception as e:
            print(f"   ❌ 边缘采样法计算失败: {e}")
            return None

    def select_best_tangent_method(self, tangent_methods, point_coords, point_index):
        """
        从多种切矢计算方法中选择最佳结果

        Args:
            tangent_methods: [(方法名, 切矢方向), ...] 列表
            point_coords: 控制点坐标
            point_index: 控制点索引

        Returns:
            list: 最佳切矢方向
        """
        try:
            if not tangent_methods:
                return None

            print(f"   🎯 从{len(tangent_methods)}种方法中选择最佳切矢方向")

            # 如果只有一种方法，直接返回
            if len(tangent_methods) == 1:
                method_name, tangent = tangent_methods[0]
                print(f"   ✅ 唯一方法: {method_name}")
                return tangent

            # 优先级排序
            method_priority = {
                "最近点法": 1,      # 最高优先级（最精确）
                "相对位置法": 2,    # 次高优先级（考虑位置关系）
                "曲线段法": 3       # 最低优先级（平均方向）
            }

            # 按优先级排序
            sorted_methods = sorted(tangent_methods,
                                  key=lambda x: method_priority.get(x[0], 999))

            # 选择最高优先级的方法
            best_method_name, best_tangent = sorted_methods[0]
            print(f"   ✅ 选择最高优先级方法: {best_method_name}")

            # 验证切矢方向的合理性
            import math
            length = math.sqrt(sum(d * d for d in best_tangent))
            if length < 1e-6:
                print(f"   ⚠️ 最佳方法产生零向量，尝试备选方法")
                for method_name, tangent in sorted_methods[1:]:
                    length = math.sqrt(sum(d * d for d in tangent))
                    if length > 1e-6:
                        print(f"   ✅ 使用备选方法: {method_name}")
                        return tangent

                # 如果所有方法都产生零向量，返回默认方向
                print(f"   ⚠️ 所有方法都产生零向量，使用默认X轴方向")
                return [1.0, 0.0, 0.0]

            return best_tangent

        except Exception as e:
            print(f"   ❌ 选择最佳切矢方向失败: {e}")
            # 返回默认方向
            return [1.0, 0.0, 0.0]

    def apply_tangent_direction_to_point(self, point_index, tangent_direction):
        """
        将切矢方向应用到指定控制点

        Args:
            point_index: 控制点索引
            tangent_direction: 切矢方向向量 [x, y, z]

        Returns:
            bool: 是否成功应用
        """
        try:
            print(f"🔧 应用切矢方向到控制点{point_index+1}: {tangent_direction}")

            # 🔧 修复：使用正确的数据结构存储切矢信息
            if point_index < len(self.tangent_data):
                # 更新切矢数据
                self.tangent_data[point_index]['direction'] = tangent_direction.copy()
                self.tangent_data[point_index]['enabled'] = True
                self.tangent_data[point_index]['length'] = 1.0  # 默认长度

                print(f"✅ 成功保存切矢方向到控制点{point_index+1}")
                print(f"   方向: {tangent_direction}")
                print(f"   启用状态: True")

                return True
            else:
                print(f"❌ 控制点索引{point_index}超出范围")
                return False

        except Exception as e:
            print(f"❌ 应用切矢方向到控制点{point_index+1}失败: {e}")
            return False

    def create_buttons(self):
        """创建按钮区域"""
        self.buttons_layout = QHBoxLayout()

        # 🔧 优化：预览按钮
        self.preview_btn = QPushButton("实时预览")
        self.preview_btn.setCheckable(True)
        self.preview_btn.setToolTip("启用实时预览功能")
        self.preview_btn.setMinimumHeight(40)
        self.preview_btn.setMinimumWidth(120)
        self.preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 13px;
                font-weight: bold;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:checked {
                background-color: #4CAF50;
            }
            QPushButton:checked:hover {
                background-color: #45a049;
            }
        """)

        # 🔧 关键修复：在创建按钮时立即初始化预览状态
        self.preview_enabled = False
        print("🔧 初始化实时预览状态: False")

        # 🔧 优化：确定和取消按钮
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setMinimumHeight(40)
        self.ok_btn.setMinimumWidth(100)
        self.ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.setMinimumWidth(100)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)

        self.buttons_layout.addWidget(self.preview_btn)
        self.buttons_layout.addStretch()
        self.buttons_layout.addWidget(self.ok_btn)
        self.buttons_layout.addWidget(self.cancel_btn)
        
    def init_tangent_data(self):
        """初始化切矢数据"""
        try:
            print("🔧 开始初始化切矢数据")
            self.tangent_data = []

            for i, point_data in enumerate(self.points_data):
                # 默认切矢数据
                tangent_info = {
                    'enabled': False,  # 默认不启用切矢
                    'direction': [1.0, 0.0, 0.0],  # 默认X方向
                    'length': 1.0,  # 默认长度
                    'auto_calculated': False  # 是否自动计算
                }
                self.tangent_data.append(tangent_info)
                print(f"📝 初始化点{i+1}的切矢状态: {tangent_info['enabled']}")

            print(f"✅ 切矢数据初始化完成，共{len(self.tangent_data)}个点")

            # 填充表格
            self.populate_table()

        except Exception as e:
            print(f"❌ 初始化切矢数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def populate_table_row(self, row):
        """填充表格行数据"""
        try:
            point_data = self.points_data[row]
            tangent_info = self.tangent_data[row]

            # 点编号
            self.tangent_table.setItem(row, 0, QTableWidgetItem(f"点{row+1}"))

            # 🔧 修复：处理简单的坐标列表
            if isinstance(point_data, dict):
                coords = point_data.get('coords', [0, 0, 0])
            else:
                # 如果是简单的坐标列表
                coords = point_data if len(point_data) >= 3 else [0, 0, 0]

            # 坐标输入框 (X, Y, Z)
            for i, coord_value in enumerate(coords):
                coord_spinbox = QDoubleSpinBox()
                coord_spinbox.setRange(-10000.0, 10000.0)
                coord_spinbox.setSingleStep(1.0)
                coord_spinbox.setDecimals(2)
                coord_spinbox.setValue(coord_value)
                coord_spinbox.valueChanged.connect(lambda val, r=row, axis=i: self.on_coordinate_changed(r, axis, val))
                self.tangent_table.setCellWidget(row, 1 + i, coord_spinbox)

            # 编辑坐标按钮
            edit_coord_btn = QPushButton("编辑")
            edit_coord_btn.setToolTip("打开坐标编辑对话框")
            edit_coord_btn.clicked.connect(lambda checked, r=row: self.edit_coordinate_dialog(r))
            self.tangent_table.setCellWidget(row, 4, edit_coord_btn)

            # 🔧 重新设计：简化为只有启用切矢复选框
            enable_checkbox = QCheckBox()
            enable_checkbox.setChecked(tangent_info['enabled'])
            enable_checkbox.stateChanged.connect(lambda state, r=row: self.on_tangent_enabled_changed(r, state))
            self.tangent_table.setCellWidget(row, 5, enable_checkbox)

            print(f"📝 初始化点{row+1}的切矢状态: {tangent_info['enabled']}")

        except Exception as e:
            print(f"❌ 填充表格行{row+1}失败: {e}")
            import traceback
            traceback.print_exc()
    
    def setup_connections(self):
        """设置信号连接"""
        try:
            print("🔗 开始设置信号连接...")

            # 🔧 修复：只连接存在的控件

            # 控制点选择按钮连接
            if hasattr(self, 'select_all_points_btn'):
                self.select_all_points_btn.clicked.connect(self.start_canvas_point_selection)
                print("✅ 连接画布控制点选择按钮")

            if hasattr(self, 'clear_points_selection_btn'):
                self.clear_points_selection_btn.clicked.connect(self.clear_control_points_selection)
                print("✅ 连接清除控制点选择按钮")

            if hasattr(self, 'invert_points_selection_btn'):
                self.invert_points_selection_btn.clicked.connect(self.invert_control_points_selection)
                print("✅ 连接反选控制点按钮")

            # 参考曲线选择按钮连接
            if hasattr(self, 'select_reference_curve_btn'):
                self.select_reference_curve_btn.clicked.connect(self.start_reference_curve_selection)
                print("✅ 连接选择参考曲线按钮")

            if hasattr(self, 'clear_reference_curve_btn'):
                self.clear_reference_curve_btn.clicked.connect(self.clear_reference_curve_selection)
                print("✅ 连接清除参考曲线按钮")

            if hasattr(self, 'apply_tangent_btn'):
                self.apply_tangent_btn.clicked.connect(self.apply_reference_curve_tangent)
                print("✅ 连接应用切矢按钮")

            # 基本按钮连接
            if hasattr(self, 'ok_btn'):
                self.ok_btn.clicked.connect(self.accept)
                print("✅ 连接确定按钮")

            if hasattr(self, 'cancel_btn'):
                self.cancel_btn.clicked.connect(self.reject)
                print("✅ 连接取消按钮")

            # 预览按钮连接
            if hasattr(self, 'preview_btn'):
                self.preview_btn.toggled.connect(self.on_preview_toggled)
                print("✅ 连接预览按钮")

            # 表格点击事件连接
            if hasattr(self, 'tangent_table'):
                self.tangent_table.cellClicked.connect(self.on_table_cell_clicked)
                print("✅ 连接表格点击事件")

            print("✅ 信号连接设置完成")

        except Exception as e:
            print(f"❌ 设置信号连接失败: {e}")
            import traceback
            traceback.print_exc()

    def on_coordinate_changed(self, row, axis, value):
        """处理坐标变化"""
        try:
            # 🔧 修复：更新内部数据，适配简单坐标列表
            if row < len(self.points_data):
                if isinstance(self.points_data[row], dict):
                    coords = self.points_data[row].get('coords', [0, 0, 0])
                    coords[axis] = value
                    self.points_data[row]['coords'] = coords
                else:
                    # 如果是简单的坐标列表，直接更新
                    self.points_data[row][axis] = value
                    coords = self.points_data[row]

                print(f"🎯 控制点{row+1}坐标变化 - 轴{axis}: {value}")

                # 🔧 关键修复：立即发出坐标变化信号
                self.point_coordinate_changed.emit(row, coords[0], coords[1], coords[2])

                # 🎨 检查实时预览状态并立即触发预览更新
                # 🔧 修复：同时检查按钮状态和内部状态
                button_checked = self.preview_btn.isChecked() if hasattr(self, 'preview_btn') else False
                internal_enabled = getattr(self, 'preview_enabled', False)

                print(f"🔍 检查实时预览状态:")
                print(f"   - 按钮状态: {button_checked}")
                print(f"   - 内部状态: {internal_enabled}")

                # 🔧 使用按钮状态作为主要判断依据
                if button_checked:
                    # 同步内部状态
                    self.preview_enabled = True
                    print(f"🎨 实时预览已启用，立即更新预览 - 点{row+1}")
                    # 通过信号立即触发预览更新，无需等待
                    # 信号已经发出，modeling.py会立即处理
                else:
                    print(f"ℹ️ 实时预览未启用，坐标变化已记录")

        except Exception as e:
            print(f"❌ 处理坐标变化失败: {e}")

    def edit_coordinate_dialog(self, row):
        """打开坐标编辑对话框"""
        try:
            if row >= len(self.points_data):
                return

            # 🔧 修复：适配简单坐标列表
            if isinstance(self.points_data[row], dict):
                coords = self.points_data[row].get('coords', [0, 0, 0])
            else:
                coords = self.points_data[row] if len(self.points_data[row]) >= 3 else [0, 0, 0]

            # 创建坐标编辑对话框
            dialog = CoordinateEditDialog(row + 1, coords, self)
            if dialog.exec_() == QDialog.Accepted:
                new_coords = dialog.get_coordinates()

                # 🔧 修复：更新内部数据，适配简单坐标列表
                if isinstance(self.points_data[row], dict):
                    self.points_data[row]['coords'] = new_coords
                else:
                    # 如果是简单的坐标列表，直接替换
                    self.points_data[row] = new_coords

                # 更新表格中的坐标输入框
                for i, coord_value in enumerate(new_coords):
                    spinbox = self.tangent_table.cellWidget(row, 1 + i)
                    if spinbox:
                        spinbox.setValue(coord_value)

                print(f"🎯 控制点{row+1}坐标批量更新: {new_coords}")

                # 🔧 关键修复：立即发出坐标变化信号
                self.point_coordinate_changed.emit(row, new_coords[0], new_coords[1], new_coords[2])

                # 🎨 检查实时预览状态并立即触发预览更新
                button_checked = self.preview_btn.isChecked() if hasattr(self, 'preview_btn') else False

                if button_checked:
                    # 同步内部状态
                    self.preview_enabled = True
                    print(f"🎨 实时预览已启用，批量坐标更新立即触发预览 - 点{row+1}")
                    # 信号已经发出，modeling.py会立即处理预览更新
                else:
                    print(f"ℹ️ 实时预览未启用，批量坐标变化已记录")

        except Exception as e:
            print(f"❌ 打开坐标编辑对话框失败: {e}")
    
    def on_tangent_enabled_changed(self, row, state):
        """切矢启用状态改变"""
        enabled = (state == Qt.Checked)
        self.tangent_data[row]['enabled'] = enabled
        print(f"🔄 点{row+1}切矢启用状态: {enabled}")

        # 🔧 修复：显示当前的切矢方向数据
        if enabled:
            direction = self.tangent_data[row]['direction']
            length = self.tangent_data[row]['length']
            print(f"  📊 启用时的切矢数据 - 方向: {direction}, 长度: {length}")

        # 🔧 修复：改进界面状态更新逻辑
        for col in range(6, 10):  # 方向和长度列
            widget = self.tangent_table.cellWidget(row, col)
            if widget:
                widget.setEnabled(enabled)

                # 🔧 修复：无论启用还是禁用，都要确保界面显示正确的数值
                if col >= 6 and col <= 8:  # 方向列 (X, Y, Z)
                    axis_index = col - 6
                    axis_name = ['X', 'Y', 'Z'][axis_index]
                    current_value = self.tangent_data[row]['direction'][axis_index]

                    # 确保界面显示与内部数据一致
                    if widget.value() != current_value:
                        # 临时断开信号连接，避免递归调用
                        widget.blockSignals(True)
                        widget.setValue(current_value)
                        widget.blockSignals(False)
                        print(f"  🔄 同步{axis_name}轴界面数值: {current_value}")
                    else:
                        print(f"  ✅ {axis_name}轴界面数值已同步: {current_value}")

                elif col == 9:  # 长度列
                    current_length = self.tangent_data[row]['length']
                    if widget.value() != current_length:
                        widget.blockSignals(True)
                        widget.setValue(current_length)
                        widget.blockSignals(False)
                        print(f"  🔄 同步长度界面数值: {current_length}")

        # 🆕 新功能：检查是否有参考曲线且现在有启用的控制点
        self.check_and_update_curve_tangent_button()

        # 🔧 新增：检测端点切矢设置，提供分割选项
        if enabled and self.is_endpoint(row):
            print(f"🎯 检测到端点{row+1}启用切矢，检查分割选项...")
            self.detect_endpoint_tangent_setting(row)

        # 只有在启用时才触发预览更新
        if enabled:
            self.emit_tangent_changed(row)
    
    def on_direction_changed(self, row, axis, value):
        """切矢方向改变 - 🔧 修复：添加X轴方向=1的特殊保护"""
        # 🔧 修复：确保数据立即更新，不依赖任何条件
        old_value = self.tangent_data[row]['direction'][axis]
        self.tangent_data[row]['direction'][axis] = value

        axis_name = ['X', 'Y', 'Z'][axis]
        print(f"🔄 切矢方向变化 - 点{row+1}, {axis_name}轴: {old_value} → {value}")

        # 🔧 修复：特别保护X轴方向=1的设置
        if axis == 0 and abs(value - 1.0) < 1e-6:  # X轴方向设置为1
            print(f"🛡️ 检测到用户设置X轴方向=1，启用特殊保护模式")
            # 标记这个点需要特殊保护
            if not hasattr(self, 'protected_x_axis_points'):
                self.protected_x_axis_points = set()
            self.protected_x_axis_points.add(row)
            print(f"📝 点{row+1}已加入X轴方向保护列表")

        # 🔧 修复：改进验证逻辑，避免误判X轴方向
        direction = self.tangent_data[row]['direction']
        import math
        dir_length = math.sqrt(sum(d * d for d in direction))

        # 只有当所有分量都接近0时才认为是零向量
        is_zero_vector = all(abs(d) < 1e-10 for d in direction)

        if is_zero_vector:
            print(f"⚠️ 检测到点{row+1}的真正零向量，保持用户设置等待最终应用时处理")
        else:
            print(f"✅ 点{row+1}切矢方向有效，{axis_name}轴={value}, 向量长度: {dir_length:.6f}")

        # 🔧 修复：始终标记数据已更改，确保用户设置被保存
        print(f"📝 切矢数据已更新并保存 - 点{row+1}, {axis_name}轴: {value}")

        # 只有在预览启用时才触发实时更新
        if self.preview_btn.isChecked():
            self.emit_tangent_changed(row)
            print(f"🔄 预览已启用，触发实时更新")

    def on_length_changed(self, row, value):
        """切矢长度改变"""
        self.tangent_data[row]['length'] = value
        print(f"🔄 切矢长度变化 - 点{row+1}: {value}")

        # 只在预览启用时发送信号
        if self.preview_btn.isChecked():
            self.emit_tangent_changed(row)
    
    def on_global_length_changed(self, value):
        """全局长度改变"""
        multiplier = value / 100.0
        self.global_length_label.setText(f"{multiplier:.1f}")

        # 更新所有启用的切矢长度
        for row in range(len(self.tangent_data)):
            if self.tangent_data[row]['enabled']:
                # 🔧 修复：长度输入框在第9列
                length_spinbox = self.tangent_table.cellWidget(row, 9)
                if length_spinbox:
                    base_length = 1.0  # 基础长度
                    new_length = base_length * multiplier
                    length_spinbox.setValue(new_length)
    
    def emit_tangent_changed(self, row):
        """发出切矢改变信号"""
        if self.preview_btn.isChecked():
            tangent_info = self.tangent_data[row]
            if tangent_info['enabled']:
                direction = tangent_info['direction']
                length = tangent_info['length']
                self.tangent_changed.emit(row, direction[0], direction[1], direction[2], length)
    
    def auto_calculate_tangents(self):
        """自动计算切矢方向"""
        try:
            for i in range(len(self.points_data)):
                direction = self.calculate_auto_tangent(i)
                if direction:
                    self.tangent_data[i]['direction'] = direction
                    self.tangent_data[i]['auto_calculated'] = True

                    # 🔧 修复：更新界面（方向输入框在第6-8列）
                    for j, val in enumerate(direction):
                        spinbox = self.tangent_table.cellWidget(i, 6 + j)
                        if spinbox:
                            spinbox.setValue(val)

            QMessageBox.information(self, "完成", "自动计算切矢方向完成")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"自动计算切矢方向失败: {str(e)}")
    
    def calculate_auto_tangent(self, index):
        """计算指定点的自动切矢方向"""
        if len(self.points_data) < 2:
            return [1.0, 0.0, 0.0]
        
        current_point = self.points_data[index]['coords']
        
        if index == 0:
            # 第一个点：使用到下一个点的方向
            next_point = self.points_data[1]['coords']
            direction = [next_point[i] - current_point[i] for i in range(3)]
        elif index == len(self.points_data) - 1:
            # 最后一个点：使用从前一个点的方向
            prev_point = self.points_data[index - 1]['coords']
            direction = [current_point[i] - prev_point[i] for i in range(3)]
        else:
            # 中间点：使用前后点的平均方向
            prev_point = self.points_data[index - 1]['coords']
            next_point = self.points_data[index + 1]['coords']
            direction = [(next_point[i] - prev_point[i]) / 2.0 for i in range(3)]
        
        # 归一化
        length = math.sqrt(sum(d * d for d in direction))
        if length > 0:
            direction = [d / length for d in direction]
        else:
            direction = [1.0, 0.0, 0.0]
        
        return direction
    
    def normalize_tangent(self, row):
        """归一化指定行的切矢方向"""
        try:
            direction = self.tangent_data[row]['direction']
            length = math.sqrt(sum(d * d for d in direction))

            if length > 0:
                normalized = [d / length for d in direction]
                self.tangent_data[row]['direction'] = normalized

                # 🔧 修复：更新界面（方向输入框在第6-8列）
                for i, val in enumerate(normalized):
                    spinbox = self.tangent_table.cellWidget(row, 6 + i)
                    if spinbox and hasattr(spinbox, 'setValue'):
                        spinbox.setValue(val)
                        print(f"✅ 归一化点{row+1}切矢方向{['X','Y','Z'][i]}: {val:.3f}")
                    else:
                        print(f"⚠️ 点{row+1}切矢方向{['X','Y','Z'][i]}输入框不存在")

                print(f"✅ 点{row+1}切矢方向归一化完成")
            else:
                print(f"⚠️ 点{row+1}切矢方向长度为0，无法归一化")

        except Exception as e:
            print(f"❌ 归一化点{row+1}切矢方向失败: {e}")
            import traceback
            traceback.print_exc()
    
    def smooth_tangents(self):
        """平滑切矢方向"""
        # 实现切矢方向的平滑算法
        QMessageBox.information(self, "提示", "切矢平滑功能待实现")
    
    def reset_tangents(self):
        """重置所有切矢设置"""
        try:
            print("🔄 开始重置所有切矢设置")

            for i in range(len(self.tangent_data)):
                print(f"🔄 重置点{i+1}的切矢设置")

                # 重置内部数据
                self.tangent_data[i] = {
                    'enabled': False,
                    'direction': [1.0, 0.0, 0.0],
                    'length': 1.0,
                    'auto_calculated': False
                }

                # 🔧 修复：更新界面（使用新的列索引）
                # 启用切矢复选框（第5列）
                checkbox = self.tangent_table.cellWidget(i, 5)
                if checkbox and hasattr(checkbox, 'setChecked'):
                    checkbox.setChecked(False)
                    print(f"✅ 重置点{i+1}切矢启用状态: False")
                else:
                    print(f"⚠️ 点{i+1}切矢复选框不存在或类型错误")

                # 🔧 修复：方向输入框重置（第6-8列）
                default_direction = [1.0, 0.0, 0.0]
                for j in range(3):
                    spinbox = self.tangent_table.cellWidget(i, 6 + j)
                    axis_name = ['X', 'Y', 'Z'][j]
                    if spinbox and hasattr(spinbox, 'setValue'):
                        # 临时断开信号连接，避免触发不必要的更新
                        spinbox.blockSignals(True)
                        spinbox.setValue(default_direction[j])
                        spinbox.blockSignals(False)
                        print(f"✅ 重置点{i+1}切矢方向{axis_name}: {default_direction[j]}")
                    else:
                        print(f"⚠️ 点{i+1}切矢方向{axis_name}输入框不存在")

                # 长度输入框（第9列）
                length_spinbox = self.tangent_table.cellWidget(i, 9)
                if length_spinbox and hasattr(length_spinbox, 'setValue'):
                    length_spinbox.setValue(1.0)
                    print(f"✅ 重置点{i+1}切矢长度: 1.0")
                else:
                    print(f"⚠️ 点{i+1}切矢长度输入框不存在")

            print("✅ 所有切矢设置重置完成")

        except Exception as e:
            print(f"❌ 重置切矢设置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def on_preview_toggled(self, checked):
        """预览模式切换"""
        print(f"🔧 预览按钮切换: {checked}")

        # 🔧 立即更新预览状态
        self.preview_enabled = checked

        if checked:
            print("✅ 启用实时预览")
            self.preview_btn.setText("停止预览")
            print(f"🔧 预览状态已设置为: {self.preview_enabled}")

            # 发送所有启用的切矢信息
            for i in range(len(self.tangent_data)):
                if self.tangent_data[i]['enabled']:
                    direction = self.tangent_data[i]['direction']
                    length = self.tangent_data[i]['length']
                    print(f"🔄 发送预览信号 - 点{i+1}: 方向({direction[0]:.2f}, {direction[1]:.2f}, {direction[2]:.2f}), 长度{length:.2f}")
                    self.emit_tangent_changed(i)
        else:
            print("❌ 禁用实时预览")
            self.preview_btn.setText("实时预览")
            print(f"🔧 预览状态已设置为: {self.preview_enabled}")

            # 发送信号清除预览
            self.clear_preview_signal.emit()
    
    def get_tangent_data(self):
        """获取切矢数据"""
        return self.tangent_data.copy()
    
    def get_enabled_tangents(self):
        """获取启用的切矢数据"""
        enabled_tangents = {}
        for i, tangent_info in enumerate(self.tangent_data):
            if tangent_info['enabled']:
                direction = tangent_info['direction'].copy()

                # 🔧 修复：改进零向量检测逻辑，避免误判有效的X轴方向
                import math
                dir_length = math.sqrt(sum(d * d for d in direction))

                # 🔧 修复：更严格的零向量检测，只有所有分量都极小时才认为是零向量
                is_zero_vector = all(abs(d) < 1e-12 for d in direction)

                # 🔧 修复：特别处理标准轴方向，避免误判
                is_standard_x_axis = (abs(direction[0] - 1.0) < 1e-6 and
                                     abs(direction[1]) < 1e-6 and
                                     abs(direction[2]) < 1e-6)
                is_standard_y_axis = (abs(direction[0]) < 1e-6 and
                                     abs(direction[1] - 1.0) < 1e-6 and
                                     abs(direction[2]) < 1e-6)
                is_standard_z_axis = (abs(direction[0]) < 1e-6 and
                                     abs(direction[1]) < 1e-6 and
                                     abs(direction[2] - 1.0) < 1e-6)

                if is_zero_vector:
                    print(f"⚠️ 检测到点{i+1}的真正零向量切矢方向，使用默认X轴方向")
                    direction = [1.0, 0.0, 0.0]
                    # 同时更新界面显示
                    for j, val in enumerate(direction):
                        spinbox = self.tangent_table.cellWidget(i, 6 + j)
                        if spinbox:
                            spinbox.blockSignals(True)
                            spinbox.setValue(val)
                            spinbox.blockSignals(False)
                    # 更新内部数据
                    self.tangent_data[i]['direction'] = direction
                elif is_standard_x_axis or is_standard_y_axis or is_standard_z_axis:
                    axis_name = "X" if is_standard_x_axis else ("Y" if is_standard_y_axis else "Z")
                    print(f"✅ 点{i+1}使用标准{axis_name}轴方向: {direction}")
                else:
                    print(f"✅ 点{i+1}使用自定义切矢方向: {direction}, 长度: {dir_length:.6f}")

                tangent_copy = tangent_info.copy()
                tangent_copy['direction'] = direction
                enabled_tangents[i] = tangent_copy

        # 🔧 新增：详细的切矢数据调试信息
        print(f"📊 获取启用的切矢数据，共{len(enabled_tangents)}个控制点:")
        for point_index, tangent_data in enabled_tangents.items():
            direction = tangent_data['direction']
            length = tangent_data['length']
            print(f"  点{point_index+1}: 方向{direction}, 长度{length}")

        return enabled_tangents

    def adjust_window_size(self):
        """根据内容自动调整窗口大小"""
        try:
            # 计算所需的最小宽度和高度
            point_count = len(self.points_data)

            # 🔧 修复：基础宽度计算（只有6列）
            table_columns_width = 60 + 100 + 100 + 100 + 100 + 100  # 各列宽度之和 = 560
            scrollbar_width = 30      # 垂直滚动条宽度
            table_margins = 40        # 表格边距
            window_margins = 80       # 窗口边距

            base_width = table_columns_width + scrollbar_width + table_margins + window_margins  # 总计约710
            min_width = max(1400, base_width)  # 🔧 增加到1400确保有足够空间显示新的选择区域

            # 🔧 优化：基础高度计算
            title_height = 80      # 标题和说明文字
            table_height = max(400, (point_count + 3) * 45)  # 表格高度，每行45像素，增加行高
            controls_height = 350  # 🔧 增加全局控制区域高度以容纳新的选择区域
            buttons_height = 80    # 按钮区域
            margins = 100          # 各种边距

            min_height = title_height + table_height + controls_height + buttons_height + margins

            # 限制最大尺寸（考虑屏幕大小）
            from PyQt5.QtWidgets import QDesktopWidget
            desktop = QDesktopWidget()
            screen_rect = desktop.screenGeometry()

            max_width = int(screen_rect.width() * 0.85)   # 屏幕宽度的85%
            max_height = int(screen_rect.height() * 0.85)  # 屏幕高度的85%

            # 应用尺寸限制
            final_width = min(min_width, max_width)
            final_height = min(min_height, max_height)

            # 设置窗口大小
            self.resize(final_width, final_height)

            print(f"切矢编辑器窗口大小调整为: {final_width}x{final_height} (控制点数: {point_count})")

        except Exception as e:
            print(f"❌ 调整窗口大小失败: {e}")
            # 使用默认大小
            self.resize(1400, 800)

        print(f"切矢编辑器窗口大小调整为: {final_width}x{final_height} (控制点数: {point_count})")

        # 重新定位到右上角
        self.position_window_top_right()

    def position_window_top_right(self):
        """
        将窗口定位到屏幕右上角
        支持多显示器环境
        """
        try:
            from PyQt5.QtWidgets import QDesktopWidget, QApplication

            # 获取屏幕信息
            desktop = QDesktopWidget()

            # 获取主屏幕或当前屏幕
            if hasattr(desktop, 'primaryScreen'):
                screen_number = desktop.primaryScreen()
            else:
                screen_number = 0

            # 获取可用屏幕区域（排除任务栏等）
            available_rect = desktop.availableGeometry(screen_number)

            # 获取当前窗口大小
            window_width = self.width()
            window_height = self.height()

            # 计算右上角位置（留出边距）
            margin = 20  # 距离屏幕边缘的边距
            x = available_rect.x() + available_rect.width() - window_width - margin
            y = available_rect.y() + margin

            # 确保窗口不会超出可用区域
            if x < available_rect.x():
                x = available_rect.x() + margin
            if y < available_rect.y():
                y = available_rect.y() + margin

            # 确保窗口完全在屏幕内
            if x + window_width > available_rect.x() + available_rect.width():
                x = available_rect.x() + available_rect.width() - window_width - margin
            if y + window_height > available_rect.y() + available_rect.height():
                y = available_rect.y() + available_rect.height() - window_height - margin

            # 移动窗口到右上角
            self.move(x, y)

            print(f"✅ 切矢编辑器定位到右上角: ({x}, {y})")
            print(f"   屏幕可用区域: {available_rect.width()}x{available_rect.height()}")
            print(f"   窗口大小: {window_width}x{window_height}")

        except Exception as e:
            print(f"❌ 定位窗口到右上角失败: {e}")
            # 降级方案：使用简单的右上角定位
            try:
                self.move(800, 50)  # 简单的右上角位置
                print("✅ 使用降级方案定位到右上角")
            except:
                pass

    def setup_window_properties(self):
        """
        设置窗口属性，确保编辑器在右上角显示良好
        """
        try:
            from PyQt5.QtCore import Qt

            # 🔧 修复：设置窗口标志，允许与画布交互
            self.setWindowFlags(
                Qt.Dialog |           # 对话框类型
                Qt.WindowCloseButtonHint |  # 显示关闭按钮
                Qt.WindowMinimizeButtonHint  # 显示最小化按钮，方便用户操作
                # 移除 WindowStaysOnTopHint，允许与画布交互
            )

            # 设置窗口图标（如果有的话）
            # self.setWindowIcon(QIcon("path/to/icon.png"))

            # 设置最小窗口大小
            self.setMinimumSize(800, 400)

            # 设置最大窗口大小（防止过大）
            self.setMaximumSize(1400, 1000)

            print("✅ 窗口属性设置完成")

        except Exception as e:
            print(f"❌ 设置窗口属性失败: {e}")

    def create_constraint_info(self):
        """创建约束信息显示区域"""
        self.constraint_info_group = QGroupBox("几何约束检测")
        self.constraint_info_group.setMaximumHeight(100)
        layout = QVBoxLayout(self.constraint_info_group)

        self.constraint_label = QLabel("正在检测几何约束...")
        self.constraint_label.setWordWrap(True)
        layout.addWidget(self.constraint_label)

        # 约束选项
        constraint_options_layout = QHBoxLayout()

        self.auto_adjust_checkbox = QCheckBox("自动调整切矢以保持约束")
        self.auto_adjust_checkbox.setChecked(True)
        self.auto_adjust_checkbox.setToolTip("自动调整切矢方向以保持检测到的几何约束")
        constraint_options_layout.addWidget(self.auto_adjust_checkbox)

        self.constraint_tolerance_label = QLabel("约束容差(mm):")
        constraint_options_layout.addWidget(self.constraint_tolerance_label)

        self.constraint_tolerance_spinbox = QDoubleSpinBox()
        self.constraint_tolerance_spinbox.setRange(0.1, 10.0)
        self.constraint_tolerance_spinbox.setValue(1.0)
        self.constraint_tolerance_spinbox.setSingleStep(0.1)
        self.constraint_tolerance_spinbox.setToolTip("几何约束检测的容差，单位：毫米")
        constraint_options_layout.addWidget(self.constraint_tolerance_spinbox)

        layout.addLayout(constraint_options_layout)

    def detect_and_display_constraints(self):
        """检测并显示几何约束"""
        try:
            # 提取坐标点
            points = []
            for point_data in self.points_data:
                points.append(point_data['coords'])

            # 使用内置的约束检测函数
            from 约束感知样条线建模 import detect_geometric_constraints

            # 检测约束
            tolerance = self.constraint_tolerance_spinbox.value()
            self.constraints = detect_geometric_constraints(points, tolerance)

            # 显示约束信息
            if self.constraints['constraint_info']:
                constraint_text = "检测到几何约束:\n" + "\n".join(self.constraints['constraint_info'])
                self.constraint_label.setText(constraint_text)
                self.constraint_label.setStyleSheet("color: blue; font-weight: bold;")
            else:
                self.constraint_label.setText("未检测到明显的几何约束条件")
                self.constraint_label.setStyleSheet("color: gray;")

            print(f"✅ 约束检测完成: {len(self.constraints['constraint_info'])}个约束")

        except Exception as e:
            print(f"❌ 约束检测失败: {e}")
            import traceback
            traceback.print_exc()
            self.constraint_label.setText("约束检测失败")
            self.constraint_label.setStyleSheet("color: red;")
            self.constraints = {'constraint_info': []}

    def apply_constraint_aware_adjustments(self):
        """应用约束感知的切矢调整 - 🔧 修复：添加安全检查机制"""
        if not hasattr(self, 'constraints') or not self.auto_adjust_checkbox.isChecked():
            return self.get_enabled_tangents()

        try:
            # 🔧 修复：添加安全检查，确认用户真的想要应用约束调整
            print("🔍 开始约束感知的切矢调整...")

            # 准备切矢数据
            tangent_data_for_adjustment = {}
            user_modified_points = []  # 记录用户修改过的点

            for i, tangent_info in enumerate(self.tangent_data):
                if tangent_info['enabled']:
                    tangent_data_for_adjustment[i] = tangent_info.copy()

                    # 🔧 修复：检查用户是否修改过默认值
                    direction = tangent_info['direction']
                    is_default = (abs(direction[0] - 1.0) < 1e-6 and
                                 abs(direction[1]) < 1e-6 and
                                 abs(direction[2]) < 1e-6)
                    if not is_default:
                        user_modified_points.append(i)

            if not tangent_data_for_adjustment:
                return self.get_enabled_tangents()

            # 🔧 修复：如果用户修改了切矢方向，给出警告
            if user_modified_points:
                print(f"⚠️ 检测到用户修改了{len(user_modified_points)}个点的切矢方向")
                print(f"   修改的点: {[f'点{i+1}' for i in user_modified_points]}")
                print(f"   约束调整可能会覆盖用户设置，请确认是否继续")

            # 提取坐标点
            points = [point_data['coords'] for point_data in self.points_data]

            # 应用内置的约束调整逻辑
            adjusted_data = self.apply_internal_constraint_adjustments(
                points, tangent_data_for_adjustment, self.constraints
            )

            # 🔧 修复：更新界面显示（使用正确的列索引）
            adjustment_count = 0
            for point_index, adjusted_info in adjusted_data.items():
                if adjusted_info.get('constraint_adjusted', False):
                    adjustment_count += 1
                    # 更新界面中的切矢方向（方向输入框在第6-8列）
                    direction = adjusted_info['direction']
                    for j, val in enumerate(direction):
                        spinbox = self.tangent_table.cellWidget(point_index, 6 + j)
                        if spinbox and hasattr(spinbox, 'setValue'):
                            # 🔧 修复：使用信号阻塞避免递归调用
                            spinbox.blockSignals(True)
                            spinbox.setValue(val)
                            spinbox.blockSignals(False)
                            print(f"✅ 约束调整更新点{point_index+1}切矢方向{['X','Y','Z'][j]}: {val:.3f}")
                        else:
                            print(f"⚠️ 点{point_index+1}切矢方向{['X','Y','Z'][j]}输入框不存在")

                    # 更新内部数据
                    self.tangent_data[point_index]['direction'] = direction
                    print(f"✅ 约束调整更新点{point_index+1}内部数据完成")

            if adjustment_count > 0:
                print(f"✅ 约束调整完成，共调整了{adjustment_count}个控制点")
            else:
                print("ℹ️ 约束调整完成，无需调整任何控制点")

            return adjusted_data

        except Exception as e:
            print(f"❌ 约束调整失败: {e}")
            import traceback
            traceback.print_exc()
            return self.get_enabled_tangents()

    def apply_internal_constraint_adjustments(self, points, tangent_data, constraints):
        """
        内置的约束调整逻辑 - 🔧 修复：保护用户设置的X轴方向

        Args:
            points: 控制点列表
            tangent_data: 切矢数据字典
            constraints: 约束条件

        Returns:
            dict: 调整后的切矢数据
        """
        adjusted_data = {}

        print("\n🔧 应用约束感知的切矢调整（保护用户设置）:")

        for point_index, tangent_info in tangent_data.items():
            direction = tangent_info['direction'].copy()
            length = tangent_info.get('length', 1.0)
            was_adjusted = False
            user_explicitly_set_x = False

            print(f"\n  点{point_index+1}切矢调整:")
            print(f"    原始方向: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")

            # 🔧 修复：检查是否在X轴方向保护列表中
            is_protected_x_axis = (hasattr(self, 'protected_x_axis_points') and
                                  point_index in self.protected_x_axis_points)

            if is_protected_x_axis:
                print(f"    🛡️ 点{point_index+1}在X轴方向保护列表中，跳过所有约束调整")
                # 完全跳过约束调整，保持用户设置
                adjusted_info = tangent_info.copy()
                adjusted_info['direction'] = direction
                adjusted_info['constraint_adjusted'] = False
                adjusted_data[point_index] = adjusted_info
                continue

            # 🔧 修复：检查用户是否明确设置了X轴方向
            # 特别保护X轴方向=1.0的情况，这是用户的明确选择
            is_standard_x_axis = (abs(direction[0] - 1.0) < 1e-6 and
                                 abs(direction[1]) < 1e-6 and
                                 abs(direction[2]) < 1e-6)

            if abs(direction[0]) > 1e-6:  # X轴方向不为0
                if is_standard_x_axis:
                    # 🔧 修复：特别保护标准X轴方向[1,0,0]
                    user_explicitly_set_x = True
                    print(f"    🛡️ 检测到用户设置的标准X轴方向[1,0,0]，将完全保护")
                elif abs(direction[0] - 1.0) > 1e-6:
                    # 用户设置了非默认的X轴方向值
                    user_explicitly_set_x = True
                    print(f"    🛡️ 检测到用户明确设置的X轴方向: {direction[0]:.3f}，将予以保护")

            # 🔧 修复：完全禁用针对用户设置X轴方向的约束调整
            if constraints.get('planar_x') is not None:
                if abs(direction[0]) > 1e-6:  # 如果X方向分量不为零
                    if user_explicitly_set_x:
                        print(f"    🚫 检测到X平面约束，但完全保护用户设置的X方向: {direction[0]:.3f}")
                        print(f"    💡 用户设置优先级最高，不应用任何约束调整")
                        print(f"    📝 如确需应用X平面约束，请手动将X方向设置为0")
                        # 完全不修改用户设置
                    else:
                        print(f"    📐 检测到X平面约束，调整默认X方向分量: {direction[0]:.3f} → 0.0")
                        direction[0] = 0.0
                        was_adjusted = True

            # 🔧 修复：类似地保护Y轴和Z轴的用户设置
            if constraints.get('planar_y') is not None:
                if abs(direction[1]) > 1e-6:  # 如果Y方向分量不为零
                    # 检查是否为用户明确设置（非默认值0.0）
                    user_set_y = abs(direction[1]) > 1e-6
                    if user_set_y:
                        print(f"    ⚠️ 检测到Y平面约束，但保护用户设置的Y方向: {direction[1]:.3f}")
                    else:
                        print(f"    📐 检测到Y平面约束，移除Y方向分量: {direction[1]:.3f}")
                        direction[1] = 0.0
                        was_adjusted = True

            if constraints.get('planar_z') is not None:
                if abs(direction[2]) > 1e-6:  # 如果Z方向分量不为零
                    # 检查是否为用户明确设置（非默认值0.0）
                    user_set_z = abs(direction[2]) > 1e-6
                    if user_set_z:
                        print(f"    ⚠️ 检测到Z平面约束，但保护用户设置的Z方向: {direction[2]:.3f}")
                    else:
                        print(f"    📐 检测到Z平面约束，移除Z方向分量: {direction[2]:.3f}")
                        direction[2] = 0.0
                        was_adjusted = True

            # 🔧 修复：改进归一化逻辑，避免意外改变用户设置
            if was_adjusted:
                magnitude = (direction[0]**2 + direction[1]**2 + direction[2]**2)**0.5
                if magnitude > 1e-6:
                    direction = [d/magnitude for d in direction]
                    print(f"    ✅ 调整后方向: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
                else:
                    # 🔧 修复：如果所有分量都被移除，优先保持用户的原始意图
                    original_direction = tangent_info['direction'].copy()
                    print(f"    ⚠️ 约束调整导致零向量，检查用户原始设置: {original_direction}")

                    # 检查用户原始设置是否为标准轴方向
                    is_orig_x = (abs(original_direction[0] - 1.0) < 1e-6 and
                                abs(original_direction[1]) < 1e-6 and
                                abs(original_direction[2]) < 1e-6)
                    is_orig_y = (abs(original_direction[0]) < 1e-6 and
                                abs(original_direction[1] - 1.0) < 1e-6 and
                                abs(original_direction[2]) < 1e-6)
                    is_orig_z = (abs(original_direction[0]) < 1e-6 and
                                abs(original_direction[1]) < 1e-6 and
                                abs(original_direction[2] - 1.0) < 1e-6)

                    if is_orig_x:
                        # 🔧 修复：用户原本设置X轴方向，恢复用户设置
                        direction = [1.0, 0.0, 0.0]
                        print(f"    🔄 恢复用户原始X轴方向设置: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
                        was_adjusted = False  # 标记为未调整，保护用户设置
                    elif is_orig_y:
                        direction = [0.0, 1.0, 0.0]
                        print(f"    🔄 恢复用户原始Y轴方向设置: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
                    elif is_orig_z:
                        direction = [0.0, 0.0, 1.0]
                        print(f"    🔄 恢复用户原始Z轴方向设置: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
                    else:
                        # 如果用户设置的不是标准轴方向，选择最安全的默认方向
                        if constraints.get('planar_x') is None:
                            direction = [1.0, 0.0, 0.0]
                        elif constraints.get('planar_y') is None:
                            direction = [0.0, 1.0, 0.0]
                        else:
                            direction = [0.0, 0.0, 1.0]
                        print(f"    🔄 使用安全默认方向: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
            else:
                print(f"    ✅ 方向无需调整，完全保持用户设置")

            # 创建调整后的切矢信息
            adjusted_info = tangent_info.copy()
            adjusted_info['direction'] = direction
            adjusted_info['constraint_adjusted'] = was_adjusted
            adjusted_data[point_index] = adjusted_info

        return adjusted_data

    def on_constraint_tolerance_changed(self, value):
        """约束容差改变时重新检测约束"""
        print(f"约束容差改变为: {value}mm，重新检测约束")
        self.detect_and_display_constraints()

    def on_auto_adjust_changed(self, state):
        """自动调整选项改变 - 🔧 修复：添加用户确认机制"""
        enabled = (state == Qt.Checked)
        print(f"自动约束调整: {'启用' if enabled else '禁用'}")

        if enabled:
            # 🔧 修复：检查是否有用户设置的切矢方向
            user_modified_count = 0
            for i, tangent_info in enumerate(self.tangent_data):
                if tangent_info['enabled']:
                    direction = tangent_info['direction']
                    is_default = (abs(direction[0] - 1.0) < 1e-6 and
                                 abs(direction[1]) < 1e-6 and
                                 abs(direction[2]) < 1e-6)
                    if not is_default:
                        user_modified_count += 1

            if user_modified_count > 0:
                print(f"⚠️ 检测到{user_modified_count}个点有用户自定义的切矢方向")
                print(f"   启用约束调整可能会覆盖这些设置")
                print(f"   如需保护用户设置，约束调整将采用保守策略")

            # 立即应用约束调整（现在有保护机制）
            self.apply_constraint_aware_adjustments()
        else:
            print("ℹ️ 约束调整已禁用，用户设置将被完全保护")

    def get_constraint_aware_tangent_data(self):
        """获取约束感知的切矢数据"""
        if self.auto_adjust_checkbox.isChecked():
            return self.apply_constraint_aware_adjustments()
        else:
            return self.get_enabled_tangents()

    def get_constraints_info(self):
        """获取约束信息"""
        return getattr(self, 'constraints', {'constraint_info': []})



    # 🔧 注意：这个方法已被新设计的on_reference_curve_selected方法替代
    # 保留这个方法是为了兼容性，但实际上不会被调用

    # 🔧 注意：这个方法已被新设计的apply_reference_curve_tangent方法替代

    # 🔧 注意：这个方法已被新设计的apply_reference_curve_tangent方法替代

    def apply_tangent_to_point(self, point_index, tangent_direction):
        """
        将切矢方向应用到指定的控制点

        Args:
            point_index: 控制点索引
            tangent_direction: 切矢方向向量 [x, y, z]

        Returns:
            bool: 是否成功应用
        """
        try:
            if point_index >= len(self.tangent_data):
                print(f"❌ 控制点索引{point_index}超出范围")
                return False

            # 🔧 改进：自动启用切矢（如果尚未启用）
            if not self.tangent_data[point_index].get('enabled', False):
                self.tangent_data[point_index]['enabled'] = True
                # 更新界面中的启用切矢复选框
                checkbox = self.tangent_table.cellWidget(point_index, 5)
                if checkbox:
                    checkbox.setChecked(True)
                print(f"🔄 自动启用点{point_index+1}的切矢")

            # 应用切矢方向
            self.tangent_data[point_index]['direction'] = tangent_direction.copy()

            # 更新界面显示
            for j, val in enumerate(tangent_direction):
                spinbox = self.tangent_table.cellWidget(point_index, 6 + j)
                if spinbox:
                    # 使用信号阻塞避免递归调用
                    spinbox.blockSignals(True)
                    spinbox.setValue(val)
                    spinbox.blockSignals(False)

            return True

        except Exception as e:
            print(f"❌ 应用切矢到点{point_index+1}失败: {e}")
            return False

    def calculate_curve_tangent(self, curve_data):
        """
        从曲线数据计算切矢方向（改进版）
        🔧 改进：使用选中控制点而不是启用切矢的控制点
        """
        try:
            print("🔧 开始从曲线计算切矢方向（改进算法）")

            # 🔧 改进：使用选中的控制点而不是启用切矢的控制点
            if not self.selected_control_points:
                print("❌ 没有选中任何控制点，请先选择要应用切矢的控制点")
                return None

            # 获取选中的控制点
            selected_points = []
            for point_index in self.selected_control_points:
                if point_index < len(self.points_data):
                    point_coords = self.points_data[point_index]['coords']
                    selected_points.append((point_index, point_coords))

            print(f"📍 为{len(selected_points)}个选中控制点计算切矢方向")

            # 为每个选中的控制点计算最佳切矢方向
            tangent_results = {}

            for point_index, point_coords in selected_points:
                tangent_direction = self.calculate_optimal_tangent_for_point(
                    curve_data, point_coords, point_index
                )
                if tangent_direction:
                    tangent_results[point_index] = tangent_direction
                    print(f"✅ 控制点{point_index+1}切矢方向: {tangent_direction}")
                else:
                    print(f"⚠️ 控制点{point_index+1}无法计算切矢方向")

            # 保存多点切矢数据供后续应用使用
            if tangent_results:
                self.multi_point_tangents = tangent_results
                print(f"📝 已保存{len(tangent_results)}个控制点的切矢方向")

                # 返回第一个计算出的切矢方向作为预览
                first_tangent = list(tangent_results.values())[0]
                print(f"🔄 返回预览切矢方向: {first_tangent}")
                return first_tangent
            else:
                print("❌ 无法为任何选中控制点计算切矢方向")
                return None

        except Exception as e:
            print(f"❌ 计算曲线切矢方向失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_optimal_tangent_for_point(self, curve_data, point_coords, point_index):
        """
        为特定控制点计算最佳切矢方向（改进版）

        Args:
            curve_data: 参考曲线数据
            point_coords: 控制点坐标 [x, y, z]
            point_index: 控制点索引

        Returns:
            list: 归一化的切矢方向 [x, y, z]
        """
        try:
            print(f"🎯 为控制点{point_index+1}计算最佳切矢方向（改进算法）")
            print(f"   控制点坐标: {point_coords}")

            # 🔧 改进：增加更多计算方法，提高成功率
            tangent_methods = []

            # 方法1：基于最近点的切矢方向（最精确）
            tangent_by_closest = self.calculate_tangent_by_closest_point(curve_data, point_coords)
            if tangent_by_closest:
                tangent_methods.append(("最近点法", tangent_by_closest))
                print(f"   ✅ 最近点法计算成功: {tangent_by_closest}")

            # 方法2：基于投影点的切矢方向
            tangent_by_projection = self.calculate_tangent_by_projection(curve_data, point_coords)
            if tangent_by_projection:
                tangent_methods.append(("投影点法", tangent_by_projection))
                print(f"   ✅ 投影点法计算成功: {tangent_by_projection}")

            # 方法3：基于控制点位置的智能选择
            tangent_by_position = self.calculate_tangent_by_position(curve_data, point_coords, point_index)
            if tangent_by_position:
                tangent_methods.append(("位置智能法", tangent_by_position))
                print(f"   ✅ 位置智能法计算成功: {tangent_by_position}")

            # 🔧 新增：基于曲线段的切矢方向
            tangent_by_segment = self.calculate_tangent_by_curve_segment(curve_data, point_coords, point_index)
            if tangent_by_segment:
                tangent_methods.append(("曲线段法", tangent_by_segment))
                print(f"   ✅ 曲线段法计算成功: {tangent_by_segment}")

            # 选择最佳的切矢方向
            if tangent_methods:
                best_tangent = self.select_best_tangent_improved(tangent_methods, point_coords, point_index)
                print(f"   🎯 最终选择的切矢方向: {best_tangent}")
                return best_tangent
            else:
                print(f"   ❌ 所有方法都无法计算切矢方向")
                return None

        except Exception as e:
            print(f"❌ 为控制点{point_index+1}计算切矢失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_tangent_by_curve_segment(self, curve_data, point_coords, point_index):
        """
        基于曲线段计算切矢方向（新方法）

        Args:
            curve_data: 参考曲线数据
            point_coords: 控制点坐标
            point_index: 控制点索引

        Returns:
            list: 切矢方向向量
        """
        try:
            print(f"   🔧 使用曲线段法计算切矢方向...")

            # 如果曲线数据中有控制点信息，使用控制点计算
            if 'points' in curve_data:
                curve_points = curve_data['points']
                if len(curve_points) >= 2:
                    # 根据控制点索引选择合适的曲线段
                    total_points = len(self.points_data)
                    curve_point_count = len(curve_points)

                    # 计算在参考曲线上的相对位置
                    relative_pos = point_index / max(1, total_points - 1)
                    curve_index = int(relative_pos * (curve_point_count - 1))
                    curve_index = max(0, min(curve_index, curve_point_count - 2))

                    # 计算曲线段的方向向量
                    p1 = curve_points[curve_index]
                    p2 = curve_points[curve_index + 1]

                    direction = [p2[i] - p1[i] for i in range(3)]

                    # 归一化
                    import math
                    length = math.sqrt(sum(d * d for d in direction))
                    if length > 1e-6:
                        direction = [d / length for d in direction]
                        print(f"   ✅ 曲线段法成功: 使用段{curve_index}-{curve_index+1}")
                        return direction

            # 如果没有控制点信息，尝试使用边缘信息
            if 'edge' in curve_data:
                return self.calculate_tangent_from_edge_sampling(curve_data['edge'], point_coords)

            return None

        except Exception as e:
            print(f"   ❌ 曲线段法计算失败: {e}")
            return None

    def calculate_tangent_from_edge_sampling(self, edge, point_coords):
        """
        通过边缘采样计算切矢方向

        Args:
            edge: OpenCASCADE边缘对象
            point_coords: 控制点坐标

        Returns:
            list: 切矢方向向量
        """
        try:
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.gp import gp_Pnt

            # 获取曲线
            curve, first, last = BRep_Tool.Curve(edge)
            if not curve:
                return None

            # 在曲线上采样多个点
            sample_count = 10
            sample_points = []

            for i in range(sample_count + 1):
                param = first + (last - first) * i / sample_count
                point = curve.Value(param)
                sample_points.append([point.X(), point.Y(), point.Z()])

            # 找到最接近控制点的采样段
            min_distance = float('inf')
            best_segment_index = 0

            for i in range(len(sample_points) - 1):
                # 计算到线段的距离
                p1 = sample_points[i]
                p2 = sample_points[i + 1]

                # 简化距离计算：到线段中点的距离
                mid_point = [(p1[j] + p2[j]) / 2 for j in range(3)]
                distance = sum((point_coords[j] - mid_point[j]) ** 2 for j in range(3)) ** 0.5

                if distance < min_distance:
                    min_distance = distance
                    best_segment_index = i

            # 使用最佳段计算方向
            p1 = sample_points[best_segment_index]
            p2 = sample_points[best_segment_index + 1]

            direction = [p2[i] - p1[i] for i in range(3)]

            # 归一化
            import math
            length = math.sqrt(sum(d * d for d in direction))
            if length > 1e-6:
                direction = [d / length for d in direction]
                print(f"   ✅ 边缘采样法成功: 使用段{best_segment_index}")
                return direction

            return None

        except Exception as e:
            print(f"   ❌ 边缘采样法失败: {e}")
            return None

    def select_best_tangent_improved(self, tangent_methods, point_coords, point_index):
        """
        改进的切矢方向选择算法

        Args:
            tangent_methods: [(方法名, 切矢方向), ...] 列表
            point_coords: 控制点坐标
            point_index: 控制点索引

        Returns:
            list: 最佳切矢方向
        """
        try:
            if not tangent_methods:
                return None

            print(f"   🎯 从{len(tangent_methods)}种方法中选择最佳切矢方向")

            # 如果只有一种方法，直接返回
            if len(tangent_methods) == 1:
                method_name, tangent = tangent_methods[0]
                print(f"   ✅ 唯一方法: {method_name}")
                return tangent

            # 🔧 改进：优先级排序
            method_priority = {
                "最近点法": 1,      # 最高优先级
                "投影点法": 2,      # 次高优先级
                "曲线段法": 3,      # 中等优先级
                "位置智能法": 4     # 最低优先级
            }

            # 按优先级排序
            sorted_methods = sorted(tangent_methods,
                                  key=lambda x: method_priority.get(x[0], 999))

            # 选择最高优先级的方法
            best_method_name, best_tangent = sorted_methods[0]
            print(f"   ✅ 选择最高优先级方法: {best_method_name}")

            # 🔧 改进：验证切矢方向的合理性
            import math
            length = math.sqrt(sum(d * d for d in best_tangent))
            if length < 1e-6:
                print(f"   ⚠️ 最佳方法产生零向量，尝试备选方法")
                for method_name, tangent in sorted_methods[1:]:
                    length = math.sqrt(sum(d * d for d in tangent))
                    if length > 1e-6:
                        print(f"   ✅ 使用备选方法: {method_name}")
                        return tangent

                # 如果所有方法都产生零向量，返回默认方向
                print(f"   ⚠️ 所有方法都产生零向量，使用默认X轴方向")
                return [1.0, 0.0, 0.0]

            return best_tangent

        except Exception as e:
            print(f"   ❌ 选择最佳切矢方向失败: {e}")
            # 返回默认方向
            return [1.0, 0.0, 0.0]

    def calculate_tangent_by_closest_point(self, curve_data, point_coords):
        """基于最近点计算切矢方向"""
        try:
            if 'edge' not in curve_data:
                return None

            edge = curve_data['edge']
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.GeomLProp import GeomLProp_CLProps
            from OCC.Core.gp import gp_Pnt
            from OCC.Core.GeomAPI import GeomAPI_ProjectPointOnCurve

            # 获取曲线
            curve, first, last = BRep_Tool.Curve(edge)
            if not curve:
                return None

            # 创建目标点
            target_point = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])

            # 投影到曲线上找最近点
            projector = GeomAPI_ProjectPointOnCurve(target_point, curve, first, last)

            if projector.NbPoints() > 0:
                # 获取最近点的参数
                closest_param = projector.LowerDistanceParameter()

                # 在最近点计算切矢
                props = GeomLProp_CLProps(curve, closest_param, 1, 1e-6)

                if props.IsTangentDefined():
                    tangent_vec = props.Tangent()
                    tangent_direction = [tangent_vec.X(), tangent_vec.Y(), tangent_vec.Z()]

                    # 归一化
                    import math
                    length = math.sqrt(sum(d * d for d in tangent_direction))
                    if length > 0:
                        tangent_direction = [d / length for d in tangent_direction]
                        print(f"  📍 最近点切矢: {tangent_direction}")
                        return tangent_direction

            return None

        except Exception as e:
            print(f"  ❌ 最近点切矢计算失败: {e}")
            return None

    def calculate_tangent_by_projection(self, curve_data, point_coords):
        """基于投影方向计算切矢方向"""
        try:
            if 'edge' not in curve_data:
                return None

            edge = curve_data['edge']
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.GeomLProp import GeomLProp_CLProps
            from OCC.Core.gp import gp_Pnt, gp_Vec
            from OCC.Core.GeomAPI import GeomAPI_ProjectPointOnCurve

            # 获取曲线
            curve, first, last = BRep_Tool.Curve(edge)
            if not curve:
                return None

            # 创建目标点
            target_point = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])

            # 投影到曲线上
            projector = GeomAPI_ProjectPointOnCurve(target_point, curve, first, last)

            if projector.NbPoints() > 0:
                # 获取投影点
                projected_point = projector.NearestPoint()
                closest_param = projector.LowerDistanceParameter()

                # 计算从投影点到目标点的方向
                projection_vec = gp_Vec(projected_point, target_point)

                # 获取曲线在投影点的切矢
                props = GeomLProp_CLProps(curve, closest_param, 1, 1e-6)

                if props.IsTangentDefined():
                    curve_tangent = props.Tangent()

                    # 计算投影方向与曲线切矢的合成方向
                    # 使用加权平均，更偏向曲线切矢方向
                    weight_curve = 0.7  # 曲线切矢权重
                    weight_projection = 0.3  # 投影方向权重

                    combined_direction = [
                        weight_curve * curve_tangent.X() + weight_projection * projection_vec.X(),
                        weight_curve * curve_tangent.Y() + weight_projection * projection_vec.Y(),
                        weight_curve * curve_tangent.Z() + weight_projection * projection_vec.Z()
                    ]

                    # 归一化
                    import math
                    length = math.sqrt(sum(d * d for d in combined_direction))
                    if length > 0:
                        combined_direction = [d / length for d in combined_direction]
                        print(f"  📐 投影合成切矢: {combined_direction}")
                        return combined_direction

            return None

        except Exception as e:
            print(f"  ❌ 投影切矢计算失败: {e}")
            return None

    def calculate_tangent_by_position(self, curve_data, point_coords, point_index):
        """基于控制点位置的智能切矢计算"""
        try:
            if 'edge' not in curve_data:
                return None

            edge = curve_data['edge']
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.GeomLProp import GeomLProp_CLProps

            # 获取曲线
            curve, first, last = BRep_Tool.Curve(edge)
            if not curve:
                return None

            # 根据控制点在样条线中的位置选择参考参数
            total_points = len(self.points_data)

            if point_index == 0:
                # 起始点：使用曲线起始处的切矢
                param = first + (last - first) * 0.1  # 稍微偏离起点
                print(f"  🎯 起始点策略，参数: {param}")
            elif point_index == total_points - 1:
                # 终点：使用曲线终点处的切矢
                param = first + (last - first) * 0.9  # 稍微偏离终点
                print(f"  🎯 终点策略，参数: {param}")
            else:
                # 中间点：根据位置比例选择曲线参数
                ratio = point_index / (total_points - 1)
                param = first + (last - first) * ratio
                print(f"  🎯 中间点策略，位置比例: {ratio:.2f}，参数: {param}")

            # 在选定参数处计算切矢
            props = GeomLProp_CLProps(curve, param, 1, 1e-6)

            if props.IsTangentDefined():
                tangent_vec = props.Tangent()
                tangent_direction = [tangent_vec.X(), tangent_vec.Y(), tangent_vec.Z()]

                # 归一化
                import math
                length = math.sqrt(sum(d * d for d in tangent_direction))
                if length > 0:
                    tangent_direction = [d / length for d in tangent_direction]
                    print(f"  🎯 位置智能切矢: {tangent_direction}")
                    return tangent_direction

            return None

        except Exception as e:
            print(f"  ❌ 位置智能切矢计算失败: {e}")
            return None

    def select_best_tangent(self, tangent_closest, tangent_projection, tangent_position,
                           point_coords, point_index):
        """选择最佳的切矢方向"""
        try:
            available_tangents = []

            if tangent_closest:
                available_tangents.append(('最近点', tangent_closest))
            if tangent_projection:
                available_tangents.append(('投影合成', tangent_projection))
            if tangent_position:
                available_tangents.append(('位置智能', tangent_position))

            if not available_tangents:
                print(f"  ❌ 没有可用的切矢方向")
                return None

            # 优先级策略：
            # 1. 对于端点（起始点和终点），优先使用位置智能切矢
            # 2. 对于中间点，优先使用投影合成切矢
            # 3. 最近点切矢作为备选

            total_points = len(self.points_data)

            if point_index == 0 or point_index == total_points - 1:
                # 端点优先级：位置智能 > 最近点 > 投影合成
                priority_order = ['位置智能', '最近点', '投影合成']
                print(f"  🎯 端点策略，优先级: {priority_order}")
            else:
                # 中间点优先级：投影合成 > 位置智能 > 最近点
                priority_order = ['投影合成', '位置智能', '最近点']
                print(f"  🎯 中间点策略，优先级: {priority_order}")

            # 按优先级选择
            for method_name in priority_order:
                for available_method, tangent in available_tangents:
                    if available_method == method_name:
                        print(f"  ✅ 选择{method_name}切矢: {tangent}")
                        return tangent

            # 如果没有匹配的，返回第一个可用的
            method_name, tangent = available_tangents[0]
            print(f"  ✅ 默认选择{method_name}切矢: {tangent}")
            return tangent

        except Exception as e:
            print(f"  ❌ 选择最佳切矢失败: {e}")
            return None

    def adjust_tangent_lengths_adaptively(self, selected_points):
        """自适应调整切矢长度"""
        try:
            print("🔧 开始自适应调整切矢长度")

            # 计算样条线的特征尺寸
            characteristic_length = self.calculate_characteristic_length()

            for point_index in selected_points:
                # 根据控制点位置和样条线特征调整长度
                adaptive_length = self.calculate_adaptive_length(point_index, characteristic_length)

                # 更新切矢长度
                self.tangent_data[point_index]['length'] = adaptive_length

                # 更新界面显示
                length_spinbox = self.tangent_table.cellWidget(point_index, 9)  # 长度列
                if length_spinbox:
                    length_spinbox.setValue(adaptive_length)

                print(f"  📏 控制点{point_index+1}自适应长度: {adaptive_length:.2f}")

            print("✅ 切矢长度自适应调整完成")

        except Exception as e:
            print(f"❌ 自适应调整切矢长度失败: {e}")

    def is_endpoint(self, point_index):
        """
        检测指定控制点是否为端点

        Args:
            point_index: 控制点索引

        Returns:
            bool: 如果是端点返回True，否则返回False
        """
        try:
            total_points = len(self.points_data)

            # 端点是第一个点（索引0）或最后一个点（索引total_points-1）
            is_endpoint = (point_index == 0) or (point_index == total_points - 1)

            print(f"🔍 检测控制点{point_index+1}是否为端点: {'是' if is_endpoint else '否'}")
            print(f"   总控制点数: {total_points}, 当前点索引: {point_index}")

            return is_endpoint

        except Exception as e:
            print(f"❌ 检测端点失败: {e}")
            return False

    def find_nearest_control_point(self, endpoint_index):
        """
        从指定端点开始寻找最近的控制点

        Args:
            endpoint_index: 端点索引（0或len(points_data)-1）

        Returns:
            int: 最近控制点的索引，如果找不到返回None
        """
        try:
            total_points = len(self.points_data)

            if not self.is_endpoint(endpoint_index):
                print(f"❌ 点{endpoint_index+1}不是端点，无法查找最近控制点")
                return None

            # 获取端点坐标
            endpoint_coords = self.points_data[endpoint_index]['coords']
            print(f"🔍 从端点{endpoint_index+1}查找最近控制点")
            print(f"   端点坐标: ({endpoint_coords[0]:.2f}, {endpoint_coords[1]:.2f}, {endpoint_coords[2]:.2f})")

            min_distance = float('inf')
            nearest_point_index = None

            # 遍历所有其他控制点，找到距离最近的
            for i in range(total_points):
                if i == endpoint_index:  # 跳过端点自身
                    continue

                point_coords = self.points_data[i]['coords']

                # 计算欧几里得距离
                distance = math.sqrt(
                    (point_coords[0] - endpoint_coords[0]) ** 2 +
                    (point_coords[1] - endpoint_coords[1]) ** 2 +
                    (point_coords[2] - endpoint_coords[2]) ** 2
                )

                print(f"   点{i+1}距离: {distance:.2f}")

                if distance < min_distance:
                    min_distance = distance
                    nearest_point_index = i

            if nearest_point_index is not None:
                print(f"✅ 找到最近控制点: 点{nearest_point_index+1}，距离: {min_distance:.2f}")
            else:
                print(f"❌ 未找到最近控制点")

            return nearest_point_index

        except Exception as e:
            print(f"❌ 查找最近控制点失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def prepare_spline_split_data(self, endpoint_index):
        """
        准备样条线分割所需的数据

        Args:
            endpoint_index: 端点索引

        Returns:
            dict: 包含分割信息的字典，如果无法分割返回None
        """
        try:
            if not self.is_endpoint(endpoint_index):
                print(f"❌ 点{endpoint_index+1}不是端点，无法进行样条线分割")
                return None

            # 查找最近的控制点
            nearest_point_index = self.find_nearest_control_point(endpoint_index)
            if nearest_point_index is None:
                print(f"❌ 无法找到端点{endpoint_index+1}的最近控制点")
                return None

            total_points = len(self.points_data)

            # 🔧 修复：特殊处理三个点的情况
            if total_points == 3:
                print(f"🔧 检测到三个点样条线，应用特殊分割逻辑")

                # 三个点的情况：点1-点2-点3
                # 不管从哪个端点分割，都会导致中间点重复
                # 解决方案：不允许三个点的样条线分割
                print(f"⚠️ 三个点样条线不支持分割操作")
                print(f"   原因：分割会导致中间点在两条样条线中重复出现")
                print(f"   建议：添加更多控制点后再进行分割操作")
                return None

            # 🔧 全新设计：基于样条线段的分割逻辑
            print(f"🔧 应用基于样条线段的分割逻辑...")

            # 新的分割理念：不是分割点，而是分割样条线段
            # 这样可以完全避免点重复问题

            # 🔧 革命性修复：基于点复制的无重复分割逻辑
            print(f"🔧 应用基于点复制的无重复分割逻辑...")

            if endpoint_index == 0:
                # 起始端点分割：创建两条完全独立的样条线
                split_points_indices = [endpoint_index, nearest_point_index]

                # 🔧 革命性改进：剩余样条线使用最近点的副本作为起点
                # 这样两条样条线在几何上连接，但在数据结构上完全独立
                remaining_points_indices = list(range(nearest_point_index, total_points))

                print(f"🔧 起始端点分割方案（无重复版本）:")
                print(f"   分割样条线: 点{endpoint_index+1} → 点{nearest_point_index+1}")
                print(f"   剩余样条线: 点{nearest_point_index+1}(副本) → 点{total_points}")
                print(f"   连接方式: 在点{nearest_point_index+1}处几何连接，数据独立")

            else:
                # 结束端点分割：创建两条完全独立的样条线
                split_points_indices = [nearest_point_index, endpoint_index]

                # 🔧 革命性改进：剩余样条线使用最近点的副本作为终点
                remaining_points_indices = list(range(0, nearest_point_index + 1))

                print(f"🔧 结束端点分割方案（无重复版本）:")
                print(f"   分割样条线: 点{nearest_point_index+1} → 点{endpoint_index+1}")
                print(f"   剩余样条线: 点1 → 点{nearest_point_index+1}(副本)")
                print(f"   连接方式: 在点{nearest_point_index+1}处几何连接，数据独立")

            # 🔧 验证新方案：检查点重复情况
            split_indices_set = set(split_points_indices)
            remaining_indices_set = set(remaining_points_indices)
            overlap = split_indices_set.intersection(remaining_indices_set)

            print(f"🔧 分割方案验证:")
            print(f"   分割样条线索引: {split_points_indices}")
            print(f"   剩余样条线索引: {remaining_points_indices}")
            print(f"   索引重复检查: {list(overlap) if overlap else '无重复'}")

            # 🔧 重要说明：虽然索引可能重复，但我们将在后续处理中创建点的副本
            # 这样确保两条样条线在数据结构上完全独立，避免冲突
            if overlap:
                print(f"📝 说明：索引{list(overlap)}将在两条样条线中使用点的副本")
                print(f"   这确保了几何连续性的同时避免了数据冲突")

            print(f"✅ 基于点复制的无重复分割方案设计完成")

            # 🔧 重要：标记这是基于样条线段的分割
            # 在这种模式下，连接点会在两条样条线中共享，但这是有意的设计
            # 我们需要在后续处理中确保几何连续性

            print(f"🔧 样条线段分割方案:")
            print(f"   分割线段索引: {split_points_indices}")
            print(f"   剩余样条线索引: {remaining_points_indices}")

            # 🔧 检查分割的有效性
            if len(split_points_indices) < 2:
                print(f"❌ 分割线段点数不足: {len(split_points_indices)}")
                return None

            if len(remaining_points_indices) < 2:
                print(f"❌ 剩余样条线点数不足: {len(remaining_points_indices)}")
                return None

            # 🔧 特殊标记：这是样条线段分割，允许连接点共享
            segment_split_mode = True
            print(f"✅ 样条线段分割模式：允许连接点共享以保持几何连续性")

            # 提取分割后的点坐标
            split_points = []
            for idx in split_points_indices:
                split_points.append(self.points_data[idx]['coords'])

            remaining_points = []
            for idx in remaining_points_indices:
                remaining_points.append(self.points_data[idx]['coords'])

            split_data = {
                'endpoint_index': endpoint_index,
                'nearest_point_index': nearest_point_index,
                'split_points_indices': split_points_indices,
                'remaining_points_indices': remaining_points_indices,
                'split_points': split_points,
                'remaining_points': remaining_points,
                'can_split': len(split_points) >= 2 and len(remaining_points) >= 2,
                'segment_split_mode': True,  # 🔧 标记为样条线段分割模式
                'connection_point_index': nearest_point_index,  # 🔧 连接点索引
                'point_copy_mode': True,  # 🔧 新增：标记使用点复制模式
                'split_method': 'point_copy_no_overlap'  # 🔧 新增：分割方法标识
            }

            # 🔧 新增：计算第二个端点的切矢方向
            if split_data['can_split']:
                second_endpoint_tangent = self.calculate_second_endpoint_tangent(split_data)
                split_data['second_endpoint_tangent'] = second_endpoint_tangent

                if second_endpoint_tangent:
                    print(f"✅ 第二个端点切矢方向已计算: [{second_endpoint_tangent[0]:.3f}, {second_endpoint_tangent[1]:.3f}, {second_endpoint_tangent[2]:.3f}]")
                else:
                    print("⚠️ 第二个端点切矢方向计算失败，将使用默认方向")

            print(f"🔧 样条线分割数据准备完成:")
            print(f"   端点: 点{endpoint_index+1}")
            print(f"   最近点: 点{nearest_point_index+1}")
            print(f"   分割样条线点数: {len(split_points)}")
            print(f"   剩余样条线点数: {len(remaining_points)}")
            print(f"   可以分割: {'是' if split_data['can_split'] else '否'}")
            print(f"   第二个端点切矢: {'已计算' if split_data.get('second_endpoint_tangent') else '未计算'}")

            return split_data

        except Exception as e:
            print(f"❌ 准备样条线分割数据失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def detect_endpoint_tangent_setting(self, point_index):
        """
        检测用户是否在端点设置切矢，如果是则提供分割选项

        Args:
            point_index: 控制点索引

        Returns:
            bool: 如果检测到端点切矢设置并处理了分割选项，返回True
        """
        try:
            # 检查是否为端点
            if not self.is_endpoint(point_index):
                return False

            # 检查是否启用了切矢
            if not self.tangent_data[point_index].get('enabled', False):
                return False

            print(f"🎯 检测到端点{point_index+1}的切矢设置")

            # 准备分割数据
            split_data = self.prepare_spline_split_data(point_index)
            if split_data is None:
                # 🔧 修复：特殊处理三个点样条线的情况
                total_points = len(self.points_data)
                if total_points == 3:
                    print(f"⚠️ 三个点样条线不支持分割操作")
                    self.update_split_status(f"三个点样条线不支持分割，建议添加更多控制点")
                    self.show_three_points_warning()
                else:
                    print(f"❌ 端点{point_index+1}无法进行样条线分割")
                    self.update_split_status(f"端点{point_index+1}切矢已设置，但无法分割")
                return False

            if not split_data['can_split']:
                print(f"❌ 端点{point_index+1}无法进行样条线分割")
                self.update_split_status(f"端点{point_index+1}切矢已设置，但无法分割")
                return False

            # 存储分割数据供后续使用
            self.endpoint_split_data[point_index] = split_data

            # 更新界面状态
            self.update_split_status(f"检测到端点{point_index+1}可分割")

            # 🔧 修复：默认启用自动分割提示，确保第一次设置时就能触发
            auto_prompt_enabled = True
            if hasattr(self, 'auto_split_checkbox'):
                auto_prompt_enabled = self.auto_split_checkbox.isChecked()

            if auto_prompt_enabled:
                print(f"🎯 自动分割提示已启用，弹出分割选项对话框")
                # 询问用户是否要进行样条线分割
                return self.prompt_spline_split_option(point_index, split_data)
            else:
                print(f"ℹ️ 自动分割提示已禁用，用户可手动选择分割")
                # 即使禁用自动提示，也要标记为可分割状态
                self.spline_split_enabled = False  # 不自动执行，但保留分割数据
                return False

        except Exception as e:
            print(f"❌ 检测端点切矢设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def show_three_points_warning(self):
        """
        显示三个点样条线的警告提示
        """
        try:
            from PyQt5.QtWidgets import QMessageBox

            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("三个点样条线分割限制")
            msg_box.setIcon(QMessageBox.Warning)

            message = """三个点样条线不支持分割操作

原因分析：
• 三个点样条线分割时，中间点会在两条分割线中重复出现
• 这会导致几何不连续和视觉效果异常
• 可能产生切矢冲突和不自然的形状

解决方案：
• 建议添加更多控制点（至少4个点）后再进行分割
• 或者使用其他方法来实现您想要的形状控制
• 可以先完成当前的切矢设置，稍后添加控制点

是否继续保持当前的切矢设置？"""

            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.setDefaultButton(QMessageBox.Yes)

            # 显示对话框
            reply = msg_box.exec_()

            if reply == QMessageBox.Yes:
                print("✅ 用户选择保持当前切矢设置")
                return True
            else:
                print("ℹ️ 用户选择取消切矢设置")
                return False

        except Exception as e:
            print(f"❌ 显示三个点警告失败: {e}")
            import traceback
            traceback.print_exc()
            return True  # 默认继续

    def prompt_spline_split_option(self, endpoint_index, split_data):
        """
        提示用户是否要进行样条线分割

        Args:
            endpoint_index: 端点索引
            split_data: 分割数据

        Returns:
            bool: 用户选择分割返回True，否则返回False
        """
        try:
            from PyQt5.QtWidgets import QMessageBox, QPushButton

            # 创建自定义消息框
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("样条线分割选项")
            msg_box.setIcon(QMessageBox.Question)

            # 设置消息内容
            nearest_point_index = split_data['nearest_point_index']
            has_second_tangent = split_data.get('second_endpoint_tangent') is not None

            message = f"""检测到您在端点{endpoint_index+1}设置了切矢方向。

是否要将样条线分割为两条新的样条线？

分割方案：
• 第一条样条线：点{endpoint_index+1} → 点{nearest_point_index+1} ({len(split_data['split_points'])}个控制点)
• 第二条样条线：剩余的{len(split_data['remaining_points'])}个控制点

🔧 切矢控制策略：
• 保持您设置的端点{endpoint_index+1}切矢方向
• {'自动计算' if has_second_tangent else '尝试计算'}第一条样条线的第二个端点（点{nearest_point_index+1}）切矢
• 第二条样条线（剩余控制点）保持其原有切矢设置，不强制添加新切矢
• 让剩余样条线保持自然形状

选择"分割"将创建两条新样条线并删除原样条线。
选择"保持"将保持原样条线不变。"""

            msg_box.setText(message)

            # 添加自定义按钮
            split_btn = msg_box.addButton("分割样条线", QMessageBox.YesRole)
            keep_btn = msg_box.addButton("保持原样", QMessageBox.NoRole)
            cancel_btn = msg_box.addButton("取消", QMessageBox.RejectRole)

            # 🔧 修复：设置默认按钮为分割，鼓励用户使用新功能
            msg_box.setDefaultButton(split_btn)

            # 显示对话框
            msg_box.exec_()
            clicked_button = msg_box.clickedButton()

            if clicked_button == split_btn:
                print(f"✅ 用户选择分割样条线")
                # 🔧 修复：确保分割数据已正确存储
                if endpoint_index not in self.endpoint_split_data:
                    self.endpoint_split_data[endpoint_index] = split_data
                self.spline_split_enabled = True
                self.update_split_status(f"用户确认分割端点{endpoint_index+1}")
                return True
            elif clicked_button == keep_btn:
                print(f"ℹ️ 用户选择保持原样条线")
                # 🔧 修复：清除该端点的分割数据
                if endpoint_index in self.endpoint_split_data:
                    del self.endpoint_split_data[endpoint_index]
                self.spline_split_enabled = False
                self.update_split_status(f"用户取消分割端点{endpoint_index+1}")
                return False
            else:
                print(f"ℹ️ 用户取消操作")
                # 🔧 修复：清除该端点的分割数据
                if endpoint_index in self.endpoint_split_data:
                    del self.endpoint_split_data[endpoint_index]
                return False

        except Exception as e:
            print(f"❌ 显示分割选项对话框失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def execute_spline_split(self, endpoint_index):
        """
        执行样条线分割操作

        Args:
            endpoint_index: 端点索引

        Returns:
            dict: 包含分割结果的字典
        """
        try:
            if endpoint_index not in self.endpoint_split_data:
                print(f"❌ 端点{endpoint_index+1}没有分割数据")
                return None

            split_data = self.endpoint_split_data[endpoint_index]

            print(f"🔧 开始执行样条线分割...")
            print(f"   端点: 点{endpoint_index+1}")
            print(f"   最近点: 点{split_data['nearest_point_index']+1}")

            # 🔧 修复：基于点复制模式准备分割后的样条线数据
            print(f"🔧 使用点复制模式创建独立的样条线数据...")

            # 🔧 关键修复：为每条样条线创建独立的点数据副本
            split_points_copy = []
            for idx in split_data['split_points_indices']:
                point_copy = self.points_data[idx]['coords'].copy()
                split_points_copy.append(point_copy)

            remaining_points_copy = []
            for idx in split_data['remaining_points_indices']:
                point_copy = self.points_data[idx]['coords'].copy()
                remaining_points_copy.append(point_copy)

            split_result = {
                'original_points': [point['coords'] for point in self.points_data],
                'original_tangent_data': self.get_enabled_tangents(),
                'split_method': split_data.get('split_method', 'point_copy_no_overlap'),
                'point_copy_mode': split_data.get('point_copy_mode', True),
                'split_spline_1': {
                    'points': split_points_copy,  # 🔧 使用点的副本
                    'point_indices': split_data['split_points_indices'],
                    'tangent_data': {},
                    'independent_data': True  # 🔧 标记为独立数据
                },
                'split_spline_2': {
                    'points': remaining_points_copy,  # 🔧 使用点的副本
                    'point_indices': split_data['remaining_points_indices'],
                    'tangent_data': {},
                    'independent_data': True  # 🔧 标记为独立数据
                }
            }

            print(f"✅ 点复制模式数据准备完成:")
            print(f"   分割样条线1: {len(split_points_copy)}个独立点")
            print(f"   分割样条线2: {len(remaining_points_copy)}个独立点")
            print(f"   数据独立性: 完全独立，无共享引用")

            # 提取相关的切矢数据
            for i, original_index in enumerate(split_data['split_points_indices']):
                if original_index in self.get_enabled_tangents():
                    split_result['split_spline_1']['tangent_data'][i] = self.get_enabled_tangents()[original_index]

            for i, original_index in enumerate(split_data['remaining_points_indices']):
                if original_index in self.get_enabled_tangents():
                    split_result['split_spline_2']['tangent_data'][i] = self.get_enabled_tangents()[original_index]

            # 🔧 新增：处理第二个端点的切矢设置
            second_endpoint_tangent = split_data.get('second_endpoint_tangent')
            if second_endpoint_tangent:
                print(f"🔧 应用第二个端点的切矢设置...")

                # 确定第二个端点在两条分割样条线中的位置
                endpoint_index = split_data['endpoint_index']
                nearest_point_index = split_data['nearest_point_index']

                if endpoint_index == 0:
                    # 起始端点分割：第二个端点是分割线1的结束点，剩余线的起始点
                    # 在分割线1中的位置（最近点是结束点）
                    second_endpoint_pos_in_split1 = len(split_result['split_spline_1']['points']) - 1
                    # 在剩余线中的位置（最近点是起始点）
                    second_endpoint_pos_in_split2 = 0
                else:
                    # 结束端点分割：第二个端点是分割线1的起始点，剩余线的结束点
                    # 在分割线1中的位置（最近点是起始点）
                    second_endpoint_pos_in_split1 = 0
                    # 在剩余线中的位置（最近点是结束点）
                    second_endpoint_pos_in_split2 = len(split_result['split_spline_2']['points']) - 1

                # 为分割线1的第二个端点设置切矢
                split_result['split_spline_1']['tangent_data'][second_endpoint_pos_in_split1] = {
                    'enabled': True,
                    'direction': second_endpoint_tangent.copy(),
                    'length': 1.0,
                    'auto_calculated': True,
                    'source': 'second_endpoint_auto'
                }

                # 🔧 修复：不强制给剩余样条线（其他控制点重新绘制的样条线）设置切矢
                # 剩余样条线应该保持其原有的切矢设置，不添加新的强制切矢
                print(f"🔧 修复：不强制给剩余样条线设置切矢，保持其自然形状")
                print(f"   剩余样条线将使用其原有的切矢约束（如果有的话）")

                # 🔧 可选：如果用户希望在连接点有切矢约束，可以通过其他方式实现
                # 但默认情况下，其他控制点重新绘制的样条线不应该被强制设置切矢

                print(f"✅ 第二个端点切矢设置完成:")
                print(f"   分割线1端点{second_endpoint_pos_in_split1}: [{second_endpoint_tangent[0]:.3f}, {second_endpoint_tangent[1]:.3f}, {second_endpoint_tangent[2]:.3f}]")
                print(f"   剩余样条线: 保持原有切矢设置，不强制添加新切矢")

            print(f"✅ 样条线分割数据准备完成")
            print(f"   第一条样条线: {len(split_result['split_spline_1']['points'])}个点, {len(split_result['split_spline_1']['tangent_data'])}个切矢")
            print(f"   第二条样条线: {len(split_result['split_spline_2']['points'])}个点, {len(split_result['split_spline_2']['tangent_data'])}个切矢")

            return split_result

        except Exception as e:
            print(f"❌ 执行样条线分割失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_second_endpoint_tangent(self, split_data):
        """
        计算第二个端点的切矢方向，参考剩余控制点重新绘制的样条线

        Args:
            split_data: 分割数据字典

        Returns:
            list: 第二个端点的切矢方向 [x, y, z]，如果计算失败返回None
        """
        try:
            print("🔧 开始计算第二个端点的切矢方向...")

            # 获取剩余控制点（用于重新绘制样条线）
            remaining_points = split_data['remaining_points']
            remaining_indices = split_data['remaining_points_indices']

            if len(remaining_points) < 2:
                print("❌ 剩余控制点不足，无法计算切矢方向")
                return None

            print(f"📊 剩余控制点数: {len(remaining_points)}")
            for i, point in enumerate(remaining_points):
                print(f"   点{remaining_indices[i]+1}: ({point[0]:.2f}, {point[1]:.2f}, {point[2]:.2f})")

            # 确定第二个端点在剩余样条线中的位置
            endpoint_index = split_data['endpoint_index']
            nearest_point_index = split_data['nearest_point_index']

            # 第二个端点是分割样条线中与原端点相连的点（最近点）
            second_endpoint_index = nearest_point_index

            # 在剩余样条线中，最近点的位置
            if endpoint_index == 0:
                # 起始端点分割：剩余样条线是 [最近点, ..., 终点]
                # 第二个端点是剩余样条线的起始点（索引0）
                second_endpoint_pos_in_remaining = 0
            else:
                # 结束端点分割：剩余样条线是 [起点, ..., 最近点]
                # 第二个端点是剩余样条线的结束点（最后一个索引）
                second_endpoint_pos_in_remaining = len(remaining_points) - 1

            print(f"🎯 第二个端点在原样条线中的索引: {second_endpoint_index+1}")
            print(f"🎯 第二个端点在剩余样条线中的位置: {second_endpoint_pos_in_remaining}")

            # 计算切矢方向：使用剩余样条线在第二个端点处的切线方向
            tangent_direction = self.calculate_tangent_from_curve_points(
                remaining_points, second_endpoint_pos_in_remaining
            )

            if tangent_direction:
                print(f"✅ 第二个端点切矢方向计算成功: [{tangent_direction[0]:.3f}, {tangent_direction[1]:.3f}, {tangent_direction[2]:.3f}]")
                return tangent_direction
            else:
                print("❌ 第二个端点切矢方向计算失败")
                return None

        except Exception as e:
            print(f"❌ 计算第二个端点切矢方向失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def calculate_tangent_from_curve_points(self, points, endpoint_pos):
        """
        从控制点序列计算指定端点的切线方向

        Args:
            points: 控制点坐标列表
            endpoint_pos: 端点在点序列中的位置（0表示起始点，-1或len-1表示结束点）

        Returns:
            list: 切线方向向量 [x, y, z]，如果计算失败返回None
        """
        try:
            if len(points) < 2:
                return None

            if endpoint_pos == 0:
                # 起始端点：使用前两个点计算方向
                p1 = points[0]
                p2 = points[1]
                direction = [p2[0] - p1[0], p2[1] - p1[1], p2[2] - p1[2]]
            elif endpoint_pos == len(points) - 1 or endpoint_pos == -1:
                # 结束端点：使用后两个点计算方向
                p1 = points[-2]
                p2 = points[-1]
                direction = [p2[0] - p1[0], p2[1] - p1[1], p2[2] - p1[2]]
            else:
                print(f"❌ 无效的端点位置: {endpoint_pos}")
                return None

            # 归一化方向向量
            import math
            length = math.sqrt(sum(d * d for d in direction))
            if length > 1e-10:
                direction = [d / length for d in direction]
                return direction
            else:
                print("❌ 方向向量长度为零，无法归一化")
                return None

        except Exception as e:
            print(f"❌ 从控制点计算切线方向失败: {e}")
            return None

    def get_spline_split_results(self):
        """
        获取所有的样条线分割结果

        Returns:
            list: 包含所有分割结果的列表
        """
        try:
            print(f"🔍 开始获取样条线分割结果...")
            print(f"   spline_split_enabled: {getattr(self, 'spline_split_enabled', False)}")
            print(f"   endpoint_split_data: {len(getattr(self, 'endpoint_split_data', {}))}")

            split_results = []

            for endpoint_index, split_data in self.endpoint_split_data.items():
                print(f"🔍 处理端点{endpoint_index+1}的分割数据...")

                # 🔧 修复：不依赖spline_split_enabled标志，直接执行分割
                split_result = self.execute_spline_split(endpoint_index)
                if split_result:
                    split_results.append(split_result)
                    print(f"✅ 添加端点{endpoint_index+1}的分割结果")
                else:
                    print(f"❌ 端点{endpoint_index+1}分割结果生成失败")

            print(f"🔧 总共获取到{len(split_results)}个分割结果")

            # 🔧 调试：输出分割结果的详细信息
            for i, result in enumerate(split_results):
                print(f"   分割结果{i+1}:")
                print(f"     原始点数: {len(result['original_points'])}")
                print(f"     分割线1点数: {len(result['split_spline_1']['points'])}")
                print(f"     分割线2点数: {len(result['split_spline_2']['points'])}")

            return split_results

        except Exception as e:
            print(f"❌ 获取样条线分割结果失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def has_spline_splits(self):
        """
        检查是否有样条线分割操作

        Returns:
            bool: 如果有分割操作返回True
        """
        return self.spline_split_enabled and len(self.endpoint_split_data) > 0

    def manual_check_split(self):
        """
        手动检查样条线分割选项
        """
        try:
            print("🔧 手动检查样条线分割选项...")

            # 🔧 修复：首先检查三个点样条线的情况
            total_points = len(self.points_data)
            if total_points == 3:
                self.update_split_status("三个点样条线不支持分割")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "分割限制",
                    "三个点样条线不支持分割操作。\n\n"
                    "原因：分割会导致中间点在两条样条线中重复出现，"
                    "可能产生几何不连续和视觉效果异常。\n\n"
                    "建议：添加更多控制点（至少4个点）后再进行分割。")
                return

            # 检查所有端点是否启用了切矢
            endpoint_tangents = []

            for point_index in [0, total_points - 1]:  # 检查首尾端点
                if point_index < len(self.tangent_data) and self.tangent_data[point_index].get('enabled', False):
                    endpoint_tangents.append(point_index)

            if not endpoint_tangents:
                self.update_split_status("未检测到端点切矢设置")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "分割检查", "当前没有端点启用切矢，无法进行样条线分割。")
                return

            # 为每个启用切矢的端点检查分割选项
            split_options = []
            for endpoint_index in endpoint_tangents:
                split_data = self.prepare_spline_split_data(endpoint_index)
                if split_data and split_data['can_split']:
                    split_options.append((endpoint_index, split_data))

            if not split_options:
                self.update_split_status("端点切矢已设置，但无法分割")
                QMessageBox.information(self, "分割检查", "虽然端点启用了切矢，但当前样条线无法进行分割。")
                return

            # 显示分割选项
            self.show_split_options_dialog(split_options)

        except Exception as e:
            print(f"❌ 手动检查分割失败: {e}")
            import traceback
            traceback.print_exc()

    def show_split_options_dialog(self, split_options):
        """
        显示分割选项对话框

        Args:
            split_options: 分割选项列表
        """
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QListWidget, QListWidgetItem, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("样条线分割选项")
            dialog.setModal(True)
            dialog.resize(400, 300)

            layout = QVBoxLayout(dialog)

            # 说明文字
            info_label = QLabel(f"检测到{len(split_options)}个可分割的端点，请选择要分割的端点：")
            layout.addWidget(info_label)

            # 分割选项列表
            options_list = QListWidget()
            for endpoint_index, split_data in split_options:
                nearest_index = split_data['nearest_point_index']
                item_text = f"端点{endpoint_index+1} → 最近点{nearest_index+1} (分割为{len(split_data['split_points'])}点 + {len(split_data['remaining_points'])}点)"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, (endpoint_index, split_data))
                options_list.addItem(item)

            layout.addWidget(options_list)

            # 按钮
            button_layout = QHBoxLayout()
            split_btn = QPushButton("执行分割")
            cancel_btn = QPushButton("取消")

            button_layout.addStretch()
            button_layout.addWidget(split_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)

            # 连接信号
            def on_split():
                selected_items = options_list.selectedItems()
                if selected_items:
                    endpoint_index, split_data = selected_items[0].data(Qt.UserRole)
                    self.endpoint_split_data[endpoint_index] = split_data
                    self.spline_split_enabled = True
                    self.update_split_status(f"已选择端点{endpoint_index+1}进行分割")
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "警告", "请选择一个分割选项")

            split_btn.clicked.connect(on_split)
            cancel_btn.clicked.connect(dialog.reject)

            dialog.exec_()

        except Exception as e:
            print(f"❌ 显示分割选项对话框失败: {e}")
            import traceback
            traceback.print_exc()

    def update_split_status(self, status_text):
        """
        更新分割状态显示

        Args:
            status_text: 状态文本
        """
        try:
            if hasattr(self, 'split_status_label'):
                self.split_status_label.setText(f"分割状态: {status_text}")

                # 更新预览信息
                if hasattr(self, 'split_preview_text'):
                    if self.endpoint_split_data:
                        preview_info = []
                        for endpoint_index, split_data in self.endpoint_split_data.items():
                            nearest_index = split_data['nearest_point_index']
                            has_second_tangent = split_data.get('second_endpoint_tangent') is not None

                            preview_info.append(f"端点{endpoint_index+1} → 点{nearest_index+1}")
                            preview_info.append(f"  分割线1: {len(split_data['split_points'])}个控制点")
                            preview_info.append(f"    - 端点{endpoint_index+1}: 保持用户设置的切矢")
                            preview_info.append(f"    - 点{nearest_index+1}: {'自动计算切矢' if has_second_tangent else '尝试计算切矢'}")
                            preview_info.append(f"  分割线2: {len(split_data['remaining_points'])}个控制点")
                            preview_info.append(f"    - 保持原有切矢设置，不强制添加新切矢")

                        self.split_preview_text.setPlainText("\n".join(preview_info))
                    else:
                        self.split_preview_text.setPlainText("暂无分割预览信息")

        except Exception as e:
            print(f"❌ 更新分割状态失败: {e}")

    def accept(self):
        """
        重写确认方法，处理样条线分割功能
        """
        try:
            print("🎯 切矢编辑器确认操作开始")

            # 🔧 修复：重新检查端点切矢设置，确保能检测到分割机会
            self.check_all_endpoint_tangents()

            # 检查是否有样条线分割操作
            has_splits = self.has_spline_splits()
            print(f"🔍 分割检查结果: has_splits={has_splits}, endpoint_split_data={len(self.endpoint_split_data)}")

            if has_splits or len(self.endpoint_split_data) > 0:
                print("🔧 检测到样条线分割操作，准备执行分割")

                # 🔧 修复：简化确认流程，直接执行分割
                from PyQt5.QtWidgets import QMessageBox

                split_count = len(self.endpoint_split_data)
                if split_count > 0:
                    reply = QMessageBox.question(
                        self,
                        "确认样条线分割",
                        f"检测到{split_count}个端点设置了切矢。\n\n"
                        "是否要将样条线分割为多条新样条线？\n"
                        "分割后将创建新的样条线并删除原样条线。",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes  # 🔧 修复：默认选择Yes
                    )

                    if reply == QMessageBox.Yes:
                        print("✅ 用户确认执行样条线分割")
                        # 设置分割标志，让父窗口处理分割
                        self.spline_split_enabled = True
                    else:
                        print("ℹ️ 用户取消样条线分割")
                        # 清除分割数据，继续常规的切矢更新
                        self.endpoint_split_data.clear()
                        self.spline_split_enabled = False
                else:
                    print("ℹ️ 没有检测到有效的分割数据")
                    self.spline_split_enabled = False

            # 调用父类的accept方法
            super().accept()

        except Exception as e:
            print(f"❌ 处理确认操作失败: {e}")
            import traceback
            traceback.print_exc()
            # 出错时也调用父类方法
            super().accept()

    def check_all_endpoint_tangents(self):
        """
        检查所有端点的切矢设置，确保能检测到分割机会
        """
        try:
            print("🔍 重新检查所有端点的切矢设置...")

            total_points = len(self.points_data)
            endpoint_indices = [0, total_points - 1]  # 首尾端点

            for endpoint_index in endpoint_indices:
                if endpoint_index < len(self.tangent_data):
                    tangent_info = self.tangent_data[endpoint_index]
                    is_enabled = tangent_info.get('enabled', False)

                    print(f"🔍 检查端点{endpoint_index+1}: 切矢启用={is_enabled}")

                    if is_enabled:
                        # 准备分割数据
                        split_data = self.prepare_spline_split_data(endpoint_index)
                        if split_data and split_data['can_split']:
                            print(f"✅ 端点{endpoint_index+1}可以分割")
                            self.endpoint_split_data[endpoint_index] = split_data
                            self.update_split_status(f"检测到端点{endpoint_index+1}可分割")
                        else:
                            print(f"❌ 端点{endpoint_index+1}无法分割")

            split_count = len(self.endpoint_split_data)
            print(f"🔍 检查完成，共发现{split_count}个可分割端点")

        except Exception as e:
            print(f"❌ 检查端点切矢设置失败: {e}")
            import traceback
            traceback.print_exc()

    def calculate_characteristic_length(self):
        """计算样条线的特征长度"""
        try:
            # 计算控制点之间的平均距离
            total_distance = 0.0
            count = 0

            for i in range(len(self.points_data) - 1):
                p1 = self.points_data[i]['coords']
                p2 = self.points_data[i + 1]['coords']

                distance = ((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2 + (p2[2] - p1[2])**2)**0.5
                total_distance += distance
                count += 1

            if count > 0:
                avg_distance = total_distance / count
                # 特征长度为平均距离的1/3，这是一个经验值
                characteristic_length = avg_distance / 3.0
                print(f"  📐 样条线特征长度: {characteristic_length:.2f}")
                return characteristic_length
            else:
                return 1.0  # 默认值

        except Exception as e:
            print(f"  ❌ 计算特征长度失败: {e}")
            return 1.0

    def calculate_adaptive_length(self, point_index, characteristic_length):
        """计算控制点的自适应切矢长度"""
        try:
            total_points = len(self.points_data)

            # 基础长度为特征长度
            base_length = characteristic_length

            # 根据控制点位置调整
            if point_index == 0 or point_index == total_points - 1:
                # 端点：使用较小的切矢长度，避免过度影响
                length_factor = 0.8
                print(f"    🎯 端点策略，长度因子: {length_factor}")
            else:
                # 中间点：使用标准长度
                length_factor = 1.0
                print(f"    🎯 中间点策略，长度因子: {length_factor}")

            # 根据相邻控制点的距离进一步调整
            if point_index > 0:
                prev_point = self.points_data[point_index - 1]['coords']
                curr_point = self.points_data[point_index]['coords']
                prev_distance = ((curr_point[0] - prev_point[0])**2 +
                               (curr_point[1] - prev_point[1])**2 +
                               (curr_point[2] - prev_point[2])**2)**0.5
            else:
                prev_distance = characteristic_length

            if point_index < total_points - 1:
                curr_point = self.points_data[point_index]['coords']
                next_point = self.points_data[point_index + 1]['coords']
                next_distance = ((next_point[0] - curr_point[0])**2 +
                               (next_point[1] - curr_point[1])**2 +
                               (next_point[2] - curr_point[2])**2)**0.5
            else:
                next_distance = characteristic_length

            # 使用相邻距离的平均值来调整长度
            local_characteristic = (prev_distance + next_distance) / 2.0
            distance_factor = local_characteristic / characteristic_length

            # 限制距离因子的范围，避免极端值
            distance_factor = max(0.5, min(2.0, distance_factor))

            adaptive_length = base_length * length_factor * distance_factor

            # 确保长度在合理范围内
            adaptive_length = max(0.1, min(adaptive_length, characteristic_length * 2.0))

            return adaptive_length

        except Exception as e:
            print(f"    ❌ 计算自适应长度失败: {e}")
            return 1.0

    # 🔧 注意：这个方法已被新设计替代，暂时保留以避免错误
    def reselect_curve(self):
        """重新选择参考曲线（兼容性方法）"""
        print("🔄 重新选择参考曲线（使用新设计的方法）")
        if hasattr(self, 'clear_reference_curve_selection'):
            self.clear_reference_curve_selection()
        if hasattr(self, 'start_reference_curve_selection'):
            self.start_reference_curve_selection()

    def reset_curve_selection_ui(self):
        """重置曲线选择UI状态（兼容性方法）"""
        try:
            print("🔧 重置曲线选择UI状态（使用新设计的方法）")

            # 使用新设计的方法
            if hasattr(self, 'clear_reference_curve_selection'):
                self.clear_reference_curve_selection()

            # 清除参考曲线数据
            if hasattr(self, 'reference_curve_data'):
                self.reference_curve_data = None

            print("✅ 曲线选择UI状态已重置")

        except Exception as e:
            print(f"❌ 重置曲线选择UI失败: {e}")

    def highlight_tangent_enable_column(self):
        """高亮显示切矢启用列，引导用户操作（兼容性方法）"""
        try:
            print("🎨 高亮显示切矢启用列，引导用户操作")

            # 🔧 修复：使用新设计的数据结构
            if not hasattr(self, 'tangent_table') or not hasattr(self, 'points_data'):
                print("❌ 表格或数据不存在，跳过高亮显示")
                return

            # 高亮切矢启用列的表头（第5列是启用切矢列）
            if self.tangent_table.columnCount() > 5:
                header_item = self.tangent_table.horizontalHeaderItem(5)
                if header_item:
                    header_item.setText("✨ 启用切矢 ✨")

            # 高亮所有切矢启用复选框
            for row in range(len(self.points_data)):
                if self.tangent_table.columnCount() > 5:
                    checkbox = self.tangent_table.cellWidget(row, 5)
                    if checkbox:
                        # 设置复选框高亮样式
                        checkbox.setStyleSheet(
                            "QCheckBox { "
                            "background-color: #fff3cd; "
                            "padding: 5px; "
                            "border: 2px solid #ffeaa7; "
                            "border-radius: 3px; "
                            "font-weight: bold; "
                            "}"
                        )

            print("✅ 切矢启用列高亮显示完成")

            # 3秒后恢复正常样式
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(3000, self.restore_table_normal_style)

        except Exception as e:
            print(f"❌ 高亮显示切矢启用列失败: {e}")
            import traceback
            traceback.print_exc()

    def restore_normal_table_style(self):
        """恢复表格的正常样式（兼容性方法）"""
        try:
            print("🎨 恢复表格正常样式")

            # 🔧 修复：使用新设计的数据结构
            if not hasattr(self, 'tangent_table') or not hasattr(self, 'points_data'):
                print("❌ 表格或数据不存在，跳过样式恢复")
                return

            # 恢复表头文本
            if self.tangent_table.columnCount() > 5:
                header_item = self.tangent_table.horizontalHeaderItem(5)
                if header_item:
                    header_item.setText("启用切矢")

            # 清除表头样式
            if hasattr(self.tangent_table, 'horizontalHeader'):
                self.tangent_table.horizontalHeader().setStyleSheet("")

            # 恢复复选框样式
            for row in range(len(self.points_data)):
                if self.tangent_table.columnCount() > 5:
                    checkbox = self.tangent_table.cellWidget(row, 5)
                    if checkbox:
                        checkbox.setStyleSheet("")

            print("✅ 表格样式恢复完成")

        except Exception as e:
            print(f"❌ 恢复表格样式失败: {e}")

    def check_and_update_curve_tangent_button(self):
        """检查并更新曲线切矢按钮状态（兼容性方法）"""
        try:
            print("🔧 检查切矢按钮状态（使用新设计的逻辑）")

            # 🔧 修复：使用新设计的数据结构和控件
            if not hasattr(self, 'reference_curve_data') or not self.reference_curve_data:
                return

            # 检查是否有选中的控制点
            if hasattr(self, 'selected_control_points') and self.selected_control_points:
                print(f"🎯 检测到{len(self.selected_control_points)}个选中的控制点")

                # 更新应用按钮状态
                if hasattr(self, 'apply_tangent_btn'):
                    self.apply_tangent_btn.setEnabled(True)
                    print("✅ 应用切矢按钮已启用")
            else:
                print("ℹ️ 当前没有选中的控制点")

        except Exception as e:
            print(f"❌ 检查和更新按钮状态失败: {e}")


class CoordinateEditDialog(QDialog):
    """坐标编辑对话框"""

    def __init__(self, point_number, coordinates, parent=None):
        """
        初始化坐标编辑对话框

        Args:
            point_number: 点编号
            coordinates: 当前坐标 [x, y, z]
            parent: 父窗口
        """
        super().__init__(parent)
        self.point_number = point_number
        self.coordinates = coordinates.copy()

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"编辑控制点{self.point_number}坐标")
        self.setModal(True)
        self.resize(400, 300)

        # 主布局
        main_layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel(f"控制点{self.point_number}坐标编辑")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 坐标输入区域
        coord_group = QGroupBox("坐标设置")
        coord_layout = QFormLayout(coord_group)

        # X坐标
        self.x_spinbox = QDoubleSpinBox()
        self.x_spinbox.setRange(-10000.0, 10000.0)
        self.x_spinbox.setSingleStep(1.0)
        self.x_spinbox.setDecimals(3)
        self.x_spinbox.setValue(self.coordinates[0])
        coord_layout.addRow("X坐标:", self.x_spinbox)

        # Y坐标
        self.y_spinbox = QDoubleSpinBox()
        self.y_spinbox.setRange(-10000.0, 10000.0)
        self.y_spinbox.setSingleStep(1.0)
        self.y_spinbox.setDecimals(3)
        self.y_spinbox.setValue(self.coordinates[1])
        coord_layout.addRow("Y坐标:", self.y_spinbox)

        # Z坐标
        self.z_spinbox = QDoubleSpinBox()
        self.z_spinbox.setRange(-10000.0, 10000.0)
        self.z_spinbox.setSingleStep(1.0)
        self.z_spinbox.setDecimals(3)
        self.z_spinbox.setValue(self.coordinates[2])
        coord_layout.addRow("Z坐标:", self.z_spinbox)

        main_layout.addWidget(coord_group)

        # 快捷操作区域
        shortcuts_group = QGroupBox("快捷操作")
        shortcuts_layout = QVBoxLayout(shortcuts_group)

        # 重置按钮
        reset_layout = QHBoxLayout()
        reset_to_origin_btn = QPushButton("重置到原点(0,0,0)")
        reset_to_origin_btn.clicked.connect(self.reset_to_origin)
        reset_layout.addWidget(reset_to_origin_btn)

        reset_to_original_btn = QPushButton("恢复原始坐标")
        reset_to_original_btn.clicked.connect(self.reset_to_original)
        reset_layout.addWidget(reset_to_original_btn)

        shortcuts_layout.addLayout(reset_layout)

        # 坐标偏移
        offset_layout = QHBoxLayout()
        offset_layout.addWidget(QLabel("坐标偏移:"))

        self.offset_spinbox = QDoubleSpinBox()
        self.offset_spinbox.setRange(-1000.0, 1000.0)
        self.offset_spinbox.setSingleStep(1.0)
        self.offset_spinbox.setDecimals(2)
        self.offset_spinbox.setValue(10.0)
        offset_layout.addWidget(self.offset_spinbox)

        # 偏移方向按钮
        offset_x_pos_btn = QPushButton("+X")
        offset_x_pos_btn.clicked.connect(lambda: self.apply_offset(0, 1))
        offset_layout.addWidget(offset_x_pos_btn)

        offset_x_neg_btn = QPushButton("-X")
        offset_x_neg_btn.clicked.connect(lambda: self.apply_offset(0, -1))
        offset_layout.addWidget(offset_x_neg_btn)

        offset_y_pos_btn = QPushButton("+Y")
        offset_y_pos_btn.clicked.connect(lambda: self.apply_offset(1, 1))
        offset_layout.addWidget(offset_y_pos_btn)

        offset_y_neg_btn = QPushButton("-Y")
        offset_y_neg_btn.clicked.connect(lambda: self.apply_offset(1, -1))
        offset_layout.addWidget(offset_y_neg_btn)

        offset_z_pos_btn = QPushButton("+Z")
        offset_z_pos_btn.clicked.connect(lambda: self.apply_offset(2, 1))
        offset_layout.addWidget(offset_z_pos_btn)

        offset_z_neg_btn = QPushButton("-Z")
        offset_z_neg_btn.clicked.connect(lambda: self.apply_offset(2, -1))
        offset_layout.addWidget(offset_z_neg_btn)

        shortcuts_layout.addLayout(offset_layout)
        main_layout.addWidget(shortcuts_group)

        # 按钮区域
        buttons_layout = QHBoxLayout()

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        main_layout.addLayout(buttons_layout)

    def reset_to_origin(self):
        """重置到原点"""
        self.x_spinbox.setValue(0.0)
        self.y_spinbox.setValue(0.0)
        self.z_spinbox.setValue(0.0)

    def reset_to_original(self):
        """恢复原始坐标"""
        self.x_spinbox.setValue(self.coordinates[0])
        self.y_spinbox.setValue(self.coordinates[1])
        self.z_spinbox.setValue(self.coordinates[2])

    def apply_offset(self, axis, direction):
        """应用坐标偏移"""
        offset_value = self.offset_spinbox.value() * direction

        if axis == 0:  # X轴
            current_value = self.x_spinbox.value()
            self.x_spinbox.setValue(current_value + offset_value)
        elif axis == 1:  # Y轴
            current_value = self.y_spinbox.value()
            self.y_spinbox.setValue(current_value + offset_value)
        elif axis == 2:  # Z轴
            current_value = self.z_spinbox.value()
            self.z_spinbox.setValue(current_value + offset_value)

    def get_coordinates(self):
        """获取编辑后的坐标"""
        return [
            self.x_spinbox.value(),
            self.y_spinbox.value(),
            self.z_spinbox.value()
        ]


