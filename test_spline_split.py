#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样条线分割功能测试脚本
用于验证端点切矢设置时的样条线分割功能
"""

import sys
import os
import math

def test_endpoint_detection():
    """测试端点检测功能"""
    print("=" * 60)
    print("🧪 测试端点检测功能")
    print("=" * 60)
    
    try:
        # 模拟切矢编辑器的端点检测
        from tangent_editor import TangentEditor
        
        # 创建测试点数据
        test_points = [
            {'coords': [0, 0, 0], 'description': '控制点1'},
            {'coords': [100, 50, 0], 'description': '控制点2'},
            {'coords': [200, 100, 0], 'description': '控制点3'},
            {'coords': [300, 150, 0], 'description': '控制点4'},
            {'coords': [400, 200, 0], 'description': '控制点5'}
        ]
        
        # 创建编辑器实例（不显示界面）
        editor = TangentEditor(test_points, None)
        
        # 测试端点检测
        print(f"📊 测试数据: {len(test_points)}个控制点")
        
        for i in range(len(test_points)):
            is_endpoint = editor.is_endpoint(i)
            expected = (i == 0 or i == len(test_points) - 1)
            status = "✅" if is_endpoint == expected else "❌"
            print(f"{status} 点{i+1}: 检测结果={is_endpoint}, 预期={expected}")
        
        print("✅ 端点检测功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 端点检测功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nearest_point_finding():
    """测试最近控制点查找功能"""
    print("=" * 60)
    print("🧪 测试最近控制点查找功能")
    print("=" * 60)
    
    try:
        from tangent_editor import TangentEditor
        
        # 创建测试点数据（线性排列）
        test_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 0, 0], 'description': '控制点2'},
            {'coords': [200, 0, 0], 'description': '控制点3'},
            {'coords': [300, 0, 0], 'description': '控制点4'},
            {'coords': [400, 0, 0], 'description': '端点5'}
        ]
        
        editor = TangentEditor(test_points, None)
        
        # 测试起始端点的最近点查找
        print("📊 测试起始端点(点1)的最近点查找:")
        nearest_to_start = editor.find_nearest_control_point(0)
        expected_start = 1  # 应该是点2
        status_start = "✅" if nearest_to_start == expected_start else "❌"
        print(f"{status_start} 起始端点最近点: 找到点{nearest_to_start+1 if nearest_to_start is not None else 'None'}, 预期点{expected_start+1}")
        
        # 测试结束端点的最近点查找
        print("📊 测试结束端点(点5)的最近点查找:")
        nearest_to_end = editor.find_nearest_control_point(4)
        expected_end = 3  # 应该是点4
        status_end = "✅" if nearest_to_end == expected_end else "❌"
        print(f"{status_end} 结束端点最近点: 找到点{nearest_to_end+1 if nearest_to_end is not None else 'None'}, 预期点{expected_end+1}")
        
        print("✅ 最近控制点查找功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 最近控制点查找功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_split_data_preparation():
    """测试样条线分割数据准备功能"""
    print("=" * 60)
    print("🧪 测试样条线分割数据准备功能")
    print("=" * 60)
    
    try:
        from tangent_editor import TangentEditor
        
        # 创建测试点数据
        test_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 50, 0], 'description': '控制点2'},
            {'coords': [200, 100, 0], 'description': '控制点3'},
            {'coords': [300, 150, 0], 'description': '控制点4'},
            {'coords': [400, 200, 0], 'description': '端点5'}
        ]
        
        editor = TangentEditor(test_points, None)
        
        # 测试起始端点的分割数据准备
        print("📊 测试起始端点(点1)的分割数据准备:")
        split_data_start = editor.prepare_spline_split_data(0)
        
        if split_data_start:
            print(f"✅ 分割数据准备成功:")
            print(f"   端点索引: {split_data_start['endpoint_index']}")
            print(f"   最近点索引: {split_data_start['nearest_point_index']}")
            print(f"   分割样条线点数: {len(split_data_start['split_points'])}")
            print(f"   剩余样条线点数: {len(split_data_start['remaining_points'])}")
            print(f"   可以分割: {split_data_start['can_split']}")
        else:
            print("❌ 分割数据准备失败")
            return False
        
        # 测试结束端点的分割数据准备
        print("📊 测试结束端点(点5)的分割数据准备:")
        split_data_end = editor.prepare_spline_split_data(4)
        
        if split_data_end:
            print(f"✅ 分割数据准备成功:")
            print(f"   端点索引: {split_data_end['endpoint_index']}")
            print(f"   最近点索引: {split_data_end['nearest_point_index']}")
            print(f"   分割样条线点数: {len(split_data_end['split_points'])}")
            print(f"   剩余样条线点数: {len(split_data_end['remaining_points'])}")
            print(f"   可以分割: {split_data_end['can_split']}")
        else:
            print("❌ 分割数据准备失败")
            return False
        
        print("✅ 样条线分割数据准备功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 样条线分割数据准备功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print("=" * 60)
    print("🧪 测试边界情况")
    print("=" * 60)
    
    try:
        from tangent_editor import TangentEditor
        
        # 测试1: 只有2个点的情况
        print("📊 测试1: 只有2个点的样条线")
        two_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 0, 0], 'description': '端点2'}
        ]
        
        editor_2 = TangentEditor(two_points, None)
        split_data_2 = editor_2.prepare_spline_split_data(0)
        
        if split_data_2:
            can_split = split_data_2['can_split']
            print(f"   结果: 可以分割={can_split} (预期=False)")
            status_2 = "✅" if not can_split else "❌"
            print(f"{status_2} 2点样条线分割测试")
        else:
            print("✅ 2点样条线正确拒绝分割")
        
        # 测试2: 3个点的情况
        print("📊 测试2: 只有3个点的样条线")
        three_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 50, 0], 'description': '中点'},
            {'coords': [200, 0, 0], 'description': '端点2'}
        ]
        
        editor_3 = TangentEditor(three_points, None)
        split_data_3 = editor_3.prepare_spline_split_data(0)
        
        if split_data_3:
            can_split = split_data_3['can_split']
            print(f"   结果: 可以分割={can_split}")
            print(f"   分割线1点数: {len(split_data_3['split_points'])}")
            print(f"   分割线2点数: {len(split_data_3['remaining_points'])}")
            status_3 = "✅" if can_split else "❌"
            print(f"{status_3} 3点样条线分割测试")
        else:
            print("❌ 3点样条线分割数据准备失败")
        
        print("✅ 边界情况测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始样条线分割功能全面测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("端点检测功能", test_endpoint_detection()))
    test_results.append(("最近控制点查找功能", test_nearest_point_finding()))
    test_results.append(("分割数据准备功能", test_split_data_preparation()))
    test_results.append(("边界情况测试", test_edge_cases()))
    
    # 汇总测试结果
    print("=" * 80)
    print("📊 测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"🎯 测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！样条线分割功能实现正确。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    # 确保能够导入必要的模块
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 运行测试
        success = run_all_tests()
        
        if success:
            print("\n🎊 样条线分割功能开发完成！")
            print("📝 功能说明:")
            print("   1. 双击样条线打开切矢编辑器")
            print("   2. 在端点启用切矢时，系统会自动检测分割选项")
            print("   3. 可选择将样条线分割为两条新样条线")
            print("   4. 分割后的样条线保持原有的切矢约束")
        else:
            print("\n⚠️ 测试发现问题，请检查实现。")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保在正确的项目目录中运行此测试脚本。")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
