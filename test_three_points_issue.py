#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三个点样条线分割问题分析和测试脚本
"""

import sys
import os

def analyze_three_points_split():
    """分析三个点样条线的分割逻辑"""
    print("=" * 60)
    print("🔍 分析三个点样条线的分割逻辑")
    print("=" * 60)
    
    # 模拟三个点的情况
    test_points = [
        {'coords': [0, 0, 0], 'description': '端点1'},
        {'coords': [100, 50, 0], 'description': '中点2'},
        {'coords': [200, 0, 0], 'description': '端点3'}
    ]
    
    total_points = len(test_points)
    print(f"📊 测试样条线: {total_points}个控制点")
    for i, point in enumerate(test_points):
        print(f"   点{i+1}: {point['coords']}")
    
    print("\n🔍 分析起始端点分割 (端点1):")
    endpoint_index = 0
    nearest_point_index = 1  # 最近的点是中点2
    
    # 按照当前逻辑计算分割
    if endpoint_index == 0:
        # 起始端点：分割点为 [端点, 最近点]，剩余点为 [最近点, ..., 终点]
        split_points_indices = [endpoint_index, nearest_point_index]  # [0, 1]
        remaining_points_indices = list(range(nearest_point_index, total_points))  # [1, 2]
    
    print(f"   分割点索引: {split_points_indices}")
    print(f"   剩余点索引: {remaining_points_indices}")
    print(f"   分割点数: {len(split_points_indices)}")
    print(f"   剩余点数: {len(remaining_points_indices)}")
    
    # 检查分割条件
    can_split = len(split_points_indices) >= 2 and len(remaining_points_indices) >= 2
    print(f"   可以分割: {can_split}")
    
    if can_split:
        print("   ✅ 分割条件满足")
        print(f"   分割线1: 点{split_points_indices[0]+1} → 点{split_points_indices[1]+1}")
        print(f"   分割线2: 点{remaining_points_indices[0]+1} → 点{remaining_points_indices[1]+1}")
        
        # 🔍 问题分析：点重复
        if split_points_indices[1] == remaining_points_indices[0]:
            print(f"   ⚠️ 发现问题：点{split_points_indices[1]+1}在两条分割线中都存在！")
            print(f"   这会导致两条样条线在同一个点相交，可能产生不一样的效果")
    else:
        print("   ❌ 分割条件不满足")
    
    print("\n🔍 分析结束端点分割 (端点3):")
    endpoint_index = 2
    nearest_point_index = 1  # 最近的点还是中点2
    
    # 按照当前逻辑计算分割
    if endpoint_index == 2:
        # 结束端点：分割点为 [最近点, 端点]，剩余点为 [起点, ..., 最近点]
        split_points_indices = [nearest_point_index, endpoint_index]  # [1, 2]
        remaining_points_indices = list(range(0, nearest_point_index + 1))  # [0, 1]
    
    print(f"   分割点索引: {split_points_indices}")
    print(f"   剩余点索引: {remaining_points_indices}")
    print(f"   分割点数: {len(split_points_indices)}")
    print(f"   剩余点数: {len(remaining_points_indices)}")
    
    # 检查分割条件
    can_split = len(split_points_indices) >= 2 and len(remaining_points_indices) >= 2
    print(f"   可以分割: {can_split}")
    
    if can_split:
        print("   ✅ 分割条件满足")
        print(f"   分割线1: 点{split_points_indices[0]+1} → 点{split_points_indices[1]+1}")
        print(f"   分割线2: 点{remaining_points_indices[0]+1} → 点{remaining_points_indices[1]+1}")
        
        # 🔍 问题分析：点重复
        if split_points_indices[0] == remaining_points_indices[1]:
            print(f"   ⚠️ 发现问题：点{split_points_indices[0]+1}在两条分割线中都存在！")
            print(f"   这会导致两条样条线在同一个点相交，可能产生不一样的效果")
    else:
        print("   ❌ 分割条件不满足")

def identify_three_points_problems():
    """识别三个点样条线的具体问题"""
    print("\n" + "=" * 60)
    print("🔍 识别三个点样条线的具体问题")
    print("=" * 60)
    
    problems = [
        "1. 点重复问题：中间点在两条分割线中都存在",
        "2. 几何连续性：两条样条线在同一点相交但可能不连续",
        "3. 切矢冲突：同一个点可能被设置不同的切矢方向",
        "4. 视觉效果：可能出现不自然的形状或断裂",
        "5. 数学定义：两条样条线共享端点但定义不同"
    ]
    
    for problem in problems:
        print(f"   ❌ {problem}")
    
    print("\n🔧 可能的解决方案:")
    solutions = [
        "1. 特殊处理三个点的情况，不允许分割",
        "2. 修改分割逻辑，避免点重复",
        "3. 在共享点处确保切矢一致性",
        "4. 提供专门的三点样条线处理方法",
        "5. 给用户明确的警告和建议"
    ]
    
    for solution in solutions:
        print(f"   ✅ {solution}")

def test_current_implementation():
    """测试当前实现在三个点情况下的行为"""
    print("\n" + "=" * 60)
    print("🧪 测试当前实现在三个点情况下的行为")
    print("=" * 60)
    
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        from tangent_editor import TangentEditor
        
        # 创建三个点的测试数据
        test_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 50, 0], 'description': '中点2'},
            {'coords': [200, 0, 0], 'description': '端点3'}
        ]
        
        editor = TangentEditor(test_points, None)
        
        # 测试起始端点分割
        print("📊 测试起始端点分割:")
        split_data_start = editor.prepare_spline_split_data(0)
        
        if split_data_start:
            print(f"   分割数据准备成功")
            print(f"   可以分割: {split_data_start['can_split']}")
            print(f"   分割点索引: {split_data_start['split_points_indices']}")
            print(f"   剩余点索引: {split_data_start['remaining_points_indices']}")
            
            # 检查点重复
            split_indices = set(split_data_start['split_points_indices'])
            remaining_indices = set(split_data_start['remaining_points_indices'])
            overlap = split_indices.intersection(remaining_indices)
            
            if overlap:
                print(f"   ⚠️ 发现重复点索引: {list(overlap)}")
                print(f"   这可能导致不一样的效果")
            else:
                print(f"   ✅ 没有重复点")
        else:
            print("   ❌ 分割数据准备失败")
        
        # 测试结束端点分割
        print("\n📊 测试结束端点分割:")
        split_data_end = editor.prepare_spline_split_data(2)
        
        if split_data_end:
            print(f"   分割数据准备成功")
            print(f"   可以分割: {split_data_end['can_split']}")
            print(f"   分割点索引: {split_data_end['split_points_indices']}")
            print(f"   剩余点索引: {split_data_end['remaining_points_indices']}")
            
            # 检查点重复
            split_indices = set(split_data_end['split_points_indices'])
            remaining_indices = set(split_data_end['remaining_points_indices'])
            overlap = split_indices.intersection(remaining_indices)
            
            if overlap:
                print(f"   ⚠️ 发现重复点索引: {list(overlap)}")
                print(f"   这可能导致不一样的效果")
            else:
                print(f"   ✅ 没有重复点")
        else:
            print("   ❌ 分割数据准备失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始三个点样条线分割问题分析")
    
    # 分析分割逻辑
    analyze_three_points_split()
    
    # 识别具体问题
    identify_three_points_problems()
    
    # 测试当前实现
    test_current_implementation()
    
    print("\n" + "=" * 60)
    print("📋 分析总结:")
    print("=" * 60)
    print("🔍 问题根源：三个点样条线分割时，中间点会在两条分割线中重复出现")
    print("🔧 影响：可能导致几何不连续、切矢冲突、视觉效果异常")
    print("💡 建议：需要特殊处理三个点的情况，或修改分割逻辑避免点重复")

if __name__ == "__main__":
    main()
