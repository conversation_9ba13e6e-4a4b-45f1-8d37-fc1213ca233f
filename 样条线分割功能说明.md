# 样条线分割功能使用说明

## 功能概述

本功能实现了在样条线切矢编辑器中，当用户在端点设置切矢时，自动检测并提供样条线分割选项的功能。分割后将创建两条新的样条线，一条包含端点和最近的控制点，另一条包含剩余的控制点。

## 功能特点

### 🎯 智能端点检测
- 自动识别样条线的端点（第一个点和最后一个点）
- 当用户在端点启用切矢时，系统自动检测分割机会

### 🔍 最近控制点查找
- 从被选中的端点开始，自动寻找距离最近的控制点
- 使用欧几里得距离计算，确保找到真正最近的点

### ✂️ 智能样条线分割
- 将原样条线分割为两条新样条线：
  - **分割线1**: 端点 → 最近控制点
  - **分割线2**: 剩余的所有控制点
- 保持原有的切矢约束和几何特性

### 🎨 用户友好界面
- 在切矢编辑器中集成分割控制面板
- 提供自动提示和手动检查两种模式
- 实时显示分割状态和预览信息

## 使用方法

### 基本操作流程

1. **打开样条线编辑器**
   - 双击画布中的样条线
   - 系统自动打开"样条线切矢编辑器 - 智能切矢设置"

2. **设置端点切矢**
   - 在表格中找到端点（第一行或最后一行）
   - 勾选"启用切矢"复选框
   - 系统自动检测分割选项

3. **选择分割操作**
   - 如果启用了"自动提示分割选项"，系统会弹出确认对话框
   - 选择"分割样条线"或"保持原样"
   - 也可以使用"手动检查分割"按钮主动检查

4. **确认执行**
   - 点击"确定"按钮
   - 系统会再次确认分割操作
   - 确认后自动执行分割并在画布中显示结果

### 分割控制选项

#### 自动提示模式
- **自动提示分割选项**: 勾选后，在端点设置切矢时自动弹出分割选项
- **手动检查分割**: 点击按钮主动检查当前是否可以进行分割

#### 分割状态显示
- **分割状态**: 实时显示当前的分割检测状态
- **分割预览**: 显示分割后两条样条线的详细信息

## 分割规则

### 端点分割逻辑

#### 起始端点分割（第一个点）
```
原样条线: 点1 → 点2 → 点3 → 点4 → 点5
分割结果:
- 分割线1: 点1 → 点2 (2个控制点)
- 分割线2: 点2 → 点3 → 点4 → 点5 (4个控制点)
```

#### 结束端点分割（最后一个点）
```
原样条线: 点1 → 点2 → 点3 → 点4 → 点5
分割结果:
- 分割线1: 点4 → 点5 (2个控制点)
- 分割线2: 点1 → 点2 → 点3 → 点4 (4个控制点)
```

### 分割条件
- 样条线至少需要3个控制点才能分割
- 分割后的每条样条线至少需要2个控制点
- 只有端点（第一个或最后一个点）可以触发分割

## 技术实现

### 核心算法

1. **端点检测算法**
   ```python
   def is_endpoint(self, point_index):
       total_points = len(self.points_data)
       return (point_index == 0) or (point_index == total_points - 1)
   ```

2. **最近点查找算法**
   ```python
   def find_nearest_control_point(self, endpoint_index):
       # 计算欧几里得距离，找到最近的控制点
       min_distance = float('inf')
       nearest_point_index = None
       # ... 距离计算逻辑
   ```

3. **分割数据准备**
   ```python
   def prepare_spline_split_data(self, endpoint_index):
       # 准备分割所需的所有数据
       # 包括点坐标、索引、切矢数据等
   ```

### 数据结构

分割数据包含以下信息：
- `endpoint_index`: 端点索引
- `nearest_point_index`: 最近控制点索引
- `split_points`: 分割样条线的点坐标
- `remaining_points`: 剩余样条线的点坐标
- `can_split`: 是否可以分割的标志

## 注意事项

### ⚠️ 重要提醒
1. **不可撤销操作**: 样条线分割后会删除原样条线，此操作不可撤销
2. **切矢保持**: 分割后的样条线会保持原有的切矢约束
3. **命名规则**: 新样条线会自动命名为"分割样条线1"和"分割样条线2"

### 🔧 使用建议
1. 在执行分割前，建议先保存项目文件
2. 可以先使用预览功能查看分割效果
3. 如果不确定分割效果，可以先取消操作

### 🐛 故障排除
1. **无法分割**: 检查样条线是否至少有3个控制点
2. **分割选项不出现**: 确保在端点启用了切矢
3. **分割失败**: 检查控制台输出的错误信息

## 更新日志

### v1.0.0 (当前版本)
- ✅ 实现端点检测和最近控制点查找算法
- ✅ 实现样条线分割算法
- ✅ 实现分割后的样条线重绘功能
- ✅ 集成分割功能到切矢编辑器界面
- ✅ 完成测试和优化

### 未来计划
- 🔄 支持中间点分割
- 🔄 支持多点同时分割
- 🔄 添加分割撤销功能
- 🔄 优化分割算法性能

---

**开发完成**: 样条线分割功能已完全实现并集成到系统中，可以正常使用。
