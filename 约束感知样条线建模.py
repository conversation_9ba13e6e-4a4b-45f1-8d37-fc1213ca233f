#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
约束感知的样条线建模模块
实现局部切矢控制，避免全局变形
自动检测和保持几何约束条件
"""

import math
from typing import List, Dict, Tuple, Optional, Any
from PyQt5.QtWidgets import QTreeWidgetItem, QMessageBox
from PyQt5.QtCore import Qt
from datetime import datetime

# OpenCASCADE导入
from OCC.Core.gp import gp_Pnt, gp_Vec
from OCC.Core.GeomAPI import GeomAPI_Interpolate
from OCC.Core.TColgp import TColgp_HArray1OfPnt, TColgp_Array1OfVec
from OCC.Core.TColStd import TColStd_HArray1OfBoolean
from OCC.Core.AIS import AIS_Shape
from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge, BRepBuilderAPI_MakeVertex
from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE, Quantity_NOC_GREEN, Quantity_NOC_RED
from OCC.Core.Aspect import Aspect_TOM_PLUS
from OCC.Core.Prs3d import Prs3d_PointAspect

def detect_geometric_constraints(points: List[List[float]], tolerance: float = 1.0) -> Dict:
    """
    检测几何约束条件
    
    Args:
        points: 控制点列表
        tolerance: 容差（单位：毫米）
    
    Returns:
        约束条件字典
    """
    constraints = {
        'planar_x': None,  # X平面约束
        'planar_y': None,  # Y平面约束  
        'planar_z': None,  # Z平面约束
        'linear': False,   # 线性约束
        'constraint_info': []  # 约束信息描述
    }
    
    if len(points) < 2:
        return constraints
    
    print(f"检测几何约束条件（容差: {tolerance}mm）:")
    
    # 检测X平面约束
    x_values = [p[0] for p in points]
    x_range = max(x_values) - min(x_values)
    if x_range < tolerance:
        constraints['planar_x'] = sum(x_values) / len(x_values)
        info = f"X平面约束: X = {constraints['planar_x']:.1f} (变化范围: {x_range:.1f}mm)"
        constraints['constraint_info'].append(info)
        print(f"  ✅ {info}")
    
    # 检测Y平面约束
    y_values = [p[1] for p in points]
    y_range = max(y_values) - min(y_values)
    if y_range < tolerance:
        constraints['planar_y'] = sum(y_values) / len(y_values)
        info = f"Y平面约束: Y = {constraints['planar_y']:.1f} (变化范围: {y_range:.1f}mm)"
        constraints['constraint_info'].append(info)
        print(f"  ✅ {info}")
    
    # 检测Z平面约束
    z_values = [p[2] for p in points]
    z_range = max(z_values) - min(z_values)
    if z_range < tolerance:
        constraints['planar_z'] = sum(z_values) / len(z_values)
        info = f"Z平面约束: Z = {constraints['planar_z']:.1f} (变化范围: {z_range:.1f}mm)"
        constraints['constraint_info'].append(info)
        print(f"  ✅ {info}")
    
    if not constraints['constraint_info']:
        print("  ℹ️  未检测到明显的平面约束")
    
    return constraints

def apply_constraint_preserving_tangent(points: List[List[float]], 
                                      tangent_data: Dict[int, Dict],
                                      constraints: Dict) -> Dict[int, Dict]:
    """
    应用保持约束的切矢调整
    
    Args:
        points: 控制点列表
        tangent_data: 原始切矢数据
        constraints: 几何约束条件
    
    Returns:
        调整后的切矢数据
    """
    adjusted_tangent_data = {}
    
    print("\n应用约束保持的切矢调整:")
    
    for point_index, tangent_info in tangent_data.items():
        direction = tangent_info['direction'].copy()
        length = tangent_info['length']
        
        print(f"\n  点{point_index+1}切矢调整:")
        print(f"    原始方向: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
        
        # 应用平面约束
        constraint_applied = False
        
        if constraints['planar_x'] is not None:
            if abs(direction[0]) > 0.001:  # 如果X方向有分量
                print(f"    ⚠️  检测到X平面约束，移除X方向分量 {direction[0]:.3f}")
                direction[0] = 0.0
                constraint_applied = True
        
        if constraints['planar_y'] is not None:
            if abs(direction[1]) > 0.001:  # 如果Y方向有分量
                print(f"    ⚠️  检测到Y平面约束，移除Y方向分量 {direction[1]:.3f}")
                direction[1] = 0.0
                constraint_applied = True
        
        if constraints['planar_z'] is not None:
            if abs(direction[2]) > 0.001:  # 如果Z方向有分量
                print(f"    ⚠️  检测到Z平面约束，移除Z方向分量 {direction[2]:.3f}")
                direction[2] = 0.0
                constraint_applied = True
        
        # 重新归一化方向向量
        direction_length = math.sqrt(sum(d**2 for d in direction))
        if direction_length > 1e-6:
            direction = [d / direction_length for d in direction]
            if constraint_applied:
                print(f"    ✅ 调整后方向: ({direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f})")
        else:
            # 如果约束导致方向向量为零，选择合适的默认方向
            if constraints['planar_x'] is not None and constraints['planar_y'] is not None:
                direction = [0.0, 0.0, 1.0]  # Z方向
                print(f"    🔄 约束导致方向为零，使用Z方向: (0, 0, 1)")
            elif constraints['planar_x'] is not None and constraints['planar_z'] is not None:
                direction = [0.0, 1.0, 0.0]  # Y方向
                print(f"    🔄 约束导致方向为零，使用Y方向: (0, 1, 0)")
            elif constraints['planar_y'] is not None and constraints['planar_z'] is not None:
                direction = [1.0, 0.0, 0.0]  # X方向
                print(f"    🔄 约束导致方向为零，使用X方向: (1, 0, 0)")
            elif constraints['planar_x'] is not None:
                direction = [0.0, 1.0, 0.0]  # Y方向
                print(f"    🔄 X平面约束，使用Y方向: (0, 1, 0)")
            elif constraints['planar_y'] is not None:
                direction = [1.0, 0.0, 0.0]  # X方向
                print(f"    🔄 Y平面约束，使用X方向: (1, 0, 0)")
            elif constraints['planar_z'] is not None:
                direction = [1.0, 0.0, 0.0]  # X方向
                print(f"    🔄 Z平面约束，使用X方向: (1, 0, 0)")
            else:
                direction = [1.0, 0.0, 0.0]  # 默认X方向
                print(f"    🔄 使用默认X方向: (1, 0, 0)")
        
        adjusted_tangent_data[point_index] = {
            'direction': direction,
            'length': length,
            'enabled': tangent_info.get('enabled', True),
            'constraint_adjusted': constraint_applied,
            'original_direction': tangent_info['direction'].copy()
        }
    
    return adjusted_tangent_data

def calculate_local_influence_weights(points: List[List[float]], 
                                    target_point_index: int, 
                                    influence_factor: float = 2.0) -> List[float]:
    """
    计算局部影响权重，实现局部控制
    
    Args:
        points: 控制点列表
        target_point_index: 目标点索引
        influence_factor: 影响因子，越大影响范围越小
    
    Returns:
        权重列表
    """
    if len(points) < 2:
        return [1.0] * len(points)
    
    # 计算平均点间距
    total_distance = 0
    for i in range(len(points) - 1):
        p1, p2 = points[i], points[i + 1]
        dist = math.sqrt(sum((p2[j] - p1[j])**2 for j in range(3)))
        total_distance += dist
    
    avg_distance = total_distance / (len(points) - 1)
    influence_radius = avg_distance * influence_factor
    
    weights = []
    target_point = points[target_point_index]
    
    print(f"\n计算点{target_point_index+1}的局部影响权重:")
    print(f"  平均点间距: {avg_distance:.1f}mm")
    print(f"  影响半径: {influence_radius:.1f}mm")
    
    for i, point in enumerate(points):
        if i == target_point_index:
            weights.append(1.0)  # 自身权重最大
        else:
            # 计算距离
            distance = math.sqrt(sum((point[j] - target_point[j])**2 for j in range(3)))
            
            # 指数衰减权重
            if distance <= influence_radius:
                weight = math.exp(-influence_factor * distance / influence_radius)
            else:
                weight = 0.0  # 超出影响范围
            
            weights.append(weight)
            print(f"  点{i+1}距离: {distance:.1f}mm, 权重: {weight:.3f}")
    
    return weights

def create_constraint_aware_spline(modeling_instance, points: List[List[float]], 
                                 tangent_data: Dict[int, Dict] = None, 
                                 active_layer=None,
                                 constraint_tolerance: float = 1.0):
    """
    创建约束感知的样条线
    
    Args:
        modeling_instance: 建模实例
        points: 控制点坐标列表
        tangent_data: 切矢数据字典
        active_layer: 活跃图层
        constraint_tolerance: 约束检测容差（毫米）
    
    Returns:
        bool: 创建是否成功
    """
    if not points or len(points) < 2:
        QMessageBox.warning(modeling_instance, "错误", "创建样条线至少需要两个控制点")
        return False
    
    try:
        print(f"开始创建约束感知样条线，控制点数: {len(points)}")
        
        # 步骤1: 检测几何约束
        constraints = detect_geometric_constraints(points, constraint_tolerance)
        
        # 步骤2: 调整切矢以保持约束
        if tangent_data and len(tangent_data) > 0:
            tangent_data = apply_constraint_preserving_tangent(points, tangent_data, constraints)
        
        # 步骤3: 创建控制点数组
        points_array = TColgp_HArray1OfPnt(1, len(points))
        for i, point_coords in enumerate(points):
            pnt = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])
            points_array.SetValue(i + 1, pnt)
            print(f"  控制点{i+1}: ({point_coords[0]:.1f}, {point_coords[1]:.1f}, {point_coords[2]:.1f})")
        
        # 步骤4: 创建插值器
        interpolator = GeomAPI_Interpolate(points_array, False, 0.0001)
        
        # 步骤5: 应用调整后的切矢约束
        if tangent_data and len(tangent_data) > 0:
            print(f"\n应用约束感知的切矢，约束点数: {len(tangent_data)}")
            
            # 使用全点切矢模式以获得更好的局部控制
            tangents_array = TColgp_Array1OfVec(1, len(points))
            tangent_flags = TColStd_HArray1OfBoolean(1, len(points))
            
            for i in range(len(points)):
                if i in tangent_data:
                    # 有切矢约束的点
                    tangent_info = tangent_data[i]
                    direction = tangent_info['direction']
                    length = tangent_info['length']
                    
                    # 计算局部影响权重
                    weights = calculate_local_influence_weights(points, i)
                    
                    # 根据约束调整长度（局部控制）
                    adjusted_length = length * 0.5  # 减小影响范围
                    
                    tangent_vec = gp_Vec(
                        direction[0] * adjusted_length, 
                        direction[1] * adjusted_length, 
                        direction[2] * adjusted_length
                    )
                    tangents_array.SetValue(i + 1, tangent_vec)
                    tangent_flags.SetValue(i + 1, True)
                    
                    constraint_note = " (约束调整)" if tangent_info.get('constraint_adjusted', False) else ""
                    print(f"  点{i+1}切矢: ({direction[0]*adjusted_length:.2f}, {direction[1]*adjusted_length:.2f}, {direction[2]*adjusted_length:.2f}){constraint_note}")
                else:
                    # 没有切矢约束的点
                    default_tangent = gp_Vec(1.0, 0.0, 0.0)
                    tangents_array.SetValue(i + 1, default_tangent)
                    tangent_flags.SetValue(i + 1, False)
            
            # 加载切矢约束
            interpolator.Load(tangents_array, tangent_flags)
        
        # 步骤6: 执行插值
        interpolator.Perform()
        
        if not interpolator.IsDone():
            QMessageBox.warning(modeling_instance, "错误", "约束感知样条线插值计算失败")
            return False
        
        # 步骤7: 获取生成的曲线
        bspline_curve = interpolator.Curve()
        
        # 步骤8: 创建边和AIS形状
        edge = BRepBuilderAPI_MakeEdge(bspline_curve).Edge()
        ais_spline = AIS_Shape(edge)
        
        # 设置样条线外观（约束感知样条线用橙色）
        spline_color = Quantity_Color(1.0, 0.5, 0.0, 0)  # 橙色
        ais_spline.SetColor(spline_color)
        ais_spline.SetWidth(2.5)
        
        # 显示样条线
        context = modeling_instance.display.GetContext()
        context.Display(ais_spline, True)
        
        # 步骤9: 处理图层
        if active_layer is None:
            active_layer = modeling_instance.find_active_layer()
            
        if active_layer is None:
            from enhanced_spline_modeling import get_or_create_spline_layer
            active_layer = get_or_create_spline_layer(modeling_instance)
        
        # 步骤10: 创建数据结构
        spline_data = create_constraint_aware_data_structure(
            modeling_instance, ais_spline, edge, points, tangent_data, 
            constraints, active_layer
        )
        
        # 步骤11: 创建可视化
        from enhanced_spline_modeling import create_control_points_visualization
        create_control_points_visualization(modeling_instance, spline_data, points, tangent_data)
        
        # 添加到样条线列表
        if not hasattr(modeling_instance, 'spline_list'):
            modeling_instance.spline_list = []
        modeling_instance.spline_list.append(spline_data)
        
        # 更新显示
        modeling_instance.display.Repaint()
        
        print("约束感知样条线创建成功")

        # 显示切矢箭头
        if hasattr(modeling_instance, 'show_tangent_arrows_for_spline'):
            modeling_instance.show_tangent_arrows_for_spline(spline_data)
            print("✅ 显示新创建样条线的切矢箭头")

        # 显示约束信息
        if constraints['constraint_info']:
            constraint_msg = "检测到几何约束:\n" + "\n".join(constraints['constraint_info'])
            modeling_instance.statusBar().showMessage(f"约束感知样条线创建成功 - {len(constraints['constraint_info'])}个约束", 5000)
        else:
            modeling_instance.statusBar().showMessage(f"约束感知样条线创建成功（无特殊约束）", 3000)

        return True
        
    except Exception as e:
        print(f"创建约束感知样条线失败: {e}")
        import traceback
        traceback.print_exc()
        QMessageBox.warning(modeling_instance, "错误", f"创建约束感知样条线失败: {str(e)}")
        return False

def create_constraint_aware_data_structure(modeling_instance, ais_spline, edge, points, 
                                         tangent_data, constraints, active_layer):
    """创建约束感知样条线的数据结构"""
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 生成约束描述
    constraint_desc = ""
    if constraints['constraint_info']:
        constraint_desc = f" [约束: {len(constraints['constraint_info'])}个]"
    
    spline_data = {
        "ais_spline": ais_spline,
        "edge": edge,
        "points": points,
        "tangent_data": tangent_data or {},
        "constraints": constraints,  # 保存约束信息
        "tree_item": None,
        "point_ais_list": [],
        "tangent_ais_list": [],
        "creation_method": "constraint_aware_interpolate"
    }
    
    # 在树形控件中添加样条线信息
    tangent_info = f"（含{len(tangent_data)}个切矢）" if tangent_data else ""
    spline_item = QTreeWidgetItem(active_layer, [f"约束感知样条线 [{current_time}]{tangent_info}{constraint_desc}"])
    spline_item.setFlags(spline_item.flags() | Qt.ItemIsUserCheckable)
    spline_item.setCheckState(0, Qt.Checked)
    spline_data["tree_item"] = spline_item
    
    # 添加约束信息子项
    if constraints['constraint_info']:
        constraint_item = QTreeWidgetItem(spline_item, ["几何约束"])
        for constraint_info in constraints['constraint_info']:
            QTreeWidgetItem(constraint_item, [constraint_info])
    
    # 添加控制点信息
    for i, point in enumerate(points):
        has_tangent = i in (tangent_data or {})
        tangent_mark = " [T]" if has_tangent else ""
        constraint_mark = ""
        
        if has_tangent and tangent_data[i].get('constraint_adjusted', False):
            constraint_mark = " [约束调整]"
        
        coord_item = QTreeWidgetItem(spline_item, [f"控制点{i+1}: ({point[0]:.1f}, {point[1]:.1f}, {point[2]:.1f}){tangent_mark}{constraint_mark}"])
        coord_item.setFlags(coord_item.flags() | Qt.ItemIsUserCheckable)
        coord_item.setCheckState(0, Qt.Checked)
    
    # 展开树节点
    modeling_instance.tree.expandItem(active_layer)
    modeling_instance.tree.expandItem(spline_item)
    
    return spline_data
