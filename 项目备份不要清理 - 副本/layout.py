# 导入所需的库和模块
from PyQt5.QtWidgets import QMainWindow, QTreeWidget, QTreeWidgetItem, QWidget, QGridLayout, QTextEdit, QShortcut, QCheckBox, QLabel, QPushButton, QLineEdit, QButtonGroup, QListWidget, QAbstractItemView, QTableWidget, QTableWidgetItem
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtGui import QIcon, QKeySequence
from OCC.Display import qtDisplay
from OCC.Core.Aspect import Aspect_GFM_VER

# 导入并加载后端
from OCC.Display.backend import load_backend
load_backend("qt-pyqt5")

class ExampleLayout(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ais_shp_vec = []
        self.undo_stack = []
        self.point_ais_list = []  # 独立点的AIS形状列表
        self.line_endpoint_ais_list = []  # 线端点的AIS形状列表 - 新增
        self._points_visible = True  # Track points visibility state
        self.tree = QTreeWidget(self)
        self.tree.setColumnCount(1)
        self.tree.setHeaderLabels(['项目_船舶'])
        self.tree.expandAll()
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.setWindowTitle("Deep For Ship 命令流自动化设计平台_南宁高博科技有限公司_覃")
        self.setWindowIcon(QIcon("img/工具栏/0.png"))
        self.setGeometry(0, 0, 3800, 2000)
        self.initUI()
        self.tree.itemDoubleClicked.connect(self.on_tree_item_double_clicked)

    def initUI(self):
        grid = QGridLayout(self.central_widget)  # 网格布局
        self.tree.setMaximumWidth(600)  # 增加树形控件的最大宽度
        self.tree.setHeaderLabels(['项目_船舶'])  # 设置树形控件的表头
        self.tree.expandAll()  # 展开所有节点
        self.tree.setExpandsOnDoubleClick(False)  # 禁止双击展开/折叠节点
        self.tree.collapseAll = lambda: self.tree.expandAll()  # 重写 collapseAll 方法，确保所有节点始终展开
        self.canvas = qtDisplay.qtViewer3d()  # 创建3D画布
        self.command_input = QTextEdit()  # 命令输入框，使用QTextEdit代替QLineEdit
        self.command_input.setFixedHeight(200)  # 设置命令输入框的高度
        self.command_input.setPlaceholderText("命令输入（每行一个命令，例如：\n创建点 0,0,0 100,0,0\n创建样条线 0,0,0/50,50,50/100,0,0\n创建点 0,200,0 200,0,0）")  # 设置占位符文本
        grid.addWidget(self.tree, 0, 0)  # 将树形控件添加到网格布局
        grid.addWidget(self.canvas, 0, 1)  # 将画布添加到网格布局
        grid.addWidget(self.command_input, 1, 0, 1, 2)  # 将命令输入框添加到网格布局

        # 🎯 添加视角切换按钮区域（调整间距适应加大的按钮）
        self.view_buttons_frame = QWidget()
        self.view_buttons_layout = QHBoxLayout(self.view_buttons_frame)
        self.view_buttons_layout.setContentsMargins(20, 10, 20, 10)
        self.view_buttons_layout.setSpacing(20)

        # 创建视角切换按钮
        self.create_view_buttons()

        # 将视角按钮区域添加到网格布局的底部
        grid.addWidget(self.view_buttons_frame, 2, 0, 1, 2)  # 跨越两列

        # 添加右侧Frame
        self.right_frame = QWidget()
        self.right_layout = QGridLayout(self.right_frame)
        grid.addWidget(self.right_frame, 0, 2, 2, 1)
        self.right_frame.setMaximumWidth(500)  # 新增：设置右侧Frame的最大宽度为300

        # 复选框布局调整
        self.checkbox_layout = QGridLayout()
        self.checkbox_layout.setSpacing(20)  # 增加复选框之间的间距

        # 创建按钮组以实现单选功能
        self.checkbox_group = QButtonGroup(self)
        self.checkbox_group.setExclusive(True)  # 设置单选模式

        # 区域1：复制、移动、镜像、旋转、阵列、拉伸、投影、相交、修剪、结合、填充、放样、扫略、桥接、展开、提取
        self.checkbox_1 = QCheckBox("复制")
        self.checkbox_1.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_1, 0, 0)
        self.checkbox_group.addButton(self.checkbox_1)

        self.checkbox_2 = QCheckBox("移动")
        self.checkbox_2.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_2, 0, 1)
        self.checkbox_group.addButton(self.checkbox_2)

        self.checkbox_3 = QCheckBox("镜像")
        self.checkbox_3.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_3, 0, 2)
        self.checkbox_group.addButton(self.checkbox_3)

        self.checkbox_4 = QCheckBox("旋转")
        self.checkbox_4.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_4, 1, 0)
        self.checkbox_group.addButton(self.checkbox_4)

        self.checkbox_5 = QCheckBox("阵列")
        self.checkbox_5.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_5, 1, 1)
        self.checkbox_group.addButton(self.checkbox_5)

        self.checkbox_6 = QCheckBox("拉伸")
        self.checkbox_6.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_6, 1, 2)
        self.checkbox_group.addButton(self.checkbox_6)

        self.checkbox_7 = QCheckBox("投影")
        self.checkbox_7.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_7, 2, 0)
        self.checkbox_group.addButton(self.checkbox_7)

        self.checkbox_8 = QCheckBox("相交")
        self.checkbox_8.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_8, 2, 1)
        self.checkbox_group.addButton(self.checkbox_8)

        self.checkbox_9 = QCheckBox("修剪")
        self.checkbox_9.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_9, 2, 2)
        self.checkbox_group.addButton(self.checkbox_9)

        self.checkbox_10 = QCheckBox("结合")
        self.checkbox_10.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_10, 3, 0)
        self.checkbox_group.addButton(self.checkbox_10)

        self.checkbox_11 = QCheckBox("填充")
        self.checkbox_11.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_11, 3, 1)
        self.checkbox_group.addButton(self.checkbox_11)

        self.checkbox_12 = QCheckBox("放样")
        self.checkbox_12.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_12, 3, 2)
        self.checkbox_group.addButton(self.checkbox_12)

        self.checkbox_34 = QCheckBox("扫略")
        self.checkbox_34.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_34, 4, 0)
        self.checkbox_group.addButton(self.checkbox_34)

        self.checkbox_35 = QCheckBox("桥接")
        self.checkbox_35.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_35, 4, 1)
        self.checkbox_group.addButton(self.checkbox_35)

        self.checkbox_36 = QCheckBox("展开")
        self.checkbox_36.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_36, 4, 2)
        self.checkbox_group.addButton(self.checkbox_36)

        self.checkbox_37 = QCheckBox("提取")
        self.checkbox_37.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_37, 5, 0)
        self.checkbox_group.addButton(self.checkbox_37)

        # 区域2：点、样条线、曲面、体
        self.checkbox_13 = QCheckBox("点")
        self.checkbox_13.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_13, 6, 0)
        self.checkbox_group.addButton(self.checkbox_13)

        self.checkbox_14 = QCheckBox("样条线")
        self.checkbox_14.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_14, 6, 1)
        self.checkbox_group.addButton(self.checkbox_14)

        self.checkbox_15 = QCheckBox("曲面")
        self.checkbox_15.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_15, 6, 2)
        self.checkbox_group.addButton(self.checkbox_15)

        self.checkbox_16 = QCheckBox("体")
        self.checkbox_16.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_16, 7, 0)
        self.checkbox_group.addButton(self.checkbox_16)

        # 区域3：钢制、铝合金、碳纤维、不锈钢
        self.checkbox_17 = QCheckBox("钢制")
        self.checkbox_17.setFixedSize(100, 30)
        self.checkbox_17.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_17, 15, 0)

        self.checkbox_18 = QCheckBox("铝合金")
        self.checkbox_18.setFixedSize(100, 30)
        self.checkbox_18.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_18, 15, 1)

        self.checkbox_19 = QCheckBox("碳纤维")
        self.checkbox_19.setFixedSize(100, 30)
        self.checkbox_19.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_19, 15, 2)

        self.checkbox_20 = QCheckBox("不锈钢")
        self.checkbox_20.setFixedSize(100, 30)
        self.checkbox_20.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_20, 16, 0)

        # 新增材质复选框
        self.checkbox_38 = QCheckBox("玻璃")
        self.checkbox_38.setFixedSize(100, 30)
        self.checkbox_38.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_38, 16, 1)

        self.checkbox_39 = QCheckBox("橡胶")
        self.checkbox_39.setFixedSize(100, 30)
        self.checkbox_39.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_39, 16, 2)

        self.checkbox_40 = QCheckBox("木质")
        self.checkbox_40.setFixedSize(100, 30)
        self.checkbox_40.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_40, 17, 0)

        self.checkbox_41 = QCheckBox("油类")
        self.checkbox_41.setFixedSize(100, 30)
        self.checkbox_41.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_41, 17, 1)

        self.checkbox_42 = QCheckBox("水类")
        self.checkbox_42.setFixedSize(100, 30)
        self.checkbox_42.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_42, 17, 2)

        self.checkbox_43 = QCheckBox("其他材质")
        self.checkbox_43.setFixedSize(100, 30)
        self.checkbox_43.setStyleSheet("font-weight: bold")
        self.checkbox_layout.addWidget(self.checkbox_43, 18, 0)

        # 创建材质复选框的按钮组以实现单选功能
        self.material_checkbox_group = QButtonGroup(self)
        self.material_checkbox_group.setExclusive(True)  # 设置单选模式

        # 将材质复选框添加到按钮组中
        self.material_checkbox_group.addButton(self.checkbox_17)
        self.material_checkbox_group.addButton(self.checkbox_18)
        self.material_checkbox_group.addButton(self.checkbox_19)
        self.material_checkbox_group.addButton(self.checkbox_20)
        self.material_checkbox_group.addButton(self.checkbox_38)
        self.material_checkbox_group.addButton(self.checkbox_39)
        self.material_checkbox_group.addButton(self.checkbox_40)
        self.material_checkbox_group.addButton(self.checkbox_41)
        self.material_checkbox_group.addButton(self.checkbox_42)
        self.material_checkbox_group.addButton(self.checkbox_43)

        self.checkbox_24 = QCheckBox("T型材")
        self.checkbox_24.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_24, 9, 0)  # 将T型材移动到第9行第0列
        self.checkbox_group.addButton(self.checkbox_24)

        self.checkbox_25 = QCheckBox("球扁钢")
        self.checkbox_25.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_25, 9, 1)  # 将球扁钢移动到第9行第1列
        self.checkbox_group.addButton(self.checkbox_25)

        self.checkbox_26 = QCheckBox("槽钢")
        self.checkbox_26.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_26, 9, 2)  # 将槽钢移动到第9行第2列
        self.checkbox_group.addButton(self.checkbox_26)

        self.checkbox_27 = QCheckBox("圆钢")
        self.checkbox_27.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_27, 10, 0)  # 将圆钢移动到第10行第0列
        self.checkbox_group.addButton(self.checkbox_27)

        self.checkbox_28 = QCheckBox("管材")
        self.checkbox_28.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_28, 10, 1)  # 将管材移动到第10行第1列
        self.checkbox_group.addButton(self.checkbox_28)

        # 区域5：测量距离、测量长度、测量面积、测量体积、测量重量重心
        self.checkbox_29 = QCheckBox("测距离")
        self.checkbox_29.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_29, 11, 0)  # 调整到第11行第0列
        self.checkbox_group.addButton(self.checkbox_29)

        self.checkbox_30 = QCheckBox("测长度")
        self.checkbox_30.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_30, 11, 1)  # 调整到第11行第1列
        self.checkbox_group.addButton(self.checkbox_30)

        self.checkbox_31 = QCheckBox("测面积")
        self.checkbox_31.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_31, 11, 2)  # 调整到第11行第2列
        self.checkbox_group.addButton(self.checkbox_31)

        self.checkbox_32 = QCheckBox("测体积")
        self.checkbox_32.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_32, 12, 0)  # 调整到第12行第0列
        self.checkbox_group.addButton(self.checkbox_32)

        self.checkbox_33 = QCheckBox("测重")
        self.checkbox_33.setFixedSize(100, 30)
        self.checkbox_layout.addWidget(self.checkbox_33, 12, 1)  # 调整到第12行第1列
        self.checkbox_group.addButton(self.checkbox_33)

        # 添加清空所有选择及其操作按钮
        self.clear_all_checkboxes_button = QPushButton("清空所有选择及其操作")
        self.clear_all_checkboxes_button.setFixedSize(500, 50)
        self.checkbox_layout.addWidget(self.clear_all_checkboxes_button, 13, 0, 1, 3)
        self.clear_all_checkboxes_button.clicked.connect(self.clear_all_checkboxes)

        # 添加清空材质按钮
        self.clear_material_button = QPushButton("清空材质")
        self.clear_material_button.setFixedSize(500, 50)
        self.checkbox_layout.addWidget(self.clear_material_button, 14, 0, 1, 3)
        self.clear_material_button.clicked.connect(self.clear_material_checkboxes)

        # 调整复选框布局
        self.checkbox_layout.setContentsMargins(20, 20, 20, 20)  # 增加边距
        self.checkbox_layout.setHorizontalSpacing(30)  # 增加水平间距
        self.checkbox_layout.setVerticalSpacing(15)  # 增加垂直间距

        # 将复选框布局添加到右侧布局中，并设置居中
        self.right_layout.addLayout(self.checkbox_layout, 0, 0, Qt.AlignTop)

        # 第2个QTextEdit_2
        # 使用QTableWidget代替QTextEdit
        self.table_widget_2 = QTableWidget()
        self.table_widget_2.setColumnCount(1)  # 设置单列
        self.table_widget_2.setHorizontalHeaderLabels(['被操作对象'])  # 设置表头
        self.table_widget_2.setSelectionMode(QAbstractItemView.ExtendedSelection)  # 设置多选模式
        self.table_widget_2.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)  # 添加垂向滚动轴
        self.table_widget_2.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)  # 添加横向滚动轴
        self.table_widget_2.setStyleSheet("QTableWidget { font-size: 10pt; }")  # 设置字体大小
        self.right_layout.addWidget(self.table_widget_2, 1, 0, Qt.AlignTop)

        # 第3个X轴
        self.text_edit_3 = QLineEdit()  # 改为单行的QLineEdit
        self.text_edit_3.setPlaceholderText("X值")
        self.text_edit_3.setFixedHeight(30)  # 设置固定高度
        self.right_layout.addWidget(self.text_edit_3, 2, 0, Qt.AlignTop)

        # 第4个Y轴
        self.text_edit_4 = QLineEdit()  # 改为单行的QLineEdit
        self.text_edit_4.setPlaceholderText("Y值")
        self.text_edit_4.setFixedHeight(30)  # 设置固定高度
        self.right_layout.addWidget(self.text_edit_4, 3, 0, Qt.AlignTop)

        # 第5个Z轴
        self.text_edit_5 = QLineEdit()  # 改为单行的QLineEdit
        self.text_edit_5.setPlaceholderText("Z值")
        self.text_edit_5.setFixedHeight(30)  # 设置固定高度
        self.right_layout.addWidget(self.text_edit_5, 4, 0, Qt.AlignTop)

        # OK按钮
        self.ok_button_3 = QPushButton("OK")
        self.ok_button_3.setFixedHeight(30)  # 设置固定高度
        self.right_layout.addWidget(self.ok_button_3, 5, 0, Qt.AlignTop)
        # 连接OK按钮的点击事件
        self.ok_button_3.clicked.connect(self.on_ok_button_3_clicked)

        # 第6个QTextEdit_6
        # 使用QTableWidget代替QTextEdit
        self.table_widget_6 = QTableWidget()
        self.table_widget_6.setColumnCount(1)  # 设置单列
        self.table_widget_6.setHorizontalHeaderLabels(['参照对象'])  # 设置表头
        self.table_widget_6.setSelectionMode(QAbstractItemView.ExtendedSelection)  # 设置多选模式
        self.table_widget_6.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)  # 添加垂向滚动轴
        self.table_widget_6.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)  # 添加横向滚动轴
        self.table_widget_6.setStyleSheet("QTableWidget { font-size: 10pt; }")  # 设置字体大小
        self.right_layout.addWidget(self.table_widget_6, 6, 0, Qt.AlignTop)

        # 第7个QTextEdit_7
        self.text_edit_7 = QLineEdit()  # 将 QTextEdit 改为 QLineEdit
        self.text_edit_7.setPlaceholderText("数值数据")
        self.text_edit_7.setFixedHeight(30)  # 设置固定高度
        self.right_layout.addWidget(self.text_edit_7, 7, 0, Qt.AlignTop)

        # OK按钮
        self.ok_button_7 = QPushButton("OK")
        self.ok_button_7.setFixedHeight(30)  # 设置固定高度
        self.right_layout.addWidget(self.ok_button_7, 8, 0, Qt.AlignTop)

        # 设置布局的边距和间距
        self.right_layout.setContentsMargins(0, 0, 0, 0)
        self.right_layout.setSpacing(5)  # 减少间距

        # 调整表格的高度
        self.table_widget_2.setFixedHeight(500)  # 增加高度
        self.table_widget_6.setFixedHeight(300)  # 增加高度

        grid.setRowStretch(0, 10)  # 设置第一行（主显示区域）的拉伸因子
        grid.setRowStretch(1, 0)  # 设置第二行（命令输入框）的拉伸因子
        grid.setRowStretch(2, 0)  # 设置第三行（视角按钮）的拉伸因子
        grid.setColumnStretch(0, 3)  # 增加第一列的拉伸因子，使树形控件占比更大
        grid.setColumnStretch(1, 7)  # 调整第二列的拉伸因子
        grid.setColumnStretch(2, 2)  # 设置第三列的拉伸因子
        self.canvas.InitDriver()  # 初始化画布驱动
        self.display = self.canvas._display  # 获取显示对象
        self.display.set_bg_gradient_color([100, 200, 100], [50, 50, 50], Aspect_GFM_VER)  # 设置背景渐变颜色
        self.display.display_triedron()  # 显示坐标系
        self.display.FitAll()  # 适应所有视图
        self.connectSignals()  # 连接信号
        QShortcut(QKeySequence("Ctrl+Z"), self).activated.connect(self.undo)  # 绑定撤销快捷键
        self.command_input.installEventFilter(self)  # 为命令输入框安装事件过滤器
        self.canvas.installEventFilter(self)  # 为画布安装事件过滤器
        self.canvas.setFocusPolicy(Qt.StrongFocus)  # 设置画布的焦点策略
        self.setFocusPolicy(Qt.StrongFocus)  # 设置主窗口的焦点策略
        self.show()  # 显示主窗口
        grid.setContentsMargins(0, 0, 0, 0)  # 设置布局的边距
        grid.setSpacing(0)  # 设置布局的间距

        # 连接树形控件的点击事件
        self.tree.itemClicked.connect(self.on_tree_item_clicked)

    def create_view_buttons(self):
        """
        创建视角切换按钮
        """
        # 定义视角按钮的配置（文本、方法名、工具提示、图标路径）
        view_configs = [
            ("俯视图", "View_Top", "从上方俯视观察模型", "img/几何建模/俯视图1.png"),
            ("仰视图", "View_Bottom", "从下方仰视观察模型", "img/几何建模/底视图1.png"),
            ("正视图", "View_Front", "从前方观察模型", "img/几何建模/正视图1.png"),
            ("后视图", "View_Back", "从后方观察模型", "img/几何建模/后视图1.png"),
            ("左视图", "View_Left", "从左侧观察模型", "img/几何建模/左视图1.png"),
            ("右视图", "View_Right", "从右侧观察模型", "img/几何建模/右视图1.png"),
            ("全部适应", "View_FitAll", "自动调整视角和缩放，使所有对象最佳显示", "img/几何建模/全部适应.png")
        ]

        # 创建按钮样式（加大文字和按钮尺寸）
        button_style = """
            QPushButton {
                background-color: #4CAF50;
                border: 4px solid #45a049;
                color: white;
                padding: 16px 24px;
                text-align: center;
                font-size: 22px;
                font-weight: bold;
                border-radius: 12px;
                min-width: 180px;
                min-height: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
                border: 4px solid #3d8b40;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
                border: 4px solid #2e6930;
            }
        """

        # 创建特殊的"全部适应"按钮样式（加大文字和按钮尺寸）
        fit_all_button_style = """
            QPushButton {
                background-color: #FF9800;
                border: 4px solid #F57C00;
                color: white;
                padding: 16px 24px;
                text-align: center;
                font-size: 22px;
                font-weight: bold;
                border-radius: 12px;
                min-width: 180px;
                min-height: 80px;
            }
            QPushButton:hover {
                background-color: #F57C00;
                border: 4px solid #E65100;
            }
            QPushButton:pressed {
                background-color: #E65100;
                border: 4px solid #BF360C;
            }
        """

        # 创建并配置每个视角按钮
        for text, method_name, tooltip, icon_path in view_configs:
            button = QPushButton(text)

            # 🎨 添加图标支持
            try:
                from PyQt5.QtGui import QIcon
                from PyQt5.QtCore import QSize
                import os

                if os.path.exists(icon_path):
                    icon = QIcon(icon_path)
                    button.setIcon(icon)
                    button.setIconSize(QSize(48, 48))  # 设置图标大小（加大一倍）
                    print(f"✅ 为'{text}'按钮加载图标: {icon_path}")
                else:
                    print(f"⚠️ 图标文件不存在: {icon_path}")
            except Exception as e:
                print(f"❌ 加载图标失败 {icon_path}: {e}")

            # 为"全部适应"按钮使用特殊样式
            if method_name == "View_FitAll":
                button.setStyleSheet(fit_all_button_style)
            else:
                button.setStyleSheet(button_style)

            button.setToolTip(tooltip)

            # 连接按钮点击事件到对应的视角方法
            button.clicked.connect(lambda checked, method=method_name: self.switch_view(method))

            # 添加按钮到布局
            self.view_buttons_layout.addWidget(button)

        # 添加弹性空间，使按钮居中
        self.view_buttons_layout.addStretch()

        print("✅ 视角切换按钮创建完成")

    def switch_view(self, view_method):
        """
        切换3D视角

        Args:
            view_method: 视角方法名称
        """
        try:
            # 获取display对象
            if hasattr(self, 'display') and self.display:
                # 根据方法名称调用对应的视角方法
                if view_method == "View_Top":
                    self.display.View_Top()
                    print("🔄 切换到俯视图")
                elif view_method == "View_Bottom":
                    self.display.View_Bottom()
                    print("🔄 切换到仰视图")
                elif view_method == "View_Front":
                    self.display.View_Front()
                    print("🔄 切换到正视图")
                elif view_method == "View_Back":
                    self.view_back_custom()
                    print("🔄 切换到后视图（自定义实现）")
                    return  # 自定义方法已经包含了FitAll和Repaint，直接返回
                elif view_method == "View_Left":
                    self.display.View_Left()
                    print("🔄 切换到左视图")
                elif view_method == "View_Right":
                    self.display.View_Right()
                    print("🔄 切换到右视图")
                elif view_method == "View_FitAll":
                    self.fit_all_view()
                    print("🔄 执行全部适应视角")
                    return  # fit_all_view已经包含了FitAll和Repaint，直接返回
                else:
                    print(f"❌ 未知的视角方法: {view_method}")
                    return

                # 适应视图以确保所有对象可见
                self.display.FitAll()

                # 刷新显示
                self.display.Repaint()

            else:
                print("❌ 显示对象未初始化，无法切换视角")

        except Exception as e:
            print(f"❌ 切换视角失败: {e}")
            import traceback
            traceback.print_exc()

    def fit_all_view(self):
        """
        全部适应视角算法
        自动调整视角和缩放，使所有对象以最佳方式显示
        """
        try:
            if not hasattr(self, 'display') or not self.display:
                print("❌ 显示对象未初始化，无法执行全部适应")
                return

            print("🎯 开始执行全部适应视角算法")

            # 第一步：重置视图到默认状态
            print("   步骤1: 重置视图到默认状态")
            self.display.View_Iso()  # 设置为等轴测视图

            # 第二步：适应所有对象到视图
            print("   步骤2: 适应所有对象到视图")
            self.display.FitAll()

            # 第三步：优化视角以获得最佳观察效果
            print("   步骤3: 优化视角设置")

            # 获取当前视图
            view = self.display.View
            if view:
                # 设置合适的投影模式（正交投影通常更适合CAD）
                try:
                    view.SetProj(0.7, -0.7, 0.7)  # 设置一个标准的等轴测投影方向
                    print("   - 设置等轴测投影方向")
                except:
                    print("   - 投影方向设置跳过（可能不支持）")

                # 确保视图更新
                view.Update()

            # 第四步：调整缩放以留出适当边距
            print("   步骤4: 调整缩放和边距")
            try:
                # 获取当前缩放比例并稍微缩小，留出边距
                current_scale = self.display.View.Scale()
                optimal_scale = current_scale * 0.9  # 缩小10%留出边距
                self.display.View.SetScale(optimal_scale)
                print(f"   - 调整缩放比例: {current_scale:.3f} → {optimal_scale:.3f}")
            except:
                print("   - 缩放调整跳过（使用默认设置）")

            # 第五步：刷新显示
            print("   步骤5: 刷新显示")
            self.display.Repaint()

            # 第六步：验证结果
            print("   步骤6: 验证适应结果")
            try:
                # 检查是否有对象在视图中
                context = self.display.GetContext()
                if context:
                    # 这里可以添加更多的验证逻辑
                    print("   - 视图上下文验证通过")
                else:
                    print("   - 警告：视图上下文为空")
            except:
                print("   - 验证步骤跳过")

            print("✅ 全部适应视角算法执行完成")

        except Exception as e:
            print(f"❌ 全部适应视角算法执行失败: {e}")
            import traceback
            traceback.print_exc()

            # 回退到基础的FitAll
            try:
                print("🔄 回退到基础FitAll操作")
                self.display.FitAll()
                self.display.Repaint()
                print("✅ 基础FitAll操作完成")
            except:
                print("❌ 基础FitAll操作也失败")

    def view_back_custom(self):
        """
        自定义后视图实现
        由于OpenCASCADE可能没有标准的View_Back方法，我们通过自定义实现
        """
        try:
            if not hasattr(self, 'display') or not self.display:
                print("❌ 显示对象未初始化，无法切换到后视图")
                return

            print("🎯 开始实现自定义后视图")

            # 方法1：尝试使用View_Front然后旋转180度
            try:
                print("   方法1: 基于正视图旋转180度")

                # 先切换到正视图
                self.display.View_Front()

                # 获取当前视图对象
                view = self.display.View
                if view:
                    # 绕Y轴旋转180度实现后视图效果
                    # 注意：角度可能需要用弧度制
                    import math
                    angle_180 = math.pi  # 180度的弧度值

                    # 尝试旋转视图
                    try:
                        # 方法1：使用Rotate方法
                        view.Rotate(0, 1, 0, angle_180)  # 绕Y轴旋转180度
                        print("   - 使用Rotate方法旋转成功")
                    except Exception as e1:
                        print(f"   - Rotate方法失败: {e1}")
                        try:
                            # 方法2：使用SetProj方法直接设置投影方向
                            # 后视图的投影方向应该是正视图的相反方向
                            view.SetProj(0, 1, 0)  # 从后方看向前方
                            print("   - 使用SetProj方法设置投影方向成功")
                        except Exception as e2:
                            print(f"   - SetProj方法也失败: {e2}")
                            # 方法3：使用SetAt和SetEye方法
                            try:
                                from OCC.Core.gp import gp_Pnt
                                # 设置观察点和目标点
                                eye_point = gp_Pnt(0, -100, 0)  # 观察者位置（后方）
                                at_point = gp_Pnt(0, 0, 0)      # 观察目标（原点）
                                view.SetAt(at_point.X(), at_point.Y(), at_point.Z())
                                view.SetEye(eye_point.X(), eye_point.Y(), eye_point.Z())
                                print("   - 使用SetAt/SetEye方法设置成功")
                            except Exception as e3:
                                print(f"   - SetAt/SetEye方法也失败: {e3}")
                                raise Exception("所有后视图实现方法都失败")

                    # 更新视图
                    view.Update()
                    print("   - 视图更新完成")
                else:
                    raise Exception("无法获取视图对象")

            except Exception as e:
                print(f"   方法1失败: {e}")

                # 方法2：回退到基础的正视图（作为临时解决方案）
                print("   方法2: 回退到正视图作为临时解决方案")
                self.display.View_Front()
                print("   - 已切换到正视图（后视图功能待完善）")

            # 适应视图以确保所有对象可见
            self.display.FitAll()

            # 刷新显示
            self.display.Repaint()

            print("✅ 后视图切换完成")

        except Exception as e:
            print(f"❌ 后视图切换失败: {e}")
            import traceback
            traceback.print_exc()

            # 最终回退：切换到正视图
            try:
                print("🔄 最终回退到正视图")
                self.display.View_Front()
                self.display.FitAll()
                self.display.Repaint()
                print("✅ 已回退到正视图")
            except:
                print("❌ 回退操作也失败")
        
        # 修改：连接所有复选框的状态变化信号
        self.checkbox_1.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_1, checked))
        self.checkbox_2.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_2, checked))
        self.checkbox_3.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_3, checked))
        self.checkbox_4.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_4, checked))
        self.checkbox_5.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_5, checked))
        self.checkbox_6.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_6, checked))
        self.checkbox_7.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_7, checked))
        self.checkbox_8.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_8, checked))
        self.checkbox_9.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_9, checked))
        self.checkbox_10.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_10, checked))
        self.checkbox_11.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_11, checked))
        self.checkbox_12.toggled.connect(lambda checked: self.on_checkbox_toggled(self.checkbox_12, checked))

    def on_checkbox_toggled(self, checkbox, checked):
        """当任何复选框状态变化时执行的函数"""
        if checked:
            print(f"{checkbox.text()} 被选中")
            # 执行选中时的操作
            # 不再直接调用复制功能，而是等待OK按钮点击
            if checkbox == self.checkbox_1:
                print("复制功能已启用，等待OK按钮点击")
            # 执行其他选中操作
            elif hasattr(self, 'execute_checkbox_action'):
                self.execute_checkbox_action(checkbox.text())
        else:
            print(f"{checkbox.text()} 被取消选中")
            # 执行取消选中时的操作
            # 例如：可以在这里清理与复选框相关的资源
            if checkbox == self.checkbox_1 and hasattr(self, 'clear_copy_action'):
                self.clear_copy_action()
            elif hasattr(self, 'clear_checkbox_action'):
                self.clear_checkbox_action(checkbox.text())

        # 如果当前选中的是材质复选框，不清空表格
        material_checkboxes = [
            self.checkbox_17, self.checkbox_18, self.checkbox_19, self.checkbox_20,
            self.checkbox_38, self.checkbox_39, self.checkbox_40, self.checkbox_41,
            self.checkbox_42, self.checkbox_43
        ]
        if checkbox not in material_checkboxes:
            # 若选中的是复制功能，不清空表格
            if checkbox == self.checkbox_1 and checked:
                print("选中复制功能，保留已选择的点")
                # 复制功能需要保留选中的点，不清空表格
            else:
                # 清空被操作对象表格
                self.table_widget_2.setRowCount(0)

        # 如果当前有任何复选框被选中，你可以选择在此处执行其他操作
        if self.is_any_checkbox_checked():
            # 此处可选择性添加一些操作
            print("当前有复选框被选中，可以执行相关操作")

    def execute_checkbox_action(self, checkbox_text):
        """根据复选框的文本执行相应的操作"""
        # 根据复选框的文本执行不同的操作
        if checkbox_text == "复制":
            self.execute_copy_action()
        elif checkbox_text == "移动":
            self.execute_move_action()
        elif checkbox_text == "镜像":
            self.execute_mirror_action()
        elif checkbox_text == "旋转":
            self.execute_rotate_action()
        elif checkbox_text == "阵列":
            self.execute_array_action()
        elif checkbox_text == "拉伸":
            self.execute_extrude_action()
        elif checkbox_text == "投影":
            self.execute_project_action()
        elif checkbox_text == "相交":
            self.execute_intersect_action()
        elif checkbox_text == "修剪":
            self.execute_trim_action()
        elif checkbox_text == "结合":
            self.execute_combine_action()
        elif checkbox_text == "填充":
            self.execute_fill_action()
        elif checkbox_text == "放样":
            self.execute_loft_action()
        elif checkbox_text == "创建点":
            self.execute_create_point_action()
        elif checkbox_text == "创建直线":
            self.execute_create_line_action()
        elif checkbox_text == "创建样条线":
            self.execute_create_spline_action()
        elif checkbox_text == "创建曲面":
            self.execute_create_surface_action()
        elif checkbox_text == "创建体":
            self.execute_create_solid_action()

    def clear_checkbox_action(self, checkbox_text):
        """根据复选框的文本清理相应的资源"""
        # 根据复选框的文本清理资源
        if checkbox_text == "复制":
            self.clear_copy_action()
        elif checkbox_text == "移动":
            self.clear_move_action()
        elif checkbox_text == "镜像":
            self.clear_mirror_action()
        elif checkbox_text == "旋转":
            self.clear_rotate_action()
        elif checkbox_text == "阵列":
            self.clear_array_action()
        elif checkbox_text == "拉伸":
            self.clear_extrude_action()
        elif checkbox_text == "投影":
            self.clear_project_action()
        elif checkbox_text == "相交":
            self.clear_intersect_action()
        elif checkbox_text == "修剪":
            self.clear_trim_action()
        elif checkbox_text == "结合":
            self.clear_combine_action()
        elif checkbox_text == "填充":
            self.clear_fill_action()
        elif checkbox_text == "放样":
            self.clear_loft_action()
        elif checkbox_text == "创建点":
            self.clear_create_point_action()
        elif checkbox_text == "创建直线":
            self.clear_create_line_action()
        elif checkbox_text == "创建样条线":
            self.clear_create_spline_action()
        elif checkbox_text == "创建曲面":
            self.clear_create_surface_action()
        elif checkbox_text == "创建体":
            self.clear_create_solid_action()

    def execute_create_point_action(self):
        """执行创建点操作"""
        print("执行创建点操作")
        # 这里可以添加具体的创建点逻辑

    def clear_create_point_action(self):
        """清理创建点操作相关的资源"""
        print("清理创建点操作相关的资源")
        # 这里可以添加清理创建点资源的逻辑

    def execute_create_line_action(self):
        """执行创建直线操作"""
        print("执行创建直线操作")
        # 这里可以添加具体的创建直线逻辑

    def clear_create_line_action(self):
        """清理创建直线操作相关的资源"""
        print("清理创建直线操作相关的资源")
        # 这里可以添加清理创建直线资源的逻辑

    def execute_create_spline_action(self):
        """执行创建样条线操作"""
        print("执行创建样条线操作")
        # 这里可以添加具体的创建样条线逻辑

    def clear_create_spline_action(self):
        """清理创建样条线操作相关的资源"""
        print("清理创建样条线操作相关的资源")
        # 这里可以添加清理创建样条线资源的逻辑

    def execute_create_surface_action(self):
        """执行创建曲面操作"""
        print("执行创建曲面操作")
        # 这里可以添加具体的创建曲面逻辑

    def clear_create_surface_action(self):
        """清理创建曲面操作相关的资源"""
        print("清理创建曲面操作相关的资源")
        # 这里可以添加清理创建曲面资源的逻辑

    def execute_create_solid_action(self):
        """执行创建体操作"""
        print("执行创建体操作")
        # 这里可以添加具体的创建体逻辑

    def clear_create_solid_action(self):
        """清理创建体操作相关的资源"""
        print("清理创建体操作相关的资源")
        # 这里可以添加清理创建体资源的逻辑

    def execute_rotate_action(self):
        """执行旋转操作"""
        print("执行旋转操作")
        try:
            # 获取选中的对象
            selected_items = self.table_widget_2.selectedItems()
            if not selected_items:
                print("未选中任何对象")
                return

            # 获取旋转轴和角度
            x_axis = self.text_edit_3.text()
            y_axis = self.text_edit_4.text()
            z_axis = self.text_edit_5.text()
            angle = self.text_edit_7.toPlainText()

            # 验证输入
            if not x_axis or not y_axis or not z_axis or not angle:
                print("请输入有效的旋转轴和角度")
                return

            # 将输入转换为数值
            try:
                x_axis = float(x_axis)
                y_axis = float(y_axis)
                z_axis = float(z_axis)
                angle = float(angle)
            except ValueError:
                print("请输入有效的数值")
                return

            # 执行旋转操作
            for item in selected_items:
                shape_name = item.text()
                # 查找对应的形状并执行旋转
                # 这里需要根据具体的形状处理逻辑进行实现
                print(f"旋转形状: {shape_name}, 轴: ({x_axis}, {y_axis}, {z_axis}), 角度: {angle}")

        except Exception as e:
            print(f"执行旋转操作时发生错误: {e}")

    def clear_rotate_action(self):
        """清理旋转操作相关的资源"""
        print("清理旋转操作相关的资源")
        # 这里可以添加清理旋转资源的逻辑

    def execute_combine_action(self):
        """执行结合操作"""
        print("执行结合操作")
        try:
            # 获取选中的对象
            selected_items = self.table_widget_2.selectedItems()
            if not selected_items:
                print("未选中任何对象")
                return

            # 执行结合操作
            shapes_to_combine = []
            for item in selected_items:
                shape_name = item.text()
                # 查找对应的形状并添加到结合列表中
                # 这里需要根据具体的形状处理逻辑进行实现
                print(f"准备结合形状: {shape_name}")
                # shapes_to_combine.append(shape)  # 假设shape是获取到的形状对象

            # 执行实际的结合操作
            # combined_shape = self.combine_shapes(shapes_to_combine)  # 假设combine_shapes是结合方法
            # self.display.DisplayShape(combined_shape, update=True)
            print("结合操作完成")

        except Exception as e:
            print(f"执行结合操作时发生错误: {e}")
            # 清理资源
            self.clear_combine_action()

    def clear_combine_action(self):
        """清理结合操作相关的资源"""
        print("清理结合操作相关的资源")
        # 这里可以添加清理结合资源的逻辑
        # 例如：释放内存、重置状态等

    def is_any_checkbox_checked(self):
        """检查是否有任何复选框被选中"""
        checkboxes = [
            self.checkbox_1, self.checkbox_2, self.checkbox_3, self.checkbox_4,
            self.checkbox_5, self.checkbox_6, self.checkbox_7, self.checkbox_8,
            self.checkbox_9, self.checkbox_10, self.checkbox_11, self.checkbox_12,
            self.checkbox_13, self.checkbox_14, self.checkbox_15, self.checkbox_16,
            self.checkbox_17
        ]
        
        for checkbox in checkboxes:
            if checkbox.isChecked():
                return True
        return False

    def on_tree_item_clicked(self, item):
        """处理树形控件的点击事件"""
        # 检查是否点击的是点项
        parent_item = item.parent()
        if (parent_item):
            point_name = item.text(0)
            parent_name = parent_item.text(0)
            # 只处理点项目，排除坐标子项，如X、Y、Z值
            if not (point_name.startswith("X:") or point_name.startswith("Y:") or point_name.startswith("Z:")):
                self.add_to_table_widget_2(point_name, parent_name)
            # 如果点击的是坐标子项，则处理其父项
            else:
                # 找到坐标项的父项（点）
                point_item = parent_item
                # 如果父项还有父项，说明是正确的点项
                grandparent_item = point_item.parent()
                if grandparent_item:
                    self.add_to_table_widget_2(point_item.text(0), grandparent_item.text(0))

    def on_canvas_selection_changed(self):
        """处理画布的选中事件"""
        # 获取选中的形状
        context = self.display.GetContext()
        context.InitSelected()
        
        while context.MoreSelected():
            selected_shape = context.SelectedShape()
            
            # 查找对应的点名称和其父节点
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    # 检查选中的形状是否与点的数据匹配
                    if (hasattr(selected_shape, "IsEqual") and 
                        hasattr(point_data["vertex"], "IsEqual") and 
                        selected_shape.IsEqual(point_data["vertex"])):
                        
                        # 获取点的树节点
                        tree_item = point_data.get("tree_item")
                        if tree_item:
                            point_name = tree_item.text(0)
                            parent_item = tree_item.parent()
                            if parent_item:
                                parent_name = parent_item.text(0)
                                # 添加到表格中
                                self.add_to_table_widget_2(point_name, parent_name)
                                
            context.NextSelected()

    def find_item_by_name(self, tree, name):
        """在树形控件中查找指定名称的项"""
        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            if item.text(0) == name:
                return item
            for j in range(item.childCount()):
                child_item = item.child(j)
                if child_item.text(0) == name:
                    return child_item
        return None

    def add_to_table_widget_2(self, point_name, parent_name):
        """将点名称添加到被操作对象表格中，确保其唯一性"""
        try:
            # 检查是否有任何复选框被选中
            if not self.is_any_checkbox_checked():
                return  # 如果没有复选框被选中，不添加到表格
                
            # 首先检查表格中是否已存在相同的点名称
            for row in range(self.table_widget_2.rowCount()):
                item = self.table_widget_2.item(row, 0)
                if item and item.text() == point_name:
                    return  # 如果已存在，直接返回
            
            # 如果不存在，添加新行
            row_position = self.table_widget_2.rowCount()
            self.table_widget_2.insertRow(row_position)
            self.table_widget_2.setItem(row_position, 0, QTableWidgetItem(point_name))
            
            # 可选：设置工具提示显示父节点名称，以提供更多上下文
            self.table_widget_2.item(row_position, 0).setToolTip(f"所属图层: {parent_name}")
            
            # 确保每次添加新项后都调整列宽以适应内容
            self.table_widget_2.resizeColumnToContents(0)
            
        except Exception as e:
            print(f"添加到被操作对象表格时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def clear_material_checkboxes(self):
        """清空材质相关复选框的勾选状态"""
        try:
            material_checkboxes = [
                self.checkbox_17, self.checkbox_18, self.checkbox_19, self.checkbox_20,
                self.checkbox_38, self.checkbox_39, self.checkbox_40, self.checkbox_41,
                self.checkbox_42, self.checkbox_43
            ]
            
            # 暂时禁用材质按钮组的互斥性
            self.material_checkbox_group.setExclusive(False)
            
            # 逐个设置复选框状态，添加错误处理
            for checkbox in material_checkboxes:
                try:
                    checkbox.setChecked(False)
                except Exception as e:
                    print(f"清除材质复选框 {checkbox} 时出错: {e}")
                    continue
                    
            # 恢复按钮组的互斥性
            self.material_checkbox_group.setExclusive(True)
        except Exception as e:
            print(f"清空材质复选框时发生错误: {e}")

    def clear_all_checkboxes(self):
        """清空所有复选框的选择状态"""
        try:
            # 暂时禁用互斥性以便批量清除
            self.checkbox_group.setExclusive(False)
            
            # 清空所有操作复选框
            checkboxes = [
                self.checkbox_1, self.checkbox_2, self.checkbox_3, self.checkbox_4,
                self.checkbox_5, self.checkbox_6, self.checkbox_7, self.checkbox_8,
                self.checkbox_9, self.checkbox_10, self.checkbox_11, self.checkbox_12,
                self.checkbox_13, self.checkbox_14, self.checkbox_15, self.checkbox_16,
                self.checkbox_24, self.checkbox_25, self.checkbox_26, self.checkbox_27,
                self.checkbox_28, self.checkbox_29, self.checkbox_30, self.checkbox_31,
                self.checkbox_32, self.checkbox_33, self.checkbox_34, self.checkbox_35,
                self.checkbox_36, self.checkbox_37
            ]
            
            # 记录是否有勾选的复制功能
            copy_checked = self.checkbox_1.isChecked()
            
            for checkbox in checkboxes:
                # 确保触发取消选中事件，这样功能相关资源会被清理
                if checkbox.isChecked():
                    checkbox.setChecked(False)
                
            # 恢复互斥性
            self.checkbox_group.setExclusive(True)
            
            # 清空被操作对象表格和参照对象表格
            self.table_widget_2.setRowCount(0)
            self.table_widget_6.setRowCount(0)
            
            # 清空坐标输入框
            self.text_edit_3.clear()
            self.text_edit_4.clear()
            self.text_edit_5.clear()
            self.text_edit_7.clear()
            
            # 如果之前复制功能被选中，调用清理资源的函数
            if copy_checked and hasattr(self, 'clear_copy_action'):
                self.clear_copy_action()
            
            print("已清空所有选择和操作")
            
        except Exception as e:
            print(f"清空复选框时发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    def execute_copy_action(self):
        """执行复制操作 - 该方法不再直接被调用，而是通过OK按钮的点击事件触发"""
        print("执行复制操作")
        # 获取选中的点
        selected_items = self.table_widget_2.selectedItems()
        if not selected_items:
            print("未选中任何点")
            return

        # 获取X、Y、Z轴偏移值
        x_offset = self.text_edit_3.text()
        y_offset = self.text_edit_4.text()
        z_offset = self.text_edit_5.text()

        # 验证输入
        if not x_offset or not y_offset or not z_offset:
            print("请输入有效的X、Y、Z轴偏移值")
            return

        # 将输入转换为数值
        try:
            x_offset = float(x_offset)
            y_offset = float(y_offset)
            z_offset = float(z_offset)
        except ValueError:
            print("请输入有效的数值")
            return

        # 执行复制操作
        for item in selected_items:
            point_name = item.text()
            # 查找对应的点并执行复制
            # 这里需要根据具体的点处理逻辑进行实现
            print(f"复制点: {point_name}, 偏移值: ({x_offset}, {y_offset}, {z_offset})")

    def show_copy_point_dialog(self):
        """显示复制点窗体"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton

        # 创建对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("复制点")
        layout = QVBoxLayout()

        # 添加标签
        label = QLabel("点已成功复制！")
        layout.addWidget(label)

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.close)
        layout.addWidget(close_button)

        # 设置布局并显示对话框
        dialog.setLayout(layout)
        dialog.exec_()

    def clear_copy_action(self):
        """清理复制操作相关的资源"""
        print("清理复制操作相关的资源")
        # 这里添加清理复制操作资源的逻辑
    
    def execute_move_action(self):
        """执行移动操作"""
        print("执行移动操作")
        # 这里添加移动操作的实现逻辑
    
    def clear_move_action(self):
        """清理移动操作相关的资源"""
        print("清理移动操作相关的资源")
        # 这里添加清理移动操作资源的逻辑
    
    def execute_mirror_action(self):
        """执行镜像操作"""
        print("执行镜像操作")
        # 这里添加镜像操作的实现逻辑
    
    def clear_mirror_action(self):
        """清理镜像操作相关的资源"""
        print("清理镜像操作相关的资源")
        # 这里添加清理镜像操作资源的逻辑
        
    def execute_array_action(self):
        """执行阵列操作"""
        print("执行阵列操作")
        # 这里添加阵列操作的实现逻辑
    
    def clear_array_action(self):
        """清理阵列操作相关的资源"""
        print("清理阵列操作相关的资源")
        # 这里添加清理阵列操作资源的逻辑
    
    def execute_extrude_action(self):
        """执行拉伸操作"""
        print("执行拉伸操作")
        # 这里添加拉伸操作的实现逻辑
    
    def clear_extrude_action(self):
        """清理拉伸操作相关的资源"""
        print("清理拉伸操作相关的资源")
        # 这里添加清理拉伸操作资源的逻辑
    
    def execute_project_action(self):
        """执行投影操作"""
        print("执行投影操作")
        # 这里添加投影操作的实现逻辑
    
    def clear_project_action(self):
        """清理投影操作相关的资源"""
        print("清理投影操作相关的资源")
        # 这里添加清理投影操作资源的逻辑
    
    def execute_intersect_action(self):
        """执行相交操作"""
        print("执行相交操作")
        # 这里添加相交操作的实现逻辑
    
    def clear_intersect_action(self):
        """清理相交操作相关的资源"""
        print("清理相交操作相关的资源")
        # 这里添加清理相交操作资源的逻辑
    
    def execute_trim_action(self):
        """执行修剪操作"""
        print("执行修剪操作")
        # 这里添加修剪操作的实现逻辑
    
    def clear_trim_action(self):
        """清理修剪操作相关的资源"""
        print("清理修剪操作相关的资源")
        # 这里添加清理修剪操作资源的逻辑
    
    def execute_fill_action(self):
        """执行填充操作"""
        print("执行填充操作")
        # 这里添加填充操作的实现逻辑
    
    def clear_fill_action(self):
        """清理填充操作相关的资源"""
        print("清理填充操作相关的资源")
        # 这里添加清理填充操作资源的逻辑
    
    def execute_loft_action(self):
        """执行放样操作"""
        print("执行放样操作")
        # 这里添加放样操作的实现逻辑
    
    def clear_loft_action(self):
        """清理放样操作相关的资源"""
        print("清理放样操作相关的资源")
        # 这里添加清理放样操作资源的逻辑
        
    def on_ok_button_3_clicked(self):
        """处理X轴OK按钮点击事件，同时处理Y轴和Z轴的输入"""
        try:
            # 获取X、Y、Z轴文本框中的值
            x_value = self.text_edit_3.text()
            y_value = self.text_edit_4.text()
            z_value = self.text_edit_5.text()
            
            # 检查是否有复制功能被选中
            if self.checkbox_1.isChecked():
                print("复制功能已启用，执行复制点操作")
                # 导入复制点对话框
                from copy import CopyPointDialog
                
                # 创建并显示复制点对话框
                copy_dialog = CopyPointDialog(self)
                if copy_dialog.exec_():
                    # 用户点击了确定，获取新点坐标
                    new_points = copy_dialog.get_new_points()
                    # 这里可以添加创建新点的代码
                    print(f"将创建 {len(new_points)} 个新点")
                    for i, coords in enumerate(new_points):
                        print(f"新点 {i+1}: ({coords[0]}, {coords[1]}, {coords[2]})")
                else:
                    print("用户取消了复制点操作")
            else:
                print("复制功能未启用，请先选中复制复选框")
                    
        except Exception as e:
            print(f"处理X、Y、Z轴输入时发生错误: {e}")
            import traceback
            traceback.print_exc()

# 必要的导入
from PyQt5.QtWidgets import QVBoxLayout, QHBoxLayout, QWidget

class MainLayout(QVBoxLayout):
    def __init__(self, parent=None):
        super(MainLayout, self).__init__(parent)
        self.initUI()

    def initUI(self):
        # 初始化布局
        self.top_layout = QHBoxLayout()
        self.bottom_layout = QHBoxLayout()

        # 添加布局到主布局
        self.addLayout(self.top_layout)
        self.addLayout(self.bottom_layout)

class SideLayout(QVBoxLayout):
    def __init__(self, parent=None):
        super(SideLayout, self).__init__(parent)
        self.initUI()

    def initUI(self):
        # 初始化侧边栏布局
        self.button_layout = QHBoxLayout()
        self.addLayout(self.button_layout)

# 主函数
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    main_window = QWidget()
    main_layout = MainLayout(main_window)
    main_window.setLayout(main_layout)
    main_window.show()
    sys.exit(app.exec_())

