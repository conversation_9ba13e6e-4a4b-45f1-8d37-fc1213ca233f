# 复制点功能实现模块
import sys
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, 
                            QLabel, QDoubleSpinBox, QHBoxLayout, QPushButton, 
                            QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox)
from PyQt5.QtCore import Qt
from OCC.Core.gp import gp_Pnt, gp_Vec

class CopyPointDialog(QDialog):
    """
    点复制对话框
    用于配置点的复制参数，并返回新点的坐标
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setWindowTitle("复制点")
        self.setMinimumWidth(1000)
        self.setMinimumHeight(1500)
        
        # 存储源点坐标和结果点坐标
        self.source_points = []
        self.new_points = []
        
        # 获取选中的点
        self.collect_selected_points()
        
        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化对话框UI"""
        main_layout = QVBoxLayout(self)
        
        # 源点信息区域
        source_group = QGroupBox("源点")
        source_layout = QVBoxLayout()
        
        # 创建表格显示源点信息
        self.source_table = QTableWidget()
        self.source_table.setColumnCount(4)
        self.source_table.setHorizontalHeaderLabels(["点名称", "X", "Y", "Z"])
        self.source_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        source_layout.addWidget(self.source_table)
        
        # 填充源点数据
        self.populate_source_points()
        
        source_group.setLayout(source_layout)
        main_layout.addWidget(source_group)
        
        # 复制参数区域
        params_group = QGroupBox("复制参数")
        params_layout = QFormLayout()
        
        # X轴偏移
        self.x_offset_spinbox = QDoubleSpinBox()
        self.x_offset_spinbox.setRange(-10000, 10000)
        self.x_offset_spinbox.setDecimals(2)
        self.x_offset_spinbox.setValue(100)  # 默认设置为100，这样更明显
        params_layout.addRow("X轴偏移:", self.x_offset_spinbox)
        
        # Y轴偏移
        self.y_offset_spinbox = QDoubleSpinBox()
        self.y_offset_spinbox.setRange(-10000, 10000)
        self.y_offset_spinbox.setDecimals(2)
        self.y_offset_spinbox.setValue(0)
        params_layout.addRow("Y轴偏移:", self.y_offset_spinbox)
        
        # Z轴偏移
        self.z_offset_spinbox = QDoubleSpinBox()
        self.z_offset_spinbox.setRange(-10000, 10000)
        self.z_offset_spinbox.setDecimals(2)
        self.z_offset_spinbox.setValue(0)
        params_layout.addRow("Z轴偏移:", self.z_offset_spinbox)
        
        params_group.setLayout(params_layout)
        main_layout.addWidget(params_group)
        
        # 预览区域
        preview_group = QGroupBox("新点预览")
        preview_layout = QVBoxLayout()
        
        # 刷新预览按钮
        refresh_button = QPushButton("刷新预览")
        refresh_button.clicked.connect(self.update_preview)
        preview_layout.addWidget(refresh_button)
        
        # 创建表格显示新点预览
        self.preview_table = QTableWidget()
        self.preview_table.setColumnCount(3)
        self.preview_table.setHorizontalHeaderLabels(["X", "Y", "Z"])
        self.preview_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        preview_layout.addWidget(self.preview_table)
        
        preview_group.setLayout(preview_layout)
        main_layout.addWidget(preview_group)
        
        # 对话框按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # 初始化预览
        self.update_preview()
        
        # 如果没有源点，显示警告
        if not self.source_points:
            QMessageBox.warning(self, "警告", "没有选中任何点进行复制。请先在表格中选择要复制的点。")
    
    def collect_selected_points(self):
        """收集所选点的信息"""
        try:
            # 清空当前源点列表
            self.source_points = []
            
            # 获取选中的项
            selected_items = self.parent.table_widget_2.selectedItems()
            if not selected_items:
                # 如果没有选中项，则考虑表格中的所有项
                print("未选中任何点，将使用表格中的所有点")
                row_count = self.parent.table_widget_2.rowCount()
                if row_count > 0:
                    selected_point_names = []
                    for row in range(row_count):
                        item = self.parent.table_widget_2.item(row, 0)
                        if item and item.text():
                            selected_point_names.append(item.text())
                else:
                    # 如果表格中也没有点，创建一个默认点(0,0,0)作为示例
                    print("表格中没有任何点，将创建默认点(0,0,0)作为示例")
                    self.source_points.append({
                        "name": "默认点",
                        "coordinates": [0, 0, 0]
                    })
                    return
            else:
                # 收集选中项的文本（点名称）
                selected_point_names = []
                for item in selected_items:
                    point_name = item.text()
                    if point_name and point_name not in selected_point_names:
                        selected_point_names.append(point_name)
            
            print(f"将复制的点名称: {selected_point_names}")
            
            # 确认父对象有point_list属性
            if not hasattr(self.parent, 'point_list'):
                print("父对象没有point_list属性")
                # 如果没有point_list属性，创建一个默认点
                self.source_points.append({
                    "name": "默认点",
                    "coordinates": [0, 0, 0]
                })
                return
                
            # 确认point_list不为空
            if not self.parent.point_list:
                print("point_list为空")
                # 如果point_list为空，创建一个默认点
                self.source_points.append({
                    "name": "默认点",
                    "coordinates": [0, 0, 0]
                })
                return
            
            # 从点列表中查找对应的点信息
            for point_data in self.parent.point_list:
                tree_item = point_data.get("tree_item")
                if tree_item and tree_item.text(0) in selected_point_names:
                    # 收集点信息
                    coords = point_data["coordinates"]
                    self.source_points.append({
                        "name": tree_item.text(0),
                        "coordinates": coords
                    })
                    print(f"找到点: {tree_item.text(0)}, 坐标: {coords}")
            
            # 如果没有找到任何点，尝试使用更宽松的匹配
            if not self.source_points:
                print("使用更宽松的匹配方式查找点...")
                for point_data in self.parent.point_list:
                    tree_item = point_data.get("tree_item")
                    if tree_item:
                        tree_text = tree_item.text(0)
                        # 检查任何选中的点名称是否是当前点名称的子串
                        for name in selected_point_names:
                            if name in tree_text or tree_text in name:
                                coords = point_data["coordinates"]
                                self.source_points.append({
                                    "name": tree_text,
                                    "coordinates": coords
                                })
                                print(f"使用宽松匹配找到点: {tree_text}, 坐标: {coords}")
                                break
                    
            # 如果仍然没有找到任何点，添加默认点
            if not self.source_points:
                print("未找到任何匹配的点，将创建默认点(0,0,0)作为示例")
                self.source_points.append({
                    "name": "默认点",
                    "coordinates": [0, 0, 0]
                })
                
            print(f"已收集{len(self.source_points)}个源点信息")
            
        except Exception as e:
            print(f"收集源点信息时发生错误: {e}")
            import traceback
            traceback.print_exc()
            # 确保至少有一个默认点
            if not self.source_points:
                self.source_points.append({
                    "name": "默认点",
                    "coordinates": [0, 0, 0]
                })
    
    def populate_source_points(self):
        """填充源点数据到表格"""
        self.source_table.setRowCount(len(self.source_points))
        
        for i, point_data in enumerate(self.source_points):
            # 点名称
            name_item = QTableWidgetItem(point_data["name"])
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
            self.source_table.setItem(i, 0, name_item)
            
            # X坐标
            x_item = QTableWidgetItem(f"{point_data['coordinates'][0]:.2f}")
            x_item.setFlags(x_item.flags() & ~Qt.ItemIsEditable)
            self.source_table.setItem(i, 1, x_item)
            
            # Y坐标
            y_item = QTableWidgetItem(f"{point_data['coordinates'][1]:.2f}")
            y_item.setFlags(y_item.flags() & ~Qt.ItemIsEditable)
            self.source_table.setItem(i, 2, y_item)
            
            # Z坐标
            z_item = QTableWidgetItem(f"{point_data['coordinates'][2]:.2f}")
            z_item.setFlags(z_item.flags() & ~Qt.ItemIsEditable)
            self.source_table.setItem(i, 3, z_item)
    
    def update_preview(self):
        """更新新点预览"""
        # 获取偏移值
        x_offset = self.x_offset_spinbox.value()
        y_offset = self.y_offset_spinbox.value()
        z_offset = self.z_offset_spinbox.value()
        
        # 计算新点坐标
        self.new_points = []
        for point_data in self.source_points:
            coords = point_data["coordinates"]
            new_x = coords[0] + x_offset
            new_y = coords[1] + y_offset
            new_z = coords[2] + z_offset
            self.new_points.append([new_x, new_y, new_z])
        
        # 更新预览表格
        self.preview_table.setRowCount(len(self.new_points))
        for i, coords in enumerate(self.new_points):
            # X坐标
            x_item = QTableWidgetItem(f"{coords[0]:.2f}")
            x_item.setFlags(x_item.flags() & ~Qt.ItemIsEditable)
            self.preview_table.setItem(i, 0, x_item)
            
            # Y坐标
            y_item = QTableWidgetItem(f"{coords[1]:.2f}")
            y_item.setFlags(y_item.flags() & ~Qt.ItemIsEditable)
            self.preview_table.setItem(i, 1, y_item)
            
            # Z坐标
            z_item = QTableWidgetItem(f"{coords[2]:.2f}")
            z_item.setFlags(z_item.flags() & ~Qt.ItemIsEditable)
            self.preview_table.setItem(i, 2, z_item)
            
        # 更新预览数量提示
        if hasattr(self, 'preview_count_label'):
            self.preview_count_label.setText(f"将创建 {len(self.new_points)} 个新点")
        else:
            self.preview_count_label = QLabel(f"将创建 {len(self.new_points)} 个新点")
            layout = self.preview_table.parent().layout()
            layout.addWidget(self.preview_count_label)
    
    def get_new_points(self):
        """获取新点坐标列表"""
        # 确保新点坐标已经更新
        x_offset = self.x_offset_spinbox.value()
        y_offset = self.y_offset_spinbox.value()
        z_offset = self.z_offset_spinbox.value()
        
        # 如果偏移都为0，提示用户
        if x_offset == 0 and y_offset == 0 and z_offset == 0:
            print("警告: 所有偏移值均为0，复制的点将与原点重叠")
        
        # 计算新点坐标
        self.new_points = []
        for point_data in self.source_points:
            coords = point_data["coordinates"]
            new_x = coords[0] + x_offset
            new_y = coords[1] + y_offset
            new_z = coords[2] + z_offset
            self.new_points.append([new_x, new_y, new_z])
        
        print(f"生成了{len(self.new_points)}个新点坐标")
        return self.new_points

    def accept(self):
        """重写accept方法，在对话框接受前进行额外检查"""
        # 如果没有源点或没有生成新点坐标，显示警告但仍允许关闭
        if not self.source_points or not self.new_points:
            # 如果表格中有数据但没有选中项，自动使用所有表格项
            if self.parent.table_widget_2.rowCount() > 0 and not self.parent.table_widget_2.selectedItems():
                # 重新收集所有表格项作为源点
                self.collect_selected_points()
                self.update_preview()
                
            # 如果仍然没有源点，显示警告
            if not self.source_points or not self.new_points:
                QMessageBox.warning(self, "警告", "没有点可以复制！请确保表格中有点或已选择源点。")
                # 用户仍可以选择继续
                super().accept()
                return
            
        # 检查偏移是否全为0
        x_offset = self.x_offset_spinbox.value()
        y_offset = self.y_offset_spinbox.value()
        z_offset = self.z_offset_spinbox.value()
        
        if x_offset == 0 and y_offset == 0 and z_offset == 0:
            # 询问用户是否确定要使用0偏移
            reply = QMessageBox.question(self, "确认", 
                                        "偏移值全为0，复制的点将与原点重叠。是否继续？", 
                                        QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                # 用户取消了操作，保持对话框打开
                return
            
        # 一切正常，关闭对话框并返回接受
        super().accept()
        
        # 调用清空所有选择及其操作功能
        if hasattr(self.parent, 'clear_all_checkboxes'):
            self.parent.clear_all_checkboxes()

