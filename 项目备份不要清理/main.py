# main.py
import sys
import os

# 在任何其他导入之前，先修复导入路径
try:
    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    sys.path.append(r"C:\Users\<USER>\.conda\envs\pyoccqt5\Library\bin")
    
    # 尝试导入并应用导入修复
    try:
        from fix_import import fix_sys_path, check_and_fix_imports
        fix_sys_path()
        check_and_fix_imports()
        print("已应用导入修复")
    except ImportError:
        print("无法导入导入修复模块，将继续尝试启动")
except Exception as e:
    print(f"应用导入修复时出错: {e}")

from PyQt5.QtWidgets import QApplication
from OCC.Display.backend import load_backend
# 仅在 main.py 中加载后端
load_backend("qt-pyqt5")
from modeling import Example
from OCC.Core.gp import gp_Pnt, gp_Vec, gp_Dir, gp_Ax1, gp_Ax2
from OCC.Core.Geom import Geom_Axis2Placement
from OCC.Core.AIS import AIS_Trihedron
from OCC.Core.Quantity import Quantity_NOC_BROWN, Quantity_Color, Quantity_NOC_WHITE
import copy

def main():
    """主函数"""
    app = QApplication(sys.argv)
    try:
        mc = Example()
        
        # 创建三轴坐标系
        origin = gp_Pnt(0, 0, 0)  # 原点
        x_point = gp_Pnt(2000, 0, 0)  # X轴终点，长度调整为200
        y_point = gp_Pnt(0, 2000, 0)  # Y轴终点，长度调整为200
        z_point = gp_Pnt(0, 0, 2000)  # Z轴终点，长度调整为200
        
        x_vector = gp_Vec(origin, x_point)
        x_dir = gp_Dir(x_vector / x_vector.Magnitude())  # X方向
        
        # 构造Z轴方向
        construct_vector = x_vector.Rotated(gp_Ax1(origin, gp_Dir(0, 0, 1)), 1.571)
        z_vector = x_vector.Crossed(construct_vector)
        z_dir = gp_Dir(z_vector / z_vector.Magnitude())  # Z方向
        
        # 创建坐标系
        axis = gp_Ax2(origin, z_dir, x_dir)
        trihedron_axis = Geom_Axis2Placement(axis)
        trihedron = AIS_Trihedron(trihedron_axis)
        
        # 设置三轴坐标系的颜色为深棕色
        brown_color = Quantity_Color(Quantity_NOC_BROWN)
        trihedron.SetColor(brown_color)
        
        # 设置三轴坐标系的大小（包括轴的长度和标记字样的大小）
        trihedron.SetSize(500)  # 调整大小，标记字样 XYZ 会相应变大
        
        # 将三轴坐标系保存到 Example 类的成员变量中
        mc.trihedron = trihedron
        
        # 显示三轴坐标系
        mc.display.GetContext().Display(trihedron, True)
        
        # 添加 reset_all_points_color 方法
        def reset_all_points_color(self):
            """重置所有点的颜色"""
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    point_data["ais_point"].SetColor(Quantity_Color(Quantity_NOC_WHITE))
        
        mc.reset_all_points_color = reset_all_points_color.__get__(mc)
        
        # 导入复制模块并设置X轴输入框的回车键事件
        try:
            # 确保Deepship目录在Python路径中
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.append(current_dir)
            
            # 直接从当前目录导入CopyPointDialog
            from copy import CopyPointDialog
            
            # 手动连接X轴输入框的回车键事件
            def on_x_axis_enter_pressed():
                if mc.text_edit_3.text():
                    mc.on_ok_button_3_clicked()
            
            # 连接X轴输入框的回车键事件
            mc.text_edit_3.returnPressed.connect(on_x_axis_enter_pressed)
            
            # 连接确认按钮的点击事件
            mc.ok_button_3.clicked.connect(mc.on_ok_button_3_clicked)
            
            print("复制模块已成功导入并完成所有初始化")
        except ImportError as e:
            print(f"导入复制模块失败: {e}")
            import traceback
            traceback.print_exc()
        
        mc.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()