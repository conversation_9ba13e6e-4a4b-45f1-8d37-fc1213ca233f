# 导入所需的库和模块
from typing import List, Tu<PERSON>, Dict, Any  # 导入类型注解相关模块
from PyQt5.QtWidgets import (QMainWindow, QTreeWidget, QTreeWidgetItem, QWidget, 
                            QGridLayout, QMessageBox, QShortcut, QMenu, QTextEdit,
                            QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, 
                            QLabel, QDoubleSpinBox, QPushButton)  # 导入PyQt5 GUI组件
from PyQt5.QtCore import pyqtSignal, Qt, QEvent  # 导入PyQt5核心模块
from PyQt5.QtGui import QIcon, QKeySequence, QColor  # 导入PyQt5图形模块
from OCC.Core.gp import gp_Pnt  # 导入OpenCASCADE几何点类
from OCC.Core.GC import GC_MakeSegment  # 导入OpenCASCADE线段创建类
from OCC.Core.AIS import AIS_Shape  # 导入OpenCASCADE形状显示类
from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge, BRepBuilderAPI_MakeVertex  # 导入OpenCASCADE边和顶点创建类
from OCC.Core.TopAbs import TopAbs_EDGE, TopAbs_VERTEX  # 导入OpenCASCADE拓扑类型
from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE, Quantity_NOC_YELLOW  # 导入OpenCASCADE颜色类
from OCC.Core.Aspect import Aspect_TOM_POINT, Aspect_TOM_PLUS, Aspect_TOM_O, Aspect_TOM_X  # 导入OpenCASCADE点样式
from OCC.Core.Prs3d import Prs3d_TextAspect  # 导入OpenCASCADE文本显示属性
from OCC.Core.Graphic3d import Graphic3d_MaterialAspect  # 导入OpenCASCADE材质属性
from OCC.Core.AIS import AIS_TextLabel  # 导入OpenCASCADE文本标签类
from OCC.Core.Aspect import Aspect_TOTP_LEFT_LOWER  # 导入OpenCASCADE文本位置
from OCC.Core.V3d import V3d_WIREFRAME  # 导入OpenCASCADE线框模式
from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE  # 导入OpenCASCADE颜色类
import re  # 导入正则表达式模块
from layout import ExampleLayout  # 导入自定义布局模块


class MyLine:
    pass  # 空类，用于占位

class Example(ExampleLayout):
    """主应用类，继承自ExampleLayout"""

    def __init__(self):
        super().__init__()  # 调用父类构造函数
        self.point_list: List[Dict[str, Any]] = []  # 存储点数据的列表
        self.shapeNew: List[Any] = []  # 存储新形状的列表
        self.endpoint_labels: Dict[str, Any] = {}  # 存储端点标签的字典
        self.highlighted_endpoints: Dict[str, Any] = {}  # 存储高亮端点的字典
        self.original_endpoints: Dict[str, Any] = {}  # 存储原始端点的字典

        # 初始化点顶层项，使其始终可用
        self.point_top_item = QTreeWidgetItem(self.tree, ['图集'])  # 创建树形控件顶层项
        self.point_top_item.setFlags(self.point_top_item.flags() | Qt.ItemIsUserCheckable)
        self.point_top_item.setCheckState(0, Qt.Checked)
        
        # 连接树形控件的点击事件
        self.tree.itemClicked.connect(self.on_tree_item_clicked)
        self.last_selected_item = None  # 用于存储最后选中的节点

        # 添加加号节点用于新建节点，并始终放置在底部
        self.add_node_item = QTreeWidgetItem(self.tree, ['+ 新建节点'])
        self.add_node_item.setFlags(self.add_node_item.flags() | Qt.ItemIsEnabled)

        # 添加减号节点用于删除节点，并始终放置在底部
        self.delete_node_item = QTreeWidgetItem(self.tree, ['- 删除节点'])
        self.delete_node_item.setFlags(self.delete_node_item.flags() | Qt.ItemIsEnabled)

        # 添加设置活跃节点项，并始终放置在底部
        self.active_node_item = QTreeWidgetItem(self.tree, ['* 设置活跃节点'])
        self.active_node_item.setFlags(self.active_node_item.flags() | Qt.ItemIsEnabled)

        # 连接复选框的功能
        # self.connect_checkboxes()

        self.tree.expandAll()  # 展开所有树项

        # 设置切矢箭头自适应缩放
        self.setup_tangent_arrow_scaling()

        # 初始化切矢箭头管理
        self.setup_tangent_arrow_management()
        
    def connect_checkboxes(self):
        """连接复选框到相应的功能方法"""
        # 复制功能复选框
        self.checkbox_1.toggled.connect(self.on_copy_checkbox_toggled)
        
    def on_copy_checkbox_toggled(self, checked):
        """处理复制复选框状态变化"""
        if checked:
            print("复制功能已启用")
            # 当选中复制复选框时，不要清空表格中已选择的点
            # 尝试调用复制方法
            if hasattr(self, 'execute_copy_action'):
                try:
                    print("尝试执行复制操作")
                    self.execute_copy_action()
                except Exception as e:
                    print(f"重新构建样条线时发生错误: {e}")
                    import traceback
                    traceback.print_exc()
        else:
            print("复制功能已禁用")
            # 当取消选中复制复选框时，清理相关资源
            if hasattr(self, 'clear_copy_action'):
                self.clear_copy_action()

    def clear_active_state(self):
        """
        清除所有节点的活跃状态
        """
        # 重置树中的所有高亮显示
        self.reset_tree_highlighting()

        # 递归清除所有节点的背景色
        def clear_background(item):
            item.setBackground(0, Qt.white)
            for i in range(item.childCount()):
                clear_background(item.child(i))

        # 遍历所有顶层节点
        for i in range(self.tree.topLevelItemCount()):
            top_item = self.tree.topLevelItem(i)
            clear_background(top_item)

    def clean_display(self):
        """
        彻底清理显示，确保没有残留对象
        """
        try:
            context = self.display.GetContext()

            # 1. 保存所有需要保留的对象引用
            objects_to_preserve = []
            if hasattr(self, 'trihedron'):
                objects_to_preserve.append(self.trihedron)

            # 2. 彻底清空显示
            context.RemoveAll(False)  # 使用Remove而不是Erase，确保彻底清理

            # 3. 重新显示需要保留的对象
            for obj in objects_to_preserve:
                context.Display(obj, False)

            # 4. 重新显示所有应该显示的点
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    if "tree_item" in point_data and point_data["tree_item"].checkState(0) == Qt.Checked:
                        # 确保父节点也是选中状态
                        parent = point_data["tree_item"].parent()
                        all_parents_checked = True
                        while parent:
                            if parent.checkState(0) != Qt.Checked:
                                all_parents_checked = False
                                break
                            parent = parent.parent()

                        # 只有当所有父节点都是选中状态时才显示
                        if all_parents_checked:
                            context.Display(point_data["ais_point"], False)
                            
            # 5. 重新显示所有应该显示的样条线
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    if "tree_item" in spline_data and spline_data["tree_item"].checkState(0) == Qt.Checked:
                        # 确保父节点也是选中状态
                        parent = spline_data["tree_item"].parent()
                        all_parents_checked = True
                        while parent:
                            if parent.checkState(0) != Qt.Checked:
                                all_parents_checked = False
                                break
                            parent = parent.parent()

                        # 只有当所有父节点都是选中状态时才显示
                        if all_parents_checked:
                            context.Display(spline_data["ais_spline"], False)
                            # 同时显示构成样条线的点
                            for point_data in spline_data.get("point_ais_list", []):
                                context.Display(point_data["ais_point"], False)

            # 6. 强制更新显示
            context.UpdateCurrentViewer()
            self.display.Repaint()

        except Exception as e:
            print(f"清理显示时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def highlight_item_in_canvas(self, item):
        """
        当在树形控件中选择节点时，高亮显示画布中对应的图形并更新被操作对象表格

        Args:
            item: 被选中的树节点
        """
        try:
            # 先重置所有点的样式
            self.reset_canvas_point_style()
            # 重置所有样条线的样式
            self.reset_canvas_spline_style()
            
            # 保存当前活跃图层
            active_layer = self.find_active_layer()
            
            # 重置所有树节点的高亮状态，但保留活跃图层
            self.reset_tree_highlighting(preserve_active=True)
            
            # 如果点击的是活跃图层，不更改其颜色
            if item.background(0) != Qt.yellow:
                # 高亮显示当前选中的树节点
                self.highlight_tree_item(item)

            # 清除之前的选择
            self.shapeNew.clear()

            # 定义一个变量来跟踪是否找到了任何匹配的点
            found_matching_point = False
            found_matching_spline = False

            # 查找并高亮显示与选中节点相关的点
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    # 情况1: 直接点击点项
                    if point_data.get("tree_item") == item:
                        self.highlight_point(point_data)
                        # 添加到被操作对象表格
                        self.add_to_table_widget_2(item.text(0), item.parent().text(0) if item.parent() else "")
                        found_matching_point = True
                        
                    # 情况2: 点击点的坐标子项
                    elif item.parent() and point_data.get("tree_item") == item.parent():
                        self.highlight_point(point_data)
                        # 添加到被操作对象表格，使用点的名称而不是坐标子项
                        self.add_to_table_widget_2(item.parent().text(0), 
                                                item.parent().parent().text(0) if item.parent().parent() else "")
                        found_matching_point = True
                        
                    # 情况3: 点击包含点的层或子层
                    elif point_data.get("tree_item") and self.is_child_of(point_data["tree_item"], item):
                        self.highlight_point(point_data)
                        # 添加到被操作对象表格
                        tree_item = point_data.get("tree_item")
                        if tree_item and tree_item.parent():
                            self.add_to_table_widget_2(tree_item.text(0), tree_item.parent().text(0))
                        found_matching_point = True

            # 查找并高亮显示与选中节点相关的样条线
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    # 情况1: 直接点击样条线项
                    if spline_data.get("tree_item") == item:
                        self.highlight_spline(spline_data)
                        # 添加到被操作对象表格
                        self.add_to_table_widget_2(item.text(0), item.parent().text(0) if item.parent() else "")
                        found_matching_spline = True
                        
                    # 情况2: 点击样条线的坐标子项
                    elif item.parent() and spline_data.get("tree_item") == item.parent():
                        self.highlight_spline(spline_data)
                        # 添加到被操作对象表格，使用样条线的名称而不是坐标子项
                        self.add_to_table_widget_2(item.parent().text(0), 
                                                item.parent().parent().text(0) if item.parent().parent() else "")
                        found_matching_spline = True
                        
                    # 情况3: 点击包含样条线的层或子层
                    elif spline_data.get("tree_item") and self.is_child_of(spline_data["tree_item"], item):
                        self.highlight_spline(spline_data)
                        # 添加到被操作对象表格
                        tree_item = spline_data.get("tree_item")
                        if tree_item and tree_item.parent():
                            self.add_to_table_widget_2(tree_item.text(0), tree_item.parent().text(0))
                        found_matching_spline = True

            # 如果找不到任何匹配的点或样条线，则不做处理，保持当前选择
            if not found_matching_point and not found_matching_spline:
                print(f"未找到与树节点 '{item.text(0)}' 相关联的几何对象")

            # 更新视图
            self.display.Context.UpdateCurrentViewer()
            self.display.Repaint()
            
        except Exception as e:
            print(f"高亮显示项目时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def on_tree_item_clicked(self, item, column):
        try:
            # 更新最后选中的节点
            if item != self.add_node_item and item != self.delete_node_item and item != self.active_node_item:
                self.last_selected_item = item

                # 检查是否点击了活跃图层
                is_active_layer = item.background(0) == Qt.yellow
                
                # 只有当点击的不是活跃图层时才高亮显示对应图形
                if not is_active_layer:
                    # 新增：处理节点选中后画布中高亮显示对应图形
                    self.highlight_item_in_canvas(item)

            # 如果点击的是加号节点
            if item == self.add_node_item:
                # 在最后选中的节点下新建子节点
                if self.last_selected_item:
                    # 直接新建一个名为"新图层"的子节点
                    new_item = QTreeWidgetItem(self.last_selected_item, ["新图层"])
                    new_item.setFlags(new_item.flags() | Qt.ItemIsEditable | Qt.ItemIsUserCheckable)
                    new_item.setCheckState(0, Qt.Checked)
                    self.tree.expandItem(self.last_selected_item)
                else:
                    # 如果没有选中的节点，则在顶层新建父节点
                    new_item = QTreeWidgetItem(self.tree, ["新图层"])
                    new_item.setFlags(new_item.flags() | Qt.ItemIsEditable | Qt.ItemIsUserCheckable)
                    new_item.setCheckState(0, Qt.Checked)
                    # 将新节点插入到加号节点之前
                    self.tree.insertTopLevelItem(self.tree.indexOfTopLevelItem(self.add_node_item), new_item)
                return

            # 如果点击的是减号节点
            if item == self.delete_node_item:
                # 删除最后选中的节点
                if self.last_selected_item:
                    # 检查是否正在删除活跃图层
                    if self.last_selected_item.background(0) == Qt.yellow:
                        # 如果删除的是活跃图层，清除活跃状态
                        self.clear_active_state()
                    # 调用新的删除节点方法，会处理节点及其所有子节点的删除
                    self.delete_tree_node_with_children(self.last_selected_item)
                    self.last_selected_item = None
                return

            # 如果点击的是设置活跃节点项
            if item == self.active_node_item:
                # 设置最后选中的节点为活跃节点
                if self.last_selected_item:
                    # 先清除所有节点的活跃状态
                    self.clear_active_state()
                    # 设置当前选中节点为活跃状态
                    self.last_selected_item.setBackground(0, Qt.yellow)
                return

            # 检查点击的是否为活跃图层
            if item.background(0) == Qt.yellow and column == 0 and item.checkState(0) is not None:
                # 如果点击的是活跃图层，只处理勾选状态的变化，不影响活跃状态
                check_state = item.checkState(column)
                
                # 如果这是一个显示/隐藏操作，先进行一次彻底清理
                self.clean_display()
                
                # 如果点击的是父节点，递归设置所有子节点的勾选状态
                self.set_children_check_state(item, check_state)
                    
                # 处理点顶层项特殊情况
                if item == self.point_top_item:
                    if check_state == Qt.Unchecked:
                        self.hide_children_points(item)
                    else:
                        self.show_children_points(item)
                    return
                    
                # 处理活跃图层的显示/隐藏
                if check_state == Qt.Unchecked:
                    self.hide_children_points(item)
                else:
                    self.show_children_points(item)
                return

            # 处理勾选状态变化
            if column == 0 and item.checkState(0) is not None:
                # 获取节点的勾选状态
                check_state = item.checkState(column)

                # 如果这是一个显示/隐藏操作，先进行一次彻底清理
                self.clean_display()

                # 如果点击的是父节点，递归设置所有子节点的勾选状态
                self.set_children_check_state(item, check_state)

                # 检查是否点击了活跃图层或其子项
                is_active_or_child = self.is_active_layer_or_child(item)

                # 处理点顶层项特殊情况
                if item == self.point_top_item:
                    if check_state == Qt.Unchecked:
                        self.hide_children_points(item)
                    else:
                        self.show_children_points(item)
                    return

                # 处理活跃图层或其子项的显示/隐藏
                if is_active_or_child:
                    if check_state == Qt.Unchecked:
                        self.hide_children_points(item)
                    else:
                        self.show_children_points(item)
                    return

                # 处理点项和样条线项的显示/隐藏
                self.handle_point_item_visibility(item, check_state)

            # 检查是否是点节点（不是坐标子项）
            if item.parent() and not (item.text(0).startswith("X:") or 
                                    item.text(0).startswith("Y:") or 
                                    item.text(0).startswith("Z:")):
                # 查找对应的点数据
                if hasattr(self, 'point_list'):
                    for point_data in self.point_list:
                        if point_data.get("tree_item") == item:
                            # 添加到被操作对象表格
                            try:
                                self.add_to_table_widget_2(item.text(0), item.parent().text(0))
                            except Exception as e:
                                print(f"添加点到被操作对象表格时出错: {e}")
                            break
            
            # 检查是否点击了坐标子项
            elif item.parent() and item.parent().parent() and (item.text(0).startswith("X:") or 
                                                              item.text(0).startswith("Y:") or 
                                                              item.text(0).startswith("Z:")):
                # 使用其父项（点）
                point_item = item.parent()
                # 添加到被操作对象表格
                try:
                    self.add_to_table_widget_2(point_item.text(0), point_item.parent().text(0))
                except Exception as e:
                    print(f"添加点到被操作对象表格时出错: {e}")
            
        except Exception as e:
            print(f"Error in on_tree_item_clicked: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")

    def delete_tree_node_with_children(self, node):
        """
        删除树节点及其所有子节点，同时删除相关联的画布上的点

        Args:
            node: 要删除的树节点
        """
        try:
            # 首先获取该节点下所有的点数据（直接子点和子层级下的点）
            points_to_delete = []

            # 递归收集所有需要删除的点
            def collect_points_to_delete(item):
                # 检查当前节点是否关联到点列表中的点
                if hasattr(self, 'point_list'):
                    for point_data in self.point_list:
                        if point_data.get("tree_item") == item:
                            points_to_delete.append(point_data)

                # 递归检查所有子节点
                for i in range(item.childCount()):
                    child = item.child(i)
                    collect_points_to_delete(child)

            # 收集需要删除的点
            collect_points_to_delete(node)

            # 删除画布上的点
            context = self.display.GetContext()
            for point_data in points_to_delete:
                # 从显示上下文中移除点
                context.Erase(point_data["ais_point"], False)
                # 从点列表中移除点数据
                if point_data in self.point_list:
                    self.point_list.remove(point_data)

            # 从树中移除节点
            parent = node.parent()
            if parent:
                parent.removeChild(node)
            else:
                # 如果是顶层节点
                index = self.tree.indexOfTopLevelItem(node)
                if (index >= 0):
                    self.tree.takeTopLevelItem(index)

            # 更新显示
            context.UpdateCurrentViewer()
            self.display.Repaint()

            # 更新list.txt文件
            self.update_list_txt()

            # 显示状态信息
            deleted_count = len(points_to_delete)
            if deleted_count > 0:
                self.statusBar().showMessage(f"已删除节点及其{deleted_count}个关联点", 3000)
            else:
                self.statusBar().showMessage("已删除节点", 3000)

        except Exception as e:
            print(f"删除节点时发生错误: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"删除节点时发生错误: {str(e)}")

    def highlight_spline(self, spline_data):
        """
        高亮显示指定的样条线

        Args:
            spline_data: 包含样条线信息的字典
        """
        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_RED
        from OCC.Core.Aspect import Aspect_TOM_PLUS
        from OCC.Core.Prs3d import Prs3d_PointAspect

        # 设置样条线颜色为红色高亮
        spline_data["ais_spline"].SetColor(Quantity_Color(Quantity_NOC_RED))
        spline_data["ais_spline"].SetWidth(3.0)

        # 添加到选中容器中
        self.shapeNew.append(spline_data["ais_spline"])

        # 立即更新视图以确保颜色变化立即显示
        self.display.Context.Redisplay(spline_data["ais_spline"], True)
        
        # 高亮显示构成样条线的点 - 使用统一样式
        for point_data in spline_data.get("point_ais_list", []):
            # 使用统一的点样式设置函数，红色高亮
            self.set_unified_point_style(point_data["ais_point"], "RED", True)

            # 添加到选中容器中
            self.shapeNew.append(point_data["ais_point"])

            # 立即更新视图以确保颜色变化立即显示
            self.display.Context.Redisplay(point_data["ais_point"], True)

    def highlight_point(self, point_data):
        """
        高亮显示指定的点

        Args:
            point_data: 包含点信息的字典
        """
        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_RED
        from OCC.Core.Aspect import Aspect_TOM_PLUS
        from OCC.Core.Prs3d import Prs3d_PointAspect

        # 使用统一的点样式设置函数，红色高亮
        self.set_unified_point_style(point_data["ais_point"], "RED", True)

        # 添加到选中容器中
        self.shapeNew.append(point_data["ais_point"])

        # 立即更新视图以确保颜色变化立即显示
        self.display.Context.Redisplay(point_data["ais_point"], True)

    def is_active_layer_or_child(self, item):
        """
        检查节点是否是活跃图层或其子项

        Args:
            item: 要检查的节点

        Returns:
            bool: 如果是活跃图层或其子项则返回True，否则返回False
        """
        # 检查自身是否是活跃图层
        if item.background(0) == Qt.yellow:
            return True

        # 递归检查父节点链
        parent = item.parent()
        while parent:
            if parent.background(0) == Qt.yellow:
                return True
            parent = parent.parent()

        return False

    def hide_children_points(self, item):
        """
        隐藏指定节点下的所有点和样条线

        Args:
            item: 要处理的节点
        """
        try:
            # 创建两个列表，分别存储需要隐藏的点和需要递归设置勾选状态的子节点
            points_to_hide = []
            children_to_process = []

            # 收集所有需要隐藏的点
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    if point_data.get("tree_item") and self.is_child_of(point_data["tree_item"], item):
                        points_to_hide.append(point_data["ais_point"])

            # 收集所有需要递归设置勾选状态的子节点
            for i in range(item.childCount()):
                children_to_process.append(item.child(i))

            # 批量隐藏点
            if points_to_hide:
                context = self.display.GetContext()
                for point in points_to_hide:
                    context.Erase(point, False)  # 使用False避免立即更新视图，提高效率

                # 显式调用Remove而不只是Erase，确保完全释放对象
                for point in points_to_hide:
                    if context.IsDisplayed(point):
                        context.Remove(point, False)

                # 统一更新一次视图，提高性能
                context.UpdateCurrentViewer()

            # 隐藏样条线对象
            self.hide_children_splines(item)

            # 递归处理子节点的勾选状态
            for child in children_to_process:
                child.setCheckState(0, item.checkState(0))
                self.hide_children_points(child)

        except Exception as e:
            print(f"隐藏节点点时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def show_children_points(self, item):
        """
        显示指定节点下的所有点和样条线

        Args:
            item: 要处理的节点
        """
        try:
            # 创建两个列表，分别存储需要显示的点和需要递归设置勾选状态的子节点
            points_to_show = []
            children_to_process = []

            # 收集所有需要显示的点
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    if point_data.get("tree_item") and self.is_child_of(point_data["tree_item"], item):
                        points_to_show.append(point_data["ais_point"])

            # 收集所有需要递归设置勾选状态的子节点
            for i in range(item.childCount()):
                children_to_process.append(item.child(i))

            # 批量显示点
            if points_to_show:
                context = self.display.GetContext()
                for point in points_to_show:
                    # 确保点不在显示状态后再显示，避免重复显示导致的问题
                    if not context.IsDisplayed(point):
                        context.Display(point, False)  # 使用False避免立即更新视图，提高效率

                # 统一更新一次视图，提高性能
                context.UpdateCurrentViewer()

            # 显示样条线对象
            self.show_children_splines(item)

            # 递归处理子节点的勾选状态
            for child in children_to_process:
                child.setCheckState(0, item.checkState(0))
                self.show_children_points(child)

        except Exception as e:
            print(f"显示节点点时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def hide_children_splines(self, item):
        """
        隐藏指定节点下的所有样条线和构成点

        Args:
            item: 要处理的节点
        """
        try:
            # 收集所有需要隐藏的样条线
            splines_to_hide = []
            
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    if spline_data.get("tree_item") and self.is_child_of(spline_data["tree_item"], item):
                        splines_to_hide.append(spline_data)

            # 隐藏样条线和构成点
            if splines_to_hide:
                context = self.display.GetContext()
                for spline_data in splines_to_hide:
                    # 隐藏样条线
                    context.Erase(spline_data["ais_spline"], False)
                    
                    # 隐藏构成样条线的点
                    for point_data in spline_data.get("point_ais_list", []):
                        context.Erase(point_data["ais_point"], False)
                    
                    # 显式调用Remove而不只是Erase，确保完全释放对象
                    if context.IsDisplayed(spline_data["ais_spline"]):
                        context.Remove(spline_data["ais_spline"], False)

                # 统一更新一次视图，提高性能
                context.UpdateCurrentViewer()

        except Exception as e:
            print(f"隐藏节点样条线时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def show_children_splines(self, item):
        """
        显示指定节点下的所有样条线和构成点

        Args:
            item: 要处理的节点
        """
        try:
            # 收集所有需要显示的样条线
            splines_to_show = []
            
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    if spline_data.get("tree_item") and self.is_child_of(spline_data["tree_item"], item):
                        splines_to_show.append(spline_data)

            # 显示样条线和构成点
            if splines_to_show:
                context = self.display.GetContext()
                for spline_data in splines_to_show:
                    # 显示样条线
                    # 确保样条线不在显示状态后再显示，避免重复显示导致的问题
                    if not context.IsDisplayed(spline_data["ais_spline"]):
                        context.Display(spline_data["ais_spline"], False)
                        
                    # 显示构成样条线的点
                    for point_data in spline_data.get("point_ais_list", []):
                        if not context.IsDisplayed(point_data["ais_point"]):
                            context.Display(point_data["ais_point"], False)

                # 统一更新一次视图，提高性能
                context.UpdateCurrentViewer()

        except Exception as e:
            print(f"显示节点样条线时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def handle_point_item_visibility(self, item, check_state):
        """
        处理点项和样条线项的显示/隐藏

        Args:
            item: 要处理的点项或样条线项
            check_state: 勾选状态
        """
        # 处理点项的显示/隐藏
        if hasattr(self, 'point_list'):
            # 处理直接点击点项的情况
            for point_data in self.point_list:
                if point_data.get("tree_item") == item:
                    if check_state == Qt.Unchecked:
                        self.display.GetContext().Erase(point_data["ais_point"], True)
                    else:
                        self.display.GetContext().Display(point_data["ais_point"], True)
                    return

                # 处理点击点坐标项的情况
                if item.parent() == point_data.get("tree_item"):
                    if check_state == Qt.Unchecked:
                        self.display.GetContext().Erase(point_data["ais_point"], True)
                    else:
                        self.display.GetContext().Display(point_data["ais_point"], True)
                    return

        # 处理样条线项的显示/隐藏
        if hasattr(self, 'spline_list'):
            # 处理直接点击样条线项的情况
            for spline_data in self.spline_list:
                if spline_data.get("tree_item") == item:
                    if check_state == Qt.Unchecked:
                        self.display.GetContext().Erase(spline_data["ais_spline"], True)
                        # 同时隐藏构成样条线的点
                        for point_data in spline_data.get("point_ais_list", []):
                            self.display.GetContext().Erase(point_data["ais_point"], False)
                    else:
                        self.display.GetContext().Display(spline_data["ais_spline"], True)
                        # 同时显示构成样条线的点
                        for point_data in spline_data.get("point_ais_list", []):
                            self.display.GetContext().Display(point_data["ais_point"], False)
                    return

                # 处理点击样条线坐标项的情况
                if item.parent() == spline_data.get("tree_item"):
                    if check_state == Qt.Unchecked:
                        self.display.GetContext().Erase(spline_data["ais_spline"], True)
                        # 同时隐藏构成样条线的点
                        for point_data in spline_data.get("point_ais_list", []):
                            self.display.GetContext().Erase(point_data["ais_point"], False)
                    else:
                        self.display.GetContext().Display(spline_data["ais_spline"], True)
                        # 同时显示构成样条线的点
                        for point_data in spline_data.get("point_ais_list", []):
                            self.display.GetContext().Display(point_data["ais_point"], False)
                    return

    def is_child_of(self, child, parent):
        """
        检查一个节点是否是另一个节点的子节点（直接或间接）

        Args:
            child: 可能的子节点
            parent: 可能的父节点

        Returns:
            bool: 如果child是parent的子节点则返回True，否则返回False
        """
        # 直接检查是否为子节点
        if child.parent() == parent:
            return True

        # 递归检查父节点链
        current = child.parent()
        while current:
            if current == parent:
                return True
            current = current.parent()

        return False

    def set_children_check_state(self, item, check_state):
        """
        递归设置所有子节点的勾选状态

        Args:
            item: 当前节点
            check_state: 勾选状态
        """
        for i in range(item.childCount()):
            child_item = item.child(i)
            child_item.setCheckState(0, check_state)
            self.set_children_check_state(child_item, check_state)

    def set_unified_point_style(self, ais_point, color_name="WHITE", is_highlighted=False):
        """
        设置统一的点样式 - 所有点都使用交叉型样式和固定大小

        Args:
            ais_point: AIS_Shape 点对象
            color_name: 颜色名称 ("WHITE", "RED", "GREEN" 等)
            is_highlighted: 是否为高亮状态（不影响大小，只影响颜色）
        """
        from OCC.Core.Aspect import Aspect_TOM_PLUS
        from OCC.Core.Prs3d import Prs3d_PointAspect
        from OCC.Core.Quantity import (Quantity_Color, Quantity_NOC_WHITE,
                                       Quantity_NOC_RED, Quantity_NOC_GREEN)

        # 统一的点大小 - 无论什么状态都使用相同大小
        UNIFIED_POINT_SIZE = 6

        # 根据颜色名称选择颜色
        color_map = {
            "WHITE": Quantity_Color(Quantity_NOC_WHITE),
            "RED": Quantity_Color(Quantity_NOC_RED),
            "GREEN": Quantity_Color(Quantity_NOC_GREEN)
        }

        color = color_map.get(color_name, Quantity_Color(Quantity_NOC_WHITE))

        try:
            # 设置点的显示样式为交叉型，使用统一大小
            drawer = ais_point.Attributes()
            point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, color, UNIFIED_POINT_SIZE)
            drawer.SetPointAspect(point_aspect)
            ais_point.SetAttributes(drawer)

            # 设置点的颜色
            ais_point.SetColor(color)

        except Exception as e:
            print(f"设置点样式时发生错误: {e}")

    def reset_canvas_point_style(self):
        """
        重置所有点的样式为默认样式
        """
        if hasattr(self, 'point_list'):
            # 收集所有需要重置的点
            points_to_reset = []
            for point_data in self.point_list:
                points_to_reset.append(point_data["ais_point"])

            # 批量重置点样式 - 使用统一样式函数
            if points_to_reset:
                context = self.display.GetContext()
                for point in points_to_reset:
                    self.set_unified_point_style(point, "WHITE", False)

                context.UpdateCurrentViewer()  # 统一更新视图

    def also_on_select(self, shapes):
        # 🔧 修复：添加调试信息以便跟踪样条线选择问题
        print(f"🔍 also_on_select 被调用，shapes参数: {len(shapes) if shapes else 0}个对象")

        # 重置所有点的样式为默认样式
        self.reset_canvas_point_style()
        # 重置所有样条线的样式为默认样式
        self.reset_canvas_spline_style()
        # 重置所有树节点的高亮状态，但保留活跃图层
        self.reset_tree_highlighting(preserve_active=True)
        self.shapeNew.clear()  # 清除之前的选中项

        # 如果shapes参数为空，可能是通过canvas的信号触发的，而不是通过OCC的选中事件
        if not shapes:
            print("🔍 shapes参数为空，从上下文获取选中对象")
            context = self.display.GetContext()
            context.InitSelected()
            selected_shapes = []
            while context.MoreSelected():
                selected_shapes.append(context.SelectedShape())
                context.NextSelected()
            shapes = selected_shapes
            print(f"🔍 从上下文获取到 {len(shapes)} 个选中对象")
        
        for i, shape in enumerate(shapes):
            print(f"🔍 处理第{i+1}个选中对象: {type(shape)}")
            # 检查选中的是否是点
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    # 尝试不同的比较方法以提高鲁棒性
                    match_found = False
                    
                    # 方法1: 使用IsEqual方法比较
                    if (hasattr(shape, "IsEqual") and
                        hasattr(point_data["vertex"], "IsEqual") and
                        shape.IsEqual(point_data["vertex"])):
                        match_found = True
                    
                    # 方法2: 比较AIS_Shape对象
                    if not match_found and point_data["ais_point"] == shape:
                        match_found = True
                        
                    # 方法3: 比较坐标值
                    if not match_found and hasattr(shape, "Coordinates") and hasattr(point_data["vertex"], "Coordinates"):
                        try:
                            shape_coords = shape.Coordinates()
                            vertex_coords = point_data["vertex"].Coordinates()
                            if (abs(shape_coords[0] - vertex_coords[0]) < 1e-6 and
                                abs(shape_coords[1] - vertex_coords[1]) < 1e-6 and
                                abs(shape_coords[2] - vertex_coords[2]) < 1e-6):
                                match_found = True
                        except:
                            pass

                    if match_found:
                        # 更新树形控件的选中项并高亮显示
                        tree_item = point_data.get("tree_item")
                        if tree_item:
                            self.tree.setCurrentItem(tree_item)
                            self.tree.expandItem(tree_item)
                            self.tree.scrollToItem(tree_item)
                            # 高亮显示树节点，保留活跃图层
                            self.highlight_tree_item(tree_item)

                        # 设置颜色为红色高亮，并使用更明显的点标记
                        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_RED
                        from OCC.Core.Aspect import Aspect_TOM_PLUS
                        from OCC.Core.Prs3d import Prs3d_PointAspect

                        # 使用统一的点样式设置函数，红色高亮
                        self.set_unified_point_style(point_data["ais_point"], "RED", True)

                        # 添加到选中容器中
                        self.shapeNew.append(point_data["ais_point"])

                        # 立即更新视图以确保颜色变化立即显示
                        self.display.Context.Redisplay(point_data["ais_point"], True)
                        self.display.Context.UpdateCurrentViewer()
                        self.display.Repaint()
                        
                        # 新增：添加到被操作对象表格
                        tree_item = point_data.get("tree_item")
                        if tree_item:
                            point_name = tree_item.text(0)
                            parent_item = tree_item.parent()
                            if parent_item:
                                parent_name = parent_item.text(0)
                                self.add_to_table_widget_2(point_name, parent_name)
                        return
                        
            # 检查选中的是否是样条线
            if hasattr(self, 'spline_list'):
                print(f"🔍 开始检查样条线匹配，当前有 {len(self.spline_list)} 条样条线")
                for j, spline_data in enumerate(self.spline_list):
                    print(f"🔍 检查第{j+1}条样条线")
                    # 🔧 修复：使用多种方法比较样条线，提高识别准确性
                    match_found = False

                    # 方法1: 直接比较AIS_Shape对象
                    if spline_data["ais_spline"] == shape:
                        match_found = True
                        print(f"🎯 样条线匹配成功 - 方法1: AIS对象直接比较")

                    # 方法2: 比较底层的Edge对象
                    if not match_found and hasattr(spline_data, "edge") and hasattr(shape, "IsEqual"):
                        try:
                            if shape.IsEqual(spline_data["edge"]):
                                match_found = True
                                print(f"🎯 样条线匹配成功 - 方法2: Edge对象比较")
                        except Exception as e:
                            print(f"⚠️ Edge对象比较失败: {e}")

                    # 方法3: 通过AIS对象的Shape方法获取底层形状进行比较
                    if not match_found:
                        try:
                            ais_shape = spline_data["ais_spline"]
                            if hasattr(ais_shape, "Shape") and hasattr(shape, "IsEqual"):
                                if shape.IsEqual(ais_shape.Shape()):
                                    match_found = True
                                    print(f"🎯 样条线匹配成功 - 方法3: AIS.Shape()比较")
                        except Exception as e:
                            print(f"⚠️ AIS.Shape()比较失败: {e}")

                    if match_found:
                        print(f"✅ 找到匹配的样条线，开始更新tree高亮")

                        # 🔧 新增：保存最近选中的样条线，供双击使用
                        self.last_selected_spline = spline_data
                        print(f"📝 保存最近选中的样条线，供双击使用")

                        # 更新树形控件的选中项并高亮显示
                        tree_item = spline_data.get("tree_item")
                        if tree_item:
                            print(f"✅ 样条线有对应的tree节点: {tree_item.text(0)}")
                            self.tree.setCurrentItem(tree_item)
                            self.tree.expandItem(tree_item)
                            self.tree.scrollToItem(tree_item)
                            # 高亮显示树节点，保留活跃图层
                            self.highlight_tree_item(tree_item)
                            print(f"✅ tree节点高亮设置完成")
                        else:
                            print(f"❌ 样条线没有对应的tree节点")

                        # 设置样条线颜色为红色高亮
                        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_RED
                        spline_data["ais_spline"].SetColor(Quantity_Color(Quantity_NOC_RED))
                        spline_data["ais_spline"].SetWidth(3.0)

                        # 添加到选中容器中
                        self.shapeNew.append(spline_data["ais_spline"])

                        # 立即更新视图以确保颜色变化立即显示
                        self.display.Context.Redisplay(spline_data["ais_spline"], True)
                        self.display.Context.UpdateCurrentViewer()
                        self.display.Repaint()
                        
                        # 高亮显示构成样条线的点
                        from OCC.Core.Aspect import Aspect_TOM_PLUS
                        from OCC.Core.Prs3d import Prs3d_PointAspect
                        for point_data in spline_data.get("point_ais_list", []):
                            # 使用统一的点样式设置函数，红色高亮
                            self.set_unified_point_style(point_data["ais_point"], "RED", True)

                            # 添加到选中容器中
                            self.shapeNew.append(point_data["ais_point"])
                            
                            # 立即更新视图以确保颜色变化立即显示
                            self.display.Context.Redisplay(point_data["ais_point"], True)
                        
                        self.display.Context.UpdateCurrentViewer()
                        self.display.Repaint()
                        
                        # 新增：添加到被操作对象表格
                        tree_item = spline_data.get("tree_item")
                        if tree_item:
                            spline_name = tree_item.text(0)
                            parent_item = tree_item.parent()
                            if parent_item:
                                parent_name = parent_item.text(0)
                                self.add_to_table_widget_2(spline_name, parent_name)
                        return
                    else:
                        print(f"❌ 第{j+1}条样条线不匹配")

            print(f"🔍 样条线匹配检查完成，未找到匹配的样条线")
            # 检查选中的是否是样条线的构成点
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    for point_data in spline_data.get("point_ais_list", []):
                        # 比较AIS_Shape对象
                        if point_data["ais_point"] == shape:
                            # 更新树形控件的选中项并高亮显示
                            tree_item = spline_data.get("tree_item")
                            if tree_item:
                                self.tree.setCurrentItem(tree_item)
                                self.tree.expandItem(tree_item)
                                self.tree.scrollToItem(tree_item)
                                # 高亮显示树节点，保留活跃图层
                                self.highlight_tree_item(tree_item)

                            # 设置点颜色为红色高亮，并使用更明显的点标记
                            from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_RED
                            from OCC.Core.Aspect import Aspect_TOM_PLUS
                            from OCC.Core.Prs3d import Prs3d_PointAspect

                            # 使用统一的点样式设置函数，红色高亮
                            self.set_unified_point_style(point_data["ais_point"], "RED", True)

                            # 添加到选中容器中
                            self.shapeNew.append(point_data["ais_point"])

                            # 立即更新视图以确保颜色变化立即显示
                            self.display.Context.Redisplay(point_data["ais_point"], True)
                            self.display.Context.UpdateCurrentViewer()
                            self.display.Repaint()
                            
                            # 新增：添加到被操作对象表格
                            tree_item = spline_data.get("tree_item")
                            if tree_item:
                                spline_name = tree_item.text(0)
                                parent_item = tree_item.parent()
                                if parent_item:
                                    parent_name = parent_item.text(0)
                                    self.add_to_table_widget_2(spline_name, parent_name)
                            return
    def reset_canvas_spline_style(self):
        """
        重置所有样条线的样式为默认样式
        """
        if hasattr(self, 'spline_list'):
            # 收集所有需要重置的样条线
            splines_to_reset = []
            for spline_data in self.spline_list:
                splines_to_reset.append(spline_data["ais_spline"])

            # 批量重置样条线样式
            if splines_to_reset:
                from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_GREEN
                from OCC.Core.Aspect import Aspect_TOM_PLUS
                from OCC.Core.Prs3d import Prs3d_PointAspect

                context = self.display.GetContext()
                for spline_data in self.spline_list:
                    spline_data["ais_spline"].SetColor(Quantity_Color(Quantity_NOC_GREEN))
                    spline_data["ais_spline"].SetWidth(2.0)
                    
                    # 重置构成样条线的点样式 - 使用统一样式函数
                    for point_data in spline_data.get("point_ais_list", []):
                        self.set_unified_point_style(point_data["ais_point"], "WHITE", False)

                context.UpdateCurrentViewer()  # 统一更新视图

    def highlight_spline(self, spline_data):
        """
        高亮显示指定的样条线

        Args:
            spline_data: 包含样条线信息的字典
        """
        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_RED

        # 设置颜色为红色高亮
        spline_data["ais_spline"].SetColor(Quantity_Color(Quantity_NOC_RED))
        spline_data["ais_spline"].SetWidth(3.0)

        # 添加到选中容器中
        self.shapeNew.append(spline_data["ais_spline"])

        # 立即更新视图以确保颜色变化立即显示
        self.display.Context.Redisplay(spline_data["ais_spline"], True)

    def highlight_tree_item(self, item):
        """
        高亮显示树形控件中的节点，并递归高亮其所有父节点

        Args:
            item: 要高亮显示的树节点
        """
        # 定义浅绿色
        light_green = QColor(200, 255, 200)  # RGB值为浅绿色
        
        # 检查节点是否是活跃节点，如果是活跃节点则不改变其背景色
        if item.background(0) != Qt.yellow:
            # 设置当前节点为浅绿色背景
            item.setBackground(0, light_green)
        
        # 递归设置所有父节点为浅绿色背景，但保留活跃节点
        parent = item.parent()
        while parent:
            if parent.background(0) != Qt.yellow:
                parent.setBackground(0, light_green)
            # 确保父节点展开，以便能看到高亮的子节点
            self.tree.expandItem(parent)
            parent = parent.parent()

    def reset_tree_highlighting(self, preserve_active=False):
        """
        重置树形控件中所有节点的高亮状态
        
        Args:
            preserve_active: 是否保留活跃节点的黄色背景，默认为False
        """
        # 递归重置节点的背景色
        def reset_background(item):
            # 检查节点是否是活跃节点（黄色背景）
            if preserve_active and item.background(0) == Qt.yellow:
                # 保留活跃节点的黄色背景
                pass
            else:
                item.setBackground(0, Qt.white)
            
            # 递归处理子节点
            for i in range(item.childCount()):
                reset_background(item.child(i))
        
        # 从顶层节点开始递归处理
        for i in range(self.tree.topLevelItemCount()):
            reset_background(self.tree.topLevelItem(i))

    def parse_coordinates(self, text: str) -> List[List[float]]:
        """
        解析点坐标字符串，将其转换为坐标列表

        Args:
            text: 包含点坐标的字符串，例如 "0,0,0 100,0,0" 或 "0,0,0/100,0,0"

        Returns:
            包含点坐标的列表，每个点是一个 [x, y, z] 列表
        """
        points = []  # 初始化点列表
        # 首先将斜杠替换为空格，统一处理
        text = text.replace('/', ' ')  # 替换斜杠为空格
        for point_str in text.split():  # 遍历分割后的字符串
            try:
                coords = [float(coord) for coord in point_str.split(",")]  # 将字符串转换为浮点数列表
                if len(coords) == 3:  # 检查是否为三维坐标
                    points.append(coords)  # 添加到点列表
            except ValueError:  # 捕获转换错误
                continue  # 跳过无效坐标
        return points  # 返回点列表

    def execute_point_command(self, points, active_layer=None):
        """
        根据给定的点坐标创建点

        Args:
            points: 包含点坐标的列表，每个点是一个 [x, y, z] 列表
            active_layer: 活跃图层，如果为 None，则在顶层创建
        """
        from OCC.Core.gp import gp_Pnt
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex
        from OCC.Core.AIS import AIS_Shape
        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE

        if not points:
            QMessageBox.warning(self, "错误", "创建点至少需要一个有效坐标")
            return

        # 获取当前时间，精确到毫秒，并添加唯一计数器
        from datetime import datetime
        import time
        base_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        counter = 0

        # 查找高亮状态的活跃图层
        if active_layer is None:
            active_layer = self.find_active_layer()

        # 如果没有找到活跃图层，默认在 point_top_item 创建
        if active_layer is None:
            active_layer = self.point_top_item

        # 创建点
        for point in points:
            if len(point) != 3:
                QMessageBox.warning(self, "错误", f"无效的坐标: {point}")
                continue

            try:
                pnt = gp_Pnt(*point)
                vertex = BRepBuilderAPI_MakeVertex(pnt).Vertex()
                ais_point = AIS_Shape(vertex)

                # 设置点颜色为白色
                ais_point.SetColor(Quantity_Color(Quantity_NOC_WHITE))

                # 显示点
                self.ais_context = self.display.GetContext()
                self.ais_context.Display(ais_point, True)

                # 创建点数据并添加到点列表
                point_data = {
                    "vertex": vertex,
                    "ais_point": ais_point,
                    "coordinates": point,
                    "tree_item": None
                }
                self.point_list.append(point_data)

                # 生成唯一时间戳，确保毫秒和计数器足够唯一
                current_time = f"{base_time}.{int(time.time() * 1000 % 1000):03d}-{counter}"
                counter += 1

                # 在树形控件中添加点信息，确保坐标不为空
                if all(coord is not None for coord in point):
                    # 创建点项并添加到活跃图层下
                    point_item = QTreeWidgetItem(active_layer, [f"点 [{current_time}]"])
                    point_item.setFlags(point_item.flags() | Qt.ItemIsUserCheckable)
                    point_item.setCheckState(0, Qt.Checked)
                    point_data["tree_item"] = point_item

                    # 添加坐标子项，确保不为空
                    if point[0] is not None:
                        coord_item = QTreeWidgetItem(point_item, [f"X: {point[0]}"])
                        coord_item.setFlags(coord_item.flags() | Qt.ItemIsUserCheckable)
                        coord_item.setCheckState(0, Qt.Checked)
                    if point[1] is not None:
                        coord_item = QTreeWidgetItem(point_item, [f"Y: {point[1]}"])
                        coord_item.setFlags(coord_item.flags() | Qt.ItemIsUserCheckable)
                        coord_item.setCheckState(0, Qt.Checked)
                    if point[2] is not None:
                        coord_item = QTreeWidgetItem(point_item, [f"Z: {point[2]}"])
                        coord_item.setFlags(coord_item.flags() | Qt.ItemIsUserCheckable)
                        coord_item.setCheckState(0, Qt.Checked)
                    
                    # 只展开到点节点级别，不展开到坐标子节点
                    self.tree.expandItem(active_layer)

                # 更新显示
                self.display.Repaint()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"创建点失败: {str(e)}")
                continue

        # 设置点的样式为统一的交叉型样式
        self.set_unified_point_style(ais_point, "WHITE", False)

    def find_active_layer(self):
        """
        查找当前活跃图层

        Returns:
            QTreeWidgetItem: 活跃图层节点，如果没有则返回None
        """
        # 递归查找高亮的节点
        def find_highlighted_item(item):
            if item.background(0) == Qt.yellow:
                return item

            for i in range(item.childCount()):
                child = item.child(i)
                result = find_highlighted_item(child)
                if result:
                    return result

            return None

        # 从顶层节点开始查找
        for i in range(self.tree.topLevelItemCount()):
            top_item = self.tree.topLevelItem(i)
            found = find_highlighted_item(top_item)
            if found:
                return found

        return None

    def on_command_entered(self):
        cmd_text = self.command_input.toPlainText().strip()
        self.command_input.clear()
        if not cmd_text:
            return

        try:
            commands = cmd_text.splitlines()
            for cmd in commands:
                if not cmd.strip():
                    continue

                parts = cmd.split(maxsplit=1)
                if len(parts) < 1:
                    continue

                operation = parts[0].lower()
                params_text = parts[1] if len(parts) > 1 else ""

                # 处理复制点命令
                if operation == "pntcopy":
                    from copy import CopyPointDialog
                    dialog = CopyPointDialog(self)
                    if dialog.exec_():
                        new_points = dialog.get_new_points()
                        # 查找活跃图层
                        active_layer = self.find_active_layer()
                        self.execute_point_command(new_points, active_layer)
                    return
                # 获取当前时间，精确到毫秒
                from datetime import datetime
                import time
                base_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                counter = 0

                if operation == "创建点":
                    try:
                        points = self.parse_coordinates(params_text)
                        if points:
                            # 查找高亮状态的活跃图层
                            active_layer = self.find_active_layer()

                            # 如果没有活跃图层，默认在 point_top_item 创建
                            if active_layer is None:
                                active_layer = self.point_top_item

                            for point in points:
                                # 生成唯一时间戳
                                current_time = f"{base_time}.{int(time.time() * 1000 % 1000):03d}-{counter}"
                                counter += 1
                                # 记录命令流到 list.txt
                                with open("list.txt", "a") as f:
                                    f.write(f"{current_time}\n创建点 {point[0]},{point[1]},{point[2]}\n")
                            
                            # 执行创建点命令，传入活跃图层
                            self.execute_point_command(points, active_layer)
                        else:
                            raise ValueError("创建点至少需要一个有效坐标")
                    except Exception as point_e:
                        print(f"创建点错误详情: {point_e}")
                        raise ValueError(f"创建点失败: {str(point_e)}")
                
                elif operation == "创建样条线":
                    try:
                        # 导入样条线处理函数
                        import sys
                        import os
                        current_dir = os.path.dirname(os.path.abspath(__file__))
                        if current_dir not in sys.path:
                            sys.path.append(current_dir)
                        
                        from spline_modeling import parse_spline_coordinates, execute_spline_command
                        
                        # 解析坐标参数
                        points = parse_spline_coordinates(self, params_text)
                        if points and len(points) >= 2:
                            # 查找活跃图层
                            active_layer = self.find_active_layer()

                            # 如果没有找到活跃图层，使用样条线顶层项
                            if active_layer is None:
                                # 查找或创建样条线顶层项
                                if hasattr(self, 'spline_top_item') and self.spline_top_item:
                                    active_layer = self.spline_top_item
                                else:
                                    # 查找是否存在"样条线"顶级节点
                                    for i in range(self.tree.topLevelItemCount()):
                                        item = self.tree.topLevelItem(i)
                                        if item.text(0) == "样条线":
                                            active_layer = item
                                            self.spline_top_item = item
                                            break

                                    # 如果仍未找到，创建"样条线"顶级节点
                                    if active_layer is None:
                                        from PyQt5.QtWidgets import QTreeWidgetItem
                                        from PyQt5.QtCore import Qt
                                        active_layer = QTreeWidgetItem(self.tree)
                                        active_layer.setText(0, "样条线")
                                        active_layer.setFlags(active_layer.flags() | Qt.ItemIsUserCheckable)
                                        active_layer.setCheckState(0, Qt.Checked)
                                        self.spline_top_item = active_layer

                            print(f"命令行样条线将创建在图层: {active_layer.text(0) if active_layer else 'None'}")

                            # 记录命令流到 list.txt
                            point_str = "/".join([f"{p[0]},{p[1]},{p[2]}" for p in points])
                            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            with open("list.txt", "a") as f:
                                f.write(f"{current_time}\n创建样条线 {point_str}\n")

                            # 执行创建样条线命令
                            execute_spline_command(self, points, active_layer)
                        else:
                            raise ValueError("创建样条线至少需要两个有效坐标点")
                    except Exception as spline_e:
                        print(f"创建样条线错误详情: {spline_e}")
                        raise ValueError(f"创建样条线失败: {str(spline_e)}")
                # 处理其他命令...
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "执行错误", f"命令解析失败:\n{str(e)}")
            print(f"命令执行错误: {str(e)}")
        self.reset_canvas_point_style()

    def connectSignals(self):
        # 连接canvas的选中信号
        self.canvas.sig_topods_selected.connect(self.also_on_select)
        self.canvas.installEventFilter(self)
        self.tree.itemClicked.connect(self.on_tree_item_clicked)
        
        # 添加快捷键 Ctrl+S 支持
        save_shortcut = QShortcut(QKeySequence("Ctrl+S"), self)
        save_shortcut.activated.connect(self.save_tree_to_list_txt)

        # 初始化样条线拾取相关变量
        self.spline_picking_mode = False  # 是否处于样条线拾取模式
        self.picked_points = []  # 拾取的点列表
        self.temp_spline_ais = None  # 临时样条线AIS对象
        self.temp_spline_shape = None  # 临时样条线形状

        # 连接样条线复选框的事件
        if hasattr(self, 'checkbox_14'):
            self.checkbox_14.stateChanged.connect(self.on_spline_checkbox_changed)
        
        # 注释掉可能导致问题的定时器
        # # 添加定时器确保样条线构成点样式一致性
        # from PyQt5.QtCore import QTimer
        # self.style_timer = QTimer(self)
        # self.style_timer.timeout.connect(self.ensure_spline_point_consistency)
        # self.style_timer.start(1000)  # 每秒检查一次

    def undo(self):
        pass

    def reset_all_points_color(self):
        """重置所有点的颜色为白色，并恢复形状"""
        # 重置独立点的颜色
        if hasattr(self, 'point_list'):
            for point_data in self.point_list:
                from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
                from OCC.Core.Aspect import Aspect_TOM_POINT
                from OCC.Core.Prs3d import Prs3d_PointAspect, Prs3d_Drawer

                # 获取当前点的显示属性
                drawer = point_data["ais_point"].Attributes()
                # 设置点形状为默认点型
                asp = Prs3d_PointAspect(Aspect_TOM_POINT, Quantity_Color(Quantity_NOC_WHITE), 3)
                drawer.SetPointAspect(asp)
                point_data["ais_point"].SetAttributes(drawer)

                # 设置颜色为白色
                point_data["ais_point"].SetColor(Quantity_Color(Quantity_NOC_WHITE))

                # 更新视图
                self.display.Context.Redisplay(point_data["ais_point"], True)
                
        # 重置所有样条线的颜色为绿色
        if hasattr(self, 'spline_list'):
            for spline_data in self.spline_list:
                from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_GREEN
                
                # 设置颜色为绿色
                spline_data["ais_spline"].SetColor(Quantity_Color(Quantity_NOC_GREEN))
                spline_data["ais_spline"].SetWidth(2.0)

                # 更新视图
                self.display.Context.Redisplay(spline_data["ais_spline"], True)
                
                # 重置构成样条线的点样式
                from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
                from OCC.Core.Aspect import Aspect_TOM_PLUS
                from OCC.Core.Prs3d import Prs3d_PointAspect
                for point_data in spline_data.get("point_ais_list", []):
                    # 使用统一的点样式设置函数
                    self.set_unified_point_style(point_data["ais_point"], "WHITE", False)
                    
                    # 更新视图
                    self.display.Context.Redisplay(point_data["ais_point"], True)

    def delete_selected_shapes(self):
        """
        从场景中删除当前选中的形状。
        当用户按下Delete键并选中形状时调用。
        """
        if not hasattr(self, 'shapeNew') or not self.shapeNew:
            return

        try:
            # 获取上下文
            context = self.display.GetContext()

            # 跟踪是否有任何形状被删除
            deleted = False

            # 处理选中的对象
            for shape in self.shapeNew:
                # 检查形状是否为点
                if hasattr(self, 'point_list'):
                    for point_data in list(self.point_list):
                        if point_data.get("ais_point") == shape:
                            # 从显示上下文中移除点
                            context.Erase(point_data["ais_point"], False)

                            # 从点列表中移除点数据
                            self.point_list.remove(point_data)

                            # 从树形控件中移除点项
                            if "tree_item" in point_data and point_data["tree_item"]:
                                tree_item = point_data["tree_item"]
                                parent = tree_item.parent()

                                # 找到节点所属的活跃图层
                                active_parent = self.find_active_parent(tree_item)

                                # 记录节点的层级路径，以便后续可能的撤销操作
                                node_path = self.get_node_path(tree_item)

                                # 移除树节点
                                if parent:
                                    parent.removeChild(tree_item)
                                else:
                                    # 顶层节点的情况很少见，但仍需处理
                                    index = self.tree.indexOfTopLevelItem(tree_item)
                                    if index >= 0:
                                        self.tree.takeTopLevelItem(index)

                                # 记录撤销操作
                                if hasattr(self, 'undo_stack'):
                                    self.undo_stack.append(("delete_point", point_data, node_path))

                                deleted = True
                                break

                # 检查形状是否为样条线
                if hasattr(self, 'spline_list'):
                    for spline_data in list(self.spline_list):
                        if spline_data.get("ais_spline") == shape:
                            # 从显示上下文中移除样条线
                            context.Erase(spline_data["ais_spline"], False)
                            
                            # 从显示上下文中移除构成样条线的点
                            for point_data in spline_data.get("point_ais_list", []):
                                context.Erase(point_data["ais_point"], False)

                            # 从样条线列表中移除样条线数据
                            self.spline_list.remove(spline_data)

                            # 从树形控件中移除样条线项
                            if "tree_item" in spline_data and spline_data["tree_item"]:
                                tree_item = spline_data["tree_item"]
                                parent = tree_item.parent()

                                # 记录节点的层级路径，以便后续可能的撤销操作
                                node_path = self.get_node_path(tree_item)

                                # 移除树节点
                                if parent:
                                    parent.removeChild(tree_item)
                                else:
                                    # 顶层节点的情况很少见，但仍需处理
                                    index = self.tree.indexOfTopLevelItem(tree_item)
                                    if index >= 0:
                                        self.tree.takeTopLevelItem(index)

                                # 记录撤销操作
                                if hasattr(self, 'undo_stack'):
                                    self.undo_stack.append(("delete_spline", spline_data, node_path))

                                deleted = True
                                break

                # 检查形状是否为样条线的构成点
                if hasattr(self, 'spline_list'):
                    for spline_data in list(self.spline_list):
                        for point_data in spline_data.get("point_ais_list", []):
                            if point_data.get("ais_point") == shape:
                                # 不能单独删除样条线的构成点，因为这会破坏样条线的结构
                                # 用户应该删除整个样条线
                                self.statusBar().showMessage("无法单独删除样条线的构成点，请删除整个样条线", 3000)
                                break

            # 清除选择
            self.shapeNew = []
            try:
                # 使用正确的API清除选择
                context.ClearPrs(context.MainSelector(), False)
            except:
                # 如果上面的方法不工作，尝试其他方法
                try:
                    context.ClearCurrents(False)
                except:
                    pass  # 如果都不工作，就跳过清除选择
            context.UpdateCurrentViewer()
            self.display.Repaint()

            if deleted:
                self.statusBar().showMessage("已删除所选对象", 3000)
            else:
                self.statusBar().showMessage("没有可删除的对象", 3000)

        except Exception as e:
            print(f"删除对象时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def find_active_parent(self, item):
        """
        查找一个节点的活跃父节点

        Args:
            item: 要查找父节点的节点

        Returns:
            活跃父节点，如果没有则返回None
        """
        current = item
        while current:
            if current.background(0) == Qt.yellow:
                return current
            current = current.parent()
        return None

    def get_node_path(self, item):
        """
        获取节点的完整路径，从根节点到当前节点

        Args:
            item: 要获取路径的节点

        Returns:
            包含路径的列表，每个元素是节点的文本
        """
        path = []
        current = item
        while current:
            path.insert(0, current.text(0))
            current = current.parent()
        return path
            
    #refresh_display 函数的主要功能是刷新显示场景，并可以选择是否清理显示内容
    def refresh_display(self, clean=False):
        """
        刷新显示，可选择是否清理显示

        Args:
            clean: 是否清理显示，默认为 False
        """
        try:
            context = self.display.GetContext()

            if clean:
                # 清理显示模式：保留自定义坐标系，选择性重新显示对象
                existing_points = self.point_list.copy() if hasattr(self, 'point_list') else []
                existing_splines = self.spline_list.copy() if hasattr(self, 'spline_list') else []
                existing_endpoints = self.line_endpoint_ais_list.copy()

                # 收集所有要保留的对象
                objects_to_preserve = []
                if hasattr(self, 'trihedron'):
                    objects_to_preserve.append(self.trihedron)

                # 擦除除保留对象之外的所有内容
                context.EraseAll(False)

                # 重新显示保留的对象
                for obj in objects_to_preserve:
                    context.Display(obj, False)

                # 如果独立点应该可见，则重新显示所有独立点
                points_visible = hasattr(self, '_points_visible') and self._points_visible
                if points_visible and hasattr(self, 'point_list'):
                    for point_data in existing_points:
                        if "ais_point" in point_data:
                            context.Display(point_data["ais_point"], False)

                # 重新显示所有样条线
                if hasattr(self, 'spline_list'):
                    for spline_data in existing_splines:
                        if "ais_spline" in spline_data:
                            context.Display(spline_data["ais_spline"], False)
                            # 同时显示构成样条线的点
                            for point_data in spline_data.get("point_ais_list", []):
                                context.Display(point_data["ais_point"], False)

                # 重新显示所有线条端点
                for endpoint in existing_endpoints:
                    context.Display(endpoint, False)
            else:
                # 普通刷新模式：重新显示所有可见对象
                context.EraseAll(False)

                # 重新显示保留的对象
                if hasattr(self, 'trihedron'):
                    context.Display(self.trihedron, False)

                # 重新显示所有活动的线条

                # 重新显示线条端点
                for endpoint in self.line_endpoint_ais_list:
                    context.Display(endpoint, False)

                # 重新显示所有可见的独立点
                if hasattr(self, '_points_visible') and self._points_visible and hasattr(self, 'point_list'):
                    for point_data in self.point_list:
                        if "ais_point" in point_data:
                            is_selected = point_data["ais_point"] in self.shapeNew
                            from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_YELLOW, Quantity_NOC_WHITE
                            color = Quantity_Color(Quantity_NOC_YELLOW if is_selected else Quantity_NOC_WHITE)
                            point_data["ais_point"].SetColor(color)
                            context.Display(point_data["ais_point"], False)
                            
                # 重新显示所有可见的样条线
                if hasattr(self, 'spline_list'):
                    for spline_data in self.spline_list:
                        if "ais_spline" in spline_data:
                            is_selected = spline_data["ais_spline"] in self.shapeNew
                            from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_GREEN, Quantity_NOC_YELLOW
                            color = Quantity_Color(Quantity_NOC_YELLOW if is_selected else Quantity_NOC_GREEN)
                            spline_data["ais_spline"].SetColor(color)
                            context.Display(spline_data["ais_spline"], False)
                            
                            # 同时显示构成样条线的点
                            for point_data in spline_data.get("point_ais_list", []):
                                # 检查点是否被选中
                                point_selected = point_data["ais_point"] in self.shapeNew
                                from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_YELLOW, Quantity_NOC_WHITE
                                point_color = Quantity_Color(Quantity_NOC_YELLOW if point_selected else Quantity_NOC_WHITE)
                                point_data["ais_point"].SetColor(point_color)
                                context.Display(point_data["ais_point"], False)

            # 在所有更改后强制更新一次
            context.UpdateCurrentViewer()
            self.display.Repaint()
        except Exception as e:
            print(f"刷新显示时发生错误: {e}")
    #eventFilter 是一个事件过滤器函数
    def eventFilter(self, obj, event):
        # 处理回车键按下事件
        if event.type() == QEvent.KeyPress and obj == self.command_input:
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                self.on_command_entered()
                return True

        # 处理删除键按下事件
        elif event.type() == QEvent.KeyPress and event.key() == Qt.Key_Delete:
            if self.shapeNew:  # 如果有选中的对象
                self.delete_selected_shapes()
                return True

        # 处理 Ctrl+S 快捷键
        elif event.type() == QEvent.KeyPress and event.modifiers() == Qt.ControlModifier and event.key() == Qt.Key_S:
            self.save_tree_to_list_txt()
            return True

        # 处理ESC键 - 退出样条线拾取模式
        elif event.type() == QEvent.KeyPress and event.key() == Qt.Key_Escape:
            if hasattr(self, 'spline_picking_mode') and self.spline_picking_mode:
                print("ESC键检测到，退出样条线拾取模式")
                self.exit_spline_picking_mode()
                return True

        # 处理双击事件 - 仅处理画布的双击事件，不包括树形控件
        elif event.type() == QEvent.MouseButtonDblClick:
            if obj is self.canvas and event.button() == Qt.LeftButton:
                # 如果当前没有处理双击事件，则执行以下代码
                if not hasattr(self, '_handling_double_click') or not self._handling_double_click:
                    try:
                        self._handling_double_click = True
                        print("=" * 60)
                        print("🎯 画布双击事件触发")
                        print("=" * 60)

                        # 获取鼠标双击位置
                        mouse_pos = event.pos()
                        print(f"🖱️ 鼠标双击位置: ({mouse_pos.x()}, {mouse_pos.y()})")

                        # 🔧 添加调试：检查当前状态
                        print(f"📊 当前状态检查:")
                        print(f"  - 有spline_list: {hasattr(self, 'spline_list')}")
                        if hasattr(self, 'spline_list'):
                            print(f"  - 样条线数量: {len(self.spline_list)}")
                        print(f"  - 有active_tangent_editor: {hasattr(self, 'active_tangent_editor')}")
                        if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                            print(f"  - 编辑器状态: 活动中")
                        else:
                            print(f"  - 编辑器状态: 无活动编辑器")

                        # 首先尝试识别样条线，然后处理点编辑
                        print("🔍 开始处理样条线或点编辑...")
                        success = self.handle_spline_or_point_edit(mouse_pos)

                        self._handling_double_click = False
                        print(f"🎯 双击处理结果: {'成功' if success else '失败'}")
                        print("=" * 60)

                        if success:
                            return True
                    except Exception as e:
                        self._handling_double_click = False
                        print(f"❌ 处理双击事件时发生错误: {e}")
                        import traceback
                        traceback.print_exc()
        # 在eventFilter方法中处理鼠标点击事件
        elif event.type() == QEvent.MouseButtonPress and obj is self.canvas:
            # 如果处于样条线拾取模式，处理点拾取
            if hasattr(self, 'spline_picking_mode') and self.spline_picking_mode and event.button() == Qt.LeftButton:
                pos = event.pos()
                print(f"样条线拾取模式 - 鼠标单击位置: ({pos.x()}, {pos.y()})")
                self.handle_point_picking(pos)
                return True

            # 🔧 修复：检查是否处于曲线选择模式
            elif event.button() == Qt.LeftButton:
                pos = event.pos()

                # 🔧 新增：检查是否处于控制点选择模式
                if hasattr(self, 'point_selection_mode') and self.point_selection_mode:
                    print(f"🎯 控制点选择模式：鼠标点击位置 ({pos.x()}, {pos.y()})")
                    self.handle_point_selection_click(pos)
                    return True  # 阻止事件继续传播
                # 如果处于曲线选择模式，优先处理曲线选择
                elif hasattr(self, 'curve_selection_mode') and self.curve_selection_mode:
                    print(f"🎯 曲线选择模式：鼠标点击位置 ({pos.x()}, {pos.y()})")
                    self.handle_curve_selection_click(pos)
                    return True  # 阻止事件继续传播
                else:
                    # 处理普通点击事件，管理切矢箭头显示
                    self.handle_single_click(pos)
                    return False  # 继续传播事件

            # 确保点击事件正确注册到选择处理程序
            if hasattr(self, 'lastSelectedShape'):
                self.lastSelectedShape = None  # 清除上次选择的对象
            return False  # 让事件继续传播给OCC的事件处理器

        # 其他事件处理保持不变
        return super().eventFilter(obj, event)

    def handle_right_click_menu(self, mouse_pos):
        """
        处理右键菜单 - 备用的样条线编辑方案

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)
        """
        try:
            # 查找样条线
            spline_data = self.find_spline_by_mouse_position(mouse_pos)
            if spline_data:
                from PyQt5.QtWidgets import QMenu, QAction

                # 创建右键菜单
                menu = QMenu(self)

                # 添加编辑切矢选项
                edit_action = QAction("编辑切矢方向", self)
                edit_action.triggered.connect(lambda: self.open_spline_tangent_editor(spline_data))
                menu.addAction(edit_action)

                # 添加其他选项
                info_action = QAction("样条线信息", self)
                info_action.triggered.connect(lambda: self.show_spline_info(spline_data))
                menu.addAction(info_action)

                # 显示菜单
                global_pos = self.mapToGlobal(mouse_pos)
                menu.exec_(global_pos)

                return True

            return False

        except Exception as e:
            print(f"处理右键菜单失败: {e}")
            return False

    def handle_spline_or_point_edit(self, mouse_pos):
        """
        处理样条线或点的双击编辑
        优先识别样条线，如果没有则处理点编辑

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)

        Returns:
            bool: 是否成功处理了双击事件
        """
        try:
            print("=" * 50)
            print("🔍 开始双击样条线调试")
            print(f"鼠标位置: ({mouse_pos.x()}, {mouse_pos.y()})")
            print(f"spline_list存在: {hasattr(self, 'spline_list')}")

            if hasattr(self, 'spline_list'):
                print(f"样条线数量: {len(self.spline_list)}")
                for i, spline_data in enumerate(self.spline_list):
                    print(f"样条线{i+1}:")
                    print(f"  - 创建方法: {spline_data.get('creation_method', '未知')}")
                    print(f"  - 有ais_spline: {'ais_spline' in spline_data}")
                    print(f"  - 控制点数: {len(spline_data.get('point_ais_list', []))}")
                    print(f"  - 切矢数: {len(spline_data.get('tangent_ais_list', []))}")
            else:
                print("❌ spline_list不存在")

            # 🔧 新策略：先检查是否有最近选中的样条线
            if hasattr(self, 'last_selected_spline') and self.last_selected_spline:
                print("🎯 发现最近选中的样条线，直接使用")
                spline_data = self.last_selected_spline
                print("✅ 使用最近选中的样条线，打开切矢编辑器")
                # 双击时显示切矢箭头
                self.show_tangent_arrows_for_spline(spline_data)
                return self.open_spline_tangent_editor(spline_data)

            # 首先尝试识别样条线
            spline_data = self.find_spline_by_mouse_position(mouse_pos)
            if spline_data:
                print(f"✅ 双击识别到样条线，显示切矢箭头并打开编辑器")
                # 双击时显示切矢箭头
                self.show_tangent_arrows_for_spline(spline_data)
                return self.open_spline_tangent_editor(spline_data)
            else:
                print("❌ 未识别到样条线，尝试点编辑")

            # 如果没有识别到样条线，则处理点编辑
            return self.handle_direct_point_edit(mouse_pos)

        except Exception as e:
            print(f"❌ 处理样条线或点编辑时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def find_spline_by_mouse_position(self, mouse_pos):
        """
        根据鼠标位置查找样条线（改进版）

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)

        Returns:
            dict: 样条线数据，如果没有找到则返回None
        """
        try:
            print("🔍 开始查找样条线（改进版）")

            # 🔧 新策略：直接使用现有的选择结果，避免重复选择
            context = self.display.GetContext()
            print(f"获取到context: {context}")

            # 🔧 关键修复：先尝试使用当前已选中的对象
            current_selected = None
            context.InitSelected()
            if context.MoreSelected():
                current_selected = context.SelectedInteractive()
                print(f"🎯 发现当前已选中对象: {current_selected}")

                # 直接使用已选中的对象进行样条线匹配
                spline_data = self.match_spline_from_ais(current_selected)
                if spline_data:
                    print("✅ 使用当前选中对象成功匹配样条线")
                    return spline_data

            # 如果当前没有选中对象，执行新的选择
            print("🔧 当前无选中对象，开始新的选择机制...")

            # 清除之前的选择状态
            context.ClearSelected(True)

            # 🔧 关键修复：先执行MoveTo，再执行Select
            print(f"🔧 执行MoveTo到位置: ({mouse_pos.x()}, {mouse_pos.y()})")
            context.MoveTo(mouse_pos.x(), mouse_pos.y(), self.display.GetView(), True)

            # 🔧 修复：检查MoveTo是否检测到对象（使用正确的API）
            detected_count = 0
            try:
                # 尝试不同的检测方法
                if hasattr(context, 'HasDetected'):
                    has_detected = context.HasDetected()
                    detected_count = 1 if has_detected else 0
                    print(f"MoveTo检测状态: {has_detected}")
                elif hasattr(context, 'NbDetected'):
                    detected_count = context.NbDetected()
                    print(f"MoveTo检测到对象数量: {detected_count}")
                else:
                    print("MoveTo检测方法不可用，继续选择流程")
                    detected_count = 0
            except Exception as e:
                print(f"检测对象时出错: {e}")
                detected_count = 0

            # 🔧 修复：使用更大的容差进行选择
            tolerance_list = [5, 10, 20, 35, 50]  # 增加容差范围

            for tolerance in tolerance_list:
                print(f"🔍 尝试容差 {tolerance} 像素的选择")

                # 先尝试精确选择
                if tolerance == 5:
                    context.Select(True)
                    selected_count = context.NbSelected()
                    print(f"精确选择结果: {selected_count}")
                    if selected_count > 0:
                        break

                # 再尝试区域选择
                context.Select(
                    mouse_pos.x() - tolerance, mouse_pos.y() - tolerance,
                    mouse_pos.x() + tolerance, mouse_pos.y() + tolerance,
                    self.display.GetView(), True
                )

                selected_count = context.NbSelected()
                print(f"容差 {tolerance} 区域选择结果: {selected_count}")

                if selected_count > 0:
                    print(f"✅ 使用容差 {tolerance} 成功选中对象")
                    break

                # 清除选择，准备下一次尝试
                context.ClearSelected(True)

            final_count = context.NbSelected()
            print(f"最终选择完成，选中对象数量: {final_count}")

            # 检查选择结果
            context.InitSelected()
            selected_count = context.NbSelected()
            print(f"选中对象数量: {selected_count}")

            if not context.MoreSelected():
                print("❌ 没有选中任何对象")
                # 🔧 修复：如果选择失败，尝试使用检测到的对象
                if detected_count > 0:
                    print("🔧 尝试使用检测到的对象...")
                    try:
                        if hasattr(context, 'DetectedInteractive'):
                            detected_ais = context.DetectedInteractive()
                            if detected_ais:
                                context.AddOrRemoveSelected(detected_ais, True)
                                context.InitSelected()
                                if context.MoreSelected():
                                    print("✅ 成功使用检测到的对象")
                                else:
                                    print("❌ 使用检测对象也失败")
                                    return None
                            else:
                                print("❌ 检测到的对象为空")
                                return None
                        else:
                            print("❌ DetectedInteractive方法不可用")
                            return None
                    except Exception as e:
                        print(f"❌ 使用检测对象时出错: {e}")
                        return None
                else:
                    return None

            # 获取选中的AIS对象
            selected_ais = context.SelectedInteractive()
            print(f"选中的AIS对象: {selected_ais}")
            print(f"AIS对象类型: {type(selected_ais)}")

            # 尝试获取选中的形状
            selected_shape = None
            try:
                if hasattr(selected_ais, 'Shape'):
                    selected_shape = selected_ais.Shape()
                    print(f"选中的形状: {selected_shape}")
                    print(f"形状类型: {type(selected_shape)}")
                else:
                    print(f"选中的AIS对象没有Shape方法")
            except Exception as e:
                print(f"获取选中形状失败: {e}")

            # 如果无法从AIS对象获取形状，尝试从context获取
            if selected_shape is None:
                try:
                    context.InitSelected()
                    if context.MoreSelected():
                        selected_shape = context.SelectedShape()
                        print(f"从context获取的形状: {selected_shape}")
                        print(f"从context获取的形状类型: {type(selected_shape)}")
                except Exception as e:
                    print(f"从context获取形状失败: {e}")

            # 在样条线列表中查找匹配的样条线
            if hasattr(self, 'spline_list'):
                print(f"开始在{len(self.spline_list)}个样条线中查找匹配")
                for i, spline_data in enumerate(self.spline_list):
                    print(f"检查样条线{i+1}:")

                    # 检查是否是样条线本身
                    spline_ais = spline_data.get("ais_spline")
                    print(f"  样条线AIS: {spline_ais}")
                    print(f"  样条线AIS类型: {type(spline_ais)}")

                    # 使用更灵活的匹配方式
                    if spline_ais == selected_ais:
                        print(f"✅ 找到匹配的样条线本身（直接匹配）")
                        return spline_data

                    # 尝试通过GetObject()方法获取底层对象进行匹配
                    print(f"  尝试对象匹配...")
                    try:
                        if hasattr(selected_ais, 'GetObject') and hasattr(spline_ais, 'GetObject'):
                            selected_obj = selected_ais.GetObject()
                            spline_obj = spline_ais.GetObject()
                            print(f"    选中对象: {selected_obj}")
                            print(f"    样条线对象: {spline_obj}")
                            if selected_obj == spline_obj:
                                print(f"✅ 找到匹配的样条线本身（对象匹配）")
                                return spline_data
                    except Exception as e:
                        print(f"    对象匹配异常: {e}")

                    # 尝试通过Shape()方法获取形状进行匹配
                    print(f"  尝试形状匹配...")
                    try:
                        if hasattr(selected_ais, 'Shape') and hasattr(spline_ais, 'Shape'):
                            selected_shape_from_ais = selected_ais.Shape()
                            spline_shape = spline_ais.Shape()
                            print(f"    选中形状: {selected_shape_from_ais}")
                            print(f"    样条线形状: {spline_shape}")
                            if selected_shape_from_ais.IsSame(spline_shape):
                                print(f"✅ 找到匹配的样条线本身（形状匹配）")
                                return spline_data
                        else:
                            print(f"    AIS对象缺少Shape方法")
                            print(f"    selected_ais有Shape: {hasattr(selected_ais, 'Shape')}")
                            print(f"    spline_ais有Shape: {hasattr(spline_ais, 'Shape')}")
                    except Exception as e:
                        print(f"    形状匹配异常: {e}")

                    # 如果有从context获取的形状，也尝试匹配
                    if selected_shape is not None:
                        print(f"  尝试用context形状匹配...")
                        try:
                            if hasattr(spline_ais, 'Shape'):
                                spline_shape = spline_ais.Shape()
                                print(f"    context形状: {selected_shape}")
                                print(f"    样条线形状: {spline_shape}")
                                if selected_shape.IsSame(spline_shape):
                                    print(f"✅ 找到匹配的样条线本身（context形状匹配）")
                                    return spline_data
                        except Exception as e:
                            print(f"    context形状匹配异常: {e}")

                    # 尝试通过DynamicType()进行类型检查和匹配
                    print(f"  尝试类型匹配...")
                    try:
                        if hasattr(selected_ais, 'DynamicType') and hasattr(spline_ais, 'DynamicType'):
                            selected_type = selected_ais.DynamicType()
                            spline_type = spline_ais.DynamicType()
                            print(f"    选中类型: {selected_type}")
                            print(f"    样条线类型: {spline_type}")
                    except Exception as e:
                        print(f"    类型检查异常: {e}")

                    # 检查是否是样条线的控制点
                    point_ais_list = spline_data.get("point_ais_list", [])
                    print(f"  控制点数量: {len(point_ais_list)}")
                    for j, point_ais_data in enumerate(point_ais_list):
                        point_ais = point_ais_data.get("ais_point")
                        print(f"    控制点{j+1} AIS: {point_ais}")
                        print(f"    控制点{j+1} AIS类型: {type(point_ais)}")

                        # 直接匹配
                        if point_ais == selected_ais:
                            print(f"✅ 通过控制点{j+1}找到样条线（直接匹配）")
                            return spline_data

                        # 形状匹配
                        try:
                            if hasattr(selected_ais, 'Shape') and hasattr(point_ais, 'Shape'):
                                if selected_ais.Shape().IsSame(point_ais.Shape()):
                                    print(f"✅ 通过控制点{j+1}找到样条线（形状匹配）")
                                    return spline_data
                        except:
                            pass

                    # 检查是否是切矢可视化
                    tangent_ais_list = spline_data.get("tangent_ais_list", [])
                    print(f"  切矢数量: {len(tangent_ais_list)}")
                    for j, tangent_ais_data in enumerate(tangent_ais_list):
                        tangent_ais = tangent_ais_data.get("ais_tangent")
                        print(f"    切矢{j+1} AIS: {tangent_ais}")
                        print(f"    切矢{j+1} AIS类型: {type(tangent_ais)}")

                        # 直接匹配
                        if tangent_ais == selected_ais:
                            print(f"✅ 通过切矢{j+1}找到样条线（直接匹配）")
                            return spline_data

                        # 形状匹配
                        try:
                            if hasattr(selected_ais, 'Shape') and hasattr(tangent_ais, 'Shape'):
                                if selected_ais.Shape().IsSame(tangent_ais.Shape()):
                                    print(f"✅ 通过切矢{j+1}找到样条线（形状匹配）")
                                    return spline_data
                        except:
                            pass

                print("❌ 在所有样条线中都没有找到匹配")

                # 如果通过AIS对象匹配失败，尝试通过形状匹配
                if selected_shape is not None:
                    print("🔄 尝试通过形状匹配样条线...")
                    for i, spline_data in enumerate(self.spline_list):
                        print(f"检查样条线{i+1}的形状匹配:")

                        # 检查样条线边
                        try:
                            spline_edge = spline_data.get("edge")
                            if spline_edge and selected_shape.IsSame(spline_edge):
                                print(f"✅ 通过边形状找到样条线{i+1}")
                                return spline_data
                        except Exception as e:
                            print(f"    边形状匹配异常: {e}")

                        # 检查样条线的AIS形状
                        try:
                            spline_ais = spline_data.get("ais_spline")
                            if spline_ais and hasattr(spline_ais, 'Shape'):
                                spline_shape = spline_ais.Shape()
                                if selected_shape.IsSame(spline_shape):
                                    print(f"✅ 通过AIS形状找到样条线{i+1}")
                                    return spline_data
                        except Exception as e:
                            print(f"    AIS形状匹配异常: {e}")

                    print("❌ 形状匹配也未找到样条线")
            else:
                print("❌ spline_list不存在")

            return None

        except Exception as e:
            print(f"❌ 查找样条线时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def match_spline_from_ais(self, selected_ais):
        """
        从AIS对象匹配样条线数据

        Args:
            selected_ais: 选中的AIS对象

        Returns:
            dict: 匹配的样条线数据，如果没有找到则返回None
        """
        try:
            if not selected_ais:
                return None

            print(f"🔍 尝试从AIS对象匹配样条线: {selected_ais}")

            # 获取选中对象的形状
            selected_shape = None
            try:
                if hasattr(selected_ais, 'Shape'):
                    selected_shape = selected_ais.Shape()
                    print(f"获取到选中对象的形状: {selected_shape}")
            except Exception as e:
                print(f"获取形状失败: {e}")

            # 遍历所有样条线进行匹配
            if hasattr(self, 'spline_list') and self.spline_list:
                print(f"🔍 开始检查样条线匹配，当前有 {len(self.spline_list)} 条样条线")

                for i, spline_data in enumerate(self.spline_list):
                    print(f"🔍 检查第{i+1}条样条线")

                    spline_ais = spline_data.get("ais_spline")
                    if not spline_ais:
                        print(f"❌ 第{i+1}条样条线没有ais_spline")
                        continue

                    # 🔧 使用多种匹配方法
                    match_found = False

                    # 方法1：直接AIS对象匹配
                    if spline_ais == selected_ais:
                        print(f"✅ 找到匹配的样条线本身（直接AIS匹配）")
                        match_found = True

                    # 方法2：通过Shape()方法进行匹配
                    if not match_found and selected_shape:
                        try:
                            if hasattr(spline_ais, 'Shape'):
                                spline_shape = spline_ais.Shape()
                                if selected_shape.IsSame(spline_shape):
                                    print(f"✅ 找到匹配的样条线（Shape.IsSame匹配）")
                                    match_found = True
                        except Exception as e:
                            print(f"  Shape匹配失败: {e}")

                    if match_found:
                        print(f"🎯 样条线{i+1}匹配成功")
                        return spline_data

                    # 检查是否点击了样条线上的控制点
                    point_ais_list = spline_data.get("point_ais_list", [])
                    for j, point_ais in enumerate(point_ais_list):
                        if point_ais == selected_ais:
                            print(f"✅ 点击了样条线{i+1}的控制点{j+1}，返回样条线数据")
                            return spline_data

                    # 检查是否点击了样条线的切矢箭头
                    tangent_ais_list = spline_data.get("tangent_ais_list", [])
                    for j, tangent_ais in enumerate(tangent_ais_list):
                        if tangent_ais == selected_ais:
                            print(f"✅ 点击了样条线{i+1}的切矢箭头{j+1}，返回样条线数据")
                            return spline_data

                print("❌ 在所有样条线中都没有找到匹配")
            else:
                print("❌ spline_list不存在")

            return None

        except Exception as e:
            print(f"❌ 匹配样条线时发生错误: {e}")
            return None

    def open_spline_tangent_editor(self, spline_data):
        """
        打开样条线的切矢编辑器

        Args:
            spline_data: 样条线数据字典

        Returns:
            bool: 是否成功打开编辑器
        """
        try:
            print("🚀 开始打开样条线切矢编辑器")
            print(f"样条线数据键: {list(spline_data.keys())}")

            # 检查样条线是否支持切矢编辑
            is_editable = self.is_spline_editable(spline_data)
            print(f"样条线可编辑性: {is_editable}")

            if not is_editable:
                print("❌ 样条线不支持切矢编辑")
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "样条线编辑",
                    "此样条线不支持切矢编辑。\n只有通过拾取方式或约束感知方式创建的样条线支持切矢编辑。"
                )
                return False

            # 准备点数据
            points = spline_data.get("points", [])
            if len(points) < 2:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "样条线数据不完整，无法编辑")
                return False

            # 转换为切矢编辑器需要的格式
            points_data = []
            for i, point_coords in enumerate(points):
                points_data.append({
                    'coords': point_coords,
                    'description': f'控制点{i+1}'
                })

            print(f"准备编辑样条线，控制点数: {len(points_data)}")

            # 🔧 关键修复：设置当前编辑的样条线
            self.last_edited_spline = spline_data
            print(f"🎯 设置当前编辑样条线，控制点: {points}")

            # 导入并创建切矢编辑器
            print("📦 导入切矢编辑器模块...")
            from tangent_editor import TangentEditor
            print("✅ 切矢编辑器模块导入成功")

            print("🔧 创建切矢编辑器实例...")
            editor = TangentEditor(points_data, self)
            print("✅ 切矢编辑器实例创建成功")

            # 如果样条线已有切矢数据，预填充到编辑器
            existing_tangent_data = spline_data.get("tangent_data", {})
            if existing_tangent_data:
                print(f"📝 预填充现有切矢数据: {len(existing_tangent_data)} 个约束点")
                self.prefill_tangent_editor(editor, existing_tangent_data)
            else:
                print("📝 无现有切矢数据，使用默认设置")

            # 连接预览信号
            print("🔗 连接预览信号...")
            editor.tangent_changed.connect(self.on_tangent_preview)
            editor.clear_preview_signal.connect(self.on_clear_preview)
            print("✅ 预览信号连接成功")

            # 设置活动编辑器引用
            self.active_tangent_editor = editor
            print("📌 设置活动编辑器引用")

            # 🔧 修复：改进编辑器显示和信号连接
            # 连接编辑器的完成信号
            editor.accepted.connect(lambda: self.on_tangent_editor_accepted(editor, spline_data))
            editor.rejected.connect(lambda: self.on_tangent_editor_rejected(editor))

            # 🔧 修复：添加窗口关闭事件处理
            def on_editor_finished(result):
                """处理编辑器完成事件"""
                print(f"🎯 编辑器完成，结果: {result}")
                if result == QDialog.Accepted:
                    self.on_tangent_editor_accepted(editor, spline_data)
                else:
                    self.on_tangent_editor_rejected(editor)

            editor.finished.connect(on_editor_finished)

            # 🔧 修复：设置编辑器属性，确保能够正常关闭
            editor.setAttribute(Qt.WA_DeleteOnClose, False)  # 不要自动删除
            editor.setModal(False)  # 非模态

            # 显示编辑器
            editor.show()
            editor.raise_()  # 确保窗口在最前面
            editor.activateWindow()  # 激活窗口

            print("✅ 切矢编辑器已以非模态方式显示，可以与画布交互")
            return True

        except Exception as e:
            print(f"打开样条线切矢编辑器失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def on_tangent_editor_accepted(self, editor, spline_data):
        """处理切矢编辑器确认事件"""
        try:
            print("🎯 处理切矢编辑器确认事件")

            # 🔧 修复：更全面的分割检测逻辑
            has_splits = False
            split_results = []

            # 检查多种分割标志
            if hasattr(editor, 'has_spline_splits') and editor.has_spline_splits():
                has_splits = True
                split_results = editor.get_spline_split_results()
                print(f"🔧 通过has_spline_splits检测到分割操作，结果数: {len(split_results)}")

            # 🔧 新增：直接检查分割数据
            if hasattr(editor, 'endpoint_split_data') and len(editor.endpoint_split_data) > 0:
                has_splits = True
                if not split_results:  # 如果上面没有获取到结果，手动获取
                    split_results = editor.get_spline_split_results()
                print(f"🔧 通过endpoint_split_data检测到分割操作，端点数: {len(editor.endpoint_split_data)}")

            # 🔧 新增：检查分割启用标志
            if hasattr(editor, 'spline_split_enabled') and editor.spline_split_enabled:
                has_splits = True
                if not split_results:  # 如果上面没有获取到结果，手动获取
                    split_results = editor.get_spline_split_results()
                print(f"🔧 通过spline_split_enabled检测到分割操作")

            if has_splits and split_results:
                print(f"🔧 确认检测到样条线分割操作，开始处理{len(split_results)}个分割结果...")
                # 处理样条线分割
                split_success = self.handle_spline_split_results(spline_data, split_results)
                if split_success:
                    print("✅ 样条线分割处理完成")
                    # 退出预览模式
                    self.exit_preview_mode()
                    # 关闭编辑器
                    editor.close()
                    return
                else:
                    print("❌ 样条线分割处理失败，回退到常规更新")
            elif has_splits:
                print("⚠️ 检测到分割标志但没有分割结果，可能是数据准备问题")

            # 用户确认，获取新的切矢数据
            new_tangent_data = editor.get_constraint_aware_tangent_data()
            constraints_info = editor.get_constraints_info()

            print(f"获取到新的切矢数据: {len(new_tangent_data)} 个约束点")

            # 退出预览模式
            self.exit_preview_mode()

            # 更新样条线
            update_success = self.update_spline_tangents(spline_data, new_tangent_data, constraints_info)

            if update_success and hasattr(self, 'spline_list'):
                # 找到更新后的样条线数据
                for updated_spline in self.spline_list:
                    if updated_spline.get("creation_method") == spline_data.get("creation_method"):
                        # 编辑完成后显示切矢箭头
                        self.show_tangent_arrows_for_spline(updated_spline)
                        print("✅ 编辑完成，显示切矢箭头")
                        break

            # 🔧 修复：编辑成功后清理状态
            self.cleanup_editor_state()

            # 关闭编辑器
            editor.close()

        except Exception as e:
            print(f"❌ 处理切矢编辑器确认事件失败: {e}")
            import traceback
            traceback.print_exc()

    def handle_spline_split_results(self, original_spline_data, split_results):
        """
        处理样条线分割结果

        Args:
            original_spline_data: 原始样条线数据
            split_results: 分割结果列表

        Returns:
            bool: 处理是否成功
        """
        try:
            print(f"🔧 开始处理样条线分割结果，共{len(split_results)}个分割")

            # 获取原始样条线的图层
            original_layer = original_spline_data.get("tree_item")
            if original_layer:
                original_layer = original_layer.parent()

            if not original_layer:
                original_layer = self.get_or_create_spline_layer()

            # 隐藏并移除原始样条线
            self.remove_original_spline(original_spline_data)

            # 创建分割后的新样条线
            created_splines = []
            for i, split_result in enumerate(split_results):
                print(f"🔧 处理第{i+1}个分割结果...")

                # 创建第一条分割样条线
                spline1_success = self.create_split_spline(
                    split_result['split_spline_1'],
                    original_layer,
                    f"分割样条线1-{i+1}"
                )
                if spline1_success:
                    created_splines.append(spline1_success)

                # 创建第二条分割样条线
                spline2_success = self.create_split_spline(
                    split_result['split_spline_2'],
                    original_layer,
                    f"分割样条线2-{i+1}"
                )
                if spline2_success:
                    created_splines.append(spline2_success)

            if created_splines:
                print(f"✅ 成功创建{len(created_splines)}条分割样条线")

                # 显示新创建的样条线的切矢箭头
                for spline_data in created_splines:
                    if spline_data.get("tangent_data"):
                        self.show_tangent_arrows_for_spline(spline_data)

                # 更新显示
                self.display.Repaint()

                # 更新状态栏
                self.statusBar().showMessage(f"样条线分割完成，创建了{len(created_splines)}条新样条线", 3000)

                return True
            else:
                print("❌ 没有成功创建任何分割样条线")
                return False

        except Exception as e:
            print(f"❌ 处理样条线分割结果失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def remove_original_spline(self, spline_data):
        """
        移除原始样条线

        Args:
            spline_data: 原始样条线数据
        """
        try:
            print("🔧 移除原始样条线...")

            # 隐藏样条线
            context = self.display.GetContext()
            if "ais_spline" in spline_data:
                context.Erase(spline_data["ais_spline"], False)

            # 隐藏控制点
            for point_data in spline_data.get("point_ais_list", []):
                if "ais_point" in point_data:
                    context.Erase(point_data["ais_point"], False)

            # 隐藏切矢箭头
            for tangent_vis in spline_data.get("tangent_ais_list", []):
                if "ais_tangent" in tangent_vis:
                    context.Erase(tangent_vis["ais_tangent"], False)

            # 从样条线列表中移除
            if hasattr(self, 'spline_list') and spline_data in self.spline_list:
                self.spline_list.remove(spline_data)

            # 从树形控件中移除
            tree_item = spline_data.get("tree_item")
            if tree_item and tree_item.parent():
                tree_item.parent().removeChild(tree_item)

            print("✅ 原始样条线移除完成")

        except Exception as e:
            print(f"❌ 移除原始样条线失败: {e}")
            import traceback
            traceback.print_exc()

    def create_split_spline(self, split_spline_data, active_layer, spline_name):
        """
        创建分割后的样条线

        Args:
            split_spline_data: 分割样条线数据
            active_layer: 活跃图层
            spline_name: 样条线名称

        Returns:
            dict: 创建的样条线数据，失败返回None
        """
        try:
            points = split_spline_data['points']
            tangent_data = split_spline_data.get('tangent_data', {})

            print(f"🔧 创建分割样条线: {spline_name}")
            print(f"   控制点数: {len(points)}")
            print(f"   切矢约束数: {len(tangent_data)}")

            # 检查点数是否足够
            if len(points) < 2:
                print(f"❌ 控制点数不足，无法创建样条线")
                return None

            # 使用约束感知样条线建模创建新样条线
            from 约束感知样条线建模 import create_constraint_aware_spline

            success = create_constraint_aware_spline(
                self, points, tangent_data, active_layer, constraint_tolerance=1.0
            )

            if success and hasattr(self, 'spline_list') and self.spline_list:
                # 获取最新创建的样条线
                new_spline = self.spline_list[-1]

                # 更新样条线名称
                tree_item = new_spline.get("tree_item")
                if tree_item:
                    tree_item.setText(0, spline_name)

                print(f"✅ 分割样条线创建成功: {spline_name}")
                return new_spline
            else:
                print(f"❌ 分割样条线创建失败: {spline_name}")
                return None

        except Exception as e:
            print(f"❌ 创建分割样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def on_tangent_editor_rejected(self, editor):
        """处理切矢编辑器取消事件"""
        try:
            print("🎯 处理切矢编辑器取消事件")

            # 退出预览模式
            self.exit_preview_mode()

            # 用户取消编辑，隐藏切矢箭头
            self.hide_all_tangent_arrows()
            print("✅ 取消编辑，隐藏切矢箭头")

            # 🔧 修复：取消编辑后清理状态
            self.cleanup_editor_state()

            # 关闭编辑器
            editor.close()

        except Exception as e:
            print(f"❌ 处理切矢编辑器取消事件失败: {e}")
            import traceback
            traceback.print_exc()

        except ImportError as e:
            print(f"无法导入切矢编辑器: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", "切矢编辑器模块未找到")
            return False

    def is_spline_editable(self, spline_data):
        """
        检查样条线是否支持切矢编辑

        Args:
            spline_data: 样条线数据字典

        Returns:
            bool: 是否支持编辑
        """
        # 检查创建方法
        creation_method = spline_data.get("creation_method", "")

        # 支持切矢编辑的创建方法
        editable_methods = [
            "interpolate_with_tangents",  # 增强样条线建模
            "constraint_aware_interpolate"  # 约束感知样条线建模
        ]

        if creation_method in editable_methods:
            return True

        # 检查是否有切矢数据（向后兼容）
        tangent_data = spline_data.get("tangent_data", {})
        if tangent_data:
            return True

        # 检查是否有控制点数据
        points = spline_data.get("points", [])
        if len(points) >= 2:
            return True

        return False

    def prefill_tangent_editor(self, editor, existing_tangent_data):
        """
        预填充切矢编辑器的现有数据

        Args:
            editor: 切矢编辑器实例
            existing_tangent_data: 现有的切矢数据
        """
        try:
            print("🔧 开始预填充切矢编辑器数据:")
            print(f"📊 预填充现有切矢数据: {len(existing_tangent_data)} 个约束点")

            for point_index, tangent_info in existing_tangent_data.items():
                print(f"🔧 处理点{point_index+1}的切矢数据:")
                print(f"   - 启用状态: {tangent_info.get('enabled', True)}")
                print(f"   - 方向: {tangent_info.get('direction', [1.0, 0.0, 0.0])}")
                print(f"   - 长度: {tangent_info.get('length', 1.0)}")

                if point_index < len(editor.tangent_data):
                    # 更新内部数据
                    editor.tangent_data[point_index]['enabled'] = tangent_info.get('enabled', True)
                    editor.tangent_data[point_index]['direction'] = tangent_info.get('direction', [1.0, 0.0, 0.0]).copy()
                    editor.tangent_data[point_index]['length'] = tangent_info.get('length', 1.0)
                    print(f"✅ 更新点{point_index+1}内部数据完成")

                    # 更新界面显示
                    # 🔧 修复：启用切矢复选框（新表格结构中是第5列）
                    checkbox = editor.tangent_table.cellWidget(point_index, 5)
                    if checkbox and hasattr(checkbox, 'setChecked'):
                        checkbox.setChecked(tangent_info.get('enabled', True))
                        print(f"✅ 预填充点{point_index+1}切矢启用状态: {tangent_info.get('enabled', True)}")
                    else:
                        print(f"⚠️ 点{point_index+1}切矢复选框不存在或类型错误")

                    # 🔧 修复：方向输入框（新表格结构中是第6-8列）
                    direction = tangent_info.get('direction', [1.0, 0.0, 0.0])
                    for i, value in enumerate(direction):
                        spinbox = editor.tangent_table.cellWidget(point_index, 6 + i)
                        if spinbox and hasattr(spinbox, 'setValue'):
                            spinbox.setValue(value)
                            print(f"✅ 预填充点{point_index+1}切矢方向{['X','Y','Z'][i]}: {value}")
                        else:
                            print(f"⚠️ 点{point_index+1}切矢方向{['X','Y','Z'][i]}输入框不存在")

                    # 🔧 修复：长度输入框（新表格结构中是第9列）
                    length_spinbox = editor.tangent_table.cellWidget(point_index, 9)
                    if length_spinbox and hasattr(length_spinbox, 'setValue'):
                        length_spinbox.setValue(tangent_info.get('length', 1.0))
                        print(f"✅ 预填充点{point_index+1}切矢长度: {tangent_info.get('length', 1.0)}")
                    else:
                        print(f"⚠️ 点{point_index+1}切矢长度输入框不存在")
                else:
                    print(f"⚠️ 点索引{point_index+1}超出范围，跳过预填充")

            print("✅ 切矢编辑器数据预填充完成")

        except Exception as e:
            print(f"预填充切矢编辑器数据失败: {e}")
            import traceback
            traceback.print_exc()

    def update_spline_tangents(self, spline_data, new_tangent_data, constraints_info):
        """
        更新样条线的切矢设置

        Args:
            spline_data: 原样条线数据
            new_tangent_data: 新的切矢数据
            constraints_info: 约束信息

        Returns:
            bool: 是否成功更新
        """
        try:
            print(f"开始更新样条线切矢，新约束点数: {len(new_tangent_data)}")

            # 🔧 使用新的彻底清理方法
            self.cleanup_spline_completely(spline_data)

            # 🔧 新增：确保清理完成后再创建新样条线
            print("🔄 等待清理完成...")
            import time
            time.sleep(0.1)  # 短暂等待，确保清理操作完成

            # 🔧 新增：验证清理效果
            context = self.display.GetContext()
            context.UpdateCurrentViewer()

            # 获取样条线的基本信息
            points = spline_data.get("points", [])
            tree_item = spline_data.get("tree_item")
            active_layer = tree_item.parent() if tree_item else None

            # 根据创建方法选择更新策略
            creation_method = spline_data.get("creation_method", "")

            if creation_method == "constraint_aware_interpolate":
                # 使用约束感知方式重新创建
                success = self.recreate_constraint_aware_spline(
                    points, new_tangent_data, active_layer, spline_data
                )
            else:
                # 使用增强样条线方式重新创建
                print("🚀 开始创建新的增强样条线...")
                success = self.recreate_enhanced_spline(
                    points, new_tangent_data, active_layer, spline_data
                )

            if success:
                print("🧹 开始清理数据结构...")

                # 🔧 增强：从样条线列表中移除旧数据
                if hasattr(self, 'spline_list') and spline_data in self.spline_list:
                    print(f"   从spline_list中移除旧样条线数据")
                    self.spline_list.remove(spline_data)
                    print(f"   当前样条线列表长度: {len(self.spline_list)}")

                # 🔧 增强：移除旧的树节点
                if tree_item and tree_item.parent():
                    parent_item = tree_item.parent()
                    print(f"   从树节点中移除: {tree_item.text(0)}")
                    parent_item.removeChild(tree_item)

                    # 🔧 新增：清理树节点引用
                    tree_item = None

                # 🔧 新增：清理样条线数据中的所有引用
                spline_data.clear()

                # 🔧 增强：强制垃圾回收
                import gc
                gc.collect()

                # 更新显示
                self.display.Repaint()

                print("✅ 样条线切矢更新成功，原始样条线已完全清理")
                self.statusBar().showMessage("样条线切矢更新成功", 3000)
                return True
            else:
                # 更新失败，恢复显示旧样条线
                if spline_data.get("ais_spline"):
                    context.Display(spline_data["ais_spline"], False)

                for point_ais_data in spline_data.get("point_ais_list", []):
                    if point_ais_data.get("ais_point"):
                        context.Display(point_ais_data["ais_point"], False)

                for tangent_ais_data in spline_data.get("tangent_ais_list", []):
                    if tangent_ais_data.get("ais_tangent"):
                        context.Display(tangent_ais_data["ais_tangent"], False)

                self.display.Repaint()

                print("样条线切矢更新失败，已恢复原状")
                self.statusBar().showMessage("样条线切矢更新失败", 3000)
                return False

        except Exception as e:
            print(f"更新样条线切矢失败: {e}")
            import traceback
            traceback.print_exc()
            self.statusBar().showMessage("样条线切矢更新失败", 3000)
            return False

    def cleanup_spline_completely(self, spline_data):
        """
        彻底清理样条线及其所有相关对象

        Args:
            spline_data: 要清理的样条线数据
        """
        try:
            print("🧹 开始彻底清理样条线...")
            context = self.display.GetContext()

            # 清理计数器
            cleaned_objects = 0

            # 1. 清理样条线主体
            if spline_data.get("ais_spline"):
                ais_spline = spline_data["ais_spline"]
                print(f"   清理样条线主体: {type(ais_spline)}")

                # 多重清理策略
                try:
                    context.Erase(ais_spline, False)
                    context.Remove(ais_spline, False)
                    cleaned_objects += 1
                except Exception as e:
                    print(f"     清理样条线主体时出错: {e}")

                # 清理引用
                spline_data["ais_spline"] = None

            # 2. 清理控制点
            point_ais_list = spline_data.get("point_ais_list", [])
            for i, point_ais_data in enumerate(point_ais_list):
                if point_ais_data.get("ais_point"):
                    ais_point = point_ais_data["ais_point"]
                    print(f"     清理控制点{i+1}: {type(ais_point)}")

                    try:
                        context.Erase(ais_point, False)
                        context.Remove(ais_point, False)
                        cleaned_objects += 1
                    except Exception as e:
                        print(f"       清理控制点{i+1}时出错: {e}")

                    # 清理引用
                    point_ais_data["ais_point"] = None

            # 3. 清理切矢箭头
            tangent_ais_list = spline_data.get("tangent_ais_list", [])
            for i, tangent_ais_data in enumerate(tangent_ais_list):
                if tangent_ais_data.get("ais_tangent"):
                    ais_tangent = tangent_ais_data["ais_tangent"]
                    print(f"     清理切矢箭头{i+1}: {type(ais_tangent)}")

                    try:
                        context.Erase(ais_tangent, False)
                        context.Remove(ais_tangent, False)
                        cleaned_objects += 1
                    except Exception as e:
                        print(f"       清理切矢箭头{i+1}时出错: {e}")

                    # 清理引用
                    tangent_ais_data["ais_tangent"] = None

            # 4. 清理边缘对象（如果有）
            if spline_data.get("edge"):
                print("     清理边缘对象")
                spline_data["edge"] = None

            # 5. 强制刷新显示
            self.display.Repaint()

            print(f"✅ 彻底清理完成，共清理{cleaned_objects}个AIS对象")
            return True

        except Exception as e:
            print(f"❌ 彻底清理样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def recreate_constraint_aware_spline(self, points, tangent_data, active_layer, original_spline_data):
        """
        重新创建约束感知样条线

        Args:
            points: 控制点列表
            tangent_data: 切矢数据
            active_layer: 活跃图层
            original_spline_data: 原始样条线数据

        Returns:
            bool: 是否成功创建
        """
        try:
            from 约束感知样条线建模 import create_constraint_aware_spline

            if active_layer is None:
                active_layer = self.get_or_create_spline_layer()

            print(f"重新创建约束感知样条线")
            return create_constraint_aware_spline(
                self, points, tangent_data, active_layer, constraint_tolerance=1.0
            )

        except ImportError as e:
            print(f"无法导入约束感知样条线模块: {e}")
            # 降级到增强样条线方式
            return self.recreate_enhanced_spline(points, tangent_data, active_layer, original_spline_data)
        except Exception as e:
            print(f"重新创建约束感知样条线失败: {e}")
            return False

    def recreate_enhanced_spline(self, points, tangent_data, active_layer, original_spline_data):
        """
        重新创建增强样条线

        Args:
            points: 控制点列表
            tangent_data: 切矢数据
            active_layer: 活跃图层
            original_spline_data: 原始样条线数据

        Returns:
            bool: 是否成功创建
        """
        try:
            from enhanced_spline_modeling import create_spline_with_tangents

            if active_layer is None:
                active_layer = self.get_or_create_spline_layer()

            print(f"重新创建增强样条线")
            return create_spline_with_tangents(
                self, points, tangent_data, active_layer
            )

        except ImportError as e:
            print(f"无法导入增强样条线模块: {e}")
            return False
        except Exception as e:
            print(f"重新创建增强样条线失败: {e}")
            return False

    def show_spline_info(self, spline_data):
        """
        显示样条线信息

        Args:
            spline_data: 样条线数据
        """
        try:
            from PyQt5.QtWidgets import QMessageBox

            # 收集样条线信息
            info_lines = []
            info_lines.append("样条线详细信息")
            info_lines.append("=" * 30)

            # 基本信息
            creation_method = spline_data.get("creation_method", "未知")
            info_lines.append(f"创建方法: {creation_method}")

            points = spline_data.get("points", [])
            info_lines.append(f"控制点数: {len(points)}")

            tangent_data = spline_data.get("tangent_data", {})
            info_lines.append(f"切矢约束数: {len(tangent_data)}")

            # 控制点信息
            if points:
                info_lines.append("\n控制点坐标:")
                for i, point in enumerate(points):
                    has_tangent = i in tangent_data
                    tangent_mark = " [有切矢]" if has_tangent else ""
                    info_lines.append(f"  点{i+1}: ({point[0]:.1f}, {point[1]:.1f}, {point[2]:.1f}){tangent_mark}")

            # 切矢信息
            if tangent_data:
                info_lines.append("\n切矢约束:")
                for point_index, tangent_info in tangent_data.items():
                    direction = tangent_info.get('direction', [0, 0, 0])
                    length = tangent_info.get('length', 0)
                    enabled = tangent_info.get('enabled', False)
                    status = "启用" if enabled else "禁用"
                    info_lines.append(f"  点{point_index+1}: 方向({direction[0]:.2f}, {direction[1]:.2f}, {direction[2]:.2f}), 长度{length:.2f}, {status}")

            # 约束信息
            constraints = spline_data.get("constraints", {})
            if constraints and constraints.get('constraint_info'):
                info_lines.append("\n几何约束:")
                for constraint in constraints['constraint_info']:
                    info_lines.append(f"  - {constraint}")

            # 可编辑性
            is_editable = self.is_spline_editable(spline_data)
            edit_status = "支持" if is_editable else "不支持"
            info_lines.append(f"\n切矢编辑: {edit_status}")

            # 显示信息
            info_text = "\n".join(info_lines)
            QMessageBox.information(self, "样条线信息", info_text)

        except Exception as e:
            print(f"显示样条线信息失败: {e}")
            QMessageBox.warning(self, "错误", f"显示样条线信息失败: {str(e)}")

    def setup_tangent_arrow_scaling(self):
        """
        设置切矢箭头自适应缩放功能
        """
        try:
            print("🔧 设置切矢箭头自适应缩放功能")

            # 创建定时器，定期检查视图变化
            from PyQt5.QtCore import QTimer
            self.tangent_scale_timer = QTimer()
            self.tangent_scale_timer.timeout.connect(self.check_and_update_tangent_arrows)
            self.tangent_scale_timer.start(2000)  # 每2秒检查一次

            # 记录上次的视图缩放
            self.last_view_scale = 1.0

            print("✅ 切矢箭头自适应缩放功能设置完成")

        except Exception as e:
            print(f"❌ 设置切矢箭头自适应缩放失败: {e}")

    def check_and_update_tangent_arrows(self):
        """
        检查并更新切矢箭头缩放
        """
        try:
            # 检查是否有样条线数据
            if not hasattr(self, 'spline_list') or not self.spline_list:
                return

            # 获取当前视图缩放
            try:
                view = self.display.GetView()
                current_scale = view.Scale()
            except:
                return

            # 检查缩放变化
            scale_change_ratio = abs(current_scale - self.last_view_scale) / max(self.last_view_scale, 0.1)

            # 如果缩放变化超过30%，则更新箭头
            if scale_change_ratio > 0.3:
                print(f"🔄 检测到视图缩放变化: {self.last_view_scale:.3f} → {current_scale:.3f}")
                self.update_all_tangent_arrows_scale(current_scale)
                self.last_view_scale = current_scale

        except Exception as e:
            print(f"❌ 检查切矢箭头缩放失败: {e}")

    def update_all_tangent_arrows_scale(self, view_scale):
        """
        更新所有切矢箭头的缩放

        Args:
            view_scale: 当前视图缩放
        """
        try:
            # 尝试使用增强模块的更新函数
            try:
                from enhanced_spline_modeling import update_tangent_arrows_scale
                update_tangent_arrows_scale(self, self.spline_list)
                return
            except ImportError:
                print("增强样条线模块未找到，使用简化更新")
            except Exception as e:
                print(f"增强更新失败: {e}，使用简化更新")

            # 简化的更新逻辑
            updated_count = 0

            for spline_data in self.spline_list:
                tangent_ais_list = spline_data.get("tangent_ais_list", [])

                for tangent_ais_data in tangent_ais_list:
                    # 检查是否需要更新
                    old_scale = tangent_ais_data.get("last_view_scale", 1.0)
                    scale_change_ratio = abs(view_scale - old_scale) / max(old_scale, 0.1)

                    # 如果缩放变化超过20%，标记需要更新
                    if scale_change_ratio > 0.2:
                        tangent_ais_data["needs_update"] = True
                        tangent_ais_data["last_view_scale"] = view_scale
                        updated_count += 1

            if updated_count > 0:
                print(f"✅ 标记了 {updated_count} 个切矢箭头需要更新")
                # 注意：实际的重绘需要在下次编辑时进行

        except Exception as e:
            print(f"❌ 更新切矢箭头缩放失败: {e}")

    def get_tangent_arrows_info(self):
        """
        获取所有切矢箭头信息

        Returns:
            list: 切矢箭头信息列表
        """
        try:
            all_info = []

            if hasattr(self, 'spline_list'):
                for i, spline_data in enumerate(self.spline_list):
                    tangent_ais_list = spline_data.get("tangent_ais_list", [])

                    for tangent_ais_data in tangent_ais_list:
                        info = {
                            'spline_index': i,
                            'point_index': tangent_ais_data.get("point_index", -1),
                            'direction': tangent_ais_data.get("direction", [0, 0, 0]),
                            'length': tangent_ais_data.get("length", 1.0),
                            'adaptive_scale': tangent_ais_data.get("adaptive_scale", 150.0),
                            'actual_length': tangent_ais_data.get("actual_length", 150.0),
                            'needs_update': tangent_ais_data.get("needs_update", False)
                        }
                        all_info.append(info)

            return all_info

        except Exception as e:
            print(f"❌ 获取切矢箭头信息失败: {e}")
            return []

    def setup_tangent_arrow_management(self):
        """
        设置切矢箭头智能显示管理
        """
        try:
            print("🔧 设置切矢箭头智能显示管理")

            # 记录当前显示的切矢箭头
            self.visible_tangent_arrows = []

            # 记录最后编辑的样条线
            self.last_edited_spline = None

            # 设置箭头显示状态
            self.tangent_arrows_visible = False

            print("✅ 切矢箭头智能显示管理设置完成")

        except Exception as e:
            print(f"❌ 设置切矢箭头管理失败: {e}")

    def show_tangent_arrows_for_spline(self, spline_data):
        """
        显示指定样条线的切矢箭头

        Args:
            spline_data: 样条线数据
        """
        try:
            print(f"🔵 显示样条线的切矢箭头")

            # 先隐藏所有现有的切矢箭头
            self.hide_all_tangent_arrows()

            # 显示指定样条线的切矢箭头
            tangent_ais_list = spline_data.get("tangent_ais_list", [])
            context = self.display.GetContext()

            displayed_count = 0
            for tangent_ais_data in tangent_ais_list:
                ais_tangent = tangent_ais_data.get("ais_tangent")
                if ais_tangent:
                    context.Display(ais_tangent, False)
                    self.visible_tangent_arrows.append(ais_tangent)
                    displayed_count += 1

            # 更新显示状态
            if displayed_count > 0:
                self.tangent_arrows_visible = True
                self.last_edited_spline = spline_data
                print(f"✅ 显示了 {displayed_count} 个切矢箭头")
            else:
                self.tangent_arrows_visible = False
                print("ℹ️ 该样条线没有切矢箭头可显示")

            # 刷新显示
            self.display.Repaint()

        except Exception as e:
            print(f"❌ 显示切矢箭头失败: {e}")

    def hide_all_tangent_arrows(self):
        """
        隐藏所有切矢箭头
        """
        try:
            if not self.tangent_arrows_visible:
                return

            print(f"🔴 隐藏所有切矢箭头")

            context = self.display.GetContext()
            hidden_count = 0

            # 隐藏当前显示的箭头
            for ais_tangent in self.visible_tangent_arrows:
                try:
                    context.Erase(ais_tangent, False)
                    hidden_count += 1
                except:
                    pass

            # 清空记录
            self.visible_tangent_arrows.clear()
            self.tangent_arrows_visible = False

            # 刷新显示
            self.display.Repaint()

            print(f"✅ 隐藏了 {hidden_count} 个切矢箭头")

        except Exception as e:
            print(f"❌ 隐藏切矢箭头失败: {e}")

    def on_object_clicked(self, clicked_object_type, clicked_object_data=None):
        """
        处理对象点击事件，管理切矢箭头显示

        Args:
            clicked_object_type: 点击的对象类型 ('spline', 'point', 'other')
            clicked_object_data: 点击的对象数据
        """
        try:
            print(f"🖱️ 检测到单击对象: {clicked_object_type}")

            # 单击任何对象都隐藏切矢箭头（除了双击样条线的情况）
            # 切矢箭头只在以下情况显示：
            # 1. 双击样条线时
            # 2. 完成切矢编辑后
            # 3. 完成样条线创建后
            self.hide_all_tangent_arrows()
            print("✅ 单击事件：隐藏所有切矢箭头")

        except Exception as e:
            print(f"❌ 处理对象点击事件失败: {e}")

    def handle_single_click(self, mouse_pos):
        """
        处理单击事件，识别点击的对象类型

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)
        """
        try:
            # 尝试识别点击的对象
            clicked_object = self.identify_clicked_object(mouse_pos)

            if clicked_object:
                object_type = clicked_object['type']
                object_data = clicked_object.get('data')

                # 调用对象点击处理
                self.on_object_clicked(object_type, object_data)
            else:
                # 点击了空白区域
                self.on_object_clicked('empty')

        except Exception as e:
            print(f"❌ 处理单击事件失败: {e}")

    def identify_clicked_object(self, mouse_pos):
        """
        识别鼠标点击的对象类型

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)

        Returns:
            dict: 包含对象类型和数据的字典，如果没有识别到则返回None
        """
        try:
            # 首先尝试识别样条线
            spline_data = self.find_spline_by_mouse_position(mouse_pos)
            if spline_data:
                return {
                    'type': 'spline',
                    'data': spline_data
                }

            # 尝试识别点
            point_data = self.find_point_by_precise_selection(mouse_pos)
            if point_data:
                return {
                    'type': 'point',
                    'data': point_data
                }

            # 检查是否点击了其他几何对象
            context = self.display.GetContext()
            context.MoveTo(mouse_pos.x(), mouse_pos.y(), self.display.GetView(), True)
            context.Select(True)

            context.InitSelected()
            if context.MoreSelected():
                return {
                    'type': 'other',
                    'data': context.SelectedInteractive()
                }

            # 没有识别到任何对象
            return None

        except Exception as e:
            print(f"❌ 识别点击对象失败: {e}")
            return None

    def on_tree_item_double_clicked(self, item, column):
        """处理树形控件项的双击事件"""
        # 检查是否已经在处理双击事件，以防止重复处理
        if hasattr(self, '_handling_double_click') and self._handling_double_click:
            return

        self._handling_double_click = True
        try:
            print(f"双击了: {item.text(0)}")
            parent = item.parent()

            # 处理点
            if hasattr(self, 'point_list') and hasattr(self, 'point_top_item'):
                # 检查点是否直接为点项（点及其坐标）
                for point_data in self.point_list:
                    if point_data.get("tree_item") == item:
                        # 记录双击前的父节点名称
                        old_parent_name = item.text(0)
                        dialog = self.create_point_edit_dialog(point_data)
                        if dialog.exec_():
                            new_coords = tuple(spinbox.value() for spinbox in dialog.spinboxes)
                            self.update_point_coordinates(point_data, new_coords)
                            # 更新list.txt文件，如果方法存在
                            if hasattr(self, 'update_list_txt'):
                                self.update_list_txt(old_parent_name, new_coords)
                        return
                    elif parent and point_data.get("tree_item") == parent:
                        # 根据双击的坐标项创建单独的编辑对话框
                        coord_text = item.text(0)
                        if ":" in coord_text:  # 确保是坐标项
                            coord_type = coord_text.split(":")[0].strip()
                            if coord_type in ['X', 'Y', 'Z']:
                                dialog = self.create_single_coord_edit_dialog(point_data, coord_type)
                                if dialog.exec_():
                                    new_value = dialog.spinbox.value()
                                    self.update_single_coordinate(point_data, coord_type, new_value)
                                    # 更新list.txt文件，如果方法存在
                                    if hasattr(self, 'update_list_txt'):
                                        self.update_list_txt(parent.text(0), point_data["coordinates"])
                                return

            # 处理样条线上的点
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    # 检查是否双击了样条线项
                    if spline_data.get("tree_item") == item:
                        # 双击样条线项，暂时不处理
                        return
                    
                    # 检查是否双击了样条线的坐标子项（样条线上的点）
                    elif parent and spline_data.get("tree_item") == parent:
                        # 获取点索引
                        point_text = item.text(0)
                        if point_text.startswith("点"):
                            # 提取点索引
                            try:
                                # 从"点1: (x,y,z)"格式中提取索引
                                point_index_str = point_text.split(":")[0].replace("点", "")
                                point_index = int(point_index_str) - 1
                                
                                # 获取对应的点数据
                                if 0 <= point_index < len(spline_data.get("point_ais_list", [])):
                                    point_ais_data = spline_data["point_ais_list"][point_index]
                                    
                                    # 创建点坐标编辑对话框
                                    dialog = self.create_spline_point_edit_dialog(
                                        spline_data, point_index, point_ais_data)
                                    if dialog.exec_():
                                        new_coords = tuple(spinbox.value() for spinbox in dialog.spinboxes)
                                        self.update_spline_point_coordinates(
                                            spline_data, point_index, point_ais_data, new_coords)
                                    return
                            except (ValueError, IndexError):
                                # 如果解析失败，继续处理其他情况
                                pass

        except Exception as e:
            print(f"处理双击事件时发生错误: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "Error", f"An error occurred: {str(e)}")
        finally:
            self._handling_double_click = False

    def create_point_edit_dialog(self, point_data):
        """
        创建点编辑对话框

        Args:
            point_data: 包含点数据的字典

        Returns:
            QDialog: 点编辑对话框
        """
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, QLabel, QDoubleSpinBox

        dialog = QDialog(self)
        dialog.setWindowTitle("编辑点坐标")
        layout = QVBoxLayout(dialog)

        # 创建表单布局
        form_layout = QFormLayout()
        spinboxes = []
        for i, coord in enumerate(['X', 'Y', 'Z']):
            spinbox = QDoubleSpinBox()
            spinbox.setRange(-10000000000, 10000000000)
            spinbox.setValue(point_data["coordinates"][i])
            form_layout.addRow(f"{coord}:", spinbox)
            spinboxes.append(spinbox)

        layout.addLayout(form_layout)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 将spinboxes保存到对话框对象中，以便后续访问
        dialog.spinboxes = spinboxes

        return dialog

    def create_single_coord_edit_dialog(self, point_data, coord_type):
        """
        创建单个坐标编辑对话框

        Args:
            point_data: 包含点数据的字典
            coord_type: 坐标类型 ('X', 'Y', 'Z')

        Returns:
            QDialog: 单个坐标编辑对话框
        """
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, QLabel, QDoubleSpinBox

        # 验证coord_type是否为有效的坐标类型
        if coord_type not in ['X', 'Y', 'Z']:
            raise ValueError(f"无效的坐标类型: {coord_type}")

        dialog = QDialog(self)
        dialog.setWindowTitle(f"编辑{coord_type}坐标")
        layout = QVBoxLayout(dialog)

        # 创建表单布局
        form_layout = QFormLayout()
        spinbox = QDoubleSpinBox()
        spinbox.setRange(-10000000000, 10000000000)
        spinbox.setValue(point_data["coordinates"][['X', 'Y', 'Z'].index(coord_type)])
        form_layout.addRow(f"{coord_type}:", spinbox)

        layout.addLayout(form_layout)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # 将spinbox保存到对话框对象中，以便后续访问
        dialog.spinbox = spinbox

        return dialog

    def handle_direct_point_edit(self, mouse_pos):
        """
        直接处理点编辑 - 使用OpenCASCADE的选择机制精确识别点击的点

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)

        Returns:
            bool: 是否成功处理了双击事件
        """
        try:
            print("开始直接点编辑处理")

            # 方法1：尝试使用OpenCASCADE的精确选择
            selected_point = self.find_point_by_precise_selection(mouse_pos)
            if selected_point:
                print(f"精确选择找到点: {selected_point['description']}")
                return self.edit_selected_point(selected_point)

            # 方法2：如果精确选择失败，使用屏幕距离匹配
            print("精确选择失败，尝试屏幕距离匹配")
            screen_closest_point = self.find_closest_point_by_screen_distance(mouse_pos)
            if screen_closest_point:
                print(f"屏幕距离匹配找到点: {screen_closest_point['description']}")
                return self.edit_selected_point(screen_closest_point)

            # 方法3：最后使用世界坐标匹配（原方法）
            print("屏幕距离匹配失败，尝试世界坐标匹配")
            world_pos = self.convert_screen_to_world(mouse_pos)
            if world_pos:
                print(f"鼠标世界坐标: ({world_pos[0]:.2f}, {world_pos[1]:.2f}, {world_pos[2]:.2f})")
                closest_point = self.find_closest_point_to_position(world_pos)
                if closest_point:
                    print(f"世界坐标匹配找到点: {closest_point['description']}")
                    return self.edit_selected_point(closest_point)

            print("所有方法都未找到可编辑的点")
            return False

        except Exception as e:
            print(f"直接点编辑处理时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def find_point_by_precise_selection(self, mouse_pos):
        """
        使用OpenCASCADE的精确选择机制查找点击的点

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)

        Returns:
            点信息字典，如果没找到则返回 None
        """
        try:
            context = self.display.GetContext()

            # 在指定位置进行选择
            context.MoveTo(mouse_pos.x(), mouse_pos.y(), self.display.GetView(), True)
            context.Select(True)

            # 检查是否有选中的对象
            context.InitSelected()
            if context.MoreSelected():
                selected_shape = context.SelectedShape()
                print(f"精确选择到形状: {type(selected_shape)}")

                # 尝试通过形状找到对应的点数据
                return self.find_point_data_by_shape(selected_shape)

            return None

        except Exception as e:
            print(f"精确选择时发生错误: {e}")
            return None

    def find_point_data_by_shape(self, shape):
        """
        通过形状对象查找对应的点数据

        Args:
            shape: OpenCASCADE形状对象

        Returns:
            点信息字典，如果没找到则返回 None
        """
        try:
            # 从形状中提取坐标
            shape_coords = self.get_shape_coordinates(shape)
            if not shape_coords:
                return None

            print(f"形状坐标: ({shape_coords[0]:.1f}, {shape_coords[1]:.1f}, {shape_coords[2]:.1f})")

            # 在所有点中查找匹配的坐标
            tolerance = 0.1  # 使用较小的容差

            # 检查独立点
            if hasattr(self, 'point_list') and self.point_list:
                for i, point_data in enumerate(self.point_list):
                    coords = point_data.get("coordinates", [])
                    if len(coords) == 3:
                        if self.coordinates_match(coords, shape_coords, tolerance):
                            return {
                                'type': 'independent',
                                'data': point_data,
                                'coords': coords,
                                'description': f"独立点 {i+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                            }

            # 检查样条线上的点
            if hasattr(self, 'spline_list') and self.spline_list:
                for spline_idx, spline_data in enumerate(self.spline_list):
                    point_ais_list = spline_data.get("point_ais_list", [])
                    for point_idx, point_ais_data in enumerate(point_ais_list):
                        coords = point_ais_data.get("coordinates", [])
                        if len(coords) == 3:
                            if self.coordinates_match(coords, shape_coords, tolerance):
                                return {
                                    'type': 'spline_point',
                                    'data': point_ais_data,
                                    'coords': coords,
                                    'spline_data': spline_data,
                                    'point_index': point_idx,
                                    'description': f"样条线 {spline_idx+1} 点 {point_idx+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                                }

            return None

        except Exception as e:
            print(f"通过形状查找点数据时发生错误: {e}")
            return None

    def find_closest_point_by_screen_distance(self, mouse_pos):
        """
        通过屏幕距离查找最近的点

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)

        Returns:
            最近的点信息字典，如果没找到则返回 None
        """
        try:
            closest_point = None
            min_screen_distance = float('inf')
            max_screen_distance = 50  # 屏幕像素距离阈值

            # 收集所有点并计算屏幕距离
            all_points = []

            # 添加独立点
            if hasattr(self, 'point_list') and self.point_list:
                for i, point_data in enumerate(self.point_list):
                    coords = point_data.get("coordinates", [])
                    if len(coords) == 3:
                        all_points.append({
                            'type': 'independent',
                            'data': point_data,
                            'coords': coords,
                            'description': f"独立点 {i+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                        })

            # 添加样条线上的点
            if hasattr(self, 'spline_list') and self.spline_list:
                for spline_idx, spline_data in enumerate(self.spline_list):
                    point_ais_list = spline_data.get("point_ais_list", [])
                    for point_idx, point_ais_data in enumerate(point_ais_list):
                        coords = point_ais_data.get("coordinates", [])
                        if len(coords) == 3:
                            all_points.append({
                                'type': 'spline_point',
                                'data': point_ais_data,
                                'coords': coords,
                                'spline_data': spline_data,
                                'point_index': point_idx,
                                'description': f"样条线 {spline_idx+1} 点 {point_idx+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                            })

            # 计算每个点在屏幕上的位置并计算距离
            for point_info in all_points:
                screen_pos = self.convert_world_to_screen(point_info['coords'])
                if screen_pos:
                    screen_distance = ((screen_pos[0] - mouse_pos.x()) ** 2 +
                                     (screen_pos[1] - mouse_pos.y()) ** 2) ** 0.5

                    print(f"点 {point_info['description']} 屏幕位置: ({screen_pos[0]:.0f}, {screen_pos[1]:.0f}), 屏幕距离: {screen_distance:.1f}")

                    if screen_distance < min_screen_distance and screen_distance <= max_screen_distance:
                        min_screen_distance = screen_distance
                        closest_point = point_info

            if closest_point:
                print(f"屏幕距离最近的点: {closest_point['description']}, 距离: {min_screen_distance:.1f} 像素")
            else:
                print(f"在 {max_screen_distance} 像素范围内未找到点")

            return closest_point

        except Exception as e:
            print(f"屏幕距离查找时发生错误: {e}")
            return None

    def convert_world_to_screen(self, world_coords):
        """
        将3D世界坐标转换为屏幕坐标

        Args:
            world_coords: 世界坐标 [x, y, z]

        Returns:
            屏幕坐标 [x, y]，如果转换失败则返回 None
        """
        try:
            view = self.display.GetView()
            if view is None:
                return None

            # 使用OpenCASCADE的世界到屏幕坐标转换
            screen_x, screen_y = view.Convert(world_coords[0], world_coords[1], world_coords[2])
            return [screen_x, screen_y]

        except Exception as e:
            print(f"世界到屏幕坐标转换失败: {e}")
            return None

    def coordinates_match(self, coords1, coords2, tolerance=0.001):
        """
        检查两个坐标是否匹配

        Args:
            coords1, coords2: 要比较的坐标 [x, y, z]
            tolerance: 匹配容差

        Returns:
            bool: 是否匹配
        """
        if len(coords1) != 3 or len(coords2) != 3:
            return False

        return (abs(coords1[0] - coords2[0]) < tolerance and
                abs(coords1[1] - coords2[1]) < tolerance and
                abs(coords1[2] - coords2[2]) < tolerance)

    def convert_screen_to_world(self, screen_pos):
        """
        将屏幕坐标转换为3D世界坐标

        Args:
            screen_pos: 屏幕坐标 (QPoint)

        Returns:
            list: [x, y, z] 世界坐标，如果转换失败则返回 None
        """
        try:
            # 获取显示视图
            view = self.display.GetView()
            if view is None:
                print("无法获取显示视图")
                return None

            # 尝试使用OpenCASCADE的坐标转换
            try:
                # 将屏幕坐标转换为世界坐标
                x_world, y_world, z_world = view.Convert(screen_pos.x(), screen_pos.y())
                print(f"OpenCASCADE转换结果: ({x_world:.2f}, {y_world:.2f}, {z_world:.2f})")
                return [x_world, y_world, z_world]
            except Exception as convert_error:
                print(f"OpenCASCADE坐标转换失败: {convert_error}")

                # 使用简化的映射方法
                # 获取视图的边界来进行比例转换
                try:
                    # 获取画布尺寸
                    canvas_width = self.canvas.width()
                    canvas_height = self.canvas.height()

                    # 简单的比例映射 (这是一个近似方法)
                    # 假设视图范围大约是 -500 到 500
                    view_range = 1000.0

                    x_world = (screen_pos.x() / canvas_width - 0.5) * view_range
                    y_world = (0.5 - screen_pos.y() / canvas_height) * view_range  # Y轴翻转
                    z_world = 0.0

                    print(f"简化映射结果: ({x_world:.2f}, {y_world:.2f}, {z_world:.2f})")
                    return [x_world, y_world, z_world]

                except Exception as mapping_error:
                    print(f"简化映射也失败: {mapping_error}")
                    return None

        except Exception as e:
            print(f"坐标转换时发生错误: {e}")
            return None

    def find_closest_point_to_position(self, target_pos, max_distance=100.0):
        """
        查找距离指定位置最近的点

        Args:
            target_pos: 目标位置 [x, y, z]
            max_distance: 最大搜索距离

        Returns:
            最近的点信息字典，如果没找到则返回 None
        """
        try:
            closest_point = None
            min_distance = float('inf')

            # 收集所有点并计算距离
            all_points = []

            # 添加独立点
            if hasattr(self, 'point_list') and self.point_list:
                for i, point_data in enumerate(self.point_list):
                    coords = point_data.get("coordinates", [])
                    if len(coords) == 3:
                        all_points.append({
                            'type': 'independent',
                            'data': point_data,
                            'coords': coords,
                            'description': f"独立点 {i+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                        })

            # 添加样条线上的点
            if hasattr(self, 'spline_list') and self.spline_list:
                for spline_idx, spline_data in enumerate(self.spline_list):
                    point_ais_list = spline_data.get("point_ais_list", [])
                    for point_idx, point_ais_data in enumerate(point_ais_list):
                        coords = point_ais_data.get("coordinates", [])
                        if len(coords) == 3:
                            all_points.append({
                                'type': 'spline_point',
                                'data': point_ais_data,
                                'coords': coords,
                                'spline_data': spline_data,
                                'point_index': point_idx,
                                'description': f"样条线 {spline_idx+1} 点 {point_idx+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                            })

            # 计算每个点到目标位置的距离
            for point_info in all_points:
                coords = point_info['coords']

                # 计算3D距离
                distance = ((coords[0] - target_pos[0]) ** 2 +
                           (coords[1] - target_pos[1]) ** 2 +
                           (coords[2] - target_pos[2]) ** 2) ** 0.5

                print(f"点 {point_info['description']} 距离: {distance:.2f}")

                if distance < min_distance and distance <= max_distance:
                    min_distance = distance
                    closest_point = point_info

            if closest_point:
                print(f"最近的点: {closest_point['description']}, 距离: {min_distance:.2f}")
            else:
                print(f"在 {max_distance} 范围内未找到点")

            return closest_point

        except Exception as e:
            print(f"查找最近点时发生错误: {e}")
            return None

    def handle_simple_double_click(self):
        """
        最简单的双击处理方法 - 直接遍历所有点，找到最近的点

        Returns:
            bool: 是否成功处理了双击事件
        """
        try:
            print("开始处理简单双击")

            # 获取鼠标位置（如果可能的话）
            # 这里我们使用一个更直接的方法：遍历所有点，让用户选择

            # 收集所有可编辑的点
            all_points = []

            # 添加独立点
            if hasattr(self, 'point_list') and self.point_list:
                for i, point_data in enumerate(self.point_list):
                    coords = point_data.get("coordinates", [])
                    if len(coords) == 3:
                        all_points.append({
                            'type': 'independent',
                            'data': point_data,
                            'coords': coords,
                            'description': f"独立点 {i+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                        })

            # 添加样条线上的点
            if hasattr(self, 'spline_list') and self.spline_list:
                for spline_idx, spline_data in enumerate(self.spline_list):
                    point_ais_list = spline_data.get("point_ais_list", [])
                    for point_idx, point_ais_data in enumerate(point_ais_list):
                        coords = point_ais_data.get("coordinates", [])
                        if len(coords) == 3:
                            all_points.append({
                                'type': 'spline_point',
                                'data': point_ais_data,
                                'coords': coords,
                                'spline_data': spline_data,
                                'point_index': point_idx,
                                'description': f"样条线 {spline_idx+1} 点 {point_idx+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})"
                            })

            if not all_points:
                print("没有找到可编辑的点")
                return False

            print(f"找到 {len(all_points)} 个可编辑的点")

            # 显示点选择对话框
            selected_point = self.show_point_selection_dialog(all_points)
            if selected_point:
                return self.edit_selected_point(selected_point)

            return False

        except Exception as e:
            print(f"简单双击处理时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def show_point_selection_dialog(self, all_points):
        """
        显示点选择对话框

        Args:
            all_points: 所有可选择的点列表

        Returns:
            选中的点信息，如果取消则返回None
        """
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QDialogButtonBox, QLabel

        dialog = QDialog(self)
        dialog.setWindowTitle("选择要编辑的点")
        dialog.setMinimumSize(400, 300)
        layout = QVBoxLayout(dialog)

        # 添加说明
        label = QLabel("请选择要编辑的点：")
        layout.addWidget(label)

        # 创建点列表
        list_widget = QListWidget()
        for point_info in all_points:
            list_widget.addItem(point_info['description'])
        layout.addWidget(list_widget)

        # 添加按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        if dialog.exec_() == QDialog.Accepted:
            current_row = list_widget.currentRow()
            if current_row >= 0:
                return all_points[current_row]

        return None

    def edit_selected_point(self, point_info):
        """
        编辑选中的点

        Args:
            point_info: 点信息字典

        Returns:
            bool: 是否成功编辑
        """
        try:
            point_data = point_info['data']
            point_type = point_info['type']

            print(f"编辑点: {point_info['description']}")

            # 创建编辑对话框
            dialog = self.create_point_edit_dialog(point_data)
            if dialog.exec_():
                new_coords = tuple(spinbox.value() for spinbox in dialog.spinboxes)
                print(f"用户输入的新坐标: {new_coords}")

                if point_type == 'independent':
                    # 编辑独立点
                    self.update_point_coordinates(point_data, new_coords)
                elif point_type == 'spline_point':
                    # 编辑样条线上的点
                    spline_data = point_info['spline_data']
                    point_index = point_info['point_index']
                    self.update_spline_point_coordinates(spline_data, point_index, point_data, new_coords)

                return True

            return False

        except Exception as e:
            print(f"编辑选中点时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def reregister_point_for_selection(self, point_data):
        """
        重新注册点对象到选择系统，确保更新后的点可以被正确选中

        Args:
            point_data: 点数据字典
        """
        try:
            context = self.display.GetContext()
            ais_point = point_data.get("ais_point")

            if ais_point:
                # 完全移除旧的选择注册
                try:
                    context.Remove(ais_point, False)
                    context.ClearPrs(ais_point, 0, False)
                except:
                    pass

                # 短暂延迟，让系统处理移除操作
                import time
                time.sleep(0.05)

                # 重新显示并注册到选择系统
                context.Display(ais_point, False)
                context.Load(ais_point, -1, False)  # 强制加载到选择器

                # 确保选择器更新
                context.RecomputeSelectionOnly(ais_point)

                # 更新显示
                context.UpdateCurrentViewer()
                self.display.Repaint()

                print(f"重新注册点对象完成: {point_data.get('coordinates', '未知坐标')}")

        except Exception as e:
            print(f"重新注册点对象时发生错误: {e}")

    def find_point_by_coordinates_simple(self, target_coords, tolerance=0.01):
        """
        简化的点查找函数 - 查找独立点和样条线上的点

        Args:
            target_coords: 目标坐标 [x, y, z]
            tolerance: 匹配容差（放宽到0.01）

        Returns:
            匹配的点数据字典，如果没找到则返回 None
        """
        print(f"查找坐标: {target_coords}，容差: {tolerance}")

        # 检查独立点
        if hasattr(self, 'point_list') and self.point_list:
            print(f"检查 {len(self.point_list)} 个独立点")
            for i, point_data in enumerate(self.point_list):
                coords = point_data.get("coordinates", [])
                print(f"  独立点 {i}: {coords}")
                if len(coords) == 3 and len(target_coords) == 3:
                    diff_x = abs(coords[0] - target_coords[0])
                    diff_y = abs(coords[1] - target_coords[1])
                    diff_z = abs(coords[2] - target_coords[2])
                    print(f"    差值: x={diff_x:.3f}, y={diff_y:.3f}, z={diff_z:.3f}")
                    if (diff_x < tolerance and diff_y < tolerance and diff_z < tolerance):
                        print(f"    ✅ 匹配成功！")
                        return point_data

        # 检查样条线上的点
        if hasattr(self, 'spline_list') and self.spline_list:
            print(f"检查 {len(self.spline_list)} 条样条线")
            for spline_idx, spline_data in enumerate(self.spline_list):
                point_ais_list = spline_data.get("point_ais_list", [])
                print(f"  样条线 {spline_idx}: {len(point_ais_list)} 个点")
                for point_idx, point_ais_data in enumerate(point_ais_list):
                    coords = point_ais_data.get("coordinates", [])
                    print(f"    样条线点 {point_idx}: {coords}")
                    if len(coords) == 3 and len(target_coords) == 3:
                        diff_x = abs(coords[0] - target_coords[0])
                        diff_y = abs(coords[1] - target_coords[1])
                        diff_z = abs(coords[2] - target_coords[2])
                        print(f"      差值: x={diff_x:.3f}, y={diff_y:.3f}, z={diff_z:.3f}")
                        if (diff_x < tolerance and diff_y < tolerance and diff_z < tolerance):
                            print(f"      ✅ 匹配成功！")
                            return point_ais_data

        print("❌ 未找到匹配的点")
        return None

    def handle_canvas_double_click(self):
        """
        处理画布双击事件的简化版本
        使用坐标匹配而不是对象引用匹配，避免对象更新后的匹配问题

        Returns:
            bool: 是否成功处理了双击事件
        """
        try:
            # 获取当前选中的形状
            context = self.display.GetContext()
            context.InitSelected()
            if not context.MoreSelected():
                return False

            shape = context.SelectedShape()

            # 获取点击位置的坐标
            clicked_coords = self.get_shape_coordinates(shape)
            if not clicked_coords:
                return False

            print(f"双击了: 点坐标 ({clicked_coords[0]:.1f}, {clicked_coords[1]:.1f}, {clicked_coords[2]:.1f})")

            # 查找匹配的点数据（独立点或样条线上的点）
            point_data, point_type, extra_info = self.find_point_by_coordinates(clicked_coords)

            if point_data:
                # 打开编辑对话框
                if point_type == "independent":
                    # 独立点
                    dialog = self.create_point_edit_dialog(point_data)
                    if dialog.exec_():
                        new_coords = tuple(spinbox.value() for spinbox in dialog.spinboxes)
                        self.update_point_coordinates(point_data, new_coords)

                elif point_type == "spline_point":
                    # 样条线上的点
                    spline_data, point_index = extra_info
                    dialog = self.create_spline_point_edit_dialog(point_data, spline_data, point_index)
                    if dialog.exec_():
                        new_coords = tuple(spinbox.value() for spinbox in dialog.spinboxes)
                        self.update_spline_point_coordinates(spline_data, point_index, point_data, new_coords)

                return True

            return False

        except Exception as e:
            print(f"处理画布双击时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_shape_coordinates(self, shape):
        """
        从形状对象中提取坐标

        Args:
            shape: OpenCASCADE 形状对象

        Returns:
            list: [x, y, z] 坐标，如果提取失败则返回 None
        """
        # 首先检查shape是否为None
        if shape is None:
            print("警告: shape对象为None，无法提取坐标")
            return None

        try:
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.TopAbs import TopAbs_VERTEX
            from OCC.Core.TopoDS import topods_Vertex

            if hasattr(shape, 'ShapeType') and shape.ShapeType() == TopAbs_VERTEX:
                vertex = topods_Vertex(shape)
                pnt = BRep_Tool.Pnt(vertex)
                return [pnt.X(), pnt.Y(), pnt.Z()]
            else:
                print(f"警告: shape不是顶点类型，类型为: {type(shape)}")

        except Exception as e:
            print(f"提取形状坐标时发生错误: {e}")

        return None

    def find_point_by_coordinates(self, target_coords, tolerance=0.001):
        """
        通过坐标查找点数据（独立点或样条线上的点）

        Args:
            target_coords: 目标坐标 [x, y, z]
            tolerance: 匹配容差

        Returns:
            tuple: (point_data, point_type, extra_info)
                  point_type: "independent" 或 "spline_point"
                  extra_info: 对于样条线点，包含 (spline_data, point_index)
        """
        # 首先检查独立点
        if hasattr(self, 'point_list') and self.point_list:
            for point_data in self.point_list:
                coords = point_data.get("coordinates", [])
                if len(coords) == 3:
                    if self.coordinates_match(coords, target_coords, tolerance):
                        return point_data, "independent", None

        # 然后检查样条线上的点
        if hasattr(self, 'spline_list') and self.spline_list:
            for spline_data in self.spline_list:
                point_ais_list = spline_data.get("point_ais_list", [])
                for point_index, point_ais_data in enumerate(point_ais_list):
                    coords = point_ais_data.get("coordinates", [])
                    if len(coords) == 3:
                        if self.coordinates_match(coords, target_coords, tolerance):
                            return point_ais_data, "spline_point", (spline_data, point_index)

        return None, None, None

    def coordinates_match(self, coords1, coords2, tolerance=0.001):
        """
        检查两个坐标是否匹配

        Args:
            coords1, coords2: 要比较的坐标 [x, y, z]
            tolerance: 匹配容差

        Returns:
            bool: 是否匹配
        """
        if len(coords1) != 3 or len(coords2) != 3:
            return False

        return (abs(coords1[0] - coords2[0]) < tolerance and
                abs(coords1[1] - coords2[1]) < tolerance and
                abs(coords1[2] - coords2[2]) < tolerance)

    def find_point_by_shape(self, shape):
        """
        通过形状对象查找对应的点数据
        使用多种匹配策略确保在点更新后仍能正确匹配

        Args:
            shape: OpenCASCADE 形状对象

        Returns:
            匹配的点数据字典，如果没找到则返回 None
        """
        if not hasattr(self, 'point_list') or not self.point_list:
            return None

        # 方法1: 通过AIS对象直接匹配
        for point_data in self.point_list:
            try:
                if "ais_point" in point_data:
                    # 检查这个AIS对象是否对应当前选中的形状
                    ais_shape = point_data["ais_point"].Shape()
                    if hasattr(ais_shape, "IsEqual") and hasattr(shape, "IsEqual"):
                        if ais_shape.IsEqual(shape):
                            return point_data
            except:
                continue

        # 方法2: 通过坐标精确匹配
        try:
            from OCC.Core.BRep import BRep_Tool
            from OCC.Core.TopAbs import TopAbs_VERTEX
            from OCC.Core.TopoDS import topods_Vertex

            if shape.ShapeType() == TopAbs_VERTEX:
                shape_vertex = topods_Vertex(shape)
                shape_pnt = BRep_Tool.Pnt(shape_vertex)

                for point_data in self.point_list:
                    try:
                        # 比较存储的坐标
                        coords = point_data.get("coordinates", [])
                        if len(coords) == 3:
                            tolerance = 1e-6
                            if (abs(shape_pnt.X() - coords[0]) < tolerance and
                                abs(shape_pnt.Y() - coords[1]) < tolerance and
                                abs(shape_pnt.Z() - coords[2]) < tolerance):
                                return point_data
                    except:
                        continue
        except:
            pass

        # 方法3: 通过vertex对象匹配（作为备用方案）
        for point_data in self.point_list:
            try:
                if (hasattr(shape, "IsEqual") and
                    hasattr(point_data.get("vertex"), "IsEqual") and
                    shape.IsEqual(point_data["vertex"])):
                    return point_data
            except:
                continue

        return None

    def find_splines_using_point(self, target_coords, tolerance=0.001):
        """
        查找使用了指定坐标点的所有样条线

        Args:
            target_coords: 目标坐标 [x, y, z]
            tolerance: 坐标匹配的容差

        Returns:
            包含样条线数据和点索引的列表 [(spline_data, point_index), ...]
        """
        dependent_splines = []

        if not hasattr(self, 'spline_list') or not self.spline_list:
            return dependent_splines

        for spline_data in self.spline_list:
            points = spline_data.get("points", [])
            for i, point_coords in enumerate(points):
                if len(point_coords) == 3 and len(target_coords) == 3:
                    # 检查坐标是否匹配（在容差范围内）
                    if (abs(point_coords[0] - target_coords[0]) < tolerance and
                        abs(point_coords[1] - target_coords[1]) < tolerance and
                        abs(point_coords[2] - target_coords[2]) < tolerance):
                        dependent_splines.append((spline_data, i))

        return dependent_splines

    def update_single_coordinate(self, point_data, coord_type, new_value):
        """
        更新单个坐标值

        Args:
            point_data: 包含点数据的字典
            coord_type: 坐标类型 ('X', 'Y', 'Z')
            new_value: 新的坐标值
        """
        from OCC.Core.gp import gp_Pnt
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex
        from OCC.Core.Geom import Geom_CartesianPoint
        from datetime import datetime
        import time

        try:
            # 保存旧坐标，用于查找依赖的样条线
            old_coords = list(point_data["coordinates"])

            # 更新点坐标
            coords = list(point_data["coordinates"])
            coords[['X', 'Y', 'Z'].index(coord_type)] = new_value
            pnt = gp_Pnt(*coords)

            # 查找使用了旧坐标的样条线
            dependent_splines = self.find_splines_using_point(old_coords)
            
            # 创建新顶点和表示点
            vertex = BRepBuilderAPI_MakeVertex(pnt).Vertex()
            point_data["vertex"] = vertex
            point_data["coordinates"] = coords
            
            # 创建新的几何点
            geom_point = Geom_CartesianPoint(pnt)
            
            # 获取上下文
            context = self.display.GetContext()
            
            # 保存当前点的颜色和样式
            old_color = None
            old_aspect = None
            # 修复: 使用GetColor()方法时传入正确的参数
            if hasattr(point_data["ais_point"], "Color"):
                try:
                    # 正确调用Color方法，传入Quantity_Color对象接收颜色
                    from OCC.Core.Quantity import Quantity_Color
                    old_color = Quantity_Color()
                    point_data["ais_point"].Color(old_color)
                except Exception as e:
                    print(f"获取点颜色时发生错误: {e}")
                    # 如果出错，使用默认白色
                    from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
                    old_color = Quantity_Color(Quantity_NOC_WHITE)
            
            # 移除旧点
            context.Remove(point_data["ais_point"], False)
            
            # 创建新点并保留旧样式
            from OCC.Core.AIS import AIS_Point
            from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
            from OCC.Core.Aspect import Aspect_TOM_PLUS
            from OCC.Core.Prs3d import Prs3d_PointAspect
            
            new_ais_point = AIS_Point(geom_point)
            
            # 设置颜色和样式
            if old_color:
                new_ais_point.SetColor(old_color)
            else:
                new_ais_point.SetColor(Quantity_Color(Quantity_NOC_WHITE))
            
            # 获取新点的绘制器并设置点样式
            drawer = new_ais_point.Attributes()
            point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, 
                                           Quantity_Color(Quantity_NOC_WHITE), 4)
            drawer.SetPointAspect(point_aspect)
            new_ais_point.SetAttributes(drawer)
            
            # 显示新点
            context.Display(new_ais_point, False)  # 使用False避免立即更新

            # 更新点数据引用
            point_data["ais_point"] = new_ais_point

            # 简单的显示设置，不使用复杂的API
            try:
                # 确保新点正确显示
                context.SetDisplayMode(new_ais_point, 0, False)
            except:
                # 如果设置显示模式失败，忽略错误
                pass

            # 生成唯一时间戳，精确到毫秒
            base_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            current_time = f"{base_time}.{int(time.time() * 1000 % 1000):03d}"

            # 更新树形控件中的坐标显示
            if point_data.get("tree_item"):
                # 更新树形控件中的父节点名称
                point_data["tree_item"].setText(0, f"点 [{current_time}]")
                
                # 更新特定坐标子项
                for i in range(point_data["tree_item"].childCount()):
                    child = point_data["tree_item"].child(i)
                    if child.text(0).startswith(f"{coord_type}:"):
                        child.setText(0, f"{coord_type}: {new_value}")
                
                # 收起当前节点到上一级
                self.tree.collapseItem(point_data["tree_item"])

            # 更新依赖的样条线
            updated_splines_count = 0
            for spline_data, point_index in dependent_splines:
                try:
                    # 更新样条线数据中的坐标
                    if "points" in spline_data and point_index < len(spline_data["points"]):
                        spline_data["points"][point_index] = coords.copy()

                        # 更新样条线上对应点的AIS显示
                        if ("point_ais_list" in spline_data and
                            point_index < len(spline_data["point_ais_list"])):
                            point_ais_data = spline_data["point_ais_list"][point_index]

                            # 移除旧点
                            context.Remove(point_ais_data["ais_point"], False)

                            # 创建新点
                            from OCC.Core.AIS import AIS_Shape
                            new_vertex = BRepBuilderAPI_MakeVertex(pnt).Vertex()
                            new_ais_point = AIS_Shape(new_vertex)

                            # 应用统一的点样式
                            self.set_unified_point_style(new_ais_point, "WHITE", False)

                            # 显示新点
                            context.Display(new_ais_point, False)

                            # 更新点数据
                            point_ais_data["ais_point"] = new_ais_point
                            point_ais_data["vertex"] = new_vertex
                            point_ais_data["point"] = pnt
                            point_ais_data["coordinates"] = coords.copy()

                        # 重建样条线
                        self.rebuild_spline(spline_data)

                        # 更新样条线树项中的点坐标显示
                        tree_item = spline_data.get("tree_item")
                        if tree_item and point_index < tree_item.childCount():
                            coords_item = tree_item.child(point_index)
                            coords_item.setText(0, f"点{point_index+1}: ({coords[0]:.1f}, {coords[1]:.1f}, {coords[2]:.1f})")

                        updated_splines_count += 1

                except Exception as spline_error:
                    print(f"更新样条线时发生错误: {spline_error}")
                    continue

            # 强制刷新显示和选择状态
            context.UpdateCurrentViewer()
            self.display.Repaint()

            # 强制重新计算选择
            try:
                context.ClearPrs(context.MainSelector(), False)
            except:
                pass

            # 显示更新结果
            if updated_splines_count > 0:
                print(f"已同步更新 {updated_splines_count} 条依赖的样条线")
                self.statusBar().showMessage(f"坐标已更新，同步更新了 {updated_splines_count} 条样条线", 3000)
            else:
                print("坐标已更新，无依赖的样条线需要同步")
                self.statusBar().showMessage("坐标已更新", 3000)

            # 简单的显示刷新
            try:
                context.UpdateCurrentViewer()
                self.display.Repaint()
            except:
                pass

        except Exception as e:
            print(f"更新坐标时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"更新坐标失败: {str(e)}")

    def update_point_coordinates(self, point_data, new_coords):
        """
        更新点的坐标

        Args:
            point_data: 包含点数据的字典
            new_coords: 新的坐标值 (x, y, z)
        """
        from OCC.Core.gp import gp_Pnt
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex
        from OCC.Core.Geom import Geom_CartesianPoint
        from datetime import datetime
        import time

        try:
            # 更新点坐标
            pnt = gp_Pnt(*new_coords)
            
            # 创建新顶点
            vertex = BRepBuilderAPI_MakeVertex(pnt).Vertex()
            
            # 保存旧坐标，用于后面比较是否真的改变了
            old_coords = point_data["coordinates"].copy() if isinstance(point_data["coordinates"], list) else list(point_data["coordinates"])
            
            # 更新点数据字典
            point_data["vertex"] = vertex
            point_data["coordinates"] = list(new_coords)  # 确保使用列表格式存储
            
            # 创建新的几何点
            geom_point = Geom_CartesianPoint(pnt)
            
            # 获取上下文
            context = self.display.GetContext()
            
            # 保存当前点的颜色
            old_color = None
            if hasattr(point_data["ais_point"], "Color"):
                try:
                    # 正确调用Color方法，传入Quantity_Color对象接收颜色
                    from OCC.Core.Quantity import Quantity_Color
                    old_color = Quantity_Color()
                    point_data["ais_point"].Color(old_color)
                except Exception as e:
                    print(f"获取点颜色时发生错误: {e}")
                    # 如果出错，使用默认白色
                    from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
                    old_color = Quantity_Color(Quantity_NOC_WHITE)
            
            # 移除旧点
            context.Remove(point_data["ais_point"], False)
            
            # 创建新点并保留旧样式
            from OCC.Core.AIS import AIS_Point
            from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
            from OCC.Core.Aspect import Aspect_TOM_PLUS
            from OCC.Core.Prs3d import Prs3d_PointAspect
            
            new_ais_point = AIS_Point(geom_point)
            
            # 设置颜色和样式
            if old_color:
                new_ais_point.SetColor(old_color)
            else:
                new_ais_point.SetColor(Quantity_Color(Quantity_NOC_WHITE))
            
            # 获取新点的绘制器并设置点样式
            drawer = new_ais_point.Attributes()
            point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, 
                                           Quantity_Color(Quantity_NOC_WHITE), 4)
            drawer.SetPointAspect(point_aspect)
            new_ais_point.SetAttributes(drawer)
            
            # 显示新点
            context.Display(new_ais_point, False)  # 使用False避免立即更新

            # 更新点数据引用
            point_data["ais_point"] = new_ais_point

            # 简单的显示设置
            try:
                context.SetDisplayMode(new_ais_point, 0, False)
            except:
                pass

            # 生成唯一时间戳，精确到毫秒
            base_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            current_time = f"{base_time}.{int(time.time() * 1000 % 1000):03d}"

            # 更新树形控件中的坐标显示
            if point_data.get("tree_item"):
                # 更新树形控件中的父节点名称
                point_data["tree_item"].setText(0, f"点 [{current_time}]")

                # 更新树形控件中的坐标显示
                for i in range(point_data["tree_item"].childCount()):
                    child = point_data["tree_item"].child(i)
                    if child.text(0).startswith("X:"):
                        child.setText(0, f"X: {new_coords[0]}")
                    elif child.text(0).startswith("Y:"):
                        child.setText(0, f"Y: {new_coords[1]}")
                    elif child.text(0).startswith("Z:"):
                        child.setText(0, f"Z: {new_coords[2]}")
                
                # 收起当前节点到上一级
                self.tree.collapseItem(point_data["tree_item"])

            # 强制刷新显示和选择状态
            context.UpdateCurrentViewer()
            self.display.Repaint()

            # 强制重新计算选择，确保更新后的点可以被重新选中
            try:
                context.ClearPrs(context.MainSelector(), False)
                # 额外刷新
                self.display.FitAll()
                context.UpdateCurrentViewer()
            except:
                pass

            print(f"点坐标更新完成: {point_data['coordinates']}")

            # 简单的显示刷新
            try:
                context.UpdateCurrentViewer()
                self.display.Repaint()
            except:
                pass

        except Exception as e:
            print(f"更新坐标时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"更新坐标失败: {str(e)}")

    def create_spline_point_edit_dialog(self, spline_data, point_index, point_ais_data):
        """
        创建样条线上点的编辑对话框

        Args:
            spline_data: 样条线数据
            point_index: 点索引
            point_ais_data: 点的AIS数据

        Returns:
            QDialog: 点编辑对话框
        """
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, QDoubleSpinBox

        dialog = QDialog(self)
        dialog.setWindowTitle(f"修改样条线上点坐标 - 点{point_index+1}")
        layout = QVBoxLayout()

        form_layout = QFormLayout()

        # 获取当前坐标
        current_point = point_ais_data["point"]
        x, y, z = current_point.X(), current_point.Y(), current_point.Z()

        # 为每个坐标创建带有适当范围和精度的微调框
        x_spinbox = QDoubleSpinBox()
        x_spinbox.setRange(-10000, 10000)
        x_spinbox.setDecimals(2)
        x_spinbox.setValue(x)
        x_spinbox.setSingleStep(1.0)

        y_spinbox = QDoubleSpinBox()
        y_spinbox.setRange(-10000, 10000)
        y_spinbox.setDecimals(2)
        y_spinbox.setValue(y)
        y_spinbox.setSingleStep(1.0)

        z_spinbox = QDoubleSpinBox()
        z_spinbox.setRange(-10000, 10000)
        z_spinbox.setDecimals(2)
        z_spinbox.setValue(z)
        z_spinbox.setSingleStep(1.0)

        form_layout.addRow("X 坐标:", x_spinbox)
        form_layout.addRow("Y 坐标:", y_spinbox)
        form_layout.addRow("Z 坐标:", z_spinbox)
        layout.addLayout(form_layout)

        # 添加标准OK/Cancel按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        dialog.setLayout(layout)
        dialog.spinboxes = (x_spinbox, y_spinbox, z_spinbox)
        return dialog

    def update_spline_point_coordinates(self, spline_data, point_index, point_ais_data, new_coords):
        """
        更新样条线上点的坐标并刷新其显示

        Args:
            spline_data: 样条线数据
            point_index: 点索引
            point_ais_data: 点的AIS数据
            new_coords: 新坐标的元组 (x, y, z)
        """
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex
        from OCC.Core.gp import gp_Pnt
        from OCC.Core.AIS import AIS_Shape

        # 使用更新后的坐标创建新点
        new_point = gp_Pnt(*new_coords)

        # 存储旧顶点和AIS形状以便移除
        old_ais_point = point_ais_data["ais_point"]

        # 创建新顶点和AIS形状
        new_vertex = BRepBuilderAPI_MakeVertex(new_point).Vertex()
        new_ais_point = AIS_Shape(new_vertex)

        # 从旧点应用相同的视觉属性
        if hasattr(old_ais_point, "GetColor"):
            color = old_ais_point.GetColor()
            new_ais_point.SetColor(color)

        # 如果点有自定义样式，尝试保留它
        try:
            if hasattr(old_ais_point, "Attributes") and hasattr(old_ais_point.Attributes(), "PointAspect"):
                aspect = old_ais_point.Attributes().PointAspect()
                if hasattr(aspect, "GetTypeOfMarker"):
                    marker_type = aspect.GetTypeOfMarker()
                    new_ais_point.Attributes().PointAspect().SetTypeOfMarker(marker_type)

                # 如果可用，获取比例
                if hasattr(aspect, "GetScale"):
                    marker_scale = aspect.GetScale()
                    new_ais_point.Attributes().PointAspect().SetScale(marker_scale)
        except Exception as e:
            print(f"复制点样式时发生错误: {e}")

        # 移除旧点并显示新点
        self.ais_context = self.display.GetContext()
        self.ais_context.Remove(old_ais_point, False)
        self.ais_context.Display(new_ais_point, False)  # 使用False避免立即更新视图

        # 更新点数据
        point_ais_data["point"] = new_point
        point_ais_data["vertex"] = new_vertex
        point_ais_data["ais_point"] = new_ais_point
        point_ais_data["coordinates"] = list(new_coords)

        # 更新树项文本
        tree_item = spline_data.get("tree_item")
        if tree_item and point_index < tree_item.childCount():
            coords_item = tree_item.child(point_index)
            coords_item.setText(0, f"点{point_index+1}: ({new_point.X():.1f}, {new_point.Y():.1f}, {new_point.Z():.1f})")

        # 更新样条线数据中的坐标
        if "points" in spline_data and point_index < len(spline_data["points"]):
            spline_data["points"][point_index] = list(new_coords)

        # 重新创建样条线以反映点坐标的变化
        self.rebuild_spline(spline_data)

        # 更新显示
        self.display.Context.UpdateCurrentViewer()
        self.display.Repaint()
        
    def rebuild_spline(self, spline_data):
        """
        重新构建样条线以反映点坐标的变化

        Args:
            spline_data: 样条线数据
        """
        from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
        from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge
        from OCC.Core.GeomAbs import GeomAbs_C2
        from OCC.Core.TColgp import TColgp_Array1OfPnt
        from OCC.Core.gp import gp_Pnt

        try:
            # 获取更新后的点坐标
            points = spline_data.get("points", [])
            if len(points) < 2:
                return

            # 创建TColgp_Array1OfPnt对象
            array = TColgp_Array1OfPnt(1, len(points))

            # 填充点数组
            for i, point_coords in enumerate(points, 1):
                if len(point_coords) == 3:
                    pnt = gp_Pnt(point_coords[0], point_coords[1], point_coords[2])
                    array.SetValue(i, pnt)

            # 创建B样条曲线
            try:
                bspline = GeomAPI_PointsToBSpline(array, GeomAbs_C2).Curve()
            except RuntimeError as e:
                print(f"无法从给定点创建样条曲线: {str(e)}")
                return

            # 创建边
            edge = BRepBuilderAPI_MakeEdge(bspline).Edge()

            # 存储旧的AIS形状以便移除
            old_ais_spline = spline_data.get("ais_spline")

            # 创建新的AIS形状
            new_ais_spline = AIS_Shape(edge)

            # 保持原有的颜色和线宽
            if old_ais_spline:
                # 复制颜色
                from OCC.Core.Quantity import Quantity_Color
                try:
                    color = Quantity_Color()
                    old_ais_spline.Color(color)
                    new_ais_spline.SetColor(color)
                except Exception as e:
                    print(f"复制颜色时发生错误: {e}")
                    # 如果出错，使用默认绿色
                    from OCC.Core.Quantity import Quantity_NOC_GREEN
                    new_ais_spline.SetColor(Quantity_Color(Quantity_NOC_GREEN))

                # 复制线宽
                try:
                    new_ais_spline.SetWidth(old_ais_spline.Width())
                except Exception as e:
                    print(f"复制线宽时发生错误: {e}")
                    # 如果出错，使用默认线宽
                    new_ais_spline.SetWidth(2.0)

            # 移除旧样条线并显示新样条线
            self.ais_context = self.display.GetContext()
            if old_ais_spline:
                self.ais_context.Remove(old_ais_spline, False)
            self.ais_context.Display(new_ais_spline, False)

            # 更新样条线数据
            spline_data["ais_spline"] = new_ais_spline
            spline_data["edge"] = edge

        except Exception as e:
            print(f"重新构建样条线时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def save_tree_to_list_txt(self):
        """
        将树形控件中的所有点和样条线信息保存到list.txt文件
        """
        try:
            import os
            from datetime import datetime

            # 生成文件内容
            content_lines = []
            content_lines.append("# 3D建模数据导出")
            content_lines.append(f"# 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content_lines.append("")

            # 保存独立点信息
            if hasattr(self, 'point_list') and self.point_list:
                content_lines.append("# 独立点列表")
                for i, point_data in enumerate(self.point_list):
                    coords = point_data.get("coordinates", [0, 0, 0])
                    content_lines.append(f"独立点 {i+1}: {coords[0]:.3f}, {coords[1]:.3f}, {coords[2]:.3f}")
                content_lines.append("")

            # 保存样条线信息
            if hasattr(self, 'spline_list') and self.spline_list:
                content_lines.append("# 样条线列表")
                for i, spline_data in enumerate(self.spline_list):
                    content_lines.append(f"样条线 {i+1}:")
                    points = spline_data.get("points", [])
                    for j, point_coords in enumerate(points):
                        if len(point_coords) == 3:
                            content_lines.append(f"  点 {j+1}: {point_coords[0]:.3f}, {point_coords[1]:.3f}, {point_coords[2]:.3f}")
                    content_lines.append("")

            # 写入文件
            with open("list.txt", "w", encoding="utf-8") as f:
                f.write("\n".join(content_lines))

            print("树形结构已保存到 list.txt 文件")
            self.statusBar().showMessage("已保存到 list.txt 文件", 3000)
            return True

        except Exception as e:
            print(f"保存树形结构到文件时发生错误: {e}")
            self.statusBar().showMessage("保存文件失败", 3000)
            import traceback
            traceback.print_exc()
            return False



    def update_list_txt(self, point_name=None, point_coords=None):
        """
        更新list.txt文件中的点信息
        
        Args:
            point_name: 要更新的点的名称
            point_coords: 新的坐标值
        """
        try:
            import os
            # 检查list.txt是否存在
            if not os.path.exists("list.txt"):
                # 如果不存在，创建一个空文件
                with open("list.txt", "w") as f:
                    pass
            
            # 读取现有的list.txt内容
            with open("list.txt", "r") as f:
                lines = f.readlines()
            
            # 如果指定了特定点名称和坐标，则更新该点
            if point_name and point_coords:
                new_lines = []
                i = 0
                while i < len(lines):
                    line = lines[i].strip()
                    if line == point_name:
                        # 找到了点名称，更新下一行的坐标
                        new_lines.append(line + "\n")
                        if i + 1 < len(lines) and lines[i + 1].strip().startswith("创建点"):
                            # 替换坐标
                            new_lines.append(f"创建点 {point_coords[0]},{point_coords[1]},{point_coords[2]}\n")
                            i += 2  # 跳过原来的坐标行
                        else:
                            # 如果下一行不是坐标，添加坐标行
                            new_lines.append(f"创建点 {point_coords[0]},{point_coords[1]},{point_coords[2]}\n")
                            i += 1
                    else:
                        # 保留其他行
                        new_lines.append(line + "\n")
                        i += 1
                
                # 写回文件
                with open("list.txt", "w") as f:
                    f.writelines(new_lines)
            else:
                # 如果没有指定点名称和坐标，则更新所有点
                self.save_tree_to_list_txt()
                
        except Exception as e:
            print(f"更新list.txt文件时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def keyPressEvent(self, event):
        """
        处理应用程序的键盘事件

        Args:
            event: 包含按键信息的QKeyEvent
        """
        from PyQt5.QtCore import Qt

        # 处理ESC键 - 退出样条线拾取模式
        if event.key() == Qt.Key_Escape:
            print(f"keyPressEvent检测到ESC键，拾取模式状态: {getattr(self, 'spline_picking_mode', False)}")
            if hasattr(self, 'spline_picking_mode') and self.spline_picking_mode:
                print("keyPressEvent中退出样条线拾取模式")
                self.exit_spline_picking_mode()
                return

        # 处理Delete键按下
        elif event.key() == Qt.Key_Delete:
            # 如果有选择，则调用删除函数
            if hasattr(self, 'shapeNew') and self.shapeNew:
                self.delete_selected_shapes()
                return

        # 对于其他键，调用父类实现
        super().keyPressEvent(event)

    def on_spline_checkbox_changed(self, state):
        """
        处理样条线复选框状态变化

        Args:
            state: 复选框状态 (Qt.Checked 或 Qt.Unchecked)
        """
        from PyQt5.QtCore import Qt

        if state == Qt.Checked:
            # 进入样条线拾取模式
            self.enter_spline_picking_mode()
        else:
            # 退出样条线拾取模式
            self.exit_spline_picking_mode()

    def enter_spline_picking_mode(self):
        """
        进入样条线拾取模式
        """
        self.spline_picking_mode = True
        self.picked_points = []

        # 更新状态栏提示
        self.statusBar().showMessage("样条线拾取模式：在画布中点击选择点，按ESC键完成创建", 0)

        # 改变鼠标光标为十字光标
        from PyQt5.QtCore import Qt
        self.canvas.setCursor(Qt.CrossCursor)

        # 设置画布焦点，确保能接收键盘事件
        self.canvas.setFocus()

        print("进入样条线拾取模式")

    def exit_spline_picking_mode(self):
        """
        退出样条线拾取模式
        """
        if self.spline_picking_mode:
            # 如果有拾取的点，完成最终样条线创建
            if len(self.picked_points) >= 2:
                self.finalize_spline_creation()
            elif len(self.picked_points) > 0:
                self.statusBar().showMessage("样条线需要至少2个点，已取消创建", 3000)

            # 清理临时样条线
            self.clear_temp_spline()

            # 重置状态
            self.spline_picking_mode = False
            self.picked_points = []
            self.temp_spline_ais = None
            self.temp_spline_shape = None

            # 恢复鼠标光标
            from PyQt5.QtCore import Qt
            self.canvas.setCursor(Qt.ArrowCursor)

            # 取消勾选复选框
            if hasattr(self, 'checkbox_14'):
                self.checkbox_14.setChecked(False)

            # 清除状态栏提示
            self.statusBar().clearMessage()

            print("退出样条线拾取模式")

    def handle_point_picking(self, mouse_pos):
        """
        处理样条线拾取模式下的点选择

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)
        """
        try:
            # 方法1：尝试使用精确选择找到现有的点
            selected_point = self.find_point_by_precise_selection(mouse_pos)
            if selected_point:
                coords = selected_point['coords']
                print(f"拾取到现有点: {selected_point['description']}")
                self.add_point_to_spline_picking(coords, f"现有{selected_point['description']}")
                return

            # 方法2：尝试使用屏幕距离匹配
            screen_closest_point = self.find_closest_point_by_screen_distance(mouse_pos)
            if screen_closest_point:
                coords = screen_closest_point['coords']
                print(f"拾取到附近点: {screen_closest_point['description']}")
                self.add_point_to_spline_picking(coords, f"附近{screen_closest_point['description']}")
                return

            # 方法3：如果没有找到现有点，在鼠标位置创建新点
            world_pos = self.convert_screen_to_world(mouse_pos)
            if world_pos:
                print(f"在鼠标位置创建新点: ({world_pos[0]:.2f}, {world_pos[1]:.2f}, {world_pos[2]:.2f})")
                self.add_point_to_spline_picking(world_pos, f"新建点 ({world_pos[0]:.1f}, {world_pos[1]:.1f}, {world_pos[2]:.1f})")
            else:
                print("无法确定鼠标位置的世界坐标")

        except Exception as e:
            print(f"处理点拾取时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def add_point_to_spline_picking(self, coords, description):
        """
        将点添加到样条线拾取列表

        Args:
            coords: 点坐标 [x, y, z]
            description: 点的描述
        """
        try:
            # 添加到拾取列表
            self.picked_points.append({
                'coords': coords.copy() if hasattr(coords, 'copy') else list(coords),
                'description': description
            })

            point_count = len(self.picked_points)
            print(f"已拾取第 {point_count} 个点: {description}")

            # 更新状态栏
            if point_count == 1:
                self.statusBar().showMessage(f"已选择 {point_count} 个点，继续选择点或按ESC完成", 0)
            else:
                self.statusBar().showMessage(f"已选择 {point_count} 个点，继续选择点或按ESC完成样条线创建", 0)

            # 在画布中高亮显示拾取的点（可选）
            self.highlight_picked_point(coords)

            # 实时创建或更新样条线
            if point_count >= 2:
                self.update_temp_spline()

        except Exception as e:
            print(f"添加拾取点时发生错误: {e}")

    def highlight_picked_point(self, coords):
        """
        在画布中高亮显示拾取的点

        Args:
            coords: 点坐标 [x, y, z]
        """
        try:
            from OCC.Core.gp import gp_Pnt
            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex
            from OCC.Core.AIS import AIS_Shape
            from OCC.Core.Quantity import Quantity_Color

            # 创建临时点用于高亮显示
            pnt = gp_Pnt(coords[0], coords[1], coords[2])
            vertex = BRepBuilderAPI_MakeVertex(pnt).Vertex()
            ais_point = AIS_Shape(vertex)

            # 设置高亮颜色（黄色）
            try:
                from OCC.Core.Quantity import Quantity_NOC_YELLOW
                yellow_color = Quantity_Color(Quantity_NOC_YELLOW)
            except ImportError:
                # 如果常量不可用，使用RGB值创建黄色
                yellow_color = Quantity_Color(1.0, 1.0, 0.0, 1)  # RGB黄色

            ais_point.SetColor(yellow_color)

            # 设置点样式
            self.set_unified_point_style(ais_point, "YELLOW", True)

            # 显示点
            context = self.display.GetContext()
            context.Display(ais_point, False)
            context.UpdateCurrentViewer()

            # 保存临时点引用，用于后续清理
            if not hasattr(self, 'temp_highlight_points'):
                self.temp_highlight_points = []
            self.temp_highlight_points.append(ais_point)

        except Exception as e:
            print(f"高亮显示拾取点时发生错误: {e}")

    def update_temp_spline(self):
        """
        实时更新临时样条线
        """
        try:
            if len(self.picked_points) < 2:
                return

            print(f"实时更新样条线，当前点数: {len(self.picked_points)}")

            # 提取坐标列表
            coords_list = []
            for point_info in self.picked_points:
                coords_list.append(point_info['coords'])

            # 创建样条线几何
            spline_shape = self.create_spline_geometry(coords_list)
            if spline_shape is None:
                print("创建样条线几何失败")
                return

            # 清理旧的临时样条线
            self.clear_temp_spline()

            # 创建新的临时样条线AIS对象
            from OCC.Core.AIS import AIS_Shape
            from OCC.Core.Quantity import Quantity_Color

            self.temp_spline_ais = AIS_Shape(spline_shape)
            self.temp_spline_shape = spline_shape

            # 设置临时样条线的颜色（蓝色，表示临时状态）
            try:
                # 尝试使用颜色常量
                from OCC.Core.Quantity import Quantity_NOC_BLUE
                blue_color = Quantity_Color(Quantity_NOC_BLUE)
            except ImportError:
                # 如果常量不可用，使用RGB值创建蓝色
                blue_color = Quantity_Color(0.0, 0.0, 1.0, 1)  # RGB蓝色

            self.temp_spline_ais.SetColor(blue_color)

            # 设置线宽
            try:
                from OCC.Core.Prs3d import Prs3d_LineAspect
                from OCC.Core.Aspect import Aspect_TOL_SOLID
                line_aspect = Prs3d_LineAspect(blue_color, Aspect_TOL_SOLID, 2.0)
                self.temp_spline_ais.Attributes().SetLineAspect(line_aspect)
            except Exception as line_error:
                print(f"设置线宽时发生错误: {line_error}")
                pass

            # 显示临时样条线
            context = self.display.GetContext()
            context.Display(self.temp_spline_ais, False)
            context.UpdateCurrentViewer()

            print("临时样条线更新成功")

        except Exception as e:
            print(f"更新临时样条线时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def create_spline_geometry(self, coords_list):
        """
        创建样条线几何

        Args:
            coords_list: 点坐标列表 [[x1,y1,z1], [x2,y2,z2], ...]

        Returns:
            样条线形状对象，失败返回None
        """
        try:
            from OCC.Core.gp import gp_Pnt
            from OCC.Core.TColgp import TColgp_Array1OfPnt
            from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge

            if len(coords_list) < 2:
                return None

            # 创建点数组
            points = TColgp_Array1OfPnt(1, len(coords_list))
            for i, coords in enumerate(coords_list):
                point = gp_Pnt(coords[0], coords[1], coords[2])
                points.SetValue(i + 1, point)

            # 创建B样条曲线
            spline_generator = GeomAPI_PointsToBSpline(points)
            if not spline_generator.IsDone():
                print("B样条曲线生成失败")
                return None

            spline_curve = spline_generator.Curve()

            # 创建边
            edge_builder = BRepBuilderAPI_MakeEdge(spline_curve)
            if not edge_builder.IsDone():
                print("样条线边创建失败")
                return None

            return edge_builder.Edge()

        except Exception as e:
            print(f"创建样条线几何时发生错误: {e}")
            return None

    def clear_temp_spline(self):
        """
        清理临时样条线
        """
        try:
            if self.temp_spline_ais is not None:
                context = self.display.GetContext()
                context.Remove(self.temp_spline_ais, False)
                context.UpdateCurrentViewer()
                self.temp_spline_ais = None
                self.temp_spline_shape = None
                print("临时样条线已清理")

        except Exception as e:
            print(f"清理临时样条线时发生错误: {e}")

    def finalize_spline_creation(self):
        """
        完成最终样条线创建，支持切矢方向编辑
        """
        try:
            if len(self.picked_points) < 2:
                print("样条线需要至少2个点")
                return False

            # 提取坐标
            coords_list = []
            for point_info in self.picked_points:
                coords_list.append(point_info['coords'])

            print(f"完成最终样条线创建，使用 {len(coords_list)} 个点:")
            for i, coords in enumerate(coords_list):
                print(f"  点 {i+1}: ({coords[0]:.2f}, {coords[1]:.2f}, {coords[2]:.2f})")

            # 询问用户是否要设置切矢方向
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "样条线创建选项",
                "是否要设置控制点的切矢方向来精确控制样条线形状？\n\n"
                "选择'是'：打开切矢编辑器进行精确控制\n"
                "选择'否'：使用默认方式创建样条线",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 打开切矢编辑器
                return self.open_tangent_editor(coords_list)
            else:
                # 使用传统方式创建样条线
                return self.create_traditional_spline(coords_list)

        except Exception as e:
            print(f"完成样条线创建时发生错误: {e}")
            self.statusBar().showMessage("样条线创建失败", 3000)
            import traceback
            traceback.print_exc()
            return False

    def open_tangent_editor(self, coords_list):
        """
        打开切矢编辑器
        """
        try:
            # 准备点数据
            points_data = []
            for i, coords in enumerate(coords_list):
                points_data.append({
                    'coords': coords,
                    'description': f"控制点{i+1}"
                })

            # 导入切矢编辑器
            from tangent_editor import TangentEditor

            # 创建并显示切矢编辑器
            editor = TangentEditor(points_data, self)

            # 连接预览信号
            editor.tangent_changed.connect(self.on_tangent_preview)
            # 连接坐标变化信号
            editor.point_coordinate_changed.connect(self.on_point_coordinate_changed)
            # 连接清除预览信号
            editor.clear_preview_signal.connect(self.clear_spline_preview)

            # 显示对话框
            if editor.exec_() == TangentEditor.Accepted:
                # 用户确认，获取约束感知的切矢数据
                tangent_data = editor.get_constraint_aware_tangent_data()
                constraints_info = editor.get_constraints_info()

                print(f"获取到切矢数据: {len(tangent_data)} 个约束点")
                if constraints_info['constraint_info']:
                    print(f"检测到约束: {len(constraints_info['constraint_info'])} 个")

                # 创建约束感知的样条线
                return self.create_constraint_aware_spline(coords_list, tangent_data)
            else:
                # 用户取消，使用传统方式
                return self.create_traditional_spline(coords_list)

        except ImportError as e:
            print(f"无法导入切矢编辑器: {e}")
            QMessageBox.warning(self, "错误", "切矢编辑器模块未找到，将使用传统方式创建样条线")
            return self.create_traditional_spline(coords_list)
        except Exception as e:
            print(f"打开切矢编辑器失败: {e}")
            QMessageBox.warning(self, "错误", f"切矢编辑器启动失败: {str(e)}")
            return self.create_traditional_spline(coords_list)

    def create_traditional_spline(self, coords_list):
        """
        使用传统方式创建样条线
        """
        try:
            from spline_modeling import execute_spline_command

            # 查找活跃图层
            active_layer = self.find_active_layer()

            # 如果没有找到活跃图层，使用样条线顶层项
            if active_layer is None:
                active_layer = self.get_or_create_spline_layer()

            print(f"传统样条线将创建在图层: {active_layer.text(0) if active_layer else 'None'}")
            execute_spline_command(self, coords_list, active_layer)
            print("传统样条线创建成功")

            # 清理临时高亮点
            self.clear_temp_highlight_points()

            return True
        except Exception as create_error:
            print(f"传统样条线创建失败: {create_error}")
            self.statusBar().showMessage("样条线创建失败", 3000)
            return False

    def create_spline_with_tangents(self, coords_list, tangent_data):
        """
        创建带切矢控制的样条线（传统方式）
        """
        try:
            # 导入增强的样条线建模模块
            from enhanced_spline_modeling import create_spline_with_tangents

            # 查找活跃图层
            active_layer = self.find_active_layer()
            if active_layer is None:
                active_layer = self.get_or_create_spline_layer()

            print(f"带切矢控制的样条线将创建在图层: {active_layer.text(0) if active_layer else 'None'}")

            # 创建样条线
            success = create_spline_with_tangents(self, coords_list, tangent_data, active_layer)

            if success:
                # 清理临时高亮点
                self.clear_temp_highlight_points()
                print("带切矢控制的样条线创建成功")

            return success

        except ImportError as e:
            print(f"无法导入增强样条线模块: {e}")
            QMessageBox.warning(self, "错误", "增强样条线模块未找到，将使用传统方式创建")
            return self.create_traditional_spline(coords_list)
        except Exception as e:
            print(f"创建带切矢控制的样条线失败: {e}")
            self.statusBar().showMessage("样条线创建失败", 3000)
            return False

    def create_constraint_aware_spline(self, coords_list, tangent_data):
        """
        创建约束感知的样条线
        """
        try:
            # 导入约束感知样条线建模模块
            from 约束感知样条线建模 import create_constraint_aware_spline

            # 查找活跃图层
            active_layer = self.find_active_layer()
            if active_layer is None:
                active_layer = self.get_or_create_spline_layer()

            print(f"约束感知样条线将创建在图层: {active_layer.text(0) if active_layer else 'None'}")

            # 创建约束感知样条线
            success = create_constraint_aware_spline(
                self, coords_list, tangent_data, active_layer, constraint_tolerance=1.0
            )

            if success:
                # 清理临时高亮点
                self.clear_temp_highlight_points()
                print("约束感知样条线创建成功")

            return success

        except ImportError as e:
            print(f"无法导入约束感知样条线模块: {e}")
            QMessageBox.warning(self, "错误", "约束感知样条线模块未找到，将使用传统方式创建")
            return self.create_traditional_spline(coords_list)
        except Exception as e:
            print(f"创建约束感知样条线失败: {e}")
            self.statusBar().showMessage("约束感知样条线创建失败", 3000)
            return False

    def get_or_create_spline_layer(self):
        """
        获取或创建样条线图层
        """
        # 查找或创建样条线顶层项
        if hasattr(self, 'spline_top_item') and self.spline_top_item:
            return self.spline_top_item

        # 查找是否存在"样条线"顶级节点
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            if item.text(0) == "样条线":
                self.spline_top_item = item
                return item

        # 如果仍未找到，创建"样条线"顶级节点
        from PyQt5.QtWidgets import QTreeWidgetItem
        from PyQt5.QtCore import Qt
        active_layer = QTreeWidgetItem(self.tree)
        active_layer.setText(0, "样条线")
        active_layer.setFlags(active_layer.flags() | Qt.ItemIsUserCheckable)
        active_layer.setCheckState(0, Qt.Checked)
        self.spline_top_item = active_layer

        return active_layer

    def on_tangent_preview(self, point_index, x, y, z, length):
        """
        切矢预览回调函数
        实现样条曲线形状的即时预览
        """
        print(f"🔄 实时预览切矢变化 - 点{point_index+1}: 方向({x:.2f}, {y:.2f}, {z:.2f}), 长度{length:.2f}")

        # 确保在编辑过程中切矢箭头保持显示
        if hasattr(self, 'last_edited_spline') and self.last_edited_spline:
            if not self.tangent_arrows_visible:
                print("🔄 编辑过程中恢复切矢箭头显示")
                self.show_tangent_arrows_for_spline(self.last_edited_spline)

            # 检查是否启用了实时预览
            try:
                # 检查是否有活动的切矢编辑器并且启用了预览
                preview_enabled = self.is_preview_enabled()

                if preview_enabled:
                    print(f"🎨 触发实时预览更新")
                    # 实现真正的实时预览
                    self.update_spline_preview(point_index, x, y, z, length)
                else:
                    print("ℹ️ 实时预览未启用")

            except Exception as e:
                print(f"❌ 预览状态检查失败: {e}")
                import traceback
                traceback.print_exc()

    def on_clear_preview(self):
        """
        处理清除预览信号
        """
        try:
            print("🧹 收到清除预览信号")
            self.exit_preview_mode()
        except Exception as e:
            print(f"❌ 处理清除预览信号失败: {e}")

    def is_preview_enabled(self):
        """
        检查实时预览是否启用

        Returns:
            bool: 预览是否启用
        """
        try:
            # 检查是否有活动的切矢编辑器
            if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                # 检查编辑器的预览按钮状态
                if hasattr(self.active_tangent_editor, 'preview_btn'):
                    is_enabled = self.active_tangent_editor.preview_btn.isChecked()
                    print(f"🔍 预览状态检查: {'启用' if is_enabled else '禁用'}")
                    return is_enabled
                else:
                    print("❌ 编辑器没有预览按钮")
                    return False
            else:
                print("❌ 没有活动的切矢编辑器")
                return False

        except Exception as e:
            print(f"❌ 检查预览状态失败: {e}")
            return False

    def update_spline_preview(self, point_index, x, y, z, length):
        """
        更新样条线预览
        根据切矢变化实时更新样条曲线形状

        Args:
            point_index: 控制点索引
            x, y, z: 切矢方向
            length: 切矢长度
        """
        try:
            if not hasattr(self, 'last_edited_spline') or not self.last_edited_spline:
                print("❌ 没有可编辑的样条线")
                return

            print(f"🎨 更新样条线预览 - 点{point_index+1}: 方向({x:.2f}, {y:.2f}, {z:.2f}), 长度{length:.2f}")

            # 获取当前样条线数据
            spline_data = self.last_edited_spline
            points = spline_data.get("points", [])

            # 🔍 调试信息：确认使用的样条线数据
            print(f"🎯 预览使用的样条线控制点: {points}")

            if point_index >= len(points):
                print(f"❌ 点索引超出范围: {point_index} >= {len(points)}")
                return

            # 🔍 预览一致性验证：显示当前编辑状态
            if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                current_enabled = self.active_tangent_editor.get_enabled_tangents()
                print(f"🔍 当前编辑器启用的切矢: {len(current_enabled)} 个")
                for idx, data in current_enabled.items():
                    dir_vec = data['direction']
                    length_val = data['length']
                    print(f"   编辑器点{idx+1}: 方向({dir_vec[0]:.2f}, {dir_vec[1]:.2f}, {dir_vec[2]:.2f}), 长度{length_val:.2f}")

            # 创建预览用的切矢数据
            preview_tangent_data = self.create_preview_tangent_data(spline_data, point_index, x, y, z, length)

            # 创建预览样条线
            success = self.create_preview_spline_with_tangents(points, preview_tangent_data)

            if success:
                print(f"✅ 预览更新成功")
            else:
                print(f"❌ 预览更新失败")

        except Exception as e:
            print(f"❌ 更新样条线预览失败: {e}")
            import traceback
            traceback.print_exc()

    def create_preview_tangent_data(self, spline_data, changed_point_index, x, y, z, length):
        """
        创建预览用的切矢数据

        Args:
            spline_data: 原始样条线数据
            changed_point_index: 变化的点索引
            x, y, z: 新的切矢方向
            length: 新的切矢长度

        Returns:
            dict: 预览用的切矢数据
        """
        try:
            # 🔧 关键修复：使用与最终结果完全相同的数据处理逻辑
            preview_data = {}

            # 获取编辑器中当前启用的切矢数据
            if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                # 先更新编辑器中变化点的数据
                if changed_point_index < len(self.active_tangent_editor.tangent_data):
                    self.active_tangent_editor.tangent_data[changed_point_index]['direction'] = [x, y, z]
                    self.active_tangent_editor.tangent_data[changed_point_index]['length'] = length
                    self.active_tangent_editor.tangent_data[changed_point_index]['enabled'] = True

                # 🎯 关键：使用与最终创建相同的约束感知数据获取方法
                preview_data = self.active_tangent_editor.get_constraint_aware_tangent_data()

                print(f"🎯 使用约束感知切矢数据进行预览: {len(preview_data)} 个")

                # 🔍 调试信息：显示预览切矢数据详情
                for idx, data in preview_data.items():
                    direction = data['direction']
                    length = data['length']
                    constraint_adjusted = data.get('constraint_adjusted', False)
                    status = "约束调整" if constraint_adjusted else "原始数据"
                    print(f"   点{idx+1}: 方向({direction[0]:.2f}, {direction[1]:.2f}, {direction[2]:.2f}), 长度{length:.2f} ({status})")

            else:
                # 回退：获取原始切矢数据
                original_tangent_data = spline_data.get("tangent_data", {})
                for point_idx, tangent_info in original_tangent_data.items():
                    preview_data[point_idx] = tangent_info.copy()

                # 更新变化的点的切矢数据
                preview_data[changed_point_index] = {
                    'direction': [x, y, z],
                    'length': length,
                    'enabled': True
                }

                print(f"⚠️ 回退到原始切矢数据: {len(preview_data)} 个")

            print(f"✅ 创建预览切矢数据，包含 {len(preview_data)} 个约束点")
            return preview_data

        except Exception as e:
            print(f"❌ 创建预览切矢数据失败: {e}")
            import traceback
            traceback.print_exc()
            return {}

    def create_preview_spline(self, points, tangent_data):
        """
        创建预览样条线

        Args:
            points: 控制点列表
            tangent_data: 切矢数据
        """
        try:
            # 隐藏原始样条线（如果存在预览模式标志）
            if not hasattr(self, 'preview_mode'):
                self.preview_mode = True
                self.hide_original_spline_for_preview()

            # 删除之前的预览样条线
            self.clear_preview_spline()

            # 使用约束感知样条线建模创建预览
            from 约束感知样条线建模 import create_constraint_aware_spline

            # 创建预览样条线数据结构
            preview_spline_data = {
                "points": points,
                "tangent_data": tangent_data,
                "creation_method": "preview_spline",
                "ais_spline": None,
                "edge": None,
                "point_ais_list": [],
                "tangent_ais_list": []
            }

            # 检测约束
            from 约束感知样条线建模 import detect_geometric_constraints
            constraints = detect_geometric_constraints(points, 1.0)

            # 使用增强样条线建模创建预览
            success = self.create_preview_spline_with_tangents(points, tangent_data)

            if success:
                print("✅ 预览样条线创建成功")
            else:
                print("❌ 预览样条线创建失败")

        except Exception as e:
            print(f"❌ 创建预览样条线失败: {e}")
            import traceback
            traceback.print_exc()

    def create_preview_spline_with_tangents(self, points, tangent_data):
        """
        创建简化的预览样条线

        Args:
            points: 控制点列表
            tangent_data: 切矢数据

        Returns:
            bool: 创建是否成功
        """
        try:
            # 先清除之前的预览样条线
            self.clear_preview_spline()

            # 确保原始样条线被隐藏
            if not hasattr(self, 'original_spline_hidden') or not self.original_spline_hidden:
                self.hide_original_spline_for_preview()

            print(f"🎨 开始创建预览样条线，控制点数: {len(points)}")

            # 🔧 关键修复：使用带切矢约束的样条线创建方法
            try:
                # 尝试使用增强样条线建模创建带切矢的预览
                from enhanced_spline_modeling import create_spline_with_tangents_preview
                preview_edge = create_spline_with_tangents_preview(points, tangent_data)

                if preview_edge is None:
                    raise Exception("增强样条线创建失败，使用基础方法")

                print("🎨 使用增强样条线建模创建预览（包含切矢约束）")

            except Exception as e:
                print(f"⚠️ 增强样条线创建失败: {e}，使用基础方法")

                # 回退到基础方法
                from OCC.Core.TColgp import TColgp_Array1OfPnt
                from OCC.Core.gp import gp_Pnt
                from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
                from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge

                # 创建点数组
                point_array = TColgp_Array1OfPnt(1, len(points))
                for i, point in enumerate(points):
                    gp_point = gp_Pnt(point[0], point[1], point[2])
                    point_array.SetValue(i + 1, gp_point)

                # 创建B样条曲线
                spline_generator = GeomAPI_PointsToBSpline(point_array)
                if not spline_generator.IsDone():
                    print("❌ B样条曲线生成失败")
                    return False

                bspline_curve = spline_generator.Curve()

                # 创建边
                edge_builder = BRepBuilderAPI_MakeEdge(bspline_curve)
                if not edge_builder.IsDone():
                    print("❌ 样条线边创建失败")
                    return False

                preview_edge = edge_builder.Edge()
                print("🎨 使用基础方法创建预览样条线")

            # 导入必要的类
            from OCC.Core.AIS import AIS_Shape
            from OCC.Core.Quantity import Quantity_Color

            # 创建AIS对象
            preview_ais = AIS_Shape(preview_edge)

            # 设置预览样式（橙色虚线）
            orange_color = Quantity_Color(1.0, 0.5, 0.0, 0)  # 橙色
            preview_ais.SetColor(orange_color)
            preview_ais.SetWidth(3.0)

            # 设置透明度
            try:
                preview_ais.SetTransparency(0.3)
            except:
                pass

            # 显示预览样条线
            context = self.display.GetContext()
            context.Display(preview_ais, True)  # 立即更新显示
            print("🎨 预览样条线已显示到3D视图")

            # 保存预览数据
            self.preview_spline_data = {
                "ais_spline": preview_ais,
                "edge": preview_edge,
                "points": points,
                "tangent_data": tangent_data,
                "is_preview": True
            }

            # 🎨 关键修复：设置预览样条线的特殊外观
            self.style_preview_spline(self.preview_spline_data)

            # 刷新显示
            self.display.Repaint()

            print("✅ 预览样条线创建成功")
            return True

        except Exception as e:
            print(f"❌ 创建预览样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def style_preview_spline(self, preview_spline_data):
        """
        设置预览样条线的特殊外观

        Args:
            preview_spline_data: 预览样条线数据
        """
        try:
            ais_spline = preview_spline_data.get("ais_spline")
            if not ais_spline:
                return

            from OCC.Core.Quantity import Quantity_Color
            from OCC.Core.Aspect import Aspect_TOL_DASH

            # 设置预览样条线为醒目的橙色，加粗显示
            preview_color = Quantity_Color(1.0, 0.6, 0.0, 0)  # 更亮的橙色
            ais_spline.SetColor(preview_color)
            ais_spline.SetWidth(5.0)  # 更粗的线条，更醒目

            # 设置为虚线样式（如果支持）
            try:
                from OCC.Core.Aspect import Aspect_TOL_DASH
                from OCC.Core.Prs3d import Prs3d_LineAspect

                # 获取AIS对象的属性
                attributes = ais_spline.Attributes()
                if attributes is None:
                    from OCC.Core.AIS import AIS_GraphicTool
                    attributes = AIS_GraphicTool.GetLineAspect()

                # 设置虚线样式
                line_aspect = Prs3d_LineAspect(preview_color, Aspect_TOL_DASH, 5.0)
                attributes.SetLineAspect(line_aspect)
                ais_spline.SetAttributes(attributes)
                print("🎨 预览样条线设置为虚线样式")
            except Exception as e:
                print(f"⚠️ 虚线样式设置失败，使用实线: {e}")

            # 不设置透明度，确保完全可见
            # try:
            #     ais_spline.SetTransparency(0.3)
            # except:
            #     pass

            print("✅ 预览样条线样式设置完成（醒目橙色，加粗显示）")

        except Exception as e:
            print(f"❌ 设置预览样条线样式失败: {e}")

    def hide_original_spline_for_preview(self):
        """
        在预览模式下隐藏原始样条线
        """
        try:
            if not hasattr(self, 'last_edited_spline') or not self.last_edited_spline:
                print("❌ 没有可隐藏的原始样条线")
                return

            # 避免重复隐藏
            if hasattr(self, 'original_spline_hidden') and self.original_spline_hidden:
                print("ℹ️ 原始样条线已经隐藏")
                return

            original_ais = self.last_edited_spline.get("ais_spline")
            if original_ais:
                context = self.display.GetContext()
                context.Erase(original_ais, False)
                self.original_spline_hidden = True
                print("✅ 隐藏原始样条线进入预览模式")
            else:
                print("❌ 原始样条线AIS对象不存在")

        except Exception as e:
            print(f"❌ 隐藏原始样条线失败: {e}")
            import traceback
            traceback.print_exc()

    def restore_original_spline(self):
        """
        恢复原始样条线显示
        """
        try:
            if hasattr(self, 'original_spline_hidden') and self.original_spline_hidden:
                if hasattr(self, 'last_edited_spline') and self.last_edited_spline:
                    original_ais = self.last_edited_spline.get("ais_spline")
                    if original_ais:
                        context = self.display.GetContext()
                        context.Display(original_ais, False)
                        self.original_spline_hidden = False
                        print("✅ 恢复原始样条线显示")

        except Exception as e:
            print(f"❌ 恢复原始样条线失败: {e}")

    def clear_preview_spline(self):
        """
        立即清除预览样条线
        """
        try:
            print("🧹 开始立即清除预览样条线")

            # 清除新的预览样条线对象
            if hasattr(self, 'preview_spline_ais') and self.preview_spline_ais:
                try:
                    context = self.display.GetContext()
                    context.Erase(self.preview_spline_ais, True)  # 🔧 立即清除
                    self.preview_spline_ais = None
                    print("🗑️ 立即清除新预览样条线AIS对象")
                except Exception as e:
                    print(f"⚠️ 清除新预览AIS对象失败: {e}")

            # 清除旧的预览样条线数据
            if hasattr(self, 'preview_spline_data') and self.preview_spline_data:
                # 隐藏预览样条线
                preview_ais = self.preview_spline_data.get("ais_spline")
                if preview_ais:
                    context = self.display.GetContext()
                    context.Erase(preview_ais, True)  # 🔧 立即清除
                    print("🗑️ 立即清除旧预览样条线AIS对象")

                # 从样条线列表中移除预览样条线
                if hasattr(self, 'spline_list') and self.preview_spline_data in self.spline_list:
                    self.spline_list.remove(self.preview_spline_data)
                    print("🗑️ 从样条线列表中移除预览对象")

                # 清除预览数据
                self.preview_spline_data = None
                print("🗑️ 清除旧预览数据完成")

            # 🚀 强制立即刷新显示
            try:
                self.display.Repaint()
                print("🚀 立即刷新显示完成")
            except Exception as e:
                print(f"⚠️ 立即刷新失败: {e}")

            print("✅ 立即清除预览样条线完成")

        except Exception as e:
            print(f"❌ 立即清除预览样条线失败: {e}")
            import traceback
            traceback.print_exc()

    def exit_preview_mode(self):
        """
        退出预览模式
        """
        try:
            print("🚪 开始退出预览模式")

            # 清除预览样条线
            self.clear_preview_spline()

            # 恢复原始样条线
            self.restore_original_spline()

            # 重置预览模式标志
            if hasattr(self, 'preview_mode'):
                self.preview_mode = False

            # 重置原始样条线隐藏标志
            if hasattr(self, 'original_spline_hidden'):
                self.original_spline_hidden = False

            # 刷新显示
            self.display.Repaint()

            print("✅ 退出预览模式完成")

        except Exception as e:
            print(f"❌ 退出预览模式失败: {e}")
            import traceback
            traceback.print_exc()



    def clear_temp_highlight_points(self):
        """
        清理临时高亮点
        """
        try:
            if hasattr(self, 'temp_highlight_points'):
                context = self.display.GetContext()
                for ais_point in self.temp_highlight_points:
                    try:
                        context.Remove(ais_point, False)
                    except:
                        pass
                self.temp_highlight_points = []
                context.UpdateCurrentViewer()

        except Exception as e:
            print(f"清理临时高亮点时发生错误: {e}")

    def sync_tree_with_display(self):
        """
        同步树形控件的勾选状态和显示状态
        """
        try:
            context = self.display.GetContext()
            
            # 同步点的显示状态
            if hasattr(self, 'point_list'):
                for point_data in self.point_list:
                    if "tree_item" in point_data and "ais_point" in point_data:
                        tree_item = point_data["tree_item"]
                        ais_point = point_data["ais_point"]

                        # 检查树形控件的勾选状态
                        should_be_visible = (tree_item.checkState(0) == Qt.Checked)

                        # 查询所有父节点的状态
                        parent = tree_item.parent()
                        while parent and should_be_visible:
                            if parent.checkState(0) != Qt.Checked:
                                should_be_visible = False
                                break
                            parent = parent.parent()

                        # 检查当前显示状态
                        is_visible = context.IsDisplayed(ais_point)

                        # 如果不一致，进行调整
                        if should_be_visible and not is_visible:
                            context.Display(ais_point, False)
                        elif not should_be_visible and is_visible:
                            context.Erase(ais_point, False)

            # 同步样条线的显示状态
            if hasattr(self, 'spline_list'):
                for spline_data in self.spline_list:
                    if "tree_item" in spline_data and "ais_spline" in spline_data:
                        tree_item = spline_data["tree_item"]
                        ais_spline = spline_data["ais_spline"]

                        # 检查树形控件的勾选状态
                        should_be_visible = (tree_item.checkState(0) == Qt.Checked)

                        # 查询所有父节点的状态
                        parent = tree_item.parent()
                        while parent and should_be_visible:
                            if parent.checkState(0) != Qt.Checked:
                                should_be_visible = False
                                break
                            parent = parent.parent()

                        # 检查当前显示状态
                        is_visible = context.IsDisplayed(ais_spline)

                        # 如果不一致，进行调整
                        if should_be_visible and not is_visible:
                            context.Display(ais_spline, False)
                            # 同时显示构成样条线的点
                            for point_data in spline_data.get("point_ais_list", []):
                                context.Display(point_data["ais_point"], False)
                        elif not should_be_visible and is_visible:
                            context.Erase(ais_spline, False)
                            # 同时隐藏构成样条线的点
                            for point_data in spline_data.get("point_ais_list", []):
                                context.Erase(point_data["ais_point"], False)

            # 更新视图
            context.UpdateCurrentViewer()
            self.display.Repaint()

        except Exception as e:
            print(f"同步树形控件和显示状态时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def add_point_to_canvas(self, coords, point_name=None):
        """将新点添加到画布中
        
        Args:
            coords: 点的坐标列表 [x, y, z]
            point_name: 可选的点名称
        """
        try:
            from OCC.Core.gp import gp_Pnt
            from OCC.Core.AIS import AIS_Point
            from OCC.Core.Geom import Geom_CartesianPoint
            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex
            from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
            
            # 创建几何点
            x, y, z = coords
            pnt = gp_Pnt(x, y, z)
            vertex = BRepBuilderAPI_MakeVertex(pnt).Vertex()
            geom_point = Geom_CartesianPoint(pnt)
            
            # 创建AIS点以便显示
            ais_point = AIS_Point(geom_point)
            ais_point.SetColor(Quantity_Color(Quantity_NOC_WHITE))
            self.display.Context.Display(ais_point, True)
            
            # 如果未提供点名称，生成一个默认名称
            if not point_name:
                # 找到现有的最大点编号
                max_num = 0
                for i in range(self.tree.topLevelItemCount()):
                    item = self.tree.topLevelItem(i)
                    for j in range(item.childCount()):
                        child = item.child(j)
                        if child.text(0).startswith("点"):
                            try:
                                num = int(child.text(0)[1:])
                                max_num = max(max_num, num)
                            except ValueError:
                                pass
                point_name = f"点{max_num + 1}"
            
            # 查找活跃图层
            active_layer = self.find_active_layer()
            
            # 如果没有找到活跃图层，查找或创建"点"顶级节点
            if not active_layer:
                # 查找是否存在"点"顶级节点，而不是"几何体"
                if hasattr(self, 'point_top_item'):
                    active_layer = self.point_top_item
                else:
                    # 查找是否存在"点"顶级节点
                    for i in range(self.tree.topLevelItemCount()):
                        item = self.tree.topLevelItem(i)
                        if item.text(0) == "点"or item.text(0) == "图集":
                            active_layer = item
                            break
                    
                    # 如果仍未找到，创建"点"顶级节点
                    if not active_layer:
                        active_layer = QTreeWidgetItem(self.tree)
                        active_layer.setText(0, "点")
                        active_layer.setFlags(active_layer.flags() | Qt.ItemIsUserCheckable)
                        active_layer.setCheckState(0, Qt.Checked)
                        self.point_top_item = active_layer
            
            # 添加点到活跃图层
            point_item = QTreeWidgetItem(active_layer)
            point_item.setText(0, point_name)
            point_item.setFlags(point_item.flags() | Qt.ItemIsUserCheckable)
            point_item.setCheckState(0, Qt.Checked)
            
            # 添加坐标子项
            x_item = QTreeWidgetItem(point_item)
            x_item.setText(0, f"X:{x}")
            x_item.setFlags(x_item.flags() | Qt.ItemIsUserCheckable)
            x_item.setCheckState(0, Qt.Checked)
            
            y_item = QTreeWidgetItem(point_item)
            y_item.setText(0, f"Y:{y}")
            y_item.setFlags(y_item.flags() | Qt.ItemIsUserCheckable)
            y_item.setCheckState(0, Qt.Checked)
            
            z_item = QTreeWidgetItem(point_item)
            z_item.setText(0, f"Z:{z}")
            z_item.setFlags(z_item.flags() | Qt.ItemIsUserCheckable)
            z_item.setCheckState(0, Qt.Checked)
            
            # 展开树到点层级
            self.tree.expandItem(active_layer)
            
            # 更新显示
            self.display.Repaint()
            
            # 如果存在 point_list 属性，添加点到列表中
            if hasattr(self, 'point_list'):
                point_data = {
                    "name": point_name,
                    "coordinates": [x, y, z],
                    "ais_point": ais_point,
                    "vertex": vertex,
                    "tree_item": point_item
                }
                self.point_list.append(point_data)
            
            # 设置点的样式为统一的交叉型样式
            self.set_unified_point_style(ais_point, "WHITE", False)
            self.display.Context.Redisplay(ais_point, True)
            
            return point_item
        except Exception as e:
            print(f"添加点到画布时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def execute_copy_action(self):
        """执行复制操作"""
        print("执行复制操作")
        try:
            # 确保当前目录在Python路径中
            import sys
            import os
            
            # 确保Deepship目录在Python路径中
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.append(current_dir)
            
            # 直接导入CopyPointDialog类
            try:
                from copy import CopyPointDialog
                print("成功导入CopyPointDialog")
            except ImportError as e:
                print(f"导入CopyPointDialog失败: {e}")
                import traceback
                traceback.print_exc()
                return
            
            # 创建并显示复制点对话框
            dialog = CopyPointDialog(self)
            
            # 获取X、Y、Z轴偏移值，如果为空使用默认值0
            x_offset = 0
            if self.text_edit_3.text():
                try:
                    x_offset = float(self.text_edit_3.text())
                except ValueError:
                    print("X轴偏移值无效，将使用默认值0")
                    
            # 获取Y轴偏移值
            y_offset = 0
            if self.text_edit_4.text():
                try:
                    y_offset = float(self.text_edit_4.text())
                except ValueError:
                    print("Y轴偏移值无效，将使用默认值0")
                    
            # 获取Z轴偏移值
            z_offset = 0
            if self.text_edit_5.text():
                try:
                    z_offset = float(self.text_edit_5.text())
                except ValueError:
                    print("Z轴偏移值无效，将使用默认值0")
            
            # 如果有任意轴的偏移值，预设值
            dialog.x_offset_spinbox.setValue(x_offset)
            dialog.y_offset_spinbox.setValue(y_offset)
            dialog.z_offset_spinbox.setValue(z_offset)
            dialog.update_preview()
                
            if dialog.exec_():
                # 获取新的点坐标
                new_points = dialog.get_new_points()
                print(f"获取到新点坐标: {new_points}")
                
                if new_points:
                    # 查找活跃图层
                    active_layer = self.find_active_layer()
                    print(f"活跃图层: {active_layer.text(0) if active_layer else 'None'}")
                    
                    # 执行点创建
                    self.execute_point_command(new_points, active_layer)
                    
                    # 显示成功信息
                    self.statusBar().showMessage(f"已成功复制 {len(new_points)} 个点", 3000)
                    print(f"已复制 {len(new_points)} 个点")
                else:
                    print("没有获取到新点坐标")
            else:
                print("用户取消了复制操作")
        except Exception as e:
            print(f"执行复制操作时发生错误: {e}")
            import traceback
            traceback.print_exc()
            self.statusBar().showMessage(f"复制失败: {str(e)}", 3000)

    def clear_copy_action(self):
        """清理复制操作相关的资源"""
        print("清理复制操作相关的资源")
        # 这里只需清理状态即可，不需要特别的资源释放
        self.statusBar().showMessage("已退出复制模式", 2000)
        # 确保重置所有点的颜色
        if hasattr(self, 'reset_all_points_color'):
            self.reset_all_points_color()

    def on_ok_button_3_clicked(self):
        """处理X轴OK按钮点击事件"""
        try:
            # 获取X轴文本框中的值
            x_value = self.text_edit_3.text()
            
            # 即使X轴值为空，也继续执行复制操作
            # 检查是否有复制功能被选中
            if self.checkbox_1.isChecked():
                print("复制功能已启用，将使用X轴偏移值")
                # 执行复制操作，即使X轴值为空
                self.execute_copy_action()
            else:
                # 其他功能处理，可能需要有效的X轴值
                if not x_value:
                    print("请输入X轴数值")
                    return
                    
                # 尝试将输入值转换为数值
                try:
                    x_value = float(x_value)
                    print(f"已设置X轴值: {x_value}")
                    # 处理其他功能...
                except ValueError:
                    print("无效的X轴数值，请输入有效的数字")
                        
        except Exception as e:
            print(f"处理X轴输入时发生错误: {e}")
            import traceback
            traceback.print_exc()

    def on_point_coordinate_changed(self, point_index, x, y, z):
        """
        处理控制点坐标变化

        Args:
            point_index: 点索引
            x, y, z: 新的坐标值
        """
        try:
            print(f"🎯 接收到控制点坐标变化信号 - 点{point_index+1}: ({x:.2f}, {y:.2f}, {z:.2f})")

            # 更新当前编辑样条线的坐标数据
            if hasattr(self, 'last_edited_spline') and self.last_edited_spline:
                points = self.last_edited_spline.get("points", [])
                if point_index < len(points):
                    # 立即更新坐标
                    points[point_index] = [x, y, z]
                    self.last_edited_spline["points"] = points

                    print(f"✅ 立即更新样条线数据中的控制点{point_index+1}坐标")

                    # 🎨 检查实时预览状态并立即更新
                    if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                        if hasattr(self.active_tangent_editor, 'preview_enabled') and self.active_tangent_editor.preview_enabled:
                            print(f"🚀 实时预览已启用，立即更新预览显示")
                            # 立即触发实时预览更新
                            success = self.update_coordinate_preview_immediately(point_index, x, y, z)
                            if success:
                                print("✅ 实时预览更新成功")
                            else:
                                print("❌ 实时预览更新失败")
                        else:
                            print("ℹ️ 实时预览未启用，坐标变化已保存")
                    else:
                        print("⚠️ 编辑器对象不存在，无法检查预览状态")
                else:
                    print(f"❌ 点索引超出范围: {point_index} >= {len(points)}")
            else:
                print("❌ 没有当前编辑的样条线数据")

        except Exception as e:
            print(f"❌ 处理控制点坐标变化失败: {e}")
            import traceback
            traceback.print_exc()

    def update_coordinate_preview_immediately(self, point_index, x, y, z):
        """
        立即更新坐标变化的实时预览

        Args:
            point_index: 变化的点索引
            x, y, z: 新的坐标值
        """
        try:
            print(f"🎨 开始立即更新坐标实时预览 - 点{point_index+1}: ({x:.2f}, {y:.2f}, {z:.2f})")

            # 获取当前样条线数据
            spline_data = self.last_edited_spline
            if not spline_data:
                print("❌ 没有当前编辑的样条线数据")
                return False

            points = spline_data.get("points", [])
            print(f"🎯 当前样条线控制点数量: {len(points)}")

            if point_index >= len(points):
                print(f"❌ 点索引超出范围: {point_index} >= {len(points)}")
                return False

            # 🔧 立即清除之前的预览
            self.clear_preview_spline()
            print("🧹 已清除之前的预览对象")

            # 🔧 立即隐藏原始样条线
            if spline_data.get("ais_spline"):
                try:
                    self.display.Context.Erase(spline_data["ais_spline"], True)
                    print("👻 已隐藏原始样条线，进入预览模式")
                except Exception as e:
                    print(f"⚠️ 隐藏原始样条线失败: {e}")

            # 🔧 获取当前启用的切矢数据
            preview_tangent_data = {}
            if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                enabled_tangents = self.active_tangent_editor.get_enabled_tangents()
                preview_tangent_data = enabled_tangents

                print(f"🔍 获取到当前启用的切矢: {len(enabled_tangents)} 个")
                for idx, data in enabled_tangents.items():
                    dir_vec = data['direction']
                    length_val = data['length']
                    print(f"   点{idx+1}: 方向({dir_vec[0]:.2f}, {dir_vec[1]:.2f}, {dir_vec[2]:.2f}), 长度{length_val:.2f}")

            # 🎨 立即创建并显示实时预览样条线
            success = self.create_and_display_preview_immediately(points, preview_tangent_data)

            if success:
                print("✅ 坐标实时预览立即更新成功")
                return True
            else:
                print("❌ 坐标实时预览立即更新失败")
                return False

        except Exception as e:
            print(f"❌ 立即更新坐标实时预览失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_coordinate_preview(self, point_index, x, y, z):
        """
        更新坐标变化的实时预览

        Args:
            point_index: 变化的点索引
            x, y, z: 新的坐标值
        """
        try:
            print(f"🎨 更新坐标实时预览 - 点{point_index+1}: ({x:.2f}, {y:.2f}, {z:.2f})")

            # 获取当前样条线数据
            spline_data = self.last_edited_spline
            if not spline_data:
                print("❌ 没有当前编辑的样条线数据")
                return

            points = spline_data.get("points", [])
            print(f"🎯 当前样条线控制点: {points}")

            if point_index >= len(points):
                print(f"❌ 点索引超出范围: {point_index} >= {len(points)}")
                return

            # 获取当前启用的切矢数据
            preview_tangent_data = {}
            if hasattr(self, 'active_tangent_editor') and self.active_tangent_editor:
                enabled_tangents = self.active_tangent_editor.get_enabled_tangents()
                preview_tangent_data = enabled_tangents

                print(f"🔍 当前启用的切矢: {len(enabled_tangents)} 个")
                for idx, data in enabled_tangents.items():
                    dir_vec = data['direction']
                    length_val = data['length']
                    print(f"   点{idx+1}: 方向({dir_vec[0]:.2f}, {dir_vec[1]:.2f}, {dir_vec[2]:.2f}), 长度{length_val:.2f}")

            # 清除之前的预览
            self.clear_preview_spline()

            # 隐藏原始样条线进入预览模式
            if spline_data.get("ais_spline"):
                try:
                    self.display.Context.Erase(spline_data["ais_spline"], True)
                    print("✅ 隐藏原始样条线进入预览模式")
                except Exception as e:
                    print(f"⚠️ 隐藏原始样条线失败: {e}")

            # 创建实时预览样条线
            success = self.create_coordinate_preview_spline(points, preview_tangent_data)

            if success:
                print("✅ 坐标实时预览更新成功")
            else:
                print("❌ 坐标实时预览更新失败")

        except Exception as e:
            print(f"❌ 更新坐标实时预览失败: {e}")
            import traceback
            traceback.print_exc()

    def create_coordinate_preview_spline(self, points, tangent_data=None):
        """
        创建坐标变化的实时预览样条线

        Args:
            points: 更新后的控制点列表
            tangent_data: 切矢数据（可选）

        Returns:
            bool: 创建是否成功
        """
        try:
            print(f"🎨 开始创建坐标实时预览样条线，控制点数: {len(points)}")

            # 验证控制点数量
            if len(points) < 2:
                print("❌ 控制点数量不足，至少需要2个点")
                return False

            # 使用增强样条线建模创建预览
            preview_edge = None

            if tangent_data and len(tangent_data) > 0:
                print(f"🎨 使用增强样条线建模创建预览（包含切矢约束）")
                print(f"🎨 应用切矢约束，约束点数: {len(tangent_data)}")

                try:
                    from enhanced_spline_modeling import create_spline_with_tangents_preview
                    preview_edge = create_spline_with_tangents_preview(points, tangent_data)
                    print("✅ 增强样条线预览创建成功")
                except Exception as e:
                    print(f"❌ 增强样条线预览创建失败: {e}")
                    # 回退到基础预览
                    preview_edge = None

            if not preview_edge:
                print("🎨 创建基础实时预览样条线")
                try:
                    from enhanced_spline_modeling import create_basic_spline_preview
                    preview_edge = create_basic_spline_preview(points)
                    print("✅ 基础样条线预览创建成功")
                except Exception as e:
                    print(f"❌ 基础样条线预览创建失败: {e}")
                    # 最终回退：使用内置方法
                    preview_edge = self.create_simple_spline_preview(points)

            if preview_edge:
                # 显示预览样条线
                success = self.display_preview_spline(preview_edge)
                return success
            else:
                print("❌ 所有预览样条线创建方法都失败")
                return False

        except Exception as e:
            print(f"❌ 创建坐标实时预览样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_simple_spline_preview(self, points):
        """
        创建简单的样条线预览（内置方法回退）

        Args:
            points: 控制点列表

        Returns:
            TopoDS_Edge: 样条线边或None
        """
        try:
            print("🎨 使用内置方法创建简单样条线预览")

            # 导入必要的模块
            from OCC.Core.TColgp import TColgp_Array1OfPnt
            from OCC.Core.gp import gp_Pnt
            from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
            from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge

            # 创建点数组
            point_array = TColgp_Array1OfPnt(1, len(points))
            for i, point in enumerate(points):
                gp_point = gp_Pnt(float(point[0]), float(point[1]), float(point[2]))
                point_array.SetValue(i + 1, gp_point)

            # 创建B样条曲线
            spline_generator = GeomAPI_PointsToBSpline(point_array)
            if spline_generator.IsDone():
                curve = spline_generator.Curve()

                # 创建边
                edge_builder = BRepBuilderAPI_MakeEdge(curve)
                if edge_builder.IsDone():
                    edge = edge_builder.Edge()
                    print("✅ 简单样条线预览创建成功")
                    return edge
                else:
                    print("❌ 创建样条线边失败")
                    return None
            else:
                print("❌ B样条曲线生成失败")
                return None

        except Exception as e:
            print(f"❌ 创建简单样条线预览失败: {e}")
            return None

    def display_preview_spline(self, preview_edge):
        """
        显示预览样条线

        Args:
            preview_edge: 预览样条线边

        Returns:
            bool: 显示是否成功
        """
        try:
            print("🎨 开始显示实时预览样条线")

            # 创建AIS对象
            from OCC.Core.AIS import AIS_Shape
            from OCC.Core.Quantity import Quantity_Color, Quantity_TOC_RGB

            ais_preview = AIS_Shape(preview_edge)

            # 设置预览样式（橙色，加粗）
            preview_color = Quantity_Color(1.0, 0.5, 0.0, Quantity_TOC_RGB)  # 橙色
            ais_preview.SetColor(preview_color)
            ais_preview.SetWidth(5.0)

            print("🎨 设置预览样条线样式（橙色，加粗）")

            # 显示预览
            self.display.Context.Display(ais_preview, True)
            print("🎨 实时预览样条线已显示到3D视图")

            # 设置为虚线样式（如果支持）
            try:
                from OCC.Core.Aspect import Aspect_TOL_DASH
                from OCC.Core.Prs3d import Prs3d_LineAspect

                # 获取AIS对象的属性
                attributes = ais_preview.Attributes()
                if attributes is None:
                    from OCC.Core.AIS import AIS_GraphicTool
                    attributes = AIS_GraphicTool.GetLineAspect()

                # 设置虚线样式
                line_aspect = Prs3d_LineAspect(preview_color, Aspect_TOL_DASH, 5.0)
                attributes.SetLineAspect(line_aspect)
                ais_preview.SetAttributes(attributes)
                print("🎨 实时预览样条线设置为虚线样式")
            except Exception as e:
                print(f"⚠️ 虚线样式设置失败，使用实线: {e}")

            print("✅ 实时预览样条线样式设置完成（醒目橙色，加粗显示）")

            # 保存预览对象引用
            self.preview_spline_ais = ais_preview

            # 刷新显示
            self.display.Repaint()

            print("✅ 实时预览样条线显示成功")
            return True

        except Exception as e:
            print(f"❌ 显示实时预览样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_and_display_preview_immediately(self, points, tangent_data=None):
        """
        立即创建并显示预览样条线

        Args:
            points: 更新后的控制点列表
            tangent_data: 切矢数据（可选）

        Returns:
            bool: 创建和显示是否成功
        """
        try:
            print(f"🚀 开始立即创建并显示预览样条线，控制点数: {len(points)}")

            # 验证控制点数量
            if len(points) < 2:
                print("❌ 控制点数量不足，至少需要2个点")
                return False

            # 🎨 立即创建预览样条线
            preview_edge = None

            # 尝试多种创建方法，确保成功
            if tangent_data and len(tangent_data) > 0:
                print(f"🎨 尝试创建带切矢约束的预览样条线")
                try:
                    from enhanced_spline_modeling import create_spline_with_tangents_preview
                    preview_edge = create_spline_with_tangents_preview(points, tangent_data)
                    if preview_edge:
                        print("✅ 带切矢约束的预览样条线创建成功")
                except Exception as e:
                    print(f"⚠️ 带切矢约束的预览创建失败: {e}")

            if not preview_edge:
                print("🎨 尝试创建基础预览样条线")
                try:
                    from enhanced_spline_modeling import create_basic_spline_preview
                    preview_edge = create_basic_spline_preview(points)
                    if preview_edge:
                        print("✅ 基础预览样条线创建成功")
                except Exception as e:
                    print(f"⚠️ 基础预览创建失败: {e}")

            if not preview_edge:
                print("🎨 尝试使用内置方法创建预览样条线")
                preview_edge = self.create_simple_spline_preview(points)
                if preview_edge:
                    print("✅ 内置方法预览样条线创建成功")

            if preview_edge:
                # 🎨 立即显示预览样条线
                success = self.display_preview_spline_immediately(preview_edge)
                return success
            else:
                print("❌ 所有预览样条线创建方法都失败")
                return False

        except Exception as e:
            print(f"❌ 立即创建并显示预览样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def display_preview_spline_immediately(self, preview_edge):
        """
        立即显示预览样条线

        Args:
            preview_edge: 预览样条线边

        Returns:
            bool: 显示是否成功
        """
        try:
            print("🎨 开始立即显示实时预览样条线")

            # 创建AIS对象
            from OCC.Core.AIS import AIS_Shape
            from OCC.Core.Quantity import Quantity_Color, Quantity_TOC_RGB

            ais_preview = AIS_Shape(preview_edge)

            # 🎨 设置醒目的预览样式
            preview_color = Quantity_Color(1.0, 0.5, 0.0, Quantity_TOC_RGB)  # 橙色
            ais_preview.SetColor(preview_color)
            ais_preview.SetWidth(6.0)  # 更粗的线条，更醒目

            print("🎨 设置预览样条线样式（醒目橙色，加粗显示）")

            # 🎨 立即显示预览
            self.display.Context.Display(ais_preview, True)  # 🔧 修改为立即更新
            print("🎨 实时预览样条线已立即显示到上下文")

            # 🎨 设置虚线样式（如果支持）
            try:
                from OCC.Core.Aspect import Aspect_TOL_DASH
                from OCC.Core.Prs3d import Prs3d_LineAspect

                # 获取AIS对象的属性
                attributes = ais_preview.Attributes()
                if attributes is None:
                    from OCC.Core.AIS import AIS_GraphicTool
                    attributes = AIS_GraphicTool.GetLineAspect()

                # 设置虚线样式
                line_aspect = Prs3d_LineAspect(preview_color, Aspect_TOL_DASH, 6.0)
                attributes.SetLineAspect(line_aspect)
                ais_preview.SetAttributes(attributes)
                print("🎨 实时预览样条线设置为虚线样式")
            except Exception as e:
                print(f"⚠️ 虚线样式设置失败，使用实线: {e}")

            # 保存预览对象引用
            self.preview_spline_ais = ais_preview

            # 🚀 强制立即刷新显示（多重刷新确保立即可见）
            print("🚀 开始强制立即刷新显示")

            # 方法1：强制重绘
            self.display.Repaint()
            print("   - 执行Repaint()刷新")

            # 方法2：更新视图
            try:
                self.display.View.Update()
                print("   - 执行View.Update()更新")
            except Exception as e:
                print(f"   - View.Update()失败: {e}")

            # 方法3：刷新上下文
            try:
                self.display.Context.UpdateCurrentViewer()
                print("   - 执行Context.UpdateCurrentViewer()刷新")
            except Exception as e:
                print(f"   - Context.UpdateCurrentViewer()失败: {e}")

            # 方法4：强制重新计算
            try:
                self.display.Context.RecomputePrsOnly(ais_preview, True)
                print("   - 执行RecomputePrsOnly()重新计算")
            except Exception as e:
                print(f"   - RecomputePrsOnly()失败: {e}")

            print("🚀 强制立即刷新显示完成")

            print("✅ 实时预览样条线立即显示成功")
            return True

        except Exception as e:
            print(f"❌ 立即显示实时预览样条线失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def cleanup_editor_state(self):
        """
        清理编辑器状态，确保tree控件坐标编辑功能正常
        """
        try:
            print("🧹 开始清理编辑器状态")

            # 清理活动编辑器引用
            if hasattr(self, 'active_tangent_editor'):
                self.active_tangent_editor = None
                print("✅ 已清理活动编辑器引用")

            # 清理当前编辑样条线引用
            if hasattr(self, 'current_editing_spline'):
                self.current_editing_spline = None
                print("✅ 已清理当前编辑样条线引用")

            # 重置最后编辑样条线引用（但保留用于其他功能）
            if hasattr(self, 'last_edited_spline'):
                # 不完全清除，但标记编辑状态结束
                print("✅ 已标记编辑状态结束")

            # 清理保存的编辑器状态
            if hasattr(self, 'saved_editor_state'):
                self.saved_editor_state = None
                print("✅ 已清理保存的编辑器状态")

            # 清理保存的样条线引用
            if hasattr(self, 'saved_editing_spline'):
                self.saved_editing_spline = None
                print("✅ 已清理保存的样条线引用")

            # 确保预览模式完全退出
            if hasattr(self, 'preview_mode'):
                self.preview_mode = False

            # 清理坐标预览相关状态
            if hasattr(self, 'coordinate_preview_ais'):
                self.coordinate_preview_ais = None

            if hasattr(self, 'coordinate_preview_points'):
                self.coordinate_preview_points = None

            # 刷新显示
            self.display.Repaint()

            print("✅ 编辑器状态清理完成，tree控件坐标编辑功能已恢复")

        except Exception as e:
            print(f"❌ 清理编辑器状态失败: {e}")
            import traceback
            traceback.print_exc()

    # 🆕 新功能：曲线选择模式支持
    def start_curve_selection_mode(self, callback):
        """
        启动曲线选择模式

        Args:
            callback: 曲线选择完成后的回调函数
        """
        try:
            print("🎯 进入曲线选择模式")

            # 保存回调函数
            self.curve_selection_callback = callback

            # 设置选择模式标志
            self.curve_selection_mode = True

            # 🔧 修复：不再需要替换also_on_select方法
            # 曲线选择直接在鼠标点击事件中处理
            if hasattr(self, 'curve_selection_mode') and self.curve_selection_mode:
                print("⚠️ 已经在曲线选择模式中")
                return

            # 更新状态栏或提示信息
            print("✅ 曲线选择模式已启动，请在画布中点击选择曲线")
            print("🎯 曲线选择模式：现在可以在画布中点击样条线进行选择")

            # 🔧 修复：确保画布获得焦点并可以接收鼠标事件
            try:
                from PyQt5.QtCore import Qt

                # 设置十字光标
                self.canvas.setCursor(Qt.CrossCursor)
                print("✅ 已设置十字光标提示用户选择")

                # 🆕 关键修复：激活画布窗口并设置焦点
                self.canvas.activateWindow()
                self.canvas.setFocus()
                self.canvas.raise_()  # 将画布提到前台

                # 确保画布可以接收鼠标事件
                self.canvas.setAttribute(Qt.WA_AcceptTouchEvents, False)  # 禁用触摸事件避免冲突
                self.canvas.setMouseTracking(True)  # 启用鼠标跟踪

                print("✅ 画布已激活并获得焦点，可以接收鼠标事件")

            except Exception as cursor_error:
                print(f"⚠️ 设置画布焦点失败: {cursor_error}")

        except Exception as e:
            print(f"❌ 启动曲线选择模式失败: {e}")
            import traceback
            traceback.print_exc()



    def is_same_curve(self, spline_data, shape):
        """
        判断选中的形状是否与样条线匹配

        Args:
            spline_data: 样条线数据
            shape: 选中的形状

        Returns:
            bool: 是否匹配
        """
        try:
            # 方法1: 直接比较AIS对象
            if spline_data.get("ais_spline") == shape:
                return True

            # 方法2: 比较底层的Edge对象
            if hasattr(spline_data, "edge") and hasattr(shape, "IsEqual"):
                try:
                    if shape.IsEqual(spline_data["edge"]):
                        return True
                except:
                    pass

            # 方法3: 通过AIS对象的Shape方法获取底层形状进行比较
            try:
                ais_shape = spline_data.get("ais_spline")
                if hasattr(ais_shape, "Shape") and hasattr(shape, "IsEqual"):
                    if shape.IsEqual(ais_shape.Shape()):
                        return True
            except:
                pass

            return False

        except Exception as e:
            print(f"❌ 曲线匹配判断失败: {e}")
            return False

    def exit_curve_selection_mode(self):
        """退出曲线选择模式"""
        try:
            print("🔚 退出曲线选择模式")

            # 🔧 修复：不再需要恢复also_on_select方法
            # 直接清除选择模式标志
            self.curve_selection_mode = False

            # 清除回调函数
            if hasattr(self, 'curve_selection_callback'):
                delattr(self, 'curve_selection_callback')

            # 恢复默认光标
            try:
                from PyQt5.QtCore import Qt
                self.canvas.setCursor(Qt.ArrowCursor)
                print("✅ 已恢复默认光标")
            except Exception as cursor_error:
                print(f"⚠️ 恢复光标失败: {cursor_error}")

            print("✅ 曲线选择模式已退出")

        except Exception as e:
            print(f"❌ 退出曲线选择模式失败: {e}")
            import traceback
            traceback.print_exc()

    def handle_curve_selection_click(self, mouse_pos):
        """
        处理曲线选择模式下的鼠标点击

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)
        """
        try:
            print(f"🔍 曲线选择模式：处理鼠标点击 ({mouse_pos.x()}, {mouse_pos.y()})")

            # 🔧 修复：使用现有的样条线查找机制
            spline_data = self.find_spline_by_mouse_position(mouse_pos)

            if spline_data:
                print(f"✅ 曲线选择模式：找到样条线")

                # 准备曲线数据，使用更准确的名称
                spline_index = None
                for i, existing_spline in enumerate(self.spline_list):
                    if existing_spline == spline_data:
                        spline_index = i + 1
                        break

                curve_name = f"样条线{spline_index}" if spline_index else "未知样条线"

                curve_data = {
                    'name': curve_name,
                    'edge': spline_data.get('edge'),
                    'points': spline_data.get('points', []),
                    'type': 'spline',
                    'spline_data': spline_data  # 保存完整的样条线数据
                }

                # 调用曲线选择处理方法
                if hasattr(self, 'curve_selection_callback') and self.curve_selection_callback:
                    self.curve_selection_callback(curve_data)

                # 退出曲线选择模式
                self.exit_curve_selection_mode()

            else:
                print("❌ 曲线选择模式：没有找到样条线")
                print("💡 提示：请点击画布中的样条线曲线")

        except Exception as e:
            print(f"❌ 处理曲线选择点击失败: {e}")
            import traceback
            traceback.print_exc()

    # 🆕 新功能：控制点选择模式支持
    def start_point_selection_mode(self, callback):
        """
        启动控制点选择模式

        Args:
            callback: 控制点选择完成后的回调函数
        """
        try:
            print("🎯 启动控制点选择模式")

            # 保存回调函数
            self.point_selection_callback = callback

            # 设置选择模式标志
            self.point_selection_mode = True

            # 检查是否已经在控制点选择模式中
            if hasattr(self, 'point_selection_mode') and self.point_selection_mode:
                print("⚠️ 已经在控制点选择模式中")
                return

            # 更新状态栏或提示信息
            print("✅ 控制点选择模式已启动，请在画布中点击选择控制点")

            # 设置光标为十字光标，表示选择模式
            from PyQt5.QtCore import Qt
            self.canvas.setCursor(Qt.CrossCursor)

        except Exception as e:
            print(f"❌ 启动控制点选择模式失败: {e}")

    def exit_point_selection_mode(self):
        """退出控制点选择模式"""
        try:
            print("🔚 退出控制点选择模式")

            # 清除选择模式标志
            self.point_selection_mode = False

            # 清除回调函数
            if hasattr(self, 'point_selection_callback'):
                delattr(self, 'point_selection_callback')

            # 恢复默认光标
            from PyQt5.QtCore import Qt
            self.canvas.setCursor(Qt.ArrowCursor)
            print("✅ 已恢复默认光标")

            print("✅ 控制点选择模式已退出")

        except Exception as e:
            print(f"❌ 退出控制点选择模式失败: {e}")

    def handle_point_selection_click(self, mouse_pos):
        """
        处理控制点选择模式下的鼠标点击

        Args:
            mouse_pos: 鼠标点击位置 (QPoint)
        """
        try:
            print(f"🔍 控制点选择模式：处理鼠标点击 ({mouse_pos.x()}, {mouse_pos.y()})")

            # 查找点击位置的控制点
            point_data = self.find_control_point_at_position(mouse_pos)

            if point_data:
                print("✅ 控制点选择模式：找到控制点")

                # 调用控制点选择处理方法
                if hasattr(self, 'point_selection_callback') and self.point_selection_callback:
                    self.point_selection_callback(point_data)

                # 退出控制点选择模式
                self.exit_point_selection_mode()

            else:
                print("❌ 控制点选择模式：没有找到控制点")
                print("💡 提示：请点击画布中的样条线控制点")

        except Exception as e:
            print(f"❌ 处理控制点选择点击失败: {e}")
            import traceback
            traceback.print_exc()

    def find_control_point_at_position(self, mouse_pos):
        """
        在指定位置查找控制点

        Args:
            mouse_pos: 鼠标位置 (QPoint)

        Returns:
            dict: 控制点数据，包含point_index等信息
        """
        try:
            print(f"🔍 在位置 ({mouse_pos.x()}, {mouse_pos.y()}) 查找控制点")
            print(f"🔍 当前有 {len(self.spline_list) if hasattr(self, 'spline_list') else 0} 条样条线")

            # 🔧 优化：首先尝试直接查找控制点，然后再查找样条线
            direct_point = self.find_control_point_directly(mouse_pos)
            if direct_point:
                print("✅ 直接找到控制点")
                return direct_point

            # 🔧 修复：使用正确的样条线查找方法
            spline_data = self.find_spline_by_mouse_position(mouse_pos)

            if spline_data:
                print("✅ 找到样条线，检查控制点")

                # 🔧 优化：使用更直接的方法获取点击的控制点
                context = self.display.GetContext()
                context.MoveTo(mouse_pos.x(), mouse_pos.y(), self.display.GetView(), True)
                context.Select(True)
                context.InitSelected()

                if context.MoreSelected():
                    shape = context.SelectedShape()
                    print(f"精确选择到形状: {type(shape)}")

                    # 🔧 改进：直接检查是否选中了控制点
                    if 'point_ais_list' in spline_data:
                        for i, point_data in enumerate(spline_data['point_ais_list']):
                            point_ais = point_data.get('ais_point')
                            if point_ais:
                                try:
                                    # 检查是否选中了这个控制点的AIS对象
                                    selected_ais = context.SelectedInteractive()
                                    if point_ais == selected_ais:
                                        print(f"✅ 直接匹配到控制点{i+1}")
                                        return {
                                            'point_index': i,
                                            'coordinates': point_data.get('coordinates', [0, 0, 0]),
                                            'spline_data': spline_data,
                                            'method': 'direct_ais_match'
                                        }

                                    # 检查形状匹配
                                    if hasattr(point_ais, 'Shape') and hasattr(selected_ais, 'Shape'):
                                        if point_ais.Shape().IsSame(selected_ais.Shape()):
                                            print(f"✅ 形状匹配到控制点{i+1}")
                                            return {
                                                'point_index': i,
                                                'coordinates': point_data.get('coordinates', [0, 0, 0]),
                                                'spline_data': spline_data,
                                                'method': 'shape_match'
                                            }
                                except Exception as e:
                                    print(f"检查控制点{i+1}时出错: {e}")
                                    continue

                    # 🔧 备用方法：通过坐标距离匹配
                    clicked_coords = self.get_shape_coordinates(shape)
                    if clicked_coords and 'points' in spline_data:
                        print(f"形状坐标: ({clicked_coords[0]:.1f}, {clicked_coords[1]:.1f}, {clicked_coords[2]:.1f})")

                        for i, point_coords in enumerate(spline_data['points']):
                            # 计算距离
                            distance = ((clicked_coords[0] - point_coords[0])**2 +
                                      (clicked_coords[1] - point_coords[1])**2 +
                                      (clicked_coords[2] - point_coords[2])**2)**0.5

                            if distance < 100:  # 增加容差到100mm
                                print(f"✅ 距离匹配到控制点{i+1}，距离: {distance:.1f}mm")
                                return {
                                    'point_index': i,
                                    'coordinates': point_coords,
                                    'spline_data': spline_data,
                                    'distance': distance,
                                    'method': 'distance_match'
                                }

                        print("❌ 没有找到匹配的控制点（距离太远）")
                    else:
                        print("❌ 无法获取形状坐标或样条线数据")
                else:
                    print("❌ 没有选中任何形状")
            else:
                print("❌ 没有找到样条线")

                # 🔧 备用方案：如果没有找到样条线，尝试直接检查是否点击了控制点
                print("🔄 尝试直接查找控制点...")
                context = self.display.GetContext()
                context.MoveTo(mouse_pos.x(), mouse_pos.y(), self.display.GetView(), True)
                context.Select(True)
                context.InitSelected()

                if context.MoreSelected():
                    shape = context.SelectedShape()
                    print(f"直接选择到形状: {type(shape)}")

                    # 检查所有样条线的控制点
                    if hasattr(self, 'spline_list') and self.spline_list:
                        for spline_idx, spline_data in enumerate(self.spline_list):
                            if 'point_ais_list' in spline_data:
                                for point_idx, point_data in enumerate(spline_data['point_ais_list']):
                                    point_ais = point_data.get('ais_point')
                                    if point_ais:
                                        try:
                                            selected_ais = context.SelectedInteractive()
                                            if point_ais == selected_ais:
                                                print(f"✅ 直接找到样条线{spline_idx+1}的控制点{point_idx+1}")
                                                return {
                                                    'point_index': point_idx,
                                                    'coordinates': point_data.get('coordinates', [0, 0, 0]),
                                                    'spline_data': spline_data,
                                                    'method': 'direct_point_search'
                                                }
                                        except Exception as e:
                                            continue

                    print("❌ 直接查找控制点也失败")

            return None

        except Exception as e:
            print(f"❌ 查找控制点失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def find_control_point_directly(self, mouse_pos):
        """
        直接查找控制点，不依赖样条线查找

        Args:
            mouse_pos: 鼠标位置 (QPoint)

        Returns:
            dict: 控制点数据，包含point_index等信息
        """
        try:
            print(f"🔍 直接查找控制点模式")

            # 获取点击位置的选中对象
            context = self.display.GetContext()
            context.MoveTo(mouse_pos.x(), mouse_pos.y(), self.display.GetView(), True)
            context.Select(True)
            context.InitSelected()

            if not context.MoreSelected():
                print("❌ 没有选中任何对象")
                return None

            selected_ais = context.SelectedInteractive()
            selected_shape = context.SelectedShape()

            print(f"🔍 选中的AIS对象: {type(selected_ais)}")
            print(f"🔍 选中的形状: {type(selected_shape)}")

            # 检查所有样条线的控制点
            if hasattr(self, 'spline_list') and self.spline_list:
                for spline_idx, spline_data in enumerate(self.spline_list):
                    print(f"🔍 检查样条线{spline_idx+1}")

                    if 'point_ais_list' in spline_data:
                        print(f"   控制点数量: {len(spline_data['point_ais_list'])}")

                        for point_idx, point_data in enumerate(spline_data['point_ais_list']):
                            point_ais = point_data.get('ais_point')
                            if point_ais:
                                try:
                                    # 方法1：直接AIS对象比较
                                    if point_ais == selected_ais:
                                        print(f"✅ 方法1成功：直接AIS匹配到样条线{spline_idx+1}的控制点{point_idx+1}")
                                        return {
                                            'point_index': point_idx,
                                            'coordinates': point_data.get('coordinates', [0, 0, 0]),
                                            'spline_data': spline_data,
                                            'spline_index': spline_idx,
                                            'method': 'direct_ais_match'
                                        }

                                    # 方法2：形状比较
                                    if hasattr(point_ais, 'Shape') and hasattr(selected_ais, 'Shape'):
                                        try:
                                            if point_ais.Shape().IsSame(selected_ais.Shape()):
                                                print(f"✅ 方法2成功：形状匹配到样条线{spline_idx+1}的控制点{point_idx+1}")
                                                return {
                                                    'point_index': point_idx,
                                                    'coordinates': point_data.get('coordinates', [0, 0, 0]),
                                                    'spline_data': spline_data,
                                                    'spline_index': spline_idx,
                                                    'method': 'shape_match'
                                                }
                                        except Exception as shape_error:
                                            print(f"   形状比较出错: {shape_error}")

                                    # 方法3：坐标距离比较
                                    if 'coordinates' in point_data:
                                        point_coords = point_data['coordinates']
                                        clicked_coords = self.get_shape_coordinates(selected_shape)

                                        if clicked_coords:
                                            distance = ((clicked_coords[0] - point_coords[0])**2 +
                                                      (clicked_coords[1] - point_coords[1])**2 +
                                                      (clicked_coords[2] - point_coords[2])**2)**0.5

                                            print(f"   控制点{point_idx+1}距离: {distance:.1f}mm")

                                            if distance < 100:  # 100mm容差
                                                print(f"✅ 方法3成功：距离匹配到样条线{spline_idx+1}的控制点{point_idx+1}")
                                                return {
                                                    'point_index': point_idx,
                                                    'coordinates': point_coords,
                                                    'spline_data': spline_data,
                                                    'spline_index': spline_idx,
                                                    'distance': distance,
                                                    'method': 'distance_match'
                                                }

                                except Exception as point_error:
                                    print(f"   检查控制点{point_idx+1}时出错: {point_error}")
                                    continue
                    else:
                        print(f"   样条线{spline_idx+1}没有point_ais_list")

            print("❌ 直接查找控制点失败")
            return None

        except Exception as e:
            print(f"❌ 直接查找控制点失败: {e}")
            import traceback
            traceback.print_exc()
            return None

