# 导入所需的库和模块
from typing import List, Tu<PERSON>, Dict, Any  # 导入类型注解相关模块
from PyQt5.QtWidgets import (QMainWindow, QTreeWidget, QTreeWidgetItem, QWidget, 
                            QGridLayout, QMessageBox, QShortcut, QMenu, QTextEdit,
                            QDialog, QVBoxLayout, QFormLayout, QDialogButtonBox, 
                            QLabel, QDoubleSpinBox, QPushButton)  # 导入PyQt5 GUI组件
from PyQt5.QtCore import pyqtSignal, Qt, QEvent  # 导入PyQt5核心模块
from PyQt5.QtGui import QIcon, QKeySequence  # 导入PyQt5图形模块
from OCC.Core.gp import gp_Pnt  # 导入OpenCASCADE几何点类
from OCC.Core.GC import GC_MakeSegment  # 导入OpenCASCADE线段创建类
from OCC.Core.AIS import AIS_Shape  # 导入OpenCASCADE形状显示类
from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge, BRepBuilderAPI_MakeVertex  # 导入OpenCASCADE边和顶点创建类
from OCC.Core.TopAbs import TopAbs_EDGE, TopAbs_VERTEX  # 导入OpenCASCADE拓扑类型
from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE, Quantity_NOC_YELLOW  # 导入OpenCASCADE颜色类
from OCC.Core.Aspect import Aspect_TOM_POINT, Aspect_TOM_PLUS, Aspect_TOM_O, Aspect_TOM_X  # 导入OpenCASCADE点样式
from OCC.Core.Prs3d import Prs3d_TextAspect  # 导入OpenCASCADE文本显示属性
from OCC.Core.Graphic3d import Graphic3d_MaterialAspect  # 导入OpenCASCADE材质属性
from OCC.Core.AIS import AIS_TextLabel  # 导入OpenCASCADE文本标签类
import re  # 导入正则表达式模块
from layout import ExampleLayout  # 导入自定义布局模块

# 定义全局变量
point_list: List[Dict[str, Any]] = []  # 存储点数据的列表
shapeNew: List[Any] = []  # 存储新形状的列表
endpoint_labels: Dict[str, Any] = {}  # 存储端点标签的字典
highlighted_endpoints: Dict[str, Any] = {}  # 存储高亮端点的字典
original_endpoints: Dict[str, Any] = {}  # 存储原始端点的字典

def show_points(self):
    # 仅显示独立点
    self._points_visible = True  # 设置点可见标志为True
    if hasattr(self, 'point_list'):  # 检查是否存在点列表
        for point_data in self.point_list:  # 遍历点列表
            self.ais_context.Display(point_data["ais_point"], True)  # 显示点
    self.display.Repaint()  # 重绘显示

def hide_points(self):
    # 仅隐藏独立点
    self._points_visible = False  # 设置点可见标志为False
    if hasattr(self, 'point_list'):  # 检查是否存在点列表
        for point_data in self.point_list:  # 遍历点列表
            self.ais_context.Erase(point_data["ais_point"], True)  # 隐藏点
    self.display.Repaint()  # 重绘显示

def execute_point_command(self, point_coords):
    """
    创建点并显示在场景中

    Args:
        point_coords: 每个点的坐标列表 [x, y, z]
    """
    from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex  # 导入顶点创建类
    from OCC.Core.AIS import AIS_Shape  # 导入形状显示类
    from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE  # 导入颜色类
    from datetime import datetime  # 导入日期时间模块

    if not hasattr(self, 'point_list'):  # 检查是否存在点列表
        self.point_list = []  # 初始化点列表
        # 如果不存在点的顶层项，则创建
        self.point_top_item = QTreeWidgetItem(self.tree, ['点'])  # 创建树形控件顶层项
        self.tree.expandAll()  # 展开所有树项

    for coords in point_coords:  # 遍历坐标列表
        # 从坐标创建 gp_Pnt
        point = gp_Pnt(*coords)  # 创建几何点

        # 创建顶点并转换为AIS_Shape显示
        vertex = BRepBuilderAPI_MakeVertex(point).Vertex()  # 创建顶点
        ais_point = AIS_Shape(vertex)  # 创建显示形状

        # 设置点的默认外观为统一的交叉型样式
        from OCC.Core.Aspect import Aspect_TOM_PLUS
        from OCC.Core.Prs3d import Prs3d_PointAspect

        # 统一的点大小和样式
        UNIFIED_POINT_SIZE = 6
        color = Quantity_Color(Quantity_NOC_WHITE)

        # 设置点的显示样式为交叉型
        drawer = ais_point.Attributes()
        point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, color, UNIFIED_POINT_SIZE)
        drawer.SetPointAspect(point_aspect)
        ais_point.SetAttributes(drawer)
        ais_point.SetColor(color)

        # 显示点
        self.ais_context = self.display.GetContext()  # 获取显示上下文
        self.ais_context.Display(ais_point, True)  # 显示点

        # 添加到点列表
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # 获取当前时间
        self.point_list.append({"point": point, "ais_point": ais_point, "vertex": vertex})  # 添加点数据

        # 添加到树中
        point_item = QTreeWidgetItem(self.point_top_item, [f"点 [{current_time}]"])  # 创建树项
        coords_item = QTreeWidgetItem(
            point_item,
            [f"坐标: ({point.X():.1f}, {point.Y():.1f}, {point.Z():.1f})"]
        )  # 创建坐标子项

        # 在点数据中存储树项引用
        self.point_list[-1]["tree_item"] = point_item  # 存储树项引用

        # 添加到撤销栈
        self.undo_stack.append(("create_point", self.point_list[-1]))  # 添加撤销操作

    # 按名称排序树项
    self.point_top_item.sortChildren(0, Qt.AscendingOrder)  # 排序树项
    self.display.Repaint()  # 重绘显示

def remove_point(self, point_data, record_undo=True):
    """
    从场景和树中删除点
    """
    try:
        if record_undo:  # 如果需要记录撤销操作
            # 获取树项引用
            tree_item = point_data.get("tree_item", None)  # 获取树项
            tree_item_name = tree_item.text(0) if tree_item else "未知点名称"  # 获取树项名称
            self.undo_stack.append(("delete_point", point_data, tree_item_name))  # 添加撤销操作

        # 从显示中擦除，但只擦除点对象，不影响线端点显示
        self.ais_context.Remove(point_data["ais_point"], False)  # 移除点显示

        # 从点列表中移除
        if point_data in self.point_list:  # 检查点是否在列表中
            self.point_list.remove(point_data)  # 移除点数据

        # 从树中移除
        tree_item = point_data.get("tree_item", None)  # 获取树项
        if tree_item:  # 如果存在树项
            self.point_top_item.removeChild(tree_item)  # 移除树项

        # 更新显示，但不刷新线端点的显示
        self.display.Context.UpdateCurrentViewer()  # 更新显示
        self.display.Repaint()  # 重绘显示
    except Exception as e:  # 捕获异常
        print(f"删除点时发生错误: {e}")  # 打印错误信息

def restore_point(self, point_data, tree_item_name=None):
    """
    恢复先前删除的点
    """
    self.point_list.append(point_data)  # 添加点数据到列表

    if tree_item_name is None:  # 如果未提供树项名称
        tree_item_name = f"点 {len(self.point_list)}"  # 生成默认名称

    # 重新创建树项
    point_item = QTreeWidgetItem(self.point_top_item, [tree_item_name])  # 创建树项
    point_data["tree_item"] = point_item  # 存储树项引用

    # 在树中恢复信息
    point = point_data["point"]  # 获取点数据
    QTreeWidgetItem(
        point_item,
        [f"坐标: ({point.X():.1f}, {point.Y():.1f}, {point.Z():.1f})"]
    )  # 创建坐标子项

    # 排序树项
    self.point_top_item.sortChildren(0, Qt.AscendingOrder)  # 排序树项

    # 显示点
    self.display.Context.Display(point_data["ais_point"], True)  # 显示点
    self.display.Repaint()  # 重绘显示

def reset_all_points_color(self):
    """
    重置所有点的颜色为白色
    """
    if hasattr(self, 'point_list'):  # 检查是否存在点列表
        from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE  # 导入颜色类
        for point_data in self.point_list:  # 遍历点列表
            point_data["ais_point"].SetColor(Quantity_Color(Quantity_NOC_WHITE))  # 设置颜色为白色
        self.display.Context.UpdateCurrentViewer()  # 更新显示

def set_point_style(self, style):
    """
    设置点的显示样式

    Args:
        style: 样式名称 ("pointstye1" 为圆点样式, "pointstye2" 为十字样式)
    """
    from OCC.Core.Aspect import Aspect_TOM_POINT, Aspect_TOM_PLUS, Aspect_TOM_O, Aspect_TOM_X  # 导入点样式
    from OCC.Core.Prs3d import Prs3d_PointAspect  # 导入点显示属性
    from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE  # 导入颜色类

    if not hasattr(self, 'point_list') or not self.point_list:  # 检查是否存在点列表
        QMessageBox.information(self, "提示", "当前场景中没有点")  # 显示提示信息
        return

    # 获取显示上下文
    self.ais_context = self.display.GetContext()  # 获取显示上下文

    # 统一的点大小 - 无论什么样式都使用相同大小
    UNIFIED_POINT_SIZE = 6

    if style == "pointstye1":  # 如果选择圆点样式
        # 设置圆点样式
        marker_type = Aspect_TOM_O  # 使用圆圈标记
        marker_size = UNIFIED_POINT_SIZE  # 使用统一大小
        message = "已设置点样式: 圆点"  # 设置状态栏消息
    elif style == "pointstye2":  # 如果选择十字样式
        # 设置十字样式
        marker_type = Aspect_TOM_PLUS  # 使用加号标记
        marker_size = UNIFIED_POINT_SIZE  # 使用统一大小
        message = "已设置点样式: 十字"  # 设置状态栏消息
    else:  # 如果样式未知
        QMessageBox.warning(self, "错误", f"未知的点样式: {style}")  # 显示错误信息
        return

    # 遍历所有点
    for point_data in self.point_list:  # 遍历点列表
        if "ais_point" in point_data:  # 检查是否存在显示形状
            ais_point = point_data["ais_point"]  # 获取显示形状

            # 更改点的显示属性
            if style == "pointstye1":  # 如果选择圆点样式
                # 圆点样式 - 使用SetMarkerType方法
                ais_point.Attributes().PointAspect().SetTypeOfMarker(Aspect_TOM_O)  # 设置标记类型
            else:  # pointstye2
                # 十字样式
                ais_point.Attributes().PointAspect().SetTypeOfMarker(Aspect_TOM_PLUS)  # 设置标记类型

            # 设置大小
            ais_point.Attributes().PointAspect().SetScale(marker_size)  # 设置标记大小

            # 更新点的显示
            self.ais_context.Redisplay(ais_point, True)  # 重绘显示

    # 更新显示
    self.ais_context.UpdateCurrentViewer()  # 更新显示
    self.display.Repaint()  # 重绘显示

    # 显示状态栏消息
    self.statusBar().showMessage(message, 3000)  # 显示状态栏消息

def _is_same_point(self, p1, p2, tolerance=0.001):
    """比较两个点是否相同"""
    return (abs(p1.X() - p2.X()) < tolerance and
            abs(p1.Y() - p2.Y()) < tolerance and
            abs(p1.Z() - p2.Z()) < tolerance)  # 比较三个坐标值是否在容差范围内

def create_point_edit_dialog(self, point_data):
    """
    创建并返回用于编辑点坐标的对话框

    Args:
        point_data: 包含点信息的字典

    Returns:
        配置用于点编辑的QDialog对象
    """
    dialog = QDialog(self)  # 创建对话框
    dialog.setWindowTitle("修改点坐标")  # 设置对话框标题
    layout = QVBoxLayout()  # 创建垂直布局

    form_layout = QFormLayout()  # 创建表单布局

    # 获取当前坐标
    current_point = point_data["point"]  # 获取当前点
    x, y, z = current_point.X(), current_point.Y(), current_point.Z()  # 获取坐标值

    # 为每个坐标创建带有适当范围和精度的微调框
    x_spinbox = QDoubleSpinBox()  # 创建X坐标微调框
    x_spinbox.setRange(-10000, 10000)  # 设置范围
    x_spinbox.setDecimals(2)  # 设置小数位数
    x_spinbox.setValue(x)  # 设置当前值
    x_spinbox.setSingleStep(1.0)  # 设置步长

    y_spinbox = QDoubleSpinBox()  # 创建Y坐标微调框
    y_spinbox.setRange(-10000, 10000)  # 设置范围
    y_spinbox.setDecimals(2)  # 设置小数位数
    y_spinbox.setValue(y)  # 设置当前值
    y_spinbox.setSingleStep(1.0)  # 设置步长

    z_spinbox = QDoubleSpinBox()  # 创建Z坐标微调框
    z_spinbox.setRange(-10000, 10000)  # 设置范围
    z_spinbox.setDecimals(2)  # 设置小数位数
    z_spinbox.setValue(z)  # 设置当前值
    z_spinbox.setSingleStep(1.0)  # 设置步长

    form_layout.addRow("X 坐标:", x_spinbox)  # 添加X坐标行
    form_layout.addRow("Y 坐标:", y_spinbox)  # 添加Y坐标行
    form_layout.addRow("Z 坐标:", z_spinbox)  # 添加Z坐标行
    layout.addLayout(form_layout)  # 添加表单布局

    # 添加标准OK/Cancel按钮
    button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)  # 创建按钮框
    button_box.accepted.connect(dialog.accept)  # 连接接受信号
    button_box.rejected.connect(dialog.reject)  # 连接拒绝信号
    layout.addWidget(button_box)  # 添加按钮框

    dialog.setLayout(layout)  # 设置对话框布局
    dialog.spinboxes = (x_spinbox, y_spinbox, z_spinbox)  # 存储微调框引用
    return dialog  # 返回对话框

def update_point_coordinates(self, point_data, new_coords):
    """
    更新点的坐标并刷新其显示

    Args:
        point_data: 包含点信息的字典
        new_coords: 新坐标的元组 (x, y, z)
    """
    from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeVertex  # 导入顶点创建类
    from OCC.Core.gp import gp_Pnt  # 导入几何点类

    # 使用更新后的坐标创建新点
    new_point = gp_Pnt(*new_coords)  # 创建新点

    # 存储旧顶点和AIS形状以便移除
    old_ais_point = point_data["ais_point"]  # 获取旧显示形状

    # 创建新顶点和AIS形状
    new_vertex = BRepBuilderAPI_MakeVertex(new_point).Vertex()  # 创建新顶点
    new_ais_point = AIS_Shape(new_vertex)  # 创建新显示形状

    # 从旧点应用相同的视觉属性
    if hasattr(old_ais_point, "GetColor"):  # 检查是否存在颜色属性
        color = old_ais_point.GetColor()  # 获取颜色
        new_ais_point.SetColor(color)  # 设置颜色

    # 如果点有自定义样式，尝试保留它
    try:
        if hasattr(old_ais_point, "Attributes") and hasattr(old_ais_point.Attributes(), "PointAspect"):  # 检查点样式属性
            # 正确的方法可能是SetTypeOfMarker，而不是GetTypeOfMarker
            aspect = old_ais_point.Attributes().PointAspect()  # 获取点样式
            if hasattr(aspect, "GetTypeOfMarker"):  # 检查是否存在标记类型
                marker_type = aspect.GetTypeOfMarker()  # 获取标记类型
                new_ais_point.Attributes().PointAspect().SetTypeOfMarker(marker_type)  # 设置标记类型

            # 如果可用，获取比例
            if hasattr(aspect, "GetScale"):  # 检查是否存在比例
                marker_scale = aspect.GetScale()  # 获取比例
                new_ais_point.Attributes().PointAspect().SetScale(marker_scale)  # 设置比例
    except Exception as e:  # 捕获异常
        print(f"复制点样式时发生错误: {e}")  # 打印错误信息

    # 移除旧点并显示新点
    self.ais_context = self.display.GetContext()  # 获取显示上下文
    self.ais_context.Remove(old_ais_point, False)  # 移除旧点
    self.ais_context.Display(new_ais_point, True)  # 显示新点

    # 更新点数据
    point_data["point"] = new_point  # 更新点
    point_data["vertex"] = new_vertex  # 更新顶点
    point_data["ais_point"] = new_ais_point  # 更新显示形状

    # 更新树项文本
    tree_item = point_data.get("tree_item")  # 获取树项
    if tree_item and tree_item.childCount() > 0:  # 检查是否存在子项
        coords_item = tree_item.child(0)  # 获取坐标子项
        coords_item.setText(0, f"坐标: ({new_point.X():.1f}, {new_point.Y():.1f}, {new_point.Z():.1f})")  # 更新坐标文本

    # 更新显示
    self.display.Context.UpdateCurrentViewer()  # 更新显示
    self.display.Repaint()  # 重绘显示