# 导入所需的库和模块
from typing import List, Tuple, Dict, Any
from PyQt5.QtWidgets import (QTreeWidgetItem, QMessageBox)
from PyQt5.QtCore import Qt
from OCC.Core.gp import gp_Pnt
from OCC.Core.GeomAPI import GeomAPI_PointsToBSpline
from OCC.Core.TColgp import TColgp_Array1OfPnt
from OCC.Core.AIS import AIS_Shape
from OCC.Core.BRepBuilderAPI import BRepBuilderAPI_MakeEdge, BRepBuilderAPI_MakeVertex
from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE, Quantity_NOC_GREEN
from OCC.Core.GeomAbs import GeomAbs_C2
from OCC.Core.Aspect import Aspect_TOM_POINT, Aspect_TOM_PLUS
from OCC.Core.Prs3d import Prs3d_PointAspect
import re
from datetime import datetime

def parse_spline_coordinates(self, text: str) -> List[List[float]]:
    """
    解析样条线坐标字符串，将其转换为坐标列表

    Args:
        text: 包含点坐标的字符串，例如 "0,1,2/33,44,50.5/100,200,300"

    Returns:
        包含点坐标的列表，每个点是一个 [x, y, z] 列表
    """
    points = []
    # 使用斜杠分割点
    for point_str in text.split('/'):
        try:
            coords = [float(coord) for coord in point_str.split(",")]
            if len(coords) == 3:  # 检查是否为三维坐标
                points.append(coords)
        except ValueError:
            continue  # 跳过无效坐标
    return points

def execute_spline_command(self, points, active_layer=None):
    """
    根据给定的点坐标创建样条线

    Args:
        points: 包含点坐标的列表，每个点是一个 [x, y, z] 列表
        active_layer: 活跃图层，如果为 None，则在顶层创建
    """
    if not points:
        QMessageBox.warning(self, "错误", "创建样条线至少需要两个有效坐标点")
        return

    if len(points) < 2:
        QMessageBox.warning(self, "错误", "创建样条线至少需要两个点")
        return

    try:
        # 创建TColgp_Array1OfPnt对象
        array = TColgp_Array1OfPnt(1, len(points))
        
        # 填充点数组
        for i, point in enumerate(points, 1):
            if len(point) == 3:
                pnt = gp_Pnt(point[0], point[1], point[2])
                array.SetValue(i, pnt)
            else:
                QMessageBox.warning(self, "错误", f"无效的坐标: {point}")
                return

        # 创建B样条曲线
        try:
            bspline = GeomAPI_PointsToBSpline(array, GeomAbs_C2).Curve()
        except RuntimeError as e:
            QMessageBox.warning(self, "错误", f"无法从给定点创建样条曲线: {str(e)}")
            return

        # 创建边
        edge = BRepBuilderAPI_MakeEdge(bspline).Edge()
        
        # 创建AIS形状
        ais_spline = AIS_Shape(edge)
        
        # 设置样条线颜色为绿色
        ais_spline.SetColor(Quantity_Color(Quantity_NOC_GREEN))
        
        # 设置线宽
        ais_spline.SetWidth(2.0)
        
        # 显示样条线
        self.ais_context = self.display.GetContext()
        self.ais_context.Display(ais_spline, True)
        
        # 查找活跃图层
        if active_layer is None:
            active_layer = self.find_active_layer()
            
        # 如果没有找到活跃图层，默认创建"样条线"顶层项
        if active_layer is None:
            # 查找或创建"样条线"顶层项
            spline_top_item = None
            for i in range(self.tree.topLevelItemCount()):
                item = self.tree.topLevelItem(i)
                if item.text(0) == "样条线":
                    spline_top_item = item
                    break
            
            if spline_top_item is None:
                spline_top_item = QTreeWidgetItem(self.tree, ['样条线'])
                spline_top_item.setFlags(spline_top_item.flags() | Qt.ItemIsUserCheckable)
                spline_top_item.setCheckState(0, Qt.Checked)
            
            active_layer = spline_top_item

        # 生成时间戳
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 创建样条线数据
        spline_data = {
            "ais_spline": ais_spline,
            "edge": edge,
            "points": points,
            "tree_item": None,
            "point_ais_list": []  # 存储构成样条线的点的AIS对象
        }
        
        # 为样条线的每个控制点创建可视化点
        for i, point_coords in enumerate(points):
            # 创建点几何对象
            point = gp_Pnt(*point_coords)
            vertex = BRepBuilderAPI_MakeVertex(point).Vertex()
            ais_point = AIS_Shape(vertex)
            
            # 设置点的默认外观为白色
            color = Quantity_Color(Quantity_NOC_WHITE)
            ais_point.SetColor(color)
            ais_point.SetWidth(12)  # 设置点的大小，从8增加到12
            
            # 设置点的显示样式为统一的交叉型样式
            UNIFIED_POINT_SIZE = 6  # 统一的点大小
            drawer = ais_point.Attributes()
            point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, Quantity_Color(Quantity_NOC_WHITE), UNIFIED_POINT_SIZE)
            drawer.SetPointAspect(point_aspect)
            ais_point.SetAttributes(drawer)
            
            # 强制更新点的显示
            self.ais_context.Redisplay(ais_point, True)
            
            # 显示点
            self.ais_context.Display(ais_point, False)
            
            # 添加到样条线数据中
            spline_data["point_ais_list"].append({
                "ais_point": ais_point,
                "vertex": vertex,
                "point": point,
                "coordinates": point_coords
            })

        # 添加到样条线列表（如果不存在则创建）
        if not hasattr(self, 'spline_list'):
            self.spline_list = []
        self.spline_list.append(spline_data)

        # 在树形控件中添加样条线信息
        spline_item = QTreeWidgetItem(active_layer, [f"样条线 [{current_time}]"])
        spline_item.setFlags(spline_item.flags() | Qt.ItemIsUserCheckable)
        spline_item.setCheckState(0, Qt.Checked)
        spline_data["tree_item"] = spline_item

        # 添加点坐标子项
        for i, point in enumerate(points):
            coord_item = QTreeWidgetItem(spline_item, [f"点{i+1}: ({point[0]}, {point[1]}, {point[2]})"])
            coord_item.setFlags(coord_item.flags() | Qt.ItemIsUserCheckable)
            coord_item.setCheckState(0, Qt.Checked)

        # 展开到样条线节点级别
        self.tree.expandItem(active_layer)
        self.tree.expandItem(spline_item)

        # 更新显示
        self.display.Repaint()
        
        self.statusBar().showMessage(f"已成功创建包含{len(points)}个点的样条线", 3000)

    except Exception as e:
        QMessageBox.warning(self, "错误", f"创建样条线失败: {str(e)}")
        import traceback
        traceback.print_exc()

def show_splines(self):
    """
    显示所有样条线
    """
    if hasattr(self, 'spline_list'):
        for spline_data in self.spline_list:
            if "ais_spline" in spline_data:
                self.ais_context.Display(spline_data["ais_spline"], True)
                # 同时显示构成样条线的点
                for point_data in spline_data.get("point_ais_list", []):
                    self.ais_context.Display(point_data["ais_point"], False)
        self.display.Repaint()

def hide_splines(self):
    """
    隐藏所有样条线
    """
    if hasattr(self, 'spline_list'):
        for spline_data in self.spline_list:
            if "ais_spline" in spline_data:
                self.ais_context.Erase(spline_data["ais_spline"], True)
                # 同时隐藏构成样条线的点
                for point_data in spline_data.get("point_ais_list", []):
                    self.ais_context.Erase(point_data["ais_point"], False)
        self.display.Repaint()
        
def remove_spline(self, spline_data, record_undo=True):
    """
    从场景和树中删除样条线
    """
    try:
        if record_undo:
            # 获取树项引用
            tree_item = spline_data.get("tree_item", None)
            tree_item_name = tree_item.text(0) if tree_item else "未知样条线名称"
            if not hasattr(self, 'undo_stack'):
                self.undo_stack = []
            self.undo_stack.append(("delete_spline", spline_data, tree_item_name))

        # 从显示中擦除样条线和点
        self.ais_context.Remove(spline_data["ais_spline"], False)
        # 移除构成样条线的点
        for point_data in spline_data.get("point_ais_list", []):
            self.ais_context.Remove(point_data["ais_point"], False)

        # 从样条线列表中移除
        if hasattr(self, 'spline_list') and spline_data in self.spline_list:
            self.spline_list.remove(spline_data)

        # 从树中移除
        tree_item = spline_data.get("tree_item", None)
        if tree_item:
            parent = tree_item.parent()
            if parent:
                parent.removeChild(tree_item)

        # 更新显示
        self.display.Context.UpdateCurrentViewer()
        self.display.Repaint()
    except Exception as e:
        print(f"删除样条线时发生错误: {e}")
        
def restore_spline(self, spline_data, tree_item_name=None):
    """
    恢复先前删除的样条线
    """
    # 添加到样条线列表
    if not hasattr(self, 'spline_list'):
        self.spline_list = []
    self.spline_list.append(spline_data)

    # 重新创建树项
    if tree_item_name is None:
        tree_item_name = f"样条线 {len(self.spline_list)}"

    # 查找或创建"样条线"顶层项
    spline_top_item = None
    for i in range(self.tree.topLevelItemCount()):
        item = self.tree.topLevelItem(i)
        if item.text(0) == "样条线":
            spline_top_item = item
            break

    if spline_top_item is None:
        spline_top_item = QTreeWidgetItem(self.tree, ['样条线'])
        spline_top_item.setFlags(spline_top_item.flags() | Qt.ItemIsUserCheckable)
        spline_top_item.setCheckState(0, Qt.Checked)

    spline_item = QTreeWidgetItem(spline_top_item, [tree_item_name])
    spline_data["tree_item"] = spline_item

    # 在树中恢复信息
    points = spline_data["points"]
    for i, point in enumerate(points):
        QTreeWidgetItem(
            spline_item,
            [f"点{i+1}: ({point[0]}, {point[1]}, {point[2]})"]
        )

    # 排序树项
    spline_top_item.sortChildren(0, Qt.AscendingOrder)

    # 显示样条线和点
    self.display.Context.Display(spline_data["ais_spline"], True)
    for point_data in spline_data.get("point_ais_list", []):
        self.display.Context.Display(point_data["ais_point"], False)
        # 应用正确的点样式
        self._apply_point_style(point_data["ais_point"])
        
    self.display.Repaint()

def _apply_point_style(self, ais_point):
    """
    应用交叉型样式到点对象
    
    Args:
        ais_point: AIS点对象
    """
    from OCC.Core.Aspect import Aspect_TOM_PLUS
    from OCC.Core.Prs3d import Prs3d_PointAspect
    from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
    
    # 设置点的显示样式为统一的交叉型样式
    UNIFIED_POINT_SIZE = 6  # 统一的点大小
    drawer = ais_point.Attributes()
    point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, Quantity_Color(Quantity_NOC_WHITE), UNIFIED_POINT_SIZE)
    drawer.SetPointAspect(point_aspect)
    ais_point.SetAttributes(drawer)
    
    # 设置颜色为白色
    ais_point.SetColor(Quantity_Color(Quantity_NOC_WHITE))

def set_spline_points_style(self, style):
    """
    设置样条线构成点的显示样式

    Args:
        style: 样式名称 ("pointstye1" 为圆点样式, "pointstye2" 为十字样式)
    """
    if not hasattr(self, 'spline_list') or not self.spline_list:
        QMessageBox.information(self, "提示", "当前场景中没有样条线")
        return

    # 获取显示上下文
    self.ais_context = self.display.GetContext()

    if style == "pointstye1":
        # 设置圆点样式（但我们仍然使用PLUS标记以保持一致性）
        marker_type = Aspect_TOM_PLUS
        marker_size = 5
        message = "已设置样条线构成点样式: 圆点(但使用十字标记)"
    elif style == "pointstye2":
        # 设置十字样式
        marker_type = Aspect_TOM_PLUS
        marker_size = 5
        message = "已设置样条线构成点样式: 十字"
    else:
        QMessageBox.warning(self, "错误", f"未知的点样式: {style}")
        return

    # 遍历所有样条线的构成点
    updated_count = 0
    for spline_data in self.spline_list:
        for point_data in spline_data.get("point_ais_list", []):
            if "ais_point" in point_data:
                ais_point = point_data["ais_point"]

                # 更改点的显示属性
                drawer = ais_point.Attributes()
                point_aspect = Prs3d_PointAspect(marker_type, Quantity_Color(Quantity_NOC_WHITE), marker_size)
                drawer.SetPointAspect(point_aspect)
                ais_point.SetAttributes(drawer)

                # 更新点的显示
                self.ais_context.Redisplay(ais_point, True)
                updated_count += 1

    # 更新显示
    if updated_count > 0:
        self.ais_context.UpdateCurrentViewer()
        self.display.Repaint()
        self.statusBar().showMessage(message, 3000)
    else:
        QMessageBox.information(self, "提示", "没有找到需要更新样式的点")

def ensure_spline_point_style(self):
    """
    确保所有样条线构成点都使用交叉型样式
    """
    if not hasattr(self, 'spline_list') or not self.spline_list:
        return
        
    # 遍历所有样条线的构成点并确保使用交叉型样式
    for spline_data in self.spline_list:
        for point_data in spline_data.get("point_ais_list", []):
            if "ais_point" in point_data:
                from OCC.Core.Aspect import Aspect_TOM_PLUS
                from OCC.Core.Prs3d import Prs3d_PointAspect
                from OCC.Core.Quantity import Quantity_Color, Quantity_NOC_WHITE
                
                # 设置点的显示样式为统一的交叉型样式
                UNIFIED_POINT_SIZE = 6  # 统一的点大小
                drawer = point_data["ais_point"].Attributes()
                point_aspect = Prs3d_PointAspect(Aspect_TOM_PLUS, Quantity_Color(Quantity_NOC_WHITE), UNIFIED_POINT_SIZE)
                drawer.SetPointAspect(point_aspect)
                point_data["ais_point"].SetAttributes(drawer)

                # 设置颜色为白色
                point_data["ais_point"].SetColor(Quantity_Color(Quantity_NOC_WHITE))
                
                # 更新显示
                if hasattr(self, 'ais_context'):
                    self.ais_context.Redisplay(point_data["ais_point"], True)
                    
    # 更新显示
    if hasattr(self, 'ais_context'):
        self.ais_context.UpdateCurrentViewer()
        self.display.Repaint()
