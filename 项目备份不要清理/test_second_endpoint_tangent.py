#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二个端点切矢控制功能测试脚本
验证第二个端点的切矢自动计算和光顺连接功能
"""

import sys
import os
import math

def test_tangent_calculation_from_points():
    """测试从控制点计算切线方向的功能"""
    print("=" * 60)
    print("🧪 测试从控制点计算切线方向")
    print("=" * 60)
    
    try:
        from tangent_editor import TangentEditor
        
        # 创建测试点数据（线性排列）
        test_points = [
            {'coords': [0, 0, 0], 'description': '起点'},
            {'coords': [100, 0, 0], 'description': '点2'},
            {'coords': [200, 50, 0], 'description': '点3'},
            {'coords': [300, 100, 0], 'description': '点4'},
            {'coords': [400, 150, 0], 'description': '终点'}
        ]
        
        editor = TangentEditor(test_points, None)
        
        # 测试起始端点切线计算
        print("📊 测试起始端点切线计算:")
        points_coords = [point['coords'] for point in test_points]
        start_tangent = editor.calculate_tangent_from_curve_points(points_coords, 0)
        
        if start_tangent:
            print(f"✅ 起始端点切线方向: [{start_tangent[0]:.3f}, {start_tangent[1]:.3f}, {start_tangent[2]:.3f}]")
            # 验证方向是否正确（应该指向第二个点）
            expected_direction = [1.0, 0.0, 0.0]  # 从(0,0,0)到(100,0,0)
            is_correct = all(abs(start_tangent[i] - expected_direction[i]) < 0.1 for i in range(3))
            print(f"   方向正确性: {'✅ 正确' if is_correct else '❌ 错误'}")
        else:
            print("❌ 起始端点切线计算失败")
            return False
        
        # 测试结束端点切线计算
        print("📊 测试结束端点切线计算:")
        end_tangent = editor.calculate_tangent_from_curve_points(points_coords, -1)
        
        if end_tangent:
            print(f"✅ 结束端点切线方向: [{end_tangent[0]:.3f}, {end_tangent[1]:.3f}, {end_tangent[2]:.3f}]")
            # 验证方向是否正确（应该从倒数第二个点指向最后一个点）
            p1, p2 = points_coords[-2], points_coords[-1]
            expected_dir = [p2[0] - p1[0], p2[1] - p1[1], p2[2] - p1[2]]
            length = math.sqrt(sum(d * d for d in expected_dir))
            expected_dir = [d / length for d in expected_dir]
            
            is_correct = all(abs(end_tangent[i] - expected_dir[i]) < 0.1 for i in range(3))
            print(f"   方向正确性: {'✅ 正确' if is_correct else '❌ 错误'}")
        else:
            print("❌ 结束端点切线计算失败")
            return False
        
        print("✅ 切线方向计算功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 切线方向计算功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_second_endpoint_tangent_calculation():
    """测试第二个端点切矢计算功能"""
    print("=" * 60)
    print("🧪 测试第二个端点切矢计算")
    print("=" * 60)
    
    try:
        from tangent_editor import TangentEditor
        
        # 创建测试点数据
        test_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 50, 0], 'description': '控制点2'},
            {'coords': [200, 100, 0], 'description': '控制点3'},
            {'coords': [300, 150, 0], 'description': '控制点4'},
            {'coords': [400, 200, 0], 'description': '端点5'}
        ]
        
        editor = TangentEditor(test_points, None)
        
        # 测试起始端点分割的第二个端点切矢计算
        print("📊 测试起始端点分割:")
        split_data_start = editor.prepare_spline_split_data(0)
        
        if split_data_start and split_data_start['can_split']:
            second_tangent = split_data_start.get('second_endpoint_tangent')
            if second_tangent:
                print(f"✅ 起始端点分割 - 第二个端点切矢: [{second_tangent[0]:.3f}, {second_tangent[1]:.3f}, {second_tangent[2]:.3f}]")
                
                # 验证切矢方向的合理性
                length = math.sqrt(sum(d * d for d in second_tangent))
                is_normalized = abs(length - 1.0) < 0.01
                print(f"   切矢归一化: {'✅ 正确' if is_normalized else '❌ 错误'} (长度: {length:.3f})")
            else:
                print("❌ 起始端点分割 - 第二个端点切矢计算失败")
                return False
        else:
            print("❌ 起始端点分割数据准备失败")
            return False
        
        # 测试结束端点分割的第二个端点切矢计算
        print("📊 测试结束端点分割:")
        split_data_end = editor.prepare_spline_split_data(4)
        
        if split_data_end and split_data_end['can_split']:
            second_tangent = split_data_end.get('second_endpoint_tangent')
            if second_tangent:
                print(f"✅ 结束端点分割 - 第二个端点切矢: [{second_tangent[0]:.3f}, {second_tangent[1]:.3f}, {second_tangent[2]:.3f}]")
                
                # 验证切矢方向的合理性
                length = math.sqrt(sum(d * d for d in second_tangent))
                is_normalized = abs(length - 1.0) < 0.01
                print(f"   切矢归一化: {'✅ 正确' if is_normalized else '❌ 错误'} (长度: {length:.3f})")
            else:
                print("❌ 结束端点分割 - 第二个端点切矢计算失败")
                return False
        else:
            print("❌ 结束端点分割数据准备失败")
            return False
        
        print("✅ 第二个端点切矢计算功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 第二个端点切矢计算功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_split_with_second_endpoint_tangent():
    """测试包含第二个端点切矢的分割执行，验证剩余样条线不被强制设置切矢"""
    print("=" * 60)
    print("🧪 测试分割执行中的切矢控制策略修复")
    print("=" * 60)
    
    try:
        from tangent_editor import TangentEditor
        
        # 创建测试点数据
        test_points = [
            {'coords': [0, 0, 0], 'description': '端点1'},
            {'coords': [100, 0, 0], 'description': '控制点2'},
            {'coords': [200, 50, 0], 'description': '控制点3'},
            {'coords': [300, 100, 0], 'description': '控制点4'}
        ]
        
        editor = TangentEditor(test_points, None)
        
        # 设置第一个端点的切矢
        editor.tangent_data[0]['enabled'] = True
        editor.tangent_data[0]['direction'] = [1.0, 0.0, 0.0]
        
        # 准备分割数据
        split_data = editor.prepare_spline_split_data(0)
        if not split_data or not split_data['can_split']:
            print("❌ 分割数据准备失败")
            return False
        
        # 存储分割数据
        editor.endpoint_split_data[0] = split_data
        
        # 执行分割
        split_result = editor.execute_spline_split(0)
        
        if split_result:
            print("✅ 分割执行成功")
            
            # 检查第一条样条线的切矢数据
            spline1_tangents = split_result['split_spline_1']['tangent_data']
            spline2_tangents = split_result['split_spline_2']['tangent_data']
            
            print(f"📊 第一条样条线切矢数: {len(spline1_tangents)}")
            print(f"📊 第二条样条线切矢数: {len(spline2_tangents)}")
            
            # 检查第二个端点的切矢设置
            has_second_endpoint_tangent = False
            for pos, tangent_data in spline1_tangents.items():
                if tangent_data.get('source') == 'second_endpoint_auto':
                    has_second_endpoint_tangent = True
                    direction = tangent_data['direction']
                    print(f"✅ 找到第二个端点切矢 (位置{pos}): [{direction[0]:.3f}, {direction[1]:.3f}, {direction[2]:.3f}]")
                    break
            
            # 🔧 修复验证：检查剩余样条线是否被强制设置切矢
            forced_tangent_found = False
            for pos, tangent_data in spline2_tangents.items():
                if tangent_data.get('source') == 'second_endpoint_auto_opposite':
                    forced_tangent_found = True
                    print(f"❌ 发现强制设置的反向切矢 (位置{pos})，这不应该存在")
                    break

            if has_second_endpoint_tangent and not forced_tangent_found:
                print("✅ 第二个端点切矢正确设置，剩余样条线未被强制设置切矢")
                print("✅ 修复验证通过：剩余样条线保持其原有切矢设置")
                return True
            elif has_second_endpoint_tangent and forced_tangent_found:
                print("❌ 第二个端点切矢设置正确，但剩余样条线被强制设置了切矢")
                print("❌ 修复验证失败：需要移除对剩余样条线的强制切矢设置")
                return False
            else:
                print("❌ 第二个端点切矢设置失败")
                return False
        else:
            print("❌ 分割执行失败")
            return False
        
    except Exception as e:
        print(f"❌ 分割执行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始第二个端点切矢控制功能全面测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("切线方向计算功能", test_tangent_calculation_from_points()))
    test_results.append(("第二个端点切矢计算", test_second_endpoint_tangent_calculation()))
    test_results.append(("切矢控制策略修复验证", test_split_with_second_endpoint_tangent()))
    
    # 汇总测试结果
    print("=" * 80)
    print("📊 测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"🎯 测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！第二个端点切矢控制功能实现正确。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
        return False

if __name__ == "__main__":
    # 确保能够导入必要的模块
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 运行测试
        success = run_all_tests()
        
        if success:
            print("\n🎊 切矢控制策略修复完成！")
            print("📝 修复说明:")
            print("   1. 在端点设置切矢并选择分割时")
            print("   2. 系统自动计算第一条样条线的第二个端点切矢方向")
            print("   3. 剩余样条线（其他控制点重新绘制）保持原有切矢设置")
            print("   4. 不强制给剩余样条线添加新的切矢约束")
            print("   5. 让剩余样条线保持自然形状")
        else:
            print("\n⚠️ 测试发现问题，请检查实现。")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保在正确的项目目录中运行此测试脚本。")
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
